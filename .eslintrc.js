module.exports = {
  env: {
    browser: true,
    es2021: true
  },
  extends: [
    'eslint:recommended',
    'plugin:import/recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:vue/vue3-recommended',
    '.eslintrc-auto-import.json',
    'plugin:import/typescript'
  ],
  overrides: [
    {
      env: {
        node: true
      },
      files: ['.eslintrc.{js,cjs}'],
      parserOptions: {
        sourceType: 'script'
      }
    }
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    parser: '@typescript-eslint/parser',
    sourceType: 'module'
  },
  plugins: ['@typescript-eslint', 'import', 'node', 'vue'],
  globals: {
    document: true,
    localStorage: true,
    window: true
  },
  rules: {
    indent: ['error', 2],
    'linebreak-style': ['error', 'unix'],
    quotes: ['error', 'single'],
    semi: ['error', 'never'],
    'no-console': 'warn',
    'no-undef': 0,
    'vue/attribute-hyphenation': 0,
    'vue/multi-word-component-names': 0,
    'vue/html-self-closing': 0,
    'comma-dangle': [
      'error',
      {
        arrays: 'never',
        objects: 'never',
        imports: 'never',
        exports: 'never',
        functions: 'never'
      }
    ],
    '@typescript-eslint/ban-types': 'off',
    'import/no-unresolved': [
      2,
      {
        ignore: ['^@/']
      }
    ]
  },
  settings: {
    'import/resolver': {
      alias: {
        map: [['@', './src']]
      },
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx', 'vue']
      }
    }
  }
}
