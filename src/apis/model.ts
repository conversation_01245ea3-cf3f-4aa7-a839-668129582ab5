import baseRequest from '../utils/http/baseRequest'

export const PREFIX = '/aip'

// 导航菜单项的接口定义
interface NavigationDetail {
  name: string
  param: Record<string, string>
}

export interface NavigationItem {
  value: string
  name: string
  details: NavigationDetail[]
}

// 模型数据接口定义
interface ModelRecord {
  id: string
  name: string
  code: string
  usageType: string
  groupType: string
  provider: string
  contentLength: number
  size: number
  architectureType: string
  updateTime: string
}

interface ModelPageResponse {
  records: ModelRecord[]
  pageSize: number
  current: number
  total: number
}

interface ModelPageParams {
  current: number
  pageSize: number
  name?: string
  usageType?: { value: string }
  groupOrProvider?: { group: string; provider: string }
  contentLength?: Record<string, string>
  size?: Record<string, string>
  architectureType?: string
}

interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// API 函数
/**
 * 获取模型导航数据
 */
export function getModelNavigation() {
  return baseRequest.get<ApiResponse<NavigationItem[]>>(`${PREFIX}/model/navigation`)
}

/**
 * 获取模型分页数据
 */
export function getModelPage(params: ModelPageParams) {
  return baseRequest.post<ApiResponse<ModelPageResponse>>(`${PREFIX}/model/page`, params)
}

/**
 * 获取智能体导航数据
 */
export function getAgentNavigation() {
  return baseRequest.get<ApiResponse<NavigationItem[]>>(`${PREFIX}/agent/navigation`)
}

/**
 * 获取智能体分页数据
 */
export interface AgentPageParams {
  current?: number
  pageSize?: number
  name?: string
  agentType?: string
}

export interface AgentRecord {
  id: string
  project: string
  subsystem: string
  module: string
  description: string
  type: string
  templateId: string
  reqParam: string
  path: string
  createTime: string
  updateTime: string
}

export interface AgentPageResponse {
  records: AgentRecord[]
  pageSize: number
  current: number
  total: number
}

export function getAgentPage(params: AgentPageParams) {
  return baseRequest.post<ApiResponse<AgentPageResponse>>(`${PREFIX}/agent/page`, params)
}

/**
 * 保存提示信息
 */
export interface SavePromptParams {
  prompt: string
  id: string
}

export function savePrompt(params: SavePromptParams) {
  return baseRequest.post<ApiResponse<null>>(`${PREFIX}/agent/savePrompt`, params)
}

/**
 * 保存工作流ID
 */
export interface SaveWorkFlowIdsParams {
  workFlowIds: string
  id: string
}

export function saveWorkFlowIds(params: SaveWorkFlowIdsParams) {
  return baseRequest.post<ApiResponse<null>>(`${PREFIX}/agent/saveWorkFlowIds`, params)
}
/**
 * 获取Agent系统关键字
 * @param id Agent ID
 * @returns Promise<ApiResponse<string>>
 */
export function getAgentPrompt(pamars) {
  return baseRequest.post<ApiResponse<string>>('/aip/agent/getPrompt', pamars, {
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

/**
 * 获取工作流列表
 * @param params 查询参数
 * @returns Promise<ApiResponse<PageResponse<WorkflowModel>>>
 */
export interface WorkflowModel {
  id: string
  name: string
  content: string
  directoryId: string
  description: string | null
  createTime: string
  updateTime: string
}

export interface PageResponse<T> {
  records: T[]
  pageSize: number
  current: number
  total: number
}

export interface WorkflowQueryParams {
  agentId: string
  current: number
  pageSize: number
}

export function getWorkFlows(params: WorkflowQueryParams) {
  return baseRequest.post<ApiResponse<PageResponse<WorkflowModel>>>(
    '/aip/agent/getWorkFlows',
    params,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    },
  )
}
