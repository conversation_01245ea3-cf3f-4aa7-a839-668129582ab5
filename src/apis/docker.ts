///
/// Copyright (c) 2019 Of Him Code Technology Studio
/// Jpom is licensed under Mulan PSL v2.
/// You can use this software according to the terms and conditions of the Mulan PSL v2.
/// You may obtain a copy of Mulan PSL v2 at:
/// 			http://license.coscl.org.cn/MulanPSL2
/// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
/// See the Mulan PSL v2 for more details.
///

import baseRequest from '../utils/http/baseRequest'

// api前缀常量
export const PREFIX_DOCKER = '/aip/assets/docker'

/**
 * 容器列表
 * @param {JSON} params
 */
export function dockerList(data = {}) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/list-data`, data)
}

/**
 *  获取支持的所有 api 版本
 * @returns json
 */
export function apiVersions() {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/api-versions`)
}

export function editDocker(data = {}) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/edit`, data)
}

/**
 * 删除 docker
 * @param {
 *  id: docker ID
 * } params
 */
export function deleteDcoker(data) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/del`, data)
}

/**
 * 容器中的列表
 * @param {JSON} params
 */
export function dockerContainerList(id) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/container/list?id=${id}`)
}

/**
 * 容器中的列表
 * @param {JSON} params
 */
export function dockerContainerListCompose(id) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/container/list-compose?id=${id}`)
}

/**
 * 查看 docker info
 * @param {JSON} params
 */
export function dockerInfo(id) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/container/info?id=${id}`)
}

/**
 * 修剪 docker
 * @param {JSON} params
 */
export function dockerPrune(id, pruneType, labels, until, dangling) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/container/prune?id=${id}&pruneType=${pruneType}&labels=${labels}&until=${until}&dangling=${dangling}`)
}

/**
 * 删除容器
 * @param {JSON} params
 */
export function dockerContainerRemove(params) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/container/remove`, params)
}

/**
 * 重启容器
 * @param {JSON} params
 */
export function dockerContainerRestart(params) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/container/restart`, params)
}

/**
 * 启动容器
 * @param {JSON} params
 */
export function dockerContainerStart(params) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/container/start`, params)
}

/**
 * 停止容器
 * @param {JSON} params
 */
export function dockerContainerStop(params) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/container/stop`, params)
}

/**
 * 获取容器统计信息
 * @param {JSON} params
 */
export function dockerContainerStats(id, containerId) {
  return baseRequest.get<any>(
    `${PREFIX_DOCKER}/container/stats?id=${id}&containerId=${containerId}`
  )
}

/**
 * 获取容器信息
 * @param {JSON} params
 */
export function dockerInspectContainer(id, containerId) {
  return baseRequest.get<any>(
    `${PREFIX_DOCKER}/container/inspect-container?id=${id}&containerId=${containerId}`
  )
}

/**
 * 更新容器
 * @param {JSON} params
 * @returns
 */
export function dockerUpdateContainer(params) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/container/update-container`, params, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

export function dockerContainerDownloaLog(id) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/container/download-log`, { id: id })
}

/**
 * 容器中的镜像列表
 * @param {JSON} params
 */
export function dockerImagesList(id) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/images/list?id=${id}`)
}

/**
 * 删除镜像
 * @param {JSON} params
 */
export function dockerImageRemove(params) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/images/remove`, params)
}

/**
 * 批量删除镜像
 * @param {JSON} params
 */
export function dockerImageBatchRemove(id,imagesIds) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/images/batchRemove?id=${id}&imagesIds=${imagesIds}`)
}

/**
 * inspect 镜像
 * @param {JSON} params
 */
export function dockerImageInspect(id, imageId) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/images/inspect?id=${id}&imageId=${imageId}`)
}

/**
 * 镜像 创建容器
 * @param {JSON} params
 */
export function dockerImageCreateContainer(data = {}) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/images/create-container`, data, {
    headers: { 'Content-Type': 'application/json' }
  })
}

/**
 * 拉取镜像
 * @param {JSON} params
 */
export function dockerImagePullImage(id, repository) {
  return baseRequest.get<any>(
    `${PREFIX_DOCKER}/images/pull-image?id=${id}&repository=${repository}`
  )
}

/**
 * 导出镜像
 * @param {JSON} params
 */
// export function dockerImageSaveImage(id,imageId) {
//   return baseRequest.get<any>(`${PREFIX_DOCKER}/images/save-image?id=${id}&imageId=${imageId}`)
// }

/**
 * 导入镜像到容器 节点
 * @param {
 *  file: 文件 multipart/form-data,
 *  id: 容器ID,
 *
 * } formData
 */
export function dockerImageLoadImage(formData) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/images/load-image`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data;charset=UTF-8'
    },
    timeout: 0
  })
}

/**
 * 拉取镜像日志
 * @param {JSON} params
 */
export function dockerImagePullImageLog(id, line) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/images/pull-image-log?id=${id}&line=${line}`)
}

/**
 * 卷
 * @param {JSON} params
 */
export function dockerVolumesList(id) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/volumes/list?id=${id}`)
}

/**
 * 删除卷
 * @param {JSON} params
 */
export function dockerVolumesRemove(id, volumeName) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/volumes/remove?id=${id}&volumeName=${volumeName}`)
}

/**
 * 网络
 * @param {JSON} params
 */
export function dockerNetworksList(id) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/networks/list?id=${id}`)
}

export function syncToWorkspace(params) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/sync-to-workspace`, params)
}

export function dockerAllTag(params) {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/all-tag`, params)
}

/**
 * 容器 重建容器
 * @param {JSON} params
 */
export function dockerContainerRebuildContainer(params) {
  return baseRequest.post<any>(`${PREFIX_DOCKER}/container/rebuild-container`, params, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
// 自动探测docker
export function tryLocalDocker() {
  return baseRequest.get<any>(`${PREFIX_DOCKER}/try-local-docker`)
}
// 获取WebSocket URL
export function getWebSocketUrl(url: string, parameter: any) {
  const protocol: string = location.protocol === 'https:' ? 'wss://' : 'ws://'
  const fullUrl: string = url.replace(new RegExp('//', 'gm'), '/')
  return `${protocol}192.168.1.113:9120${fullUrl}?${parameter}`
}
