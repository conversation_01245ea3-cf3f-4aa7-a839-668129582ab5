import baseRequest from '../utils/http/baseRequest'

// api前缀常量
export const PREFIX_AIP = '/aip'

// 分页查询操作日志
export function getOperationLogs(params: {
  current: number
  size: number
  operationType?: string
}) {
  return baseRequest.get(`${PREFIX_AIP}/operation-logs`, params)
}

// 导出操作日志
export function operationLogsExport(params: {
  operationType?: string
  description: string
  startTime?: string
  endTime?: string
}) {
  return baseRequest.get(`${PREFIX_AIP}/operation-logs/export`, params, true)
}

// 删除指定日期之前的日志
export function deleteOperationLogs(params: { time?: string }) {
  return baseRequest.delete(`${PREFIX_AIP}/operation-logs`, params)
}

// 批量删除日志
export function operationLogsDeleteBatch(extend) {
  return baseRequest.delete(`${PREFIX_AIP}/operation-logs/deleteBatch`, null, extend)
}

// 批量删除日志
export function operationLogsQueryId(id: string) {
  return baseRequest.get(`${PREFIX_AIP}/operation-logs/query/${id}`)
}

// 服务器性能监控-最近1小时
export function getMetricsHour() {
  return baseRequest.get(`${PREFIX_AIP}/metrics/hour`)
}

// 服务器性能监控-最近24小时
export function getMetricsDay() {
  return baseRequest.get(`${PREFIX_AIP}/metrics/day`)
}

// 服务器性能监控-最近7小时
export function getMetricsWeek() {
  return baseRequest.get(`${PREFIX_AIP}/metrics/week`)
}

// 许可证管理相关接口
// 分页查询许可证列表
export function getLicenseList(params: {
  pageNo?: number
  pageSize?: number
  licenseNumber?: string
  projectName?: string
  productName?: string
  applicantName?: string
  licenseStatus?: string
}) {
  return baseRequest.get(`${PREFIX_AIP}/license/list`, params)
}

// 创建许可证
export function createLicense(data: {
  projectName: string
  productName: string
  applicantName: string
  issuedTime: string
  expiryTime: string
  content: string
  remarks?: string
  keyPairId: string
}) {
  return baseRequest.post(`${PREFIX_AIP}/license`, data)
}

// 更新许可证
export function updateLicense(data: {
  id: string
  projectName?: string
  productName?: string
  applicantName?: string
  content?: string
  remarks?: string
  keyPairId?: string
  issuedTime?: string
  expiryTime?: string
}) {
  return baseRequest.put(`${PREFIX_AIP}/license`, data)
}

// 续期许可证
export function renewLicense(id: string, expiryTime: string) {
  return baseRequest.post(`${PREFIX_AIP}/license/renew`, { id, expiryTime })
}

// 使许可证失效
export function invalidateLicense(id: string) {
  return baseRequest.put(`${PREFIX_AIP}/license/invalidate/${id}`)
}

// 下载许可证
export function downloadLicense(id: string, fileName?: string) {
  return baseRequest.downloadFile(
    `${PREFIX_AIP}/license/download/${id}`,
    {},
    fileName || `license_${id}.lic`,
  )
}

// 验证许可证
export function verifyLicense() {
  return baseRequest.post(`${PREFIX_AIP}/license/verify`)
}

// 获取服务器信息
export function getServerInfo() {
  return baseRequest.get(`${PREFIX_AIP}/monitor/server/info`)
}

// 获取密钥对列表
export function getKeyPairs(data: {
  pageNo?: number
  pageSize?: number
  keyword?: string | null
} ) {
  return baseRequest.get(`${PREFIX_AIP}/sys/key-pair/list`, data)
}

// 创建密钥对
export function createKeyPair(data) {
  return baseRequest.post(`${PREFIX_AIP}/sys/key-pair`, data)
}

// 获取许可证列表
export function getSysLicenseList(data: {
  pageNo?: number
  pageSize?: number
  keyword?: string | null
} ) {
  return baseRequest.get(`${PREFIX_AIP}/sys/license/list`, data)
}

// 创建许可证
export function createSysLicense(data) {
  return baseRequest.post(`${PREFIX_AIP}/sys/license`, data)
}

// 更新许可证
export function updateSysLicense(data) {
  return baseRequest.post(`${PREFIX_AIP}/sys/license/update`, data)
}

// 下载许可证
export function downloadSysLicense(id: string, fileName?: string) {
  return baseRequest.downloadFile(
    `${PREFIX_AIP}/sys/license/download/${id}`,
    {},
    fileName || `license_${id}.lic`,
  )
}

// 下载公钥
export function downloadSysLicensePublicKey(id: string, fileName?: string) {
  return baseRequest.downloadFile(
    `${PREFIX_AIP}/sys/license/public-key/${id}`,
    {},
    fileName || `public-key_${id}.pub`,
  )
}

// 删除密钥对
export function deleteKeyPair(id: string) {
  return baseRequest.get(`${PREFIX_AIP}/sys/key-pair/delete?id=${id}`)
}