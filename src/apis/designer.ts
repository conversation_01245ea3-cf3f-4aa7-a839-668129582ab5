import baseRequest from '../utils/http/baseRequest'
import { useSSERequest } from '@yss-design/utils/hooks'

// api前缀常量
export const PREFIX_SEE = '/code'
export const PREFIX_SEEP = '/aip'
export const PREFIX_SSE = '/aip/channel/sse'

export const generationCodeUrl = `${PREFIX_SEE}/conversation/request`

export function getSse(data = {}, aipSession: string) {
  return baseRequest.post<any>(`${PREFIX_SSE}?aip-session=${aipSession}`, data)
}
// 获取目录树
export function getFileTree(data = {}) {
  return baseRequest.get<any>(`/aip/directory/full-tree/v2?userId=${data.userId}&type=${data.type}`)
}
// 创建文件夹
export function mkdirApi(data = {}) {
  return baseRequest.post<any>(`${PREFIX_SEEP}/directory/create`, data)
}
// 打开文件
export function getFileApi(data = {}) {
  return baseRequest.get<any>(`${PREFIX_SEEP}/canvas/get/${data.id}`)
}
// 创建文件
export function createFileApi(data = {}) {
  return baseRequest.post<any>('/aip/canvas/create', data)
}
// 保存文件
export function saveFileApi(data = {}) {
  return baseRequest.post<any>(`${PREFIX_SEEP}/canvas/update`, data)
}
// 重命名文件或移动文件夹
export function renameApi(data = {}) {
  return baseRequest.post<any>(`${PREFIX_SEEP}/directory/update`, data)
}
// 删除文件夹
export function deleteApi(data = {}) {
  return baseRequest.post<any>(`${PREFIX_SEEP}/directory/delete`, data)
}
// 删除文件
export function deleteCanvasApi(data = {}) {
  return baseRequest.post<any>(`${PREFIX_SEEP}/canvas/delete`, data)
}

// 获取链接的爬虫接口
export function fetchLinks(url, relativeLinksMethod, relativeLinksLimit) {
  return baseRequest.get<any>(
    `http://127.0.0.1:5173/fetch-links?url=${encodeURIComponent(
      url
    )}&relativeLinksMethod=${relativeLinksMethod}&limit=${relativeLinksLimit}`
  )
}

// oneApi
export function oneApiRequest(data = {}) {
  return baseRequest.post<any>('/v1/chat/completions', data)
}

// 获取flowise节点配置
export function loadFlowiseNode(data = {}) {
  return baseRequest.get<any>(`${PREFIX_SEE}/file/load/config`, data)
}

// 获取知识库路径
export function getDatasetPath(data = {}) {
  return baseRequest.get<any>(`${PREFIX_SEE}/knowledge/dataset/tree`, data)
}

// 试运行
export const tryRunApi_SSE = useSSERequest({
  url: `/aip/debug/canvas/debug?stream=true`,
})

export function tryRunApi(data: FormData, headers: Record<string, string> = {}) {
  return baseRequest.post<any>('/aip/debug/canvas/debug', data, headers)
}
// 获取设计列表
export function getDesignList(params: {
  page: number
  pageSize: number
  type?: string
  startTime?: string
  endTime?: string
  keyword?: string
}) {
  return baseRequest.get<any>('/api/designs', params)
}

// 获取设计详情
export function getDesignDetail(id: string) {
  return baseRequest.get<any>(`/api/designs/${id}`)
}

// 创建设计
export function createDesign(data: { name: string; type: string; config: string }) {
  return baseRequest.post<any>('/api/designs', data)
}

// 更新设计
export function updateDesign(
  id: string,
  data: {
    name: string
    type: string
    config: string
  }
) {
  return baseRequest.put<any>(`/api/designs/${id}`, data)
}

// 删除设计
export function deleteDesign(id: string) {
  return baseRequest.delete<any>(`/api/designs/${id}`)
}

// 获取模板列表
export function getTemplateList() {
  return baseRequest.get<any>('/api/templates')
}

// 获取模板内容
export function getTemplateContent(id: string) {
  return baseRequest.get<any>(`/api/templates/${id}/content`)
}

export const PREFIX_DEBUG = '/aip/debug/canvas/debug'
// 获取chat-debug
export function getChatDebug(data = {}) {
  return baseRequest.post<any>(`${PREFIX_DEBUG}?stream=true`, data)
}

// 创建节点
export function createNode(data = {}) {
  return baseRequest.post<any>('/aip/node/create', data)
}

// 更新节点
export function updateNode(id: string, data = {}) {
  return baseRequest.post<any>('/aip/node/update', data)
}

// 删除节点
export function deleteNode(id: string) {
  return baseRequest.post<any>(`/aip/node/delete/${id}`)
}

// 获取节点
export function getNode(id: string) {
  return baseRequest.get<any>(`/aip/node/get/${id}`)
}

// 分页获取节点列表
export function getNodePage(params: {
  current: number
  pageSize: number
  keyword?: string
  type?: string
}) {
  return baseRequest.get<any>('/aip/node/page', params)
}

// 创建枚举
export function createDesignerEnum(data = {}) {
  return baseRequest.post<any>('/aip/designer-enum/create', data)
}

// 更新枚举
export function updateDesignerEnum(data = {}) {
  return baseRequest.post<any>('/aip/designer-enum/update', data)
}

// 删除枚举
export function deleteDesignerEnum(id: string) {
  return baseRequest.post<any>(`/aip/designer-enum/delete/${id}`)
}

// 获取枚举
export function getDesignerEnum(id: string) {
  return baseRequest.get<any>(`/aip/designer-enum/get/${id}`)
}

// 根据类型获取枚举列表
export function getDesignerEnumList(type: string) {
  return baseRequest.get<any>(`/aip/designer-enum/list/${type}`)
}

// 分页查询枚举
export function getDesignerEnumPage(params: { current: number; size: number; type?: string }) {
  return baseRequest.get<any>('/aip/designer-enum/page', params)
}

// 分页获取端点列表
export function getEndpointPage(params: {
  current: number
  size: number
  project?: string
  subsystem?: string
  module?: string
}) {
  return baseRequest.get<any>('/aip/endpoint/page', params)
}

// 创建端点
export function createEndpoint(data: {
  project: string
  subsystem: string
  module: string
  description: string
  type: string
  templateId: string
}) {
  return baseRequest.post<any>('/aip/endpoint/create', data)
}

// 更新端点
export function updateEndpoint(data: {
  id: string
  project: string
  subsystem: string
  module: string
  description: string
  type: string
  templateId: string
}) {
  return baseRequest.post<any>('/aip/endpoint/update', data)
}

// 删除端点
export function deleteEndpoint(id: string) {
  return baseRequest.post<any>(`/aip/endpoint/delete/${id}`)
}

// 获取端点
export function getEndpoint(id: string) {
  return baseRequest.get<any>(`/aip/endpoint/get/${id}`)
}
// 分页获取画布列表
export function getCanvasPage(params: { current: number; size: number; name?: string }) {
  return baseRequest.get<any>('/aip/canvas/page', params)
}

/**
 * 文件上传
 */
export function uploadFile(data: FormData) {
  return baseRequest.post<any>('/aip/canvas/upload-file', data)
}

// Mock字段生成接口
export function generateMockFields(data = {}) {
  return baseRequest.post<any>('/aip/mock-fields/generate', data)
}

// 数据源相关接口基础路径
const BASE_URL = '/aip/dataSource'

// 数据源类型定义
export interface DataSourceModel {
  id?: string
  code: string
  name: string
  dbType: string
  dbDriver?: string
  dbUrl: string
  dbUsername: string
  dbPassword: string
  dbName: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
}

// 数据源查询参数
export interface DataSourceQueryDTO {
  pageNo?: number
  pageSize?: number
  code?: string
  name?: string
  dbType?: string
}

// 数据库类型定义
export interface DbTypeModel {
  code: string
  driverClass: string
  description: string
}

// 表信息定义
export interface TableInfo {
  tableName: string
  tableCode: string
  tableDescription: string
  columns: ColumnInfo[]
}

// 列信息定义
export interface ColumnInfo {
  columnName: string
  columnCode: string
  columnDescription: string
  dataType: string
  isNullable: string
  isPrimary: string
  columnOrder: number
}

// 分页响应类型
export interface PageResponse<T> {
  total: number
  records: T[]
}

// 通用响应类型
export interface ApiResponse<T> {
  success: boolean
  code: number
  message: string
  data: T
}

/**
 * 保存数据源
 * @param data 数据源信息
 * @returns Promise<ApiResponse<DataSourceModel>>
 */
export function saveDataSource(data: DataSourceModel) {
  return baseRequest.post<ApiResponse<DataSourceModel>>(`${BASE_URL}/save`, data)
}

/**
 * 更新数据源
 * @param data 数据源信息
 * @returns Promise<ApiResponse<DataSourceModel>>
*/
export function updateDataSource(data: DataSourceModel) {
  return baseRequest.put<ApiResponse<DataSourceModel>>(`${BASE_URL}/update`, data)
}

/**
 * 测试数据库连接
 * @param data 数据库连接信息
 * @returns Promise<ApiResponse<boolean>>
 */
export function testDatabaseConnection(data: {
  code: string
  name: string
  dbType: string
  dbDriver?: string
  dbUrl: string
  dbUsername: string
  dbPassword: string
}) {
  return baseRequest.post<ApiResponse<boolean>>(`${BASE_URL}/testConnection`, data)
}

/**
 * 分页查询数据源列表
 * @param params 查询参数
 * @returns Promise<ApiResponse<PageResponse<DataSourceModel>>>
*/
export function getDataSourceList(params: DataSourceQueryDTO) {
  return baseRequest.get<ApiResponse<PageResponse<DataSourceModel>>>(`${BASE_URL}/list`, params)
}

/**
 * 获取数据源详情
 * @param id 数据源ID
 * @returns Promise<ApiResponse<DataSourceModel>>
*/
export function getDataSourceDetail(id: string) {
  return baseRequest.get<ApiResponse<DataSourceModel>>(`${BASE_URL}/get/${id}`)
}

/**
 * 同步数据库表信息
 * @param dataSourceId 数据源ID
 * @returns Promise<ApiResponse<void>>
*/
export function syncDatabaseTables(dataSourceId: string) {
  return baseRequest.post<ApiResponse<void>>(`${BASE_URL}/sync/${dataSourceId}`)
}

/**
 * 获取数据源的表信息
 * @param dataSourceCode 数据源编码
 * @returns Promise<ApiResponse<TableInfo[]>>
*/
export function getDataSourceTables(dataSourceCode: string) {
  return baseRequest.get<ApiResponse<TableInfo[]>>(`${BASE_URL}/tables/${dataSourceCode}`)
}

/**
 * 获取数据库类型列表
 * @returns Promise<ApiResponse<DbTypeModel[]>>
*/
export function getDatabaseTypes() {
  return baseRequest.get<ApiResponse<DbTypeModel[]>>(`${BASE_URL}/dbTypes`)
}

/**
 * 删除数据源
 * @param id 数据源ID
 * @returns Promise<ApiResponse<boolean>>
*/
export function deleteDataSource(id: string) {
  return baseRequest.delete<ApiResponse<boolean>>(`${BASE_URL}/delete/${id}`)
}


/**
 * 生成代码对话
 */
export function sendMessageAsGenerateCode(data = {}, headers: Record<string, string> = {}) {
  return baseRequest.post<any>(generationCodeUrl, data, headers)
}

// 导出节点
export function exportNodesToExcel(data, headers: Record<string, string> = {}, config= {responseType: 'blob'}) {
  return baseRequest.post<any>(`/aip/node/exportExcel`, data, headers, config)
}

// 导入节点
export function importNodesFromExcel(data: FormData) {
  return baseRequest.post<any>(`/aip/node/importExcel`, data)
}

// 生成节点配置
export function generateNodeConfig(data = {}) {
  return baseRequest.post<any>(`/aip/nodemake/getconfig`, data)
}

// 生成节点代码
export function generateNodeCode(data = {}) {
  return baseRequest.post<any>(`/aip/nodemake/code`, data)
}