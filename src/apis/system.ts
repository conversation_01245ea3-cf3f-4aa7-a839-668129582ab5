import baseRequest from '../utils/http/baseRequest'

export const PREFIX_SEEP = '/aip'

// 权限管理相关接口类型定义
export interface PermissionModel {
  id?: string
  name: string
  parentId?: string
  url?: string
  component?: string
  componentName?: string
  redirect?: string
  menuType: number
  perms?: string
  sortNo?: number
  icon?: string
  isLeaf?: number
  children?: PermissionModel[]
}

interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

// 权限管理相关接口
/**
 * 查询菜单列表
 */
export function getPermissionList(params: { name?: string; column?: string; order?: string }) {
  return baseRequest.get<ApiResponse<PermissionModel[]>>(`${PREFIX_SEEP}/permission/list`, params)
}

/**
 * 创建菜单
 */
export function addPermission(data: PermissionModel) {
  return baseRequest.post<ApiResponse<boolean>>(`${PREFIX_SEEP}/permission/add`, data)
}

/**
 * 更新菜单
 */
export function updatePermission(data: PermissionModel) {
  return baseRequest.post<ApiResponse<boolean>>(`${PREFIX_SEEP}/permission/update`, data)
}

/**
 * 删除菜单
 */
export function deletePermission(id: string) {
  return baseRequest.delete<ApiResponse<boolean>>(`${PREFIX_SEEP}/permission/${id}`)
}
/**
 * 获取菜单详情
 */
export function getPermissionDetail(id: string) {
  return baseRequest.get<ApiResponse<PermissionModel>>(`${PREFIX_SEEP}/permission/${id}`)
}
/**
 * 获取子菜单
 */
export function getPermissionChildren(parentId: string) {
  return baseRequest.get<ApiResponse<PermissionModel[]>>(
    `${PREFIX_SEEP}/permission/children/${parentId}`,
  )
}

// 角色相关接口类型定义
export interface RoleModel {
  id?: string
  roleName: string
  roleCode?: string
  description?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  tenantId?: number
}

export interface RoleQueryDTO {
  pageNo?: number
  pageSize?: number
  roleName?: string
  roleCode?: string
}

export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 角色管理相关接口
/**
 * 创建角色
 */
export function createRole(data: RoleModel) {
  return baseRequest.post<ApiResponse<RoleModel>>(`${PREFIX_SEEP}/role/create`, data)
}

/**
 * 更新角色
 */
export function updateRole(data: RoleModel) {
  return baseRequest.post<ApiResponse<RoleModel>>(`${PREFIX_SEEP}/role/update`, data)
}

/**
 * 删除角色
 */
export function deleteRole(id: string) {
  return baseRequest.delete<ApiResponse<boolean>>(`${PREFIX_SEEP}/role/${id}`)
}

/**
 * 获取角色详情
 */
export function getRoleDetail(id: string) {
  return baseRequest.get<ApiResponse<RoleModel>>(`${PREFIX_SEEP}/role/${id}`)
}

/**
 * 分页查询角色列表
 */
export function getRoleList(params: RoleQueryDTO) {
  return baseRequest.get<ApiResponse<PageResult<RoleModel>>>(`${PREFIX_SEEP}/role/list`, params)
}

/**
 * 查询角色列表（不分页）
 */
export function queryRoles(params?: Pick<RoleQueryDTO, 'roleName' | 'roleCode'>) {
  return baseRequest.get<ApiResponse<RoleModel[]>>(`${PREFIX_SEEP}/role/query`, params)
}

/**
 * 查询角色树列表
 */
export function queryTreeList() {
  return baseRequest.get<ApiResponse<RoleModel[]>>(`${PREFIX_SEEP}/role/queryTreeList`)
}

/**
 * 查询角色权限
 */
export function queryRolePermission(roleId: string) {
  return baseRequest.get<ApiResponse<RoleModel[]>>(
    `${PREFIX_SEEP}/permission/queryRolePermission`,
    { roleId },
  )
}

/**
 * 保存角色权限
 */
export function saveRolePermissions(data: RoleModel[]) {
  return baseRequest.post<ApiResponse<boolean>>(
    `${PREFIX_SEEP}/permission/saveRolePermission`,
    data,
  )
}

// 部门相关类型定义
export interface DepartModel {
  id: string
  departName: string
  parentId?: string
  departNameEn?: string
  departNameAbbr?: string
  departOrder?: number
  description?: string
  orgCategory?: string
  orgType?: string
  orgCode?: string
  mobile?: string
  fax?: string
  address?: string
  status?: string
  createTime?: string
  updateTime?: string
  children?: DepartModel[]
  isLeaf?: number
}

export interface CreateDepartDTO {
  departName: string
  parentId?: string
  departNameEn?: string
  departNameAbbr?: string
  departOrder?: number
  description?: string
  orgCategory?: string
  orgType?: string
  orgCode?: string
  mobile?: string
  fax?: string
  address?: string
  status?: string
}

export interface UpdateDepartDTO extends CreateDepartDTO {
  id: string
}

export interface DepartQueryDTO {
  pageNo?: number
  pageSize?: number
}

// 部门相关接口
const DEPART_PREFIX = '/aip/depart'

// 创建部门
export function createDepart(data: CreateDepartDTO) {
  return baseRequest.post<ApiResponse<DepartModel>>(`${DEPART_PREFIX}/create`, data)
}

// 更新部门
export function updateDepart(data) {
  return baseRequest.post<ApiResponse<DepartModel>>(`${DEPART_PREFIX}/update`, data)
}

// 删除部门
export function deleteDepart(id: string) {
  return baseRequest.delete<ApiResponse<boolean>>(`${DEPART_PREFIX}/${id}`)
}

// 获取部门详情
export function getDepartDetail(id: string) {
  return baseRequest.get<ApiResponse<DepartModel>>(`${DEPART_PREFIX}/${id}`)
}

// 获取部门树--带权限
export function getDepartTreeSync(data) {
  return baseRequest.get<ApiResponse<DepartModel[]>>(`${DEPART_PREFIX}/queryDepartTreeSync`, data)
}
// 获取部门树--不带token
export function getFreeDepartList() {
  return baseRequest.get<any>(`${PREFIX_SEEP}/free/depart/list`)
}

// 获取子部门
export function getDepartChildren(parentId: string) {
  return baseRequest.get<ApiResponse<DepartModel[]>>(`${DEPART_PREFIX}/children/${parentId}`)
}

// 分页查询部门列表
export function getDepartList(params) {
  return baseRequest.get<any>(`${DEPART_PREFIX}/searchBy`, params)
}

// 用户管理相关接口类型定义
export interface UserModel {
  id?: string
  username: string
  realname?: string
  password?: string
  avatar?: string
  birthday?: string
  sex?: number | string // 性别(1-男,2-女)
  email?: string
  phone?: string
  status?: number // 状态(1-正常,2-冻结)
  workNo?: string
  telephone?: string
  createBy?: string
  createTime?: string
  updateTime?: string
  departIds?: string | string[] // 支持字符串或字符串数组
  roleIds?: string | string[] // 支持字符串或字符串数组
  relTenantId?: string
}

export interface UserQueryDTO {
  current: number
  pageSize: number
  userName?: string
  realName?: string
  sex?: number
}

export interface ChangePasswordDTO {
  oldPwd: string
  newPwd: string
  confirmPwd: string
  userName: string
}

// 用户管理相关接口
const USER_PREFIX = '/aip/sys/user'

/**
 * 创建用户
 */
export function createUser(data: UserModel) {
  return baseRequest.post<ApiResponse<any>>(`${USER_PREFIX}`, data)
}

/**
 * 更新用户
 */
export function updateUser(data: UserModel) {
  return baseRequest.put<ApiResponse<any>>(`${USER_PREFIX}`, data)
}

/**
 * 删除用户
 */
export function deleteUser(id: string) {
  return baseRequest.delete<ApiResponse<any>>(`${USER_PREFIX}/del/${id}`)
}

/**
 * 获取用户信息
 */
export function getUserDetail(id: string) {
  return baseRequest.get<ApiResponse<UserModel>>(`${USER_PREFIX}/get/${id}`)
}

/**
 * 修改密码
 */
export function changePassword(data: ChangePasswordDTO) {
  return baseRequest.post<ApiResponse<any>>(`${USER_PREFIX}/change`, data)
}

/**
 * 获取用户列表
 */
export function getUserList(params: UserQueryDTO) {
  return baseRequest.get<ApiResponse<PageResult<UserModel>>>(`${USER_PREFIX}/list`, params)
}
/**
 * 启用/禁用用户
 * @param userId 用户ID
 * @param status 状态(true-启用,false-禁用)
 */
export function enableUser(userId: string, status: boolean) {
  return baseRequest.get<ApiResponse<any>>(`${USER_PREFIX}/enable/${userId}/${status}`)
}
// 登录相关接口类型定义
export interface LoginParams {
  username: string
  password: string
  totpCode?: string
}

export interface LoginUserInfo {
  id: string
  username: string
  realname: string
  avatar?: string
  sex?: number
  email?: string
  phone?: string
  status: number
  departIds?: string
  roleIds?: string
  relTenantId?: string
}

export interface DepartInfo {
  id: string
  parentId?: string
  departName: string
  departNameEn?: string
  departNameAbbr?: string
  departOrder?: number
  description?: string
  orgCategory?: string
  orgType?: string
  orgCode?: string
  status?: string
  children?: DepartInfo[]
}

export interface MenuInfo {
  title: string
  key: string
  icon?: string
  path?: string
  children?: MenuInfo[]
}

export interface LoginResult {
  type: number // 1-登录后跳转首页 2-TOTP初次二维码页面 3-TOTP填验证码页面
  qrCode?: string
  totpSecret?: string
  token?: string
  user?: LoginUserInfo
  departs?: DepartInfo[]
  menuTree?: Record<string, MenuInfo>
}

// 登录相关接口

/**
 * 获取验证方式：是否进行多因素验证
 */
export function checkMFAType() {
  return baseRequest.get<ApiResponse<LoginResult>>(`${PREFIX_SEEP}/system/param/get/verificationMethod`)
}

/**
 * 用户登录
 */
export function login(data: LoginParams) {
  return baseRequest.post<ApiResponse<LoginResult>>(`${PREFIX_SEEP}/login`, data)
}

/**
 * 用户登出
 */
export function logout() {
  return baseRequest.post<ApiResponse<void>>(`${PREFIX_SEEP}/logout`)
}

/**
 * TOTP绑定验证
 */
export function bindTotp(data: LoginParams) {
  return baseRequest.post<ApiResponse<void>>(`${PREFIX_SEEP}/bind`, data)
}

/**
 * 获取验证码
 */
export function getCaptcha() {
  const checkKey = new Date().getTime().toString()
  return {
    checkKey,
    captchaUrl: `${PREFIX_SEEP}/captcha?checkKey=${checkKey}`,
  }
}

// 注册相关接口类型定义
export interface RegisterParams {
  username: string // 登录账号
  realname: string // 真实姓名
  password: string // 密码
  confirmPwd: string // 确认密码
  sex?: number // 性别(0-默认未知,1-男,2-女)
  departIds: string // 负责部门 多个用逗号分隔
  totpCode: string // TOTP验证码
  totpSecret?: string // TOTP密钥
}

// 忘记密码接口类型定义
export interface ForgotPasswordParams {
  userName: string
  totpCode: string // 更改为验证码
  newPwd: string
  confirmPwd: string
}

/**
 * 用户注册
 * @param data 注册参数
 */
export function register(data: RegisterParams) {
  return baseRequest.post<ApiResponse<any>>(`${PREFIX_SEEP}/free/register`, data)
}

/**
 * 忘记密码
 * @param data 忘记密码参数
 */
export function forgotPassword(data: ForgotPasswordParams) {
  return baseRequest.post<ApiResponse<any>>(`${PREFIX_SEEP}/free/forgot`, data)
}

// 在线用户相关接口类型定义
export interface OnlineUserInfo {
  userId: string
  username: string
  realname?: string
  loginTime: string
  loginIp?: string
  browser?: string
  os?: string
  status?: number
}

// 在线用户相关接口
/**
 * 获取在线用户数量
 */
export function getOnlineCount() {
  return baseRequest.get<ApiResponse<number>>(`${PREFIX_SEEP}/monitor/online/count`)
}

/**
 * 获取在线用户列表
 */
export function getOnlineUsers() {
  return baseRequest.get<ApiResponse<OnlineUserInfo[]>>(`${PREFIX_SEEP}/monitor/online/view`)
}
/**
 * 注册时生成二维码
 * @param data 注册参数
 * @returns Promise<ApiResponse<any>>
 */
export function getTOTPQRCode(pamars) {
  return baseRequest.post<ApiResponse<any>>(`${PREFIX_SEEP}/free/register/code`, pamars, {
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

// 获取枚举列表
export function getDesignerEnumList() {
  return baseRequest.get<ApiResponse<any>>(`${PREFIX_SEEP}/designer-enum/list`)
}
