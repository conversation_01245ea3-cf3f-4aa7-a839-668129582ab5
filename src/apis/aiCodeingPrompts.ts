import baseRequest from '@/utils/http/baseRequest'

/**
 * 提示词数据接口
 */
export interface PromptDictionary {
  id: number;
  tagName: string;
  tag: string;
  content: string;
  parentId?: number;
  isDel?: number;
  level?: number;
  sentenceMatchingEmbedding?: unknown;
  sentenceMatching?: unknown;
}

/**
 * API响应接口
 */
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

/**
 * 获取系统字典列表
 * @description 获取系统中的字典数据列表，包括提取内容、意图分类、意图识别等
 * @returns {Promise<ApiResponse<PromptDictionary[]>>} 返回字典列表数据
 */
export function getSysDictionaryListApi() {
  return baseRequest.post<ApiResponse<PromptDictionary[]>>('/code/sysDictionary/list')
}

/**
 * 编辑系统字典
 * @description 更新系统字典的内容
 * @param {PromptDictionary} data - 字典数据，包含id、tagName、tag、content等字段
 * @returns {Promise<ApiResponse<boolean>>} 返回编辑结果
 */
export function editSysDictionaryApi(data: Partial<PromptDictionary>) {
  return baseRequest.post<ApiResponse<boolean>>('/code/sysDictionary/edit', data)
}
