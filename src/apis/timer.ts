import baseRequest from '../utils/http/baseRequest'

// api前缀常量
export const PREFIX_SEE = '/code'
export const PREFIX_SEEP = '/aip'
export const PREFIX_SSE = '/aip/channel/sse'

// 分页获取定时任务列表
export function getTimerTriggerPage(params: {
    current: number
    size: number
    name?: string
    userId?: string
    status?: number
  }) {
    return baseRequest.post<any>('/aip/timerTrigger/page', params)
  }

// 更新定时任务
export function updateTimerTrigger(params: {
    id: string,
    triggerName?: string, 
    workflowId?: string,  
    path?: string,        
    triggerTimeType?: string, 
    cronExpression?: string, 
    triggerTime?: string,    
    frequencyType?: string,  
    frequencyDay?: number,          
    timezone?: string
    description?: string
    userId?: string
    status?: number
}) {
    return baseRequest.post<any>('/aip/timerTrigger/update', params)
  }

// 删除定时任务
export function deleteTimerTrigger(id: string) {
    return baseRequest.post<any>(`/aip/timerTrigger/delete/${id}`)
  }