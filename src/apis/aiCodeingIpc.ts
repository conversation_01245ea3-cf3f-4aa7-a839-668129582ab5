/**
 * 代码生成相关的IPC通信接口
 * 统一管理所有electron IPC调用
 * 按照IPC名称组织（而非按照方法）
 */

import type { 
  FileContentResponse, 
  FileTreeResponse, 
  TemplatesResponse,
  ServerInfo 
} from '@/views/designer/codeGeneration/types'

/**
 * 文件操作相关IPC
 */
// 读取文件内容
export const readFileContent = (filePath: string): Promise<FileContentResponse> => {
  return window.electronAPI.invoke('readFileContent', filePath) as Promise<FileContentResponse>
}

// 更新文件内容
export const updateFileContent = (tempDir: string, fileName: string, content: string): Promise<boolean> => {
  return window.electronAPI.invoke('updateFileContent', tempDir, fileName, content) as Promise<boolean>
}

// 检查文件是否存在
export const checkFileExists = (filePath: string): Promise<boolean> => {
  return window.electronAPI.invoke('checkFileExists', filePath) as Promise<boolean>
}

// 创建文件
export const createFile = (filePath: string, content: string = ''): Promise<{ success: boolean; error?: string }> => {
  return window.electronAPI.invoke('createFile', filePath, content) as Promise<{ success: boolean; error?: string }>
}

// 创建文件夹
export const createFolder = (folderPath: string): Promise<{ success: boolean; error?: string }> => {
  return window.electronAPI.invoke('createFolder', folderPath) as Promise<{ success: boolean; error?: string }>
}

// 确保目录存在
export const ensureDirectory = (dirPath: string): Promise<boolean> => {
  return window.electronAPI.invoke('ensureDirectory', dirPath) as Promise<boolean>
}

// 重命名文件或文件夹
export const rename = (oldPath: string, newPath: string): Promise<{ success: boolean; error?: string }> => {
  return window.electronAPI.invoke('rename', oldPath, newPath) as Promise<{ success: boolean; error?: string }>
}

// 删除文件或文件夹
export const deleteFile = (path: string): Promise<{ success: boolean; error?: string }> => {
  return window.electronAPI.invoke('delete', path) as Promise<{ success: boolean; error?: string }>
}

/**
 * 文件树相关IPC
 */
// 获取文件树
export const getFileTree = (rootPath: string, maxDepth?: number): Promise<FileTreeResponse> => {
  return window.electronAPI.invoke('getFileTree', rootPath, maxDepth) as Promise<FileTreeResponse>
}

/**
 * 模板相关IPC
 */
// 获取可用模板列表
export const getAvailableTemplates = (): Promise<TemplatesResponse> => {
  return window.electronAPI.invoke('getAvailableTemplates') as Promise<TemplatesResponse>
}

// 获取模板文件
export const getTemplateFile = (templateName: string, fileName: string): Promise<{ success: boolean; content?: string; error?: string }> => {
  return window.electronAPI.invoke('getTemplateFile', templateName, fileName) as Promise<{ success: boolean; content?: string; error?: string }>
}

// 启动静态服务器
export const startStaticServer = (templateName: string, env: string): Promise<ServerInfo> => {
  return window.electronAPI.invoke('startStaticServer', templateName, env) as Promise<ServerInfo>
}
