import baseRequest from '../utils/http/baseRequest'

// 爬虫配置接口类型
export interface CrawlerConfig {
  id?: number
  name: string
  crawlUrl: string
  clickListPath: string
  nextPagePath: string
  getTitle: string
  getPubTime: string
  getFile?: string
  getContent: string
  crawlFunction?: string
  mainExtraInfo?: string
  iframe?: string
  crawlBreadCrumbs?: string
  crawlContentTitle?: string
  status?: string
  createTime?: string
  updateTime?: string
  headers?: string
  proxy?: string
  timeout?: number
  retryCount?: number
  maxPages?: number
  description?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 分页响应类型
export interface PageResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 分页获取爬虫列表
export function getCrawlerPage(params: {
  page?: number
  pageSize?: number
  title?: string
  status?: number
}) {
  return baseRequest.post<ApiResponse<PageResponse<CrawlerConfig>>>('/aip/crawler/settings/page', params)
}

// 创建爬虫
export function createCrawler(params: Omit<CrawlerConfig, 'id' | 'createTime' | 'updateTime'>) {
  return baseRequest.post<ApiResponse>('/aip/crawler/settings/create', params)
}

// 更新爬虫
export function updateCrawler(params: CrawlerConfig) {
  return baseRequest.post<ApiResponse>('/aip/crawler/settings/update', params)
}

// 删除爬虫
export function deleteCrawler(id: number) {
  return baseRequest.post<ApiResponse>('/aip/crawler/settings/delete', { id })
}

// 启动爬虫
export function startCrawler(params: {
  id: number
  status?: string
}) {
  return baseRequest.post<ApiResponse>('/aip/crawler/settings/update-status', params)
}

// 晓赢社区相关API
export function startXiaoyingCrawler() {
  return baseRequest.get<ApiResponse>('/xiaoying/start')
}

export function answerXiaoyingQuestion(params: {
  answer: string
  id: string
}) {
  return baseRequest.post<ApiResponse>('/xiaoying/answer', params)
}

export function publishXiaoyingPost(params: {
  title: string
  content: string
  type: string[]
}) {
  return baseRequest.post<ApiResponse>('/xiaoying/publish', params)
}

// 香港日历相关API
export function startHongkongCalendar(params: {
  beginDate: string
  endDate: string
  isSync?: boolean
}) {
  return baseRequest.get<ApiResponse>('/hongkong/start', params)
}
