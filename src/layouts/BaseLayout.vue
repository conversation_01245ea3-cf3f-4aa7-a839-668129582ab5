<template>
  <div
    class="relative flex flex-col h-full overflow-hidden"
    :class="{ 'hide-nav': !showNav, 'hide-sidebar': !showSidebar, 'home-page': isHome, 'canvas-page': isCanvas }"
  >
    <YssLayout
      :show-sidebar="showSidebar"
      :show-header="showHeader"
      :show-footer="showFooter"
      :headerProps="{
        showSearch: false,
        showNotification: false,
        userName: userInfo.username,
      }"
      :sidebarProps="{
        title: currentMenuTitle,
        businessMenus: businessMenu,
        userSettingMenus: userInfo.adminFlag ? userConfig : [],
        fixedMenus: fixedListMap,
      }"
      @toFixedFunc="toFixedFunc"
    >
      <template #footer>
        <Footer />
      </template>
    </YssLayout>
    
    <!-- 用户信息下拉框 -->
    <div
      v-if="showUserInfo && canShowUserInfo"
      class="fixed right-[16px] top-[56px] w-48 bg-area-fill dark:text-white dark:!border dark:!border-border rounded-lg shadow-lg py-3 z-10 user-info-dropdown"
    >
      <div class="px-4 py-2" :class="[ userInfo.adminFlag ? 'border-b border-gray-100' : '' ]">
        <div class="text-gray-700 mb-2 flex items-center justify-between">
          <span class="dark:text-white">{{ userInfo.username }}</span>
          <LogoutOutlined
            class="rotate-[-90deg] text-sm dark:text-[var(--text-secondary)] cursor-pointer hover:text-red-500"
            @click.stop="handleLogout"
          />
        </div>
        <div class="text-gray-500 text-sm mb-1 dark:!text-[var(--text-secondary)]">
          角色：{{ userInfo.roleNames }}
        </div>
        <div class="text-gray-500 text-sm mb-1 dark:!text-[var(--text-secondary)]">
          部门：{{ userInfo.departNames }}
        </div>
      </div>

      <div
        v-if="userInfo.adminFlag"
        class="mt-1 px-4 py-2 text-[12px] text-gray-600 hover:bg-gray-50 cursor-pointer flex items-center dark:!text-[var(--text-secondary)]"
        @click.stop="handleSystemManage"
      >
        <SettingOutlined class="mr-2 dark:!text-[var(--text-secondary)]" />
        系统管理
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineComponent, h, onMounted, onUnmounted } from 'vue'
import { Footer } from '@/components'
import {YssLayout} from '@yss-design/ui'
import { useRoute, useRouter } from 'vue-router'
import { logout } from '@/apis/system'
import { message } from 'ant-design-vue'
import {
  SettingOutlined,
  LogoutOutlined,
  UserOutlined,
} from '@ant-design/icons-vue'

// 定义菜单树接口
interface MenuTreeItem {
  id: string
  name: string
  parentId?: string
  url?: string
  component?: string
  componentName?: string
  redirect?: string
  menuType: number
  perms?: string
  sortNo?: number
  icon?: string
  isLeaf?: number
  children?: MenuTreeItem[]
  key?: string
  title?: string
  path?: string
  meta?: {
    icon?: string
    title?: string
  }
}

const router = useRouter()
const route = useRoute()

const businessMenu = ref<unknown[]>([])
const fixedListMap = ref<Record<string, unknown>>({})
const allFixedListMap = ref<Record<string, unknown>>(
  JSON.parse(localStorage.getItem('allFixedListMap') || '{}')
)
const userConfig = ref<any[]>([])
const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))

// 控制用户信息下拉框显示状态 - 默认显示，刷新页面后总是显示
const showUserInfo = ref(true)

// 判断是否可以显示用户信息（基于路由）
const canShowUserInfo = computed(()=>{
  return !['/designer/canvas'].includes(route.path)
})

const showNav = computed(()=>{
  return !['/'].includes(route.path)
})

const showSidebar = computed(() => {
  return !['/', '/designer/canvas'].includes(route.path)
})

const showHeader = computed(() => {
  return route.path !== '/designer/canvas'
})

const showFooter = computed(() => {
  return route.path !== '/designer/canvas'
})

const isHome = computed(() => {
  return route.path === '/'
})

const isCanvas = computed(() => {
  return route.path === '/designer/canvas'
})

const handleSystemManage = () => {
  router.push('/system/menu')
}

const handleLogout = async () => {
  try {
    // 2. 调用登出接口
    const response = await logout()

    if (response.code === 200) {
      // 3. 清除用户登录状态
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      localStorage.removeItem('menuTree')
      message.success('登出成功')
      // 5. 跳转到登录页
      router.replace('/login')
    }
  } catch (error) {
    // message.error('登出失败,请稍后重试')
  }
}

const currentMenuTitle = computed(() => {
  const length = route.matched.length
  if (length > 1) {
    return route.matched[length - 2].meta.title || '应用名称'
  }else{
    return '应用名称'
  }
})

const toFixedFunc = (item: any, tag = '') => {
  if (!item.key) return
  if (tag === 'remove' && allFixedListMap.value[item.key]) {
    delete allFixedListMap.value[item.key]
    localStorage.setItem('allFixedListMap', JSON.stringify(allFixedListMap.value))
    return
  }
  // 添加到所有快捷导航中
  allFixedListMap.value[item.key] = item
  localStorage.setItem('allFixedListMap', JSON.stringify(allFixedListMap.value))
}

// 切换用户信息显示的方法
const toggleUserInfo = () => {
  console.log('toggleUserInfo clicked, current state:', showUserInfo.value)
  showUserInfo.value = !showUserInfo.value
  console.log('toggleUserInfo new state:', showUserInfo.value)
}

// 全局点击监听器
const handleGlobalClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  
  // 检查是否点击了头部的用户名区域
  if (target.closest('.ant-avatar') || target.textContent?.includes(userInfo.value.username)) {
    // 如果下拉框隐藏了，则显示它
    if (!showUserInfo.value) {
      showUserInfo.value = true
      return
    }
  }
  
  // 检查是否点击了用户信息下拉框内部
  if (target.closest('.user-info-dropdown')) {
    return // 不做任何操作
  }
  
  // 点击其他地方则隐藏下拉框
  if (showUserInfo.value) {
    showUserInfo.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
})

// 转换菜单树数据为侧边栏格式
const convertMenuTreeToSidebar = (menuTree: MenuTreeItem[]) => {
  return menuTree.map((item) => ({
    key: item.key,
    title: item.title,
    icon: item.icon ? () => h(AliyunIcon, { type: item.icon }) : undefined,
    children: item.children ? convertMenuTreeToSidebar(item.children) : undefined,
    path: item.path,
    component: item.component,
    isLeaf: item.isLeaf,
    to: item.path
  }))
}

// 创建阿里图标组件
const AliyunIcon = defineComponent({
  name: 'AliyunIcon',
  props: {
    type: {
      type: String,
      required: true
    }
  },
  setup(props) {
    return () => h('i', {
      class: `iconfont ${props.type}`,
      style: {
        fontSize: '16px',
        marginRight: '8px'
      }
    })
  }
})

// 修改过滤函数，只影响显示，不删除数据
const filterFixedListMap = () => {
  // 获取所有可用菜单的key
  const allKeys = new Set()

  // 递归收集所有菜单项的key
  const collectKeys = (items: any[]) => {
    if (!items) return
    for (const item of items) {
      if (item.key) allKeys.add(item.key)
      if (item.children) collectKeys(item.children)
    }
  }

  // 收集当前业务模块和用户配置中的所有菜单项key
  collectKeys(businessMenu.value)
  collectKeys(userConfig.value)

  // 更新fixedListMap，只显示存在于当前菜单中的项
  fixedListMap.value = {}
  Object.entries(allFixedListMap.value).forEach(([key, value]:[string, any]) => {
    if (allKeys.has(key)) {
      const icon = value.meta?.icon
      fixedListMap.value[key] = {
        ...value as object,
        icon: icon ? () => h(AliyunIcon, { type: icon }) : undefined,
        to: value.path
      }
    }
  })
}

const getMenu = () =>{
  // 从本地存储获取菜单树
  const menuTree = JSON.parse(localStorage.getItem('menuTree') || '{}')
  const userMenuTree = menuTree.userConfig || []

  if (userMenuTree) {
    try {
      userConfig.value = convertMenuTreeToSidebar(userMenuTree)
    } catch (error) {
      console.error('解析用户菜单树数据失败:', error)
      userConfig.value = []
    }
  }

  const routesGroup = JSON.parse(localStorage.getItem('routesGroup') || '{}')
  const currentRouteGroup = localStorage.getItem('currentRouteGroup') || ''
  const routeModule = routesGroup[currentRouteGroup]

  businessMenu.value = (routeModule || []).map((item: any) => {
      const icon = item.meta?.icon
      return {
        ...item,
        icon: icon ? () => h(AliyunIcon, { type: icon }) : undefined,
        to: item.path
      }
  })

  // 初始化allFixedListMap（如果尚未设置）
  if (Object.keys(allFixedListMap.value).length === 0 && localStorage.getItem('fixedListMap')) {
    // 迁移原有数据
    allFixedListMap.value = JSON.parse(localStorage.getItem('fixedListMap') || '{}')
    localStorage.setItem('allFixedListMap', JSON.stringify(allFixedListMap.value))
  }

  // 过滤快捷导航，只显示当前功能模块中存在的菜单
  filterFixedListMap()
}

watch(showSidebar, (newValue) => {
  newValue && getMenu()
}, { immediate: true })
</script>

<style lang="scss" scoped>
/* 修改组件库的样式 */
:deep(.sidebar-toggle-btn){
  @apply dark:!bg-[var(--area-fill-color)] z-10;
  svg{
    @apply dark:!text-white dark:hover:!text-[var(--text-secondary)];
  }
}
:deep(.ant-avatar svg){
  @apply dark:!text-white dark:hover:!text-[var(--text-secondary)];
}
:deep(.ant-layout-header svg){
  @apply dark:!text-white dark:hover:!text-[var(--text-secondary)];
}
:deep(.ant-dropdown-trigger){
  span{
    @apply dark:!text-white;
  }
}
:deep(.header-box){
  @apply dark:!border-solid dark:!border-border;
}
:deep(.sidebar-wrapper){
  @apply dark:!border-solid dark:!border-border;
}
.hide-nav{
  :deep(nav){
    display: none !important;
  }
}
.hide-sidebar{
  :deep(.ant-layout-header .sidebar-right-toggle-btn){
    display: none !important;
  }
}
:deep(.ant-menu-title-content .iconfont){
  font-size: 16px !important;
}
.home-page{
  :deep(.route-view-container){
    display: flex !important;
    align-items: center !important;
  }
}
.canvas-page{
  :deep(.route-view-container){
    padding: 0 !important;
  }
}
</style>
