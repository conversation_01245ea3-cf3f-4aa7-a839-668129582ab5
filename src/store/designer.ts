import { defineStore } from 'pinia'
import type { Cell, Graph } from '@antv/x6'
import type { Dnd } from '@antv/x6-plugin-dnd'
import { getFileApi, saveFileApi } from '@/apis/designer'

export const useDesignerStore = defineStore('designer', {
  state: (): IModDesignerState => ({
    dndInstance: {} as Dnd,
    modGraph: {} as Graph,
    fileFlow: {
      startRow: 1,
      collectionType: 'file',
      textIdentifier: '',
      encoding: 'gbk',
      textSeparator: ',',
      qIndex: 'index',
      aIndex: 'content',
      isCreate: 1,
    } as FileFlow,
    connectedNodePortId: [], // 已连接的连接桩
    allPortsId: [], // 所有连接桩
    allNodesId: [], // 所有节点
    rightPanelVisiable: false,
    generateNodePanelVisiable: false,
    generateScriptPanelVisiable: false,
    currentNodeData: {} as Group,
    currentNode: null,
    currentFile: {} as TabData,
    tabData: new Map(),
    currentSelectNode: [],
    mode: '',
    isSafari: navigator.userAgent.includes('Safari'),
    nodePorts: new Map<string, number>(), // 存储节点的端口数量 nodeId -> portsCount
    nodeGroups: {} as Record<string, any>,
    shapeGroups: {} as Record<string, any>,
    /**
     * 框选的节点、连线
     */
    selectionCells: [] as Cell[],
    /**
     * 当前JSON视图的内容
     */
    jsonViewContent: '',
    debugDataMap: new Map<string, any>(), // 用于存储每个节点的 debug 数据
  }),
  getters: {},
  actions: {
    setSelectionCells(cells: Cell[]) {
      this.selectionCells = cells
    },
    setMode(mode) {
      this.mode = mode
    },
    setCurrentSelectedNode(data) {
      this.currentSelectNode = data
    },
    addTab(data: TabData) {
      try {
        console.log('添加标签:', data.id, data.name)
        // 直接设置当前文件，创建一个新对象避免引用问题
        this.currentFile = { ...data }

        // 确保内容格式正确
        if (!data.content) {
          data.content = { cells: [], mode: 'all' }
        }

        this.mode = data.content.mode || 'all'

        // 如果标签已存在，直接返回
        if (this.tabData.get(data.id)) {
          return
        }

        // 添加到标签集合
        this.tabData.set(data.id, data)

        // 添加标签后立即保存标签基本信息
        this.serializeTabData()

        // 设置活动标签ID
        localStorage.setItem('designer-active-tab', data.id)

        console.log('标签添加成功:', this.currentFile.id, this.tabData.size)
      } catch (error) {
        console.error('添加标签出错:', error)
      }
    },
    removeTab(id: string) {
      this.tabData.delete(id)
    },
    setCurrentFile(data: TabData | null) {
      try {
        if (!data) {
          this.currentFile = null
          return
        }

        // 只有当id是undefined时才设置默认id，允许空字符串
        if (data.id === undefined) {
          console.warn('setCurrentFile: 传入的数据没有id，设置默认id')
          data.id = 'default-' + Date.now()
        }

        // 确保content存在
        if (!data.content) {
          data.content = {
            cells: [],
            mode: 'all',
          }
        }

        this.currentFile = data
        this.mode = data.content.mode || 'all'
      } catch (error) {
        console.error('设置当前文件出错:', error)
      }
    },
    setRightPanelVisiable(rightPanelVisiable: boolean) {
      this.rightPanelVisiable = rightPanelVisiable
    },
    setGenerateNodePanelVisiable(generateNodePanelVisiable: boolean) {
      this.generateNodePanelVisiable = generateNodePanelVisiable
    },
    setGenerateScriptPanelVisiable(generateScriptPanelVisiable: boolean) {
      this.generateScriptPanelVisiable = generateScriptPanelVisiable
    },
    setCurrentNode(currentNode) {
      this.currentNode = currentNode
    },
    setCurrentNodeData(currentNodeData) {
      this.currentNodeData = currentNodeData
    },
    setDndInstance(dnd: Dnd) {
      this.dndInstance = dnd
    },
    setFileFlow(fileFlow) {
      this.fileFlow = { ...this.fileFlow, ...fileFlow }
    },
    setModGraph(modelGraph) {
      this.modGraph = modelGraph
    },
    setConnectedNodePort(connectedNodePortId) {
      this.connectedNodePortId.push(connectedNodePortId)
    },
    setPorts(portId) {
      this.allPortsId.push(portId)
    },
    setNode(nodeId) {
      this.allNodesId.push(nodeId)
    },
    deleteConnectedNodePort(connectedNodePortId) {
      this.connectedNodePortId = this.connectedNodePortId.filter(
        (item) => item !== connectedNodePortId,
      )
    },
    deletePort(portId) {
      this.allPortsId = this.allPortsId.filter((item) => item !== portId)
    },
    deleteNode(nodeId) {
      this.allNodesId = this.allNodesId.filter((item) => item !== nodeId)
    },
    updateNodePorts(nodeId: string, count: number) {
      this.nodePorts.set(nodeId, count)
    },
    setNodeGroups(groups: Record<string, any>) {
      this.nodeGroups = groups
    },
    setShapeGroups(groups: Record<string, any>) {
      this.shapeGroups = groups
    },
    // 设置JSON视图内容
    setJsonViewContent(content: string) {
      this.jsonViewContent = content
    },
    // 添加新的切换标签方法，使用getFileApi
    async changeTab(tabId: string) {
      try {
        if (!tabId) return false

        // 获取标签基本信息
        const tabInfo = this.tabData.get(tabId)
        if (!tabInfo) return false

        // 先设置currentFile为基本信息，确保UI可见
        // 创建一个新对象避免引用问题
        this.setCurrentFile({ ...tabInfo })

        try {
          const response = await getFileApi({ id: tabId })

          if (!response || !response.data) {
          }

          // 解析响应数据，更新标签内容
          const fileData = response.data

          // 检查API返回的内容格式
          let contentObj
          if (typeof fileData.content === 'string') {
            try {
              contentObj = JSON.parse(fileData.content)
            } catch (e) {
              contentObj = { cells: [], mode: 'all' }
            }
          } else {
            contentObj = fileData.content || { cells: [], mode: 'all' }
          }

          // 确保content是对象而非字符串
          const updatedTab = {
            ...tabInfo,
            content: contentObj,
          }

          // 更新本地缓存中的当前标签内容
          this.tabData.set(tabId, updatedTab)

          // 设置当前文件
          this.setCurrentFile(updatedTab)

          // 设置当前模式
          this.mode = updatedTab.content.mode || 'all'
          
          // 设置JSON内容供视图组件使用
          try {
            const jsonContent = JSON.stringify(updatedTab.content, null, 2)
            this.setJsonViewContent(jsonContent)
          } catch (err) {
            console.error('转换JSON内容失败:', err)
          }

          // 根据当前视图模式处理内容
          if (this.mode === 'json') {
            // JSON视图模式下不需要渲染画布，内容已在上面更新
            console.log('JSON视图模式：内容已更新')
          } else if (this.modGraph && updatedTab.content) {
            try {
              // 先清空画布
              this.modGraph.clearCells()

              // 渲染内容
              this.modGraph.fromJSON(updatedTab.content)
            } catch (err) {
              console.error('画布渲染失败:', err)
            }
          } else {
            console.error('modGraph未初始化或content为空')
          }
        } catch (error) {
          console.error('获取文件内容失败，使用本地缓存:', error)
          // 即使API调用失败，我们已经设置了基本的currentFile，UI依然可见
        }

        // 保存当前活动标签ID
        localStorage.setItem('designer-active-tab', tabId)

        // 关闭右侧面板
        this.setRightPanelVisiable(false)

        return true
      } catch (error) {
        console.error('切换标签失败:', error)
        return false
      }
    },

    // 添加通过ID加载文件的方法
    async loadFileById(fileId: string) {
      try {
        const response = await getFileApi({ id: fileId })
        
        if (!response || !response.data) {
          throw new Error('文件不存在')
        }

        const fileData = response.data

        // 解析文件内容
        let contentObj
        if (typeof fileData.content === 'string') {
          try {
            contentObj = JSON.parse(fileData.content)
          } catch (e) {
            console.error('解析文件内容失败:', e)
            contentObj = { cells: [], mode: 'all' }
          }
        } else {
          contentObj = fileData.content || { cells: [], mode: 'all' }
        }


        return contentObj
      } catch (error) {
        console.error('加载文件失败:', error)
        return {}
      }
    },
    // 修改serializeTabData，只存储标签基本信息
    serializeTabData() {
      try {
        if (this.tabData instanceof Map && this.tabData.size > 0) {
          // 将Map转换为可序列化的数组，只保存基本信息（不包含content）
          const serializedTabs = Array.from(this.tabData.entries()).map(([key, value]) => {
            // 创建一个新对象，避免修改原对象
            const { content, ...tabInfo } = JSON.parse(JSON.stringify(value))
            return [key, tabInfo]
          })

          // 保存标签数据
          localStorage.setItem('designer-tabs', JSON.stringify(serializedTabs))

          // 保存当前激活的标签ID
          if (this.currentFile && this.currentFile.id) {
            localStorage.setItem('designer-active-tab', this.currentFile.id)
          }

          // 添加日志确认保存成功
          console.log('标签数据已保存，共', this.tabData.size, '个标签')
        } else {
          // 如果没有标签数据，清除所有相关本地存储
          localStorage.removeItem('designer-tabs')
          localStorage.removeItem('designer-active-tab')
          console.warn('没有标签数据，已清除本地存储')
        }
      } catch (error) {
        console.error('序列化标签数据失败:', error, this.tabData)
      }
    },
    // 修改deserializeTabData，使用changeTab方法加载内容
    async deserializeTabData() {
      try {
        const savedTabs = localStorage.getItem('designer-tabs')

        if (!savedTabs) {
          return false
        }

        // 解析存储的数据
        const entries = JSON.parse(savedTabs)

        if (!Array.isArray(entries) || entries.length === 0) {
          return false
        }

        // 创建新的Map，只恢复标签的基本信息
        const restoredMap = new Map()

        entries.forEach(([key, value]) => {
          if (key && value && typeof value === 'object') {
            // 设置空的content占位符，实际内容将在切换标签时加载
            restoredMap.set(key, {
              ...value,
              content: { cells: [], mode: value.mode || 'all' },
            })
          }
        })

        // 只有在成功还原了基本信息时才设置tabData
        if (restoredMap.size > 0) {
          // 清空当前tabData并添加恢复的标签基本信息
          this.tabData.clear()
          restoredMap.forEach((value, key) => {
            this.tabData.set(key, value)
          })

          // 获取最后激活的标签ID
          const lastActiveTabId = localStorage.getItem('designer-active-tab')

          // 重要：先设置一个初始的currentFile，确保UI可见
          if (lastActiveTabId && restoredMap.has(lastActiveTabId)) {
            // 先设置基本信息，不等待内容加载
            const initialTab = restoredMap.get(lastActiveTabId)
            this.setCurrentFile(initialTab)
          } else if (restoredMap.size > 0) {
            // 否则使用第一个标签
            const firstTabId = Array.from(restoredMap.keys())[0]
            const firstTab = restoredMap.get(firstTabId)
            this.setCurrentFile(firstTab)
            console.log('已设置第一个标签作为初始标签:', firstTab.id, firstTab.name)
          }

          // 然后异步加载内容
          try {
            if (lastActiveTabId && restoredMap.has(lastActiveTabId)) {
              await this.changeTab(lastActiveTabId)
            } else if (restoredMap.size > 0) {
              const firstTabId = Array.from(restoredMap.keys())[0]
              await this.changeTab(firstTabId)
            }
          } catch (error) {
            console.error('加载标签内容失败:', error)
            // 即使加载失败，也已经设置了初始标签，UI依然可见
          }

          return true
        }
        return false
      } catch (error) {
        console.error('恢复标签数据失败:', error)
        return false
      }
    },
    // 添加保存当前内容的方法，使用saveFileApi
    async saveCurrentContent() {
      if (!this.currentFile || !this.currentFile.id) return false

      try {
        // 获取当前画布内容
        const content = JSON.stringify(this.modGraph.toJSON())

        // 获取userid
        const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))

        // 使用saveFileApi保存内容
        const response = await saveFileApi({
          id: this.currentFile.id,
          content: content,
          // 可能需要根据API要求添加其他字段
          directoryId: this.currentFile.directoryId,
          name: this.currentFile.name,
          userId: userInfo.value.id,
        })

        if (!response || !response.data) {
          throw new Error('保存内容失败')
        }

        // 更新本地标签中的content
        const currentTab = this.tabData.get(this.currentFile.id)
        if (currentTab) {
          currentTab.content = content
          this.tabData.set(this.currentFile.id, currentTab)
        }

        return true
      } catch (error) {
        console.error('保存内容失败:', error)
        return false
      }
    },
    // 保存节点的 debug 数据
    setNodeDebugData(nodeId: string, debugData: any) {
      this.debugDataMap.set(nodeId, debugData)
    },

    // 获取节点的 debug 数据
    getNodeDebugData(nodeId: string) {
      return this.debugDataMap.get(nodeId)
    },

    // 清除节点的 debug 数据
    clearNodeDebugData(nodeId: string) {
      this.debugDataMap.delete(nodeId)
    },

    // 清除所有 debug 数据
    clearAllDebugData() {
      this.debugDataMap.clear()
    }
  },
})
