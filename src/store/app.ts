import { defineStore } from 'pinia'
import { getDesignerEnumList } from '../apis/system'

interface AppState {
  isFullscreen: boolean
  enumList: Record<string, any> // 存储原始枚举数据
  enumOptions: Record<string, Array<{ label: string; value: string }>> // 存储转换后的label-value格式
  enumLoaded: boolean // 标记枚举是否已加载
  theme: string
  mode: 'edit' | 'readonly'
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    isFullscreen: false,
    enumList: {},
    enumOptions: {},
    enumLoaded: false,
    theme: 'light',
    mode: 'edit' as 'edit' | 'readonly'
  }),

  actions: {
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
    },
    setFullscreen(value: boolean) {
      this.isFullscreen = value
    },

    // 获取枚举数据并存储
    async fetchEnumList() {
      try {
        const res = await getDesignerEnumList()

        if (res.code === 200 && Array.isArray(res.data)) {
          // 存储原始数据
          const enumData: Record<string, any> = {}
          // 存储转换后的label-value格式
          const enumOptions: Record<string, Array<{ label: string; value: string }>> = {}

          // 处理每个枚举类型
          res.data.forEach((item) => {
            if (item.type && Array.isArray(item.content)) {
              // 存储原始内容
              enumData[item.type] = item.content

              // 转换为label-value格式
              enumOptions[item.type] = item.content.map((value) => ({
                label: value,
                value: value,
              }))
            }
          })

          this.enumList = enumData
          this.enumOptions = enumOptions
          this.enumLoaded = true
        }
        return res.data
      } catch (error) {
        console.error('获取枚举数据失败:', error)
        return null
      }
    },

    // 获取指定枚举的原始内容
    getEnum(key: string) {
      return this.enumList[key] || []
    },

    // 获取指定枚举的label-value格式
    getEnumOptions(key: string) {
      return this.enumOptions[key] || []
    },

    // 重置枚举数据
    resetEnumList() {
      this.enumList = {}
      this.enumOptions = {}
      this.enumLoaded = false
    },
    setTheme(theme: string) {
      this.theme = theme
    },
    setMode(mode: 'edit' | 'readonly') {
      this.mode = mode
    }
  },
})
