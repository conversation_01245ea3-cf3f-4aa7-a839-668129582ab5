import { defineStore } from 'pinia'

interface DebugResult {
  inputs: any
  outputs: any
  status: string
  startTime: number
  endTime: number
  errorMessage?: string
  nodeType: string
  properties?: any
}

export const useTryRunStore = defineStore('tryRun', {
  state: () => ({
    nodeDebugResults: {} as Record<string, DebugResult>,
    visible: false,
    isDirectDebug: false,
  }),

  actions: {
    setNodeDebugResult(nodeId: string, result: DebugResult) {
      this.nodeDebugResults[nodeId] = result
    },

    getNodeDebugResult(nodeId: string): DebugResult | undefined {
      return this.nodeDebugResults[nodeId]
    },

    setVisible(visible: boolean) {
      this.visible = visible
    },

    $reset() {
      this.nodeDebugResults = {}
      this.visible = false
      this.isDirectDebug = false
    },
  },
})
