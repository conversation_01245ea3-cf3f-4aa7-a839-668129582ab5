import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface SystemVariable {
  id: string
  name: string
  type: 'string' | 'number' | 'boolean' | 'json'
  value: any
  description: string
  isGlobal: boolean
  createTime: string
  updateTime: string
}

export const useSystemVariablesStore = defineStore('systemVariables', () => {
  // 状态
  const variables = ref<SystemVariable[]>([])
  const currentCanvasId = ref<string>('')

  // 计算属性
  const globalVariables = computed(() => 
    variables.value.filter(v => v.isGlobal)
  )

  const localVariables = computed(() => 
    variables.value.filter(v => !v.isGlobal)
  )

  const allVariables = computed(() => variables.value)

  // 获取所有可用变量（全局 + 当前画布的局部变量）
  const availableVariables = computed(() => {
    return variables.value.filter(v => 
      v.isGlobal || (!v.isGlobal && currentCanvasId.value)
    )
  })

  // 方法
  const createVariable = (data: Omit<SystemVariable, 'id' | 'createTime' | 'updateTime'>) => {
    const now = new Date().toISOString()
    const newVariable: SystemVariable = {
      ...data,
      id: `var_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: now,
      updateTime: now
    }
    
    // 检查变量名是否已存在
    const exists = variables.value.some(v => 
      v.name === data.name && 
      (v.isGlobal || data.isGlobal || (!v.isGlobal && !data.isGlobal))
    )
    
    if (exists) {
      throw new Error(`变量名 "${data.name}" 已存在`)
    }
    
    variables.value.push(newVariable)
    saveToStorage()
    return newVariable
  }

  const updateVariable = (id: string, data: Partial<Omit<SystemVariable, 'id' | 'createTime'>>) => {
    const index = variables.value.findIndex(v => v.id === id)
    if (index === -1) {
      throw new Error('变量不存在')
    }
    
    // 如果更新变量名，检查是否重复
    if (data.name && data.name !== variables.value[index].name) {
      const exists = variables.value.some(v => 
        v.id !== id && 
        v.name === data.name && 
        (v.isGlobal || data.isGlobal || (!v.isGlobal && !data.isGlobal))
      )
      
      if (exists) {
        throw new Error(`变量名 "${data.name}" 已存在`)
      }
    }
    
    variables.value[index] = {
      ...variables.value[index],
      ...data,
      updateTime: new Date().toISOString()
    }
    
    saveToStorage()
    return variables.value[index]
  }

  const deleteVariable = (id: string) => {
    const index = variables.value.findIndex(v => v.id === id)
    if (index === -1) {
      throw new Error('变量不存在')
    }
    
    variables.value.splice(index, 1)
    saveToStorage()
  }

  const getVariable = (name: string): SystemVariable | undefined => {
    return availableVariables.value.find(v => v.name === name)
  }

  const getVariableValue = (name: string): any => {
    const variable = getVariable(name)
    return variable?.value
  }

  const getAllVariables = (): SystemVariable[] => {
    return availableVariables.value
  }

  // 解析字符串中的变量引用
  const resolveVariables = (text: string): string => {
    if (typeof text !== 'string') return text
    
    // 匹配 ${VARIABLE_NAME} 格式的变量引用
    return text.replace(/\$\{([A-Z_][A-Z0-9_]*)\}/g, (match, varName) => {
      const value = getVariableValue(varName)
      if (value !== undefined) {
        return typeof value === 'object' ? JSON.stringify(value) : String(value)
      }
      return match // 如果变量不存在，保持原样
    })
  }

  // 检查文本中是否包含变量引用
  const hasVariableReferences = (text: string): boolean => {
    if (typeof text !== 'string') return false
    return /\$\{[A-Z_][A-Z0-9_]*\}/.test(text)
  }

  // 获取文本中的所有变量引用
  const getVariableReferences = (text: string): string[] => {
    if (typeof text !== 'string') return []
    const matches = text.match(/\$\{([A-Z_][A-Z0-9_]*)\}/g)
    return matches ? matches.map(match => match.slice(2, -1)) : []
  }

  // 验证变量引用是否有效
  const validateVariableReferences = (text: string): { valid: boolean; invalidRefs: string[] } => {
    const refs = getVariableReferences(text)
    const invalidRefs = refs.filter(ref => !getVariable(ref))
    return {
      valid: invalidRefs.length === 0,
      invalidRefs
    }
  }

  // 设置当前画布ID
  const setCurrentCanvasId = (canvasId: string) => {
    currentCanvasId.value = canvasId
  }

  // 存储到localStorage
  const saveToStorage = () => {
    try {
      localStorage.setItem('systemVariables', JSON.stringify(variables.value))
    } catch (error) {
      console.error('保存系统变量失败:', error)
    }
  }

  // 从localStorage加载
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('systemVariables')
      if (stored) {
        variables.value = JSON.parse(stored)
      }
    } catch (error) {
      console.error('加载系统变量失败:', error)
      variables.value = []
    }
  }

  // 初始化默认变量
  const initializeDefaultVariables = () => {
    const defaultVariables = [
      {
        name: 'API_BASE_URL',
        type: 'string' as const,
        value: 'https://api.example.com',
        description: 'API基础URL地址',
        isGlobal: true
      },
      {
        name: 'TIMEOUT',
        type: 'number' as const,
        value: 5000,
        description: '请求超时时间（毫秒）',
        isGlobal: true
      },
      {
        name: 'DEBUG_MODE',
        type: 'boolean' as const,
        value: false,
        description: '是否开启调试模式',
        isGlobal: true
      },
      {
        name: 'DEFAULT_HEADERS',
        type: 'json' as const,
        value: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        description: '默认请求头',
        isGlobal: true
      }
    ]

    // 只添加不存在的默认变量
    defaultVariables.forEach(varData => {
      const exists = variables.value.some(v => v.name === varData.name)
      if (!exists) {
        createVariable(varData)
      }
    })
  }

  // 清空所有变量
  const clearAllVariables = () => {
    variables.value = []
    saveToStorage()
  }

  // 导出变量配置
  const exportVariables = () => {
    return {
      version: '1.0.0',
      exportTime: new Date().toISOString(),
      variables: variables.value
    }
  }

  // 导入变量配置
  const importVariables = (data: any, options: { merge?: boolean } = {}) => {
    if (!data.variables || !Array.isArray(data.variables)) {
      throw new Error('无效的变量配置格式')
    }

    if (!options.merge) {
      variables.value = []
    }

    data.variables.forEach((varData: any) => {
      try {
        createVariable({
          name: varData.name,
          type: varData.type,
          value: varData.value,
          description: varData.description || '',
          isGlobal: varData.isGlobal !== false
        })
      } catch (error) {
        console.warn(`导入变量 ${varData.name} 失败:`, error)
      }
    })
  }

  // 初始化
  loadFromStorage()

  return {
    // 状态
    variables,
    currentCanvasId,
    
    // 计算属性
    globalVariables,
    localVariables,
    allVariables,
    availableVariables,
    
    // 方法
    createVariable,
    updateVariable,
    deleteVariable,
    getVariable,
    getVariableValue,
    getAllVariables,
    resolveVariables,
    hasVariableReferences,
    getVariableReferences,
    validateVariableReferences,
    setCurrentCanvasId,
    saveToStorage,
    loadFromStorage,
    initializeDefaultVariables,
    clearAllVariables,
    exportVariables,
    importVariables
  }
})
