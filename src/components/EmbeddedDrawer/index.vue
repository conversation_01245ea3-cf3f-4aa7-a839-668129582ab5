<template>
  <a-drawer
    v-model:open="drawerVisible"
    placement="right"
    :width="isFullscreen ? '100vw' : (EmbeddedDrawerWidth || width)"
    :height="isFullscreen ? '100vh' : undefined"
    :mask="!isFullscreen"
    :maskClosable="!isFullscreen"
    :closable="false"
    @close="handleClose"
    :rootClassName="isFullscreen ? 'common-drawer-root fullscreen-drawer' : 'common-drawer-root my-3 mr-2 !h-[calc(100%-28px)]'"
    :style="isFullscreen ? { top: 0, right: 0, margin: 0 } : {}"
  >
    <!-- 头部 -->
    <template #title>
      <div class="flex items-center justify-between w-full header-gradient">
        <div class="flex items-center gap-3">
          <slot name="title">
            <span class="text-base font-medium">{{ title }}</span>
          </slot>
        </div>
        <div class="flex items-center gap-2">
          <slot name="header-actions" />
          <!-- 全屏切换按钮 -->
          <FullscreenExitOutlined
            v-if="isFullscreen && allowFullscreen"
            class="fullscreen-icon"
            @click="toggleFullscreen"
            title="退出全屏 (ESC)"
          />
          <FullscreenOutlined
            v-else-if="allowFullscreen"
            class="fullscreen-icon"
            @click="toggleFullscreen"
            title="全屏显示"
          />
          <CloseOutlined class="close-icon" @click="handleClose" />
        </div>
      </div>
    </template>

    <!-- 内容区 -->
    <div class="drawer-content" :class="{ 'fullscreen-content': isFullscreen }">
      <template v-if="drawerVisible">
        <slot />
      </template>
    </div>
    <!-- 底部 -->
    <template #footer>
      <slot name="footer" />
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { CloseOutlined, FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue'
import {EmbeddedDrawerWidth} from '@/views/designer/canvas/constants/index.ts'

interface Props {
  modelValue: boolean
  title?: string
  width?: number
  fullHeight?: boolean
  allowFullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  width: 500,
  fullHeight: false,
  allowFullscreen: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
  'fullscreen-change': [isFullscreen: boolean]
}>()

// 全屏状态
const isFullscreen = ref(false)

// 创建计算属性来处理双向绑定
const drawerVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  emit('fullscreen-change', isFullscreen.value)
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isFullscreen.value) {
    toggleFullscreen()
  }
}

// 关闭抽屉
const handleClose = () => {
  isFullscreen.value = false
  emit('update:modelValue', false)
  emit('close')
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss">
.common-drawer-root {
  .ant-drawer-content-wrapper {
    border-radius: 12px !important;
    .ant-drawer-content{
      @apply dark:!bg-area-fill;
    }
  }

  .ant-drawer-content {
    border-radius: 12px !important;
  }

  .ant-drawer-header {
    padding: 16px 24px;
    background: linear-gradient(180deg, #f9f2fe 0%, #fcfcff 100%);
    @apply dark:bg-area-fill dark:bg-none;
    .ant-drawer-title{
      @apply dark:text-white;
      .close-icon{
        @apply dark:text-white;
      }
    }
  }

  .header-gradient {
    position: relative;
    min-height: 40px;
  }

  .close-icon, .fullscreen-icon {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;

    &:hover {
      color: rgba(0, 0, 0, 0.75);
    }
  }

  .fullscreen-icon {
    margin-right: 8px;
    @apply dark:text-white dark:hover:text-gray-300;
  }

  .drawer-content {
    height: 100%;
    overflow: unset;
  }

  .fullscreen-content {
    padding: 0 !important;
  }

  .ant-drawer-mask {
    background-color: transparent !important;
  }
}

// 全屏模式样式
.fullscreen-drawer {
  .ant-drawer-content-wrapper {
    width: 100vw !important;
    height: 100vh !important;
    top: 0 !important;
    right: 0 !important;
    border-radius: 0 !important;
    margin: 0 !important;
  }

  .ant-drawer-content {
    border-radius: 0 !important;
    height: 100vh !important;
  }

  .ant-drawer-body {
    padding: 0 !important;
    height: calc(100vh - 55px) !important;
    overflow-y: auto;
  }

  .ant-drawer-header {
    border-radius: 0 !important;
  }
}
</style>
