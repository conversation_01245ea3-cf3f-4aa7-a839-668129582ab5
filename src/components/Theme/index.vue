<template>
  <div class="theme-switch" v-if="showThemeSwitch">
    <a-tooltip :title="isDark ? '浅色主题' : '深色主题'" placement="left">
      <div class="theme-switch-btn" @click="toggleTheme">
        <AiFillSun v-if="isDark" class="text-xl" />
        <AiFillMoon v-else class="text-xl" />
      </div>
    </a-tooltip>
  </div>
</template>

<script setup lang="ts">
import { AiFillSun, AiFillMoon } from 'vue-icons-plus/ai'
import { ref, onMounted, watch, nextTick } from 'vue'
import { useAppStore } from '@/store/app'
import { useRoute } from 'vue-router'

const appStore = useAppStore()
const route = useRoute()

const isDark = ref(false)

const showThemeSwitch = computed(()=>{
  return !['/login', '/auth/secure-signup/yss-verification/new-user', '/designer/canvas'].includes(route.path)
})

// 切换主题
const toggleTheme = () => {
  isDark.value = !isDark.value
  const root = document.documentElement
  nextTick(()=>{
    if (isDark.value) {
      root.classList.add('dark')
    } else {
    root.classList.remove('dark')
    }
  })

}

// 初始化主题
onMounted(() => {
  // 检查本地存储中的主题设置
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme === 'dark') {
    isDark.value = true
    document.documentElement.classList.add('dark')
  }
  appStore.setTheme(savedTheme || 'light')
})

// 监听主题变化并保存到本地存储
watch(isDark, (newValue) => {
  localStorage.setItem('theme', newValue ? 'dark' : 'light')
  appStore.setTheme(newValue ? 'dark' : 'light')
})
</script>

<style scoped lang="scss">
.theme-switch {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;

  .theme-switch-btn {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: scale(1.1);
    }
  }
}
</style>
