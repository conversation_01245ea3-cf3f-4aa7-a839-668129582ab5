//ts-nocheck
<template>
  <div class="code-editor-container h-full w-full">
    <div ref="editorContainer" class="monaco-editor-container h-full w-full"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as monaco from 'monaco-editor';
// 导入所需的 workers
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker'
import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker'
import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker'
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker'

// 配置 Monaco Editor 的 worker
window.MonacoEnvironment = {
  getWorker(_moduleId: string, label: string) {
    const workerMap = {
      json: json<PERSON>orker,
      css: cssWorker,
      html: htmlWorker,
      typescript: tsWorker,
      javascript: tsWorker,
      java: editorWorker,
      python: editorWorker,
      cpp: editorWorker,
      csharp: editorWorker,
      go: editorWorker,
      ruby: editorWorker,
      php: editorWorker,
      default: editorWorker
    }

    return new (workerMap[label as keyof typeof workerMap] || workerMap.default)()
  }
}

// 初始化 TypeScript/JavaScript 配置
monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
  target: monaco.languages.typescript.ScriptTarget.Latest,
  allowNonTsExtensions: true,
  moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
  module: monaco.languages.typescript.ModuleKind.CommonJS,
  noEmit: true,
  esModuleInterop: true,
  jsx: monaco.languages.typescript.JsxEmit.React,
  allowJs: true,
  typeRoots: ['node_modules/@types']
})

// 设置语言同步
monaco.languages.typescript.javascriptDefaults.setEagerModelSync(true)
monaco.languages.typescript.typescriptDefaults.setEagerModelSync(true)

// Props 定义
interface Props {
  initialCode: string;
  initialLanguage: string;
  readOnly?: boolean;
  theme?: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['codeChange', 'save']);

// 编辑器引用
const editorContainer = ref<HTMLElement | null>(null);
let editor: monaco.editor.IStandaloneCodeEditor | null = null;
const hasChanges = ref(false);
const currentCode = ref(props.initialCode);

// 保存变更
const saveChanges = () => {
  if (editor && hasChanges.value) {
    const code = editor.getValue();
    currentCode.value = code;
    hasChanges.value = false;
    emit('save', code);
    emit('codeChange', code);
  }
};

// 初始化编辑器
onMounted(() => {
  console.log('【CodeEditor】Component mounted');
  console.log('【CodeEditor】Initial code:', props.initialCode);
  console.log('【CodeEditor】Initial language:', props.initialLanguage);
  
  if (editorContainer.value) {
    // 创建编辑器实例
    editor = monaco.editor.create(editorContainer.value, {
      value: props.initialCode,
      language: props.initialLanguage,
      theme: props.theme || 'vs-dark',
      automaticLayout: true,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      readOnly: props.readOnly || false,
      fontSize: 14,
      lineNumbers: 'on',
      wordWrap: 'on',
      tabSize: 2,
    });

    // 监听内容变化
    editor.onDidChangeModelContent(() => {
      if (editor) {
        const code = editor.getValue();
        // 发送变更通知，由父组件处理未保存状态
        hasChanges.value = code !== currentCode.value;
        emit('codeChange', code);
      }
    });
    
    // 添加快捷键
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      saveChanges();
    });
    
    console.log('【CodeEditor】Editor initialized');
  } else {
    console.error('【CodeEditor】Editor container not found');
  }
});

// 监听 props 变化
watch(() => props.initialCode, (newCode) => {
  console.log('【CodeEditor】initialCode changed:', newCode);
  if (editor && newCode !== editor.getValue()) {
    editor.setValue(newCode);
    currentCode.value = newCode;
    hasChanges.value = false;
  }
}, { immediate: false });

watch(() => props.initialLanguage, (newLanguage) => {
  console.log('【CodeEditor】initialLanguage changed:', newLanguage);
  if (editor) {
    const model = editor.getModel();
    if (model) {
      monaco.editor.setModelLanguage(model, newLanguage);
    }
  }
}, { immediate: false });

watch(() => props.readOnly, (newReadOnly) => {
  if (editor) {
    editor.updateOptions({ readOnly: newReadOnly });
  }
}, { immediate: false });

// 组件销毁前清理
onBeforeUnmount(() => {
  if (editor) {
    editor.dispose();
    editor = null;
  }
});
</script>

<style scoped>
.code-editor-container {
  position: relative;
  height: 100%;
  width: 100%;
}

.monaco-editor-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>