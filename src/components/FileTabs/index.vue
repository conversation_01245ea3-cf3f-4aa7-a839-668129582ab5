<template>
  <div class="editor-tabs-container">
      <YssTab 
        :key="`${tabs.map(tab => tab.key).join(',')}_${activeId}`"
        :tabs="tabs" 
        :menuConfig="menuConfig" 
        :initialTab="activeId" 
        @removeTab="removeTab"
        @changeActiveId="changeActiveId"
        class="file-tabs w-full h-full min-h-8"
      ></YssTab>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useDesignerStore } from '@/store/designer'
import { YssTab } from '@yss-design/ui'

const designerStore = useDesignerStore()
const { currentFile, tabData } = storeToRefs(designerStore)

 // 定义 Tab 类型
 interface Tab {
  key: string,
  title: string; // Tab 标题
  name: string; // Tab名称,对应的插槽名称
  closable?: boolean; // 是否展示关闭按钮
}

const menuConfig = [
  {
      key: 'closeAll',
      label: '关闭所有标签页',
      action: () => {
        closeAllTabs()
      }
    },
    {
      key: 'closeOthers',
      label: '关闭其他标签页',
      action: () => {
        closeOtherTabs()
      }
    },
    {
      key: 'refreshCurrent',
      label: '重新打开当前标签页',
      action: () => {
        refreshCurrentTab()
      }
    }
  ]

  const tabs = ref<Tab[]>([]);

  watch(() => tabData.value.values(), (newVal) => {
    const tab = Array.from(newVal)
    const curTabs: Tab[] = []
    tab.forEach((item) => {
      const curTab = {
        key: item?.id,
        title: item?.name,
        name: item?.name,
        closable: true
      }
      curTabs.push(curTab);
    })
    tabs.value = curTabs
  })

  onMounted(() => {
    const tab = Array.from(tabData.value.values())
    const curTabs: Tab[] = []
    tab.forEach((item) => {
      const curTab = {
        key: item?.id,
        title: item?.name,
        name: item?.name,
        closable: true
      }
      curTabs.push(curTab);
    })
    tabs.value = curTabs
  })

const activeId = ref('')
watch(
  () => currentFile.value,
  (file) => {
    if (file) {
      activeId.value = file.id
    }
  },
  {
    immediate: true
  }
)


const removeTab = (id: string) => {
  const tabs = Array.from(tabData.value.values())
  // 关闭的是当前激活的标签
  if (currentFile.value!.id === id) {
    tabs.forEach((tab: any, index) => {
      if (tab.id === id) {
        const nextTab = tabs[index + 1] || tabs[index - 1]
        if (nextTab && nextTab.id) {
          changeActiveId(nextTab.id)
        }
      }
    })
  }

  // 从Map中删除标签
  tabData.value.delete(id)

  // 删除标签后立即保存状态
  designerStore.serializeTabData()

  if (tabData.value.size === 0) {
    designerStore.currentFile = null;

    // 如果没有标签了，清除所有相关的本地存储数据
    localStorage.removeItem('designer-active-tab');
    localStorage.removeItem('designer-tabs');
  }
}

const changeActiveId = async (id: string) => {
  try {
    // 使用设计器store中的changeTab方法
    const success = await designerStore.changeTab(id);

    if (!success) {
      console.error('切换标签失败');
      // 如果changeTab失败，尝试使用基本方式切换
      const file = designerStore.tabData.get(id);
      if (file) {
        designerStore.setCurrentFile(file);
        if (designerStore.modGraph && file.content) {
          try {
            designerStore.modGraph.fromJSON(file.content);
          } catch (err) {
            console.error('加载画布内容失败:', err);
          }
        }
      }
    }

    // 确保标签状态保存
    designerStore.serializeTabData();
  } catch (error) {
    console.error('切换标签出错:', error);

    // 出错时回退到基本逻辑
    const file = designerStore.tabData.get(id);
    if (file) {
      designerStore.setCurrentFile(file);
      if (designerStore.modGraph && file.content) {
        try {
          designerStore.modGraph.fromJSON(file.content);
        } catch (err) {
          console.error('加载画布内容失败:', err);
        }
      }
    }

    // 确保在切换后保存当前标签状态
    designerStore.serializeTabData();
  }
}

// 关闭所有标签页
const closeAllTabs = () => {
  tabData.value.clear()
  designerStore.currentFile = null
  localStorage.removeItem('designer-active-tab')
  localStorage.removeItem('designer-tabs')
  designerStore.serializeTabData()
}

// 关闭其他标签页（保留当前标签页）
const closeOtherTabs = () => {
  if (!currentFile.value) return
  const currentId = currentFile.value.id
  const tabsToRemove: string[] = []
  tabData.value.forEach((tab, id) => {
    if (id !== currentId) {
      tabsToRemove.push(id)
    }
  })
  tabsToRemove.forEach(id => {
    tabData.value.delete(id)
  })
  designerStore.serializeTabData()
}

// 重新打开当前标签页
const refreshCurrentTab = () => {
  if (!currentFile.value) return
  
  const currentId = currentFile.value.id
  const currentTab = tabData.value.get(currentId)
  
  if (currentTab) {
    activeId.value = currentId
    if (designerStore.modGraph && currentTab.content) {
      try {
        designerStore.modGraph.fromJSON(currentTab.content)
      } catch (err) {
        console.error('重新加载画布内容失败:', err)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.editor-tabs-container {
  flex: 1;
  padding-bottom: 0;
  width: 100%;
  min-width: 200px;
  max-width: 100%;

  :deep(.ant-tabs) {
    // 调整整体标签栏背景色
    background-color: #fff !important;
    @apply dark:!bg-area-fill;
    width: 100%;
  }

  :deep(.ant-tabs-content) {
    height: 0;
    background-color: #fff !important;
    @apply dark:!bg-area-fill;
    .ant-tabs-tabpane {
      height: 100%;
      background-color: #fff !important;
      @apply dark:!bg-area-fill;
    }
  }

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
    padding: 0 4px;
    border-bottom: 1px solid #d9d9d9;
    width: 100%;
    background-color: #fff !important;
    @apply dark:!bg-area-fill;
  }

  :deep(.ant-tabs-nav-wrap),
  :deep(.ant-tabs-nav-list) {
    background-color: #fff !important;
    @apply dark:!bg-area-fill;
  }

  :deep(.ant-tabs-tab) {
    // 调整标签样式
    margin: 0 2px !important;
    padding: 4px 12px !important;
    height: 32px;
    min-width: 80px;
    max-width: 200px;
    background: #e8e8e8;
    border: 1px solid #d9d9d9 !important;
    @apply dark:!border-border;
    transition: all 0.3s;

    .ant-tabs-tab-btn {
      max-width: 160px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block; // 确保省略号正常显示
    }

    // 默认隐藏关闭按钮
    .ant-tabs-tab-remove {
      opacity: 0;
      transition: opacity 0.3s;
      margin-left: 4px;
      color: #999;
      flex-shrink: 0; // 防止关闭按钮被压缩

      &:hover {
        color: #666;
      }
    }

    // 鼠标悬停时显示关闭按钮和背景效果
    &:hover {
      background: #f0f0f0;
      .ant-tabs-tab-remove {
        opacity: 1;
      }
    }

    // 激活状态的标签样式
    &.ant-tabs-tab-active {
      background: #fff;
      @apply dark:!bg-primary-hover;
      border-bottom-color: transparent !important;

      .ant-tabs-tab-remove {
        opacity: 1;
      }

      // 确保激活状态下也保持相同的文本溢出处理
      .ant-tabs-tab-btn {
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
      }
    }
  }
}
</style>
