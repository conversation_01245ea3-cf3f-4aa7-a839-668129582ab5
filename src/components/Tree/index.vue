<template>
  <a-tree
    ref="treeRef"
    class="ai-tree"
    :class="{'dark-tree': dark }"
    :draggable="true"
    :tree-data="props.data"
    :selectable="isHighlight"
    :expanded-keys="expandedKeys"
    :selectedKeys="selectedKeys"
    :default-expand-all="defaultExpandAll"
    @click="nodeClickHandle"
    @expand="nodeExpand"
    @rightClick="onContextmenu"
    @drop="handleDrop"
  >
    <template #title="data">
      <div class="flex items-center">
        <!-- 如果是操作节点，显示输入框 -->
        <template v-if="data?.dataRef?.isOperationNode">
          <input
            ref="inputRef"
            v-model="newNodeName"
            class="input"
            @blur="handleBlur"
            @keyup.enter="handleBlur"
          />
        </template>
        <!-- 否则显示正常节点 -->
        <template v-else>
          <component 
            :is="data?.dataRef?.dir ? FolderOutlined : FileOutlined"
            class="mr-2 w-4 h-4"
            :class="data?.dataRef?.dir ? 'text-blue-600' : 'text-black'"
          />
          <span class="text-sm">{{ data?.dataRef?.label }}</span>
        </template>
      </div>
    </template>
  </a-tree>
</template>

<script lang="ts" setup>
import ContextMenu from '@imengyu/vue3-context-menu'
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
import { deleteNode } from './utils/index'
import { FolderOutlined, FileOutlined } from '@ant-design/icons-vue';


interface TreeNodeData {
  name: string
  uuid?: string
  key?: string
  children?: TreeNodeData[]
  parentId?: string
  classes?: string
  path?: string
  isOperationNode?: boolean
  icon?: string
  isLeaf?: boolean
  disabled?: boolean
  selectable?: boolean
  checkable?: boolean
  disableCheckbox?: boolean
  eventKey?: string
  id?: string
}

const treeRef = ref()
const inputRef = ref()
const operationNode = ref<TreeNodeData | null>(null)
const actionInfo = ref({
  type: ''
})
const newNodeName = ref('')

const props = withDefaults(defineProps<{
  dark?: boolean
  nodeClick?: Function
  contextMenu?: any[]
  draggable?: boolean
  isAllAdd?: boolean
  data: any[]
  defaultCheckKey?: string[]
  checkKey?: string
  isHighlight?: boolean
  isCollect?: boolean
  defaultExpandAll?: boolean
}>(), {
  dark: false,
  nodeClick: () => {},
  contextMenu: () => [],
  draggable: false,
  isAllAdd: false,
  data: () => [],
  defaultCheckKey: () => [],
  checkKey: '',
  isHighlight: false,
  isCollect: false,
  defaultExpandAll: false
})

const emits = defineEmits(['nodeDrop'])

const selectedKeys = computed(() => props.checkKey ? [props.checkKey] : [])
const expandedKeys = ref<string[]>([])

watch(() => props.checkKey,
  (_checkKey) => {
    if(_checkKey) {
      selectedKeys.value = [_checkKey]
    }
  },{
    immediate: true
  }
)

watch(() => props.defaultCheckKey,
  (_checkKey) => {
    if(_checkKey && props.isCollect) {
      expandedKeys.value = [...new Set([...expandedKeys.value, ..._checkKey])];
    } else {
      expandedKeys.value = _checkKey;
    }
  },{
    immediate: true
  }
)

// 监视数据变化，自动处理展开状态
watch(() => props.data,
  (newData) => {
    // 如果设置了默认展开所有节点，并且有数据，则展开所有节点
    if (props.defaultExpandAll && newData && newData.length > 0) {
      // 收集所有节点ID
      const allIds = [] as string[];
      const collectAllIds = (nodes) => {
        if (!Array.isArray(nodes)) return;
        nodes.forEach(node => {
          if (node.id) {
            allIds.push(node.id);
          }
          if (Array.isArray(node.children) && node.children.length > 0) {
            collectAllIds(node.children);
          }
        });
      };

      collectAllIds(newData);

      // 设置展开键
      if (allIds.length > 0) {
        expandedKeys.value = [...new Set([...expandedKeys.value, ...allIds])];
      }
    }
  },
  { immediate: true, deep: true }
)

// 处理拖拽
const handleDrop = (info: any) => {
  if (!info?.dragNode || !info?.node) return

  const dropNode = info.node
  const dragNode = info.dragNode
  const dropPos = info.node.pos.split('-')
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1])

  // 发出拖拽事件
  emits('nodeDrop', {
    dragNode,
    dropNode,
    dropPosition
  })
}

// 节点点击事件
const nodeClickHandle = (selectedKeys: string[], e: any) => {
  if (!e?.dataRef) {
    return
  }
  const node = e.dataRef
  // 如果是操作节点，不触发nodeclick
  if(node.isOperationNode) {
    return
  }
  props.nodeClick?.(node)
}

// 节点被展开时候触发
const nodeExpand = (keys: string[], e: any) => {
  expandedKeys.value = keys
}

// 失去焦点执行对应操作
const handleBlur = () => {
  if (!operationNode.value || !newNodeName.value) {
    // 如果没有输入内容，删除节点
    if (operationNode.value) {
      deleteNode(props.data, operationNode.value.key)
    }
    operationNode.value = null
    newNodeName.value = ''
    return
  }

  // 如果有输入内容，直接调用对应的回调
  let menuItem = props.contextMenu.find(item => item.code === actionInfo.value.type)
  let pathPrefix = operationNode.value.path || '/'
  menuItem?.callback?.({
    id: operationNode.value.id,
    name: newNodeName.value,
    parentId: operationNode.value.parentId,
    path: pathPrefix,
    level: operationNode.value.level,
  })

  // 清理状态
  deleteNode(props.data, operationNode.value.key)
  actionInfo.value.type = ''
  operationNode.value = null
  newNodeName.value = ''
}

// 文本框失去焦点时调用重命名操作
const onBlurRename = () => {
  return operationNode.value?.originName !== newNodeName.value
}
// 文本框失去焦点时操作
const onBlurAdd = () => {
  if(!newNodeName.value){
    deleteNode(props.data, operationNode.value?.key)
    return false
  }
  return true
}

// 将焦点设置到输入框上
const onGetFocus = () => {
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 新增文件或目录
const handleNewNode = (node: TreeNodeData) => {
  // 1. 清理现有操作节点
  const cleanExistingOperationNodes = (nodes: TreeNodeData[]) => {
    let found = false
    for (let i = nodes.length - 1; i >= 0; i--) {
      const currentNode = nodes[i]
      if (currentNode.isOperationNode) {
        nodes.splice(i, 1)
        found = true
      }
      if (currentNode.children?.length > 0) {
        const foundInChildren = cleanExistingOperationNodes(currentNode.children)
        found = found || foundInChildren
      }
    }
    return found
  }
  cleanExistingOperationNodes(props.data)

  // 2. 创建新节点
  const nodeKey = `new-${Date.now()}`
  const newNode = {
    key: nodeKey,
    children: null,
    path: '',
    label: '',
    level: (node.level || 0) + 1,
    dir: actionInfo.value.type === 'addDir',
    id: '',
    isOperationNode: true,
    parentId: node.id,
  }

  // 3. 查找并修改目标节点
  const findAndAddNode = (nodes: TreeNodeData[], targetNode: TreeNodeData) => {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].id === targetNode.id) {
        if (!nodes[i].children) {
          nodes[i].children = []
        }
        nodes[i].children.unshift(newNode)
        return true
      }
      if (nodes[i].children?.length > 0) {
        if (findAndAddNode(nodes[i].children, targetNode)) {
          return true
        }
      }
    }
    return false
  }

  findAndAddNode(props.data, node)

  // 4. 展开父节点
  const parentKey = node.id || node.key
  if (parentKey) {
    expandedKeys.value = [...new Set([...expandedKeys.value, parentKey])]
  }

  // 5. 设置操作节点
  operationNode.value = newNode

  // 6. 强制更新并聚焦输入框
  nextTick(() => {
    if (treeRef.value?.update) {
      treeRef.value.update()
    }
    inputRef.value?.focus()
  })
}

// 添加一个辅助函数来查找父节点
const findParentNode = (data: TreeNodeData[], key: string): TreeNodeData | null => {
  for (const node of data) {
    if (node.children) {
      const found = node.children.find(child => child.key === key)
      if (found) return node
      const result = findParentNode(node.children, key)
      if (result) return result
    }
  }
  return null
}

// 右键菜单
const onContextmenu = (info: any) => {
  const { event, node } = info
  if (!node) return

  event.preventDefault()
  let items = [...props.contextMenu]

  // 如果不是目录(dir=false)，只显示删除和重命名选项
  if (node.dataRef?.dir === false) {
    items = items.filter((item) => ['rename', 'delete'].includes(item.code))
  } else {
    // 如果是根节点，不显示重命名和删除选项
    if(!node.parentId) {
      items = items.filter((item) => !['rename', 'delete'].includes(item.code))
    }
  }

  items.forEach(item => {
    const action = actionMap[item.code]
    if (action) {
      item.onClick = () => action(node)
    }
  })

  ContextMenu.showContextMenu({
    x: event.clientX,
    y: event.clientY,
    items
  })
}

// 文本框丢失焦点事件
const blurMapAction = {
  rename: onBlurRename,
  addFile: onBlurAdd,
  addDir: onBlurAdd
}

// 右键事件
const actionMap = {
  rename: (node: TreeNodeData) => {
    node.dataRef.isOperationNode = true
    newNodeName.value = node.dataRef.label
    actionInfo.value.type = 'rename'
    operationNode.value = node.dataRef
    nextTick(() => {
      inputRef.value?.focus()
    })
  },
  delete: (node: TreeNodeData) => {
    actionInfo.value.type = 'delete'
    let menuItem = props.contextMenu.find(item => item.code === actionInfo.value.type)
    menuItem?.callback?.(node)
  },
  addFile: (node: TreeNodeData) => {
    actionInfo.value.type = 'addFile'
    handleNewNode(node)
  },
  addDir: (node: TreeNodeData) => {
    if(node?.classes) {
      let menuItem = props.contextMenu.find(item => item.code === 'addDir')
      menuItem?.callback?.(node)
    } else {
      actionInfo.value.type = 'addDir'
      handleNewNode(node)
    }
  },
  edit: (node: TreeNodeData) => {
    let menuItem = props.contextMenu.find(item => item.code === 'edit')
    menuItem?.callback?.(node)
  }
}

// 如果有相关的方法设置 operationNode，也要确保安全赋值
const setOperationNode = (node: any) => {
  operationNode.value = node || null
}
</script>

<style lang="scss">
.mx-context-menu{
  @apply p-[2px_0] text-xs;
  .mx-context-menu-item{
    @apply p-[4px_5px];
  }
}
</style>

<style scoped>
.ai-tree {
  @apply w-full h-full overflow-y-auto;
}

.input {
  @apply h-[23px];
}

.dark-tree {
  @apply bg-[#18181b] text-[#cdcdcd];
}

.dark-tree input {
  @apply h-[23px] bg-[#18181b];
}

.dark-tree :deep(.ant-tree-node-content-wrapper:hover) {
  @apply bg-[#333];
}

.dark-tree :deep(.ant-tree-node-selected) {
  @apply bg-[#333];
}

.dark-tree input {
  @apply border border-[#00459C] pr-[10px];
}

/* 添加拖拽相关样式 */
:deep(.ant-tree-drop-indicator) {
  @apply bg-blue-500;
}

:deep(.ant-tree-treenode-draggable) {
  @apply cursor-move;
}

:deep(.ant-tree-node-content-wrapper.dragging) {
  @apply bg-gray-100;
}
</style>
