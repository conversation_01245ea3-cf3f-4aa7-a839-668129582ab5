<template>
  <a-modal
    :visible="visible"
    @update:visible="updateVisible"
    title="API测试"
    width="800px"
    :footer="null"
  >
    <div class="curl-container">
      <div class="mb-4">
        <div class="font-bold mb-2">请求URL</div>
        <a-input v-model:value="curlForm.path" placeholder="请输入API路径" />
      </div>

      <a-tabs v-model:activeKey="activeTabKey">
        <a-tab-pane key="data" tab="请求参数">
          <div class="param-container">
            <div v-for="(param, index) in curlForm.data" :key="index" class="param-item">
              <div class="flex items-center mb-2">
                <span class="font-bold">{{ param.name }}</span>
                <span class="text-gray-500 ml-2">({{ param.type }})</span>
                <span class="text-gray-400 ml-2 text-xs">{{ param.description }}</span>
              </div>
              <a-input
                v-model:value="param.value"
                placeholder="请输入参数值"
                :rows="param.type === 'String' && param.value.length > 50 ? 4 : 1"
                :type="param.type === 'String' && param.value.length > 50 ? 'textarea' : 'text'"
              />
            </div>
            <a-button type="dashed" block @click="addDataParam" class="mt-2">
              <plus-outlined /> 添加参数
            </a-button>
          </div>
        </a-tab-pane>

        <a-tab-pane key="headers" tab="Headers">
          <div class="param-container">
            <div
              v-for="(header, index) in curlForm.headers"
              :key="index"
              class="param-item flex items-center mb-2"
            >
              <a-input
                v-model:value="header.name"
                placeholder="Header名称"
                class="mr-2"
                style="width: 40%"
              />
              <a-input v-model:value="header.value" placeholder="Header值" style="width: 50%" />
              <a-button type="text" danger @click="removeHeader(index)">
                <delete-outlined />
              </a-button>
            </div>
            <a-button type="dashed" block @click="addHeader" class="mt-2">
              <plus-outlined /> 添加Header
            </a-button>
          </div>
        </a-tab-pane>

        <a-tab-pane key="commons" tab="通用参数">
          <div class="param-container">
            <div
              v-for="(common, index) in curlForm.commons"
              :key="index"
              class="param-item flex items-center mb-2"
            >
              <a-input
                v-model:value="common.name"
                placeholder="参数名称"
                class="mr-2"
                style="width: 40%"
              />
              <a-input v-model:value="common.value" placeholder="参数值" style="width: 50%" />
              <a-button type="text" danger @click="removeCommon(index)">
                <delete-outlined />
              </a-button>
            </div>
            <a-button type="dashed" block @click="addCommon" class="mt-2">
              <plus-outlined /> 添加通用参数
            </a-button>
          </div>
        </a-tab-pane>
      </a-tabs>

      <div class="mt-4 flex justify-between">
        <a-button type="primary" @click="sendCurlRequest" :loading="curlLoading">
          发送请求
        </a-button>
        <a-button @click="generateCurlCommand"> 生成CURL命令 </a-button>
      </div>

      <div v-if="curlCommand" class="mt-4">
        <div class="font-bold mb-2">CURL命令</div>
        <a-textarea :value="curlCommand" :rows="4" readonly />
        <a-button type="link" class="p-0 mt-1" @click="copyCurlCommand">
          <copy-outlined /> 复制
        </a-button>
      </div>

      <div v-if="curlResponse" class="mt-4">
        <div class="font-bold mb-2">响应结果</div>
        <a-tabs>
          <a-tab-pane key="response" tab="响应内容">
            <div class="response-container">
              <pre class="response-json">{{ formatResponse(curlResponse.data) }}</pre>
            </div>
          </a-tab-pane>
          <a-tab-pane key="headers" tab="响应头">
            <div class="response-container">
              <div v-for="(value, key) in curlResponse.headers" :key="key" class="mb-1">
                <span class="font-bold">{{ key }}:</span> {{ value }}
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, DeleteOutlined, CopyOutlined } from '@ant-design/icons-vue'
import axios from 'axios'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  apiData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

const curlLoading = ref(false)
const activeTabKey = ref('data')
const curlCommand = ref('')
const curlResponse = ref(null)

const curlForm = reactive({
  path: '',
  data: [] as any[],
  headers: [] as { name: string; value: string }[],
  commons: [] as { name: string; value: string }[]
})

// 监听apiData变化
const initForm = () => {
  curlForm.path = props.apiData.pathUrl || ''

  try {
    const reqParam = JSON.parse(props.apiData.reqParam || '{"data":[],"headers":[],"commons":[]}')
    curlForm.data = reqParam.data || []
    curlForm.headers = reqParam.headers || []
    curlForm.commons = reqParam.commons || []
  } catch (error) {
    curlForm.data = []
    curlForm.headers = []
    curlForm.commons = []
    message.error('解析请求参数失败')
  }

  curlCommand.value = ''
  curlResponse.value = null
}

// 添加数据参数
const addDataParam = () => {
  curlForm.data.push({
    name: '',
    type: 'String',
    value: '',
    description: '',
    expanded: false,
    children: []
  })
}

// 添加Header
const addHeader = () => {
  curlForm.headers.push({ name: '', value: '' })
}

// 移除Header
const removeHeader = (index: number) => {
  curlForm.headers.splice(index, 1)
}

// 添加通用参数
const addCommon = () => {
  curlForm.commons.push({ name: '', value: '' })
}

// 移除通用参数
const removeCommon = (index: number) => {
  curlForm.commons.splice(index, 1)
}

// 生成CURL命令
const generateCurlCommand = () => {
  let command = `curl -X POST "${curlForm.path}"`

  // 添加Headers
  curlForm.headers.forEach(header => {
    if (header.name && header.value) {
      command += ` \\\n  -H "${header.name}: ${header.value}"`
    }
  })

  // 构建请求体
  const requestBody: any = {}

  // 添加数据参数
  if (curlForm.data.length > 0) {
    const dataObj: any = {}
    curlForm.data.forEach(param => {
      if (param.name) {
        dataObj[param.name] = param.value
      }
    })
    requestBody.data = dataObj
  }

  // 添加通用参数
  if (curlForm.commons.length > 0) {
    const commonsObj: any = {}
    curlForm.commons.forEach(common => {
      if (common.name) {
        commonsObj[common.name] = common.value
      }
    })
    requestBody.commons = commonsObj
  }

  command += ` \\\n  -d '${JSON.stringify(requestBody)}'`

  curlCommand.value = command
}

// 复制CURL命令
const copyCurlCommand = () => {
  navigator.clipboard.writeText(curlCommand.value)
    .then(() => message.success('已复制到剪贴板'))
    .catch(() => message.error('复制失败'))
}

// 发送CURL请求
const sendCurlRequest = async () => {
  if (!curlForm.path) {
    message.error('请输入API路径')
    return
  }

  curlLoading.value = true

  try {
    // 构建请求体
    const requestBody: any = {}

    // 添加数据参数
    if (curlForm.data.length > 0) {
      const dataObj: any = {}
      curlForm.data.forEach(param => {
        if (param.name) {
          dataObj[param.name] = param.value
        }
      })
      requestBody.data = dataObj
    }

    // 添加通用参数
    if (curlForm.commons.length > 0) {
      const commonsObj: any = {}
      curlForm.commons.forEach(common => {
        if (common.name) {
          commonsObj[common.name] = common.value
        }
      })
      requestBody.commons = commonsObj
    }

    // 构建headers
    const headers: Record<string, string> = {}
    curlForm.headers.forEach(header => {
      if (header.name) {
        headers[header.name] = header.value
      }
    })

    const response = await axios.post(curlForm.path, requestBody, { headers })
    curlResponse.value = response
    generateCurlCommand()
  } catch (error: any) {
    if (error.response) {
      curlResponse.value = error.response
      message.error(`请求失败: ${error.response.status} ${error.response.statusText}`)
    } else {
      message.error(`请求失败: ${error.message}`)
    }
  } finally {
    curlLoading.value = false
  }
}

// 格式化响应结果
const formatResponse = (data: any) => {
  try {
    if (typeof data === 'string') {
      return JSON.stringify(JSON.parse(data), null, 2)
    }
    return JSON.stringify(data, null, 2)
  } catch (e) {
    return data
  }
}

// 监听visible变化
const updateVisible = (val: boolean) => {
  emit('update:visible', val)
  if (val) {
    initForm()
  }
}

defineExpose({
  initForm
})
</script>

<style scoped>
.curl-container {
  padding: 10px;
}

.param-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.param-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #f0f0f0;
}

.param-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.response-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #fafafa;
  border-radius: 4px;
}

.response-json {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
}
</style>
