<template>
  <div class="base-table-wrapper">
    <!-- 搜索表单区域 -->
    <div v-if="searchSchema" class="search-form-area bg-global-background">
      <div class="flex items-center gap-5">
        <a-form ref="searchFormRef" layout="inline" :model="searchForm">
          <template v-for="field in searchSchema" :key="field.key">
            <a-form-item :label="field.label">
              <!-- 输入框 -->
              <a-input
                v-if="field.type === 'input'"
                v-model:value="searchForm[field.key]"
                class="bg-input-fill !text-custom border-input-border"
                :placeholder="field.placeholder"
                allow-clear
              />

              <!-- 选择框 -->
              <a-select
                v-if="field.type === 'select'"
                v-model:value="searchForm[field.key]"
                :placeholder="field.placeholder"
                :options="field.options"
                allow-clear
                style="min-width: 120px"
              />

              <!-- 日期选择 -->
              <a-date-picker
                v-if="field.type === 'date'"
                v-model:value="searchForm[field.key]"
                :placeholder="field.placeholder"
              />
            </a-form-item>
          </template>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch"> 查询 </a-button>
              <a-button @click="handleReset"> 重置 </a-button>
            </a-space>
          </a-form-item>
          <slot name="actions"></slot>
        </a-form>
      </div>
    </div>
    <YssTable
      :loading="loading"
      :scroll="{ x: '100%' }"
      v-bind="{...props.tableProps}"
      :data="dataSource"
      :columns="columns"
      :showFilter="false"
      :expend-row-keys="expandedKeys"
      :show-pagination="showPagination"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      :show-size-changer="true"
      :page-size-options="['10', '20', '50', '100']"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
      @expand="handleExpand"
      @reset-filter="handleReset"
    >

      <!-- 传递所有插槽 -->
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData"></slot>
      </template>
    </YssTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { YssTable } from '@yss-design/ui'

// 搜索字段配置接口
interface SearchField {
  key: string
  label: string
  type: 'input' | 'select' | 'date' | 'dateRange' | 'custom'
  placeholder?: string
  options?: { label: string; value: any }[]
}

// 表格列配置接口
interface TableColumn {
  title: string
  dataIndex: string
  key: string
  width?: number
  slot?: boolean
  fixed?: 'right' | 'left'
  slots?: { customRender: string }
  customRender?: ({ text }: { text: any }) => any
}

const props = defineProps({
  // 搜索表单配置
  searchSchema: {
    type: Array as PropType<SearchField[]>,
    default: () => []
  },
  // 表格列配置
  columns: {
    type: Array as PropType<TableColumn[]>,
    required: true
  },
  // 数据源
  dataSource: {
    type: Array,
    required: true
  },
  loading: Boolean,

  total: {
    type: Number,
    default: 0
  },
  // 新增分页相关的props
  current: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  },
  expandedKeys: {
    type: Array as PropType<(string | number)[]>,
    default: () => []
  },
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default:  true
  },
  // 扩展table props
  tableProps: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

const emit = defineEmits(['search', 'reset', 'pageChange', 'expand'])

// 搜索表单数据
const searchForm = reactive<Record<string, any>>({})
const searchFormRef = ref()

// 使用计算属性来处理分页数据，响应props的变化
const currentPage = computed({
  get: () => props.current,
  set: () => {} // 只读
})
const pageSize = computed({
  get: () => props.pageSize,
  set: () => {} // 只读
})

// 搜索
const handleSearch = () => {
  emit('search', { ...searchForm, current: 1, pageSize: props.pageSize })
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  // 清空 searchForm 中的所有字段
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = undefined
  })
  emit('reset')
}

// 分页变化 - 使用 Ant Design Vue 的命名规范
const handlePageChange = (current: number, pageSize: number) => {
  emit('pageChange', {
    current,
    pageSize,
    searchForm: { ...searchForm }
  })
}

const handleSizeChange = (current: number, pageSize: number) => {
  emit('pageChange', {
    current,
    pageSize,
    searchForm: { ...searchForm }
  })
}

// 处理展开/收起事件
const handleExpand = (expanded: boolean, record: any) => {
  emit('expand', expanded, record)
}
</script>

<style scoped>
.base-table-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: visible;
}

.search-form-area {
  flex-shrink: 0;
  padding: 16px;
}

.action-area {
  flex-shrink: 0;
  padding: 0 16px 16px;
}
.action-area :deep(.ant-btn + .ant-btn) {
  margin-left: 8px;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: #fff;
  position: relative;
  overflow: visible;
}
.table-container :deep(.ant-table-container) {
  height: auto;
}

.pagination-area {
  flex-shrink: 0;
  padding: 16px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-table-wrapper) {
  flex: 1;
  overflow: visible;
}

:deep(.ant-table) {
  height: auto;
}

:deep(.ant-table-body) {
  overflow-y: visible !important;
  min-height: 0;
}

:deep(.ant-spin-nested-loading) {
  height: 100%;
}

:deep(.ant-spin-container) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 移除可能有冲突的固定列样式 */
/* :deep(.ant-table-cell-fix-left),
:deep(.ant-table-cell-fix-right) {
  z-index: 2;
} */

.expand-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  background-color: white;
  transition: all 0.3s ease;
}

.expand-icon-wrapper:hover {
  border-color: var(--ant-primary-color);
  background-color: var(--ant-primary-1);
}

.expand-icon-wrapper :deep(.anticon) {
  font-size: 16px;
}

/* 主题色变量，可以根据项目主题色调整 */
:root {
  --ant-primary-color: #1890ff;
  --ant-primary-1: #e6f7ff;
}

.text-primary {
  color: var(--ant-primary-color);
}

/* 添加展开收起动画 */
.expand-icon-wrapper:hover .anticon {
  transform: scale(1.1);
}
:deep(.ant-input){
  background: var(--input-fill-color) !important;
  color: var(--text-color) !important;
}
</style>
