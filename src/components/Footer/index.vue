<template>
  <footer class="py-3 px-4 flex justify-between items-center text-sm text-[#A0AEC0] bg-[var(--bg-footer)]">
    <div class="flex items-center space-x-4">
      <span class="text-inherit">Y<PERSON><PERSON><PERSON> SOFTWARE</span>
      <span class="text-primary">赢时胜软件</span>
      <span class="text-inherit"
        >深交所创业板上市（代码）
        <span class="text-primary">300377</span>
      </span>
    </div>
    <div class="flex items-center space-x-4">
      <span class="text-inherit">服务器名称：{{ serverInfo.serverName }}</span>
      <span class="text-inherit">服务器地址：{{ serverInfo.serverIp  }}</span>
      <span class="text-inherit">服务器端口：{{ serverInfo.serverPort  }}</span>
      <span class="text-inherit flex items-center hover:text-primary cursor-pointer">
        <TeamOutlined class="mr-1" />
        在线人数：{{ onlineCount }}
      </span>
    </div>
  </footer>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { getServerInfo } from '@/apis/operations'
import { getOnlineCount } from '@/apis/system'
import { TeamOutlined } from '@ant-design/icons-vue'

const serverInfo = ref({
  serverName: '',
  serverIp: '',
  serverPort: '',
  osName: '',
  osVersion: '',
  osArch: ''
})

const onlineCount = ref(0)
let timer = null

// 获取服务器信息
const fetchServerInfo = async () => {
  try {
    const res = await getServerInfo()
    if (res && res.data) {
      serverInfo.value = res.data
    }
  } catch (error) {
    console.error('获取服务器信息失败:', error)
  }
}

// 更新在线人数
const updateOnlineCount = async () => {
  try {
    const res = await getOnlineCount()
    if (res.code === 200) {
      onlineCount.value = res.data
    }
  } catch (error) {
    console.error('获取在线用户数失败:', error)
  }
}

// 启动或停止定时更新
const setupPolling = () => {
  // 清除现有定时器
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  // 启动定时器
  updateOnlineCount()
  timer = setInterval(updateOnlineCount, 60000)
}

onMounted(() => {
  fetchServerInfo()
  setupPolling()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<style scoped>
footer {
  background-color: var(--bg-footer) !important;
}
</style>