<template>
  <div class="h-full bg-global-background p-0">
    <!-- 插槽：顶部搜索区域 -->
    <slot name="header">
      <div class="flex gap-4 mb-6">
        <a-button
          v-for="tab in tabs"
          :key="tab.value"
          :class="{ '!bg-blue-600 !text-white dark:!bg-primary-hover': activeTab === tab.value }"
          @click="handleTabChange(tab.value)"
        >
          {{ tab.label }}
        </a-button>
      </div>
    </slot>

    <!-- 内容区域 -->
    <div :class="`grid grid-cols-${colCount} gap-6`">
      <div
        v-for="item in list"
        :key="item.id"
        class="bg-area-fill rounded-lg shadow-md border border-gray-100 dark:!border-border hover:border-blue-500 transition-all duration-200 cursor-pointer relative p-4"
        style="box-sizing: border-box; margin-bottom: 16px;"
        @click="handleItemClick(item)"
      >
        <div
          v-if="isCollected"
          class="absolute right-4 top-4 z-10"
        >
          <StarOutlined 
            class="text-gray-400 hover:text-yellow-400 transition-colors cursor-pointer"
            :class="{'text-yellow-400': item.isCollected}"
            @click.stop="handleCollect(item)"
          />
        </div>
        <div class="flex gap-4">
          <div 
            class="rounded-lg overflow-hidden flex-shrink-0"
            style="width: 64px; height: 64px;"
          >
            <img
              :src="item.avatar"
              class="w-full h-full object-cover"
            />
          </div>
          <div class="flex-1 min-w-0">
            <div 
              class="text-lg font-medium truncate text-custom"
              style="padding-right: 40px; margin-bottom: 8px;"
            >
              {{ item.title }}
            </div>
            <div class="flex flex-wrap gap-2 mb-2">
              <a-tag color="pink">
                {{ item.level }}
              </a-tag>
              <a-tag
                v-for="tag in item.tags"
                class="dark:!text-[var(--text-secondary)] dark:!border-[var(--text-secondary)]"
                :key="tag"
              >
                {{ tag }}
              </a-tag>
            </div>
          </div>
        </div>
        <div 
          class="text-gray-600 text-sm dark:!text-[var(--text-secondary)]"
          style="margin: 16px 0 0px;"
        >
          {{ item.description }}
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="flex justify-center mt-6">
      <a-pagination
        :current="current"
        :total="total"
        :page-size="pageSize"
        :show-size-changer="true"
        :page-size-options="['10', '20', '50', '100']"
        @change="handlePageChange"
        @show-size-change="handleSizeChange"
      />
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { StarOutlined, UserOutlined, MessageOutlined } from '@ant-design/icons-vue'

interface TabItem {
  label: string
  value: string
}

interface AgentItem {
  id: number
  title: string
  level: string
  tags: string[]
  description: string
  users: number
  messages: number
  avatar: string
  isCollected?: boolean
}

const emit = defineEmits<{
  'update:activeTab': [value: string]
  'update:current': [page: number]
  'update:pageSize': [size: number]
  'tab-change': [value: string]
  'item-click': [item: AgentItem]
  'collect': [item: AgentItem]
}>()

const props = defineProps({
  // 列表数据
  list: {
    type: Array as () => AgentItem[],
    required: true,
    default: () => []
  },
  // 每行展示的卡片数量
  colCount: {
    type: Number,
    default: 4
  },
  // 分页相关
  current: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 12
  },
  total: {
    type: Number,
    default: 0
  },
  // 标签页配置
  tabs: {
    type: Array as () => TabItem[],
    default: () => []
  },
  // 当前激活的标签
  activeTab: {
    type: String,
    default: 'recommend'
  },
  isCollected: {
    type: Boolean,
    default: false
  }
})

// 处理标签切换
const handleTabChange = (value: string) => {
  emit('update:activeTab', value)
  emit('tab-change', value)
}

// 处理卡片点击
const handleItemClick = (item: AgentItem) => {
  emit('item-click', item)
}

// 处理收藏
const handleCollect = (item: AgentItem) => {
  emit('collect', item)
}

// 处理页码变化
const handlePageChange = (page: number) => {
  emit('update:current', page)
}

// 处理每页条数变化
const handleSizeChange = (_current: number, size: number) => {
  emit('update:pageSize', size)
  emit('update:current', 1) // 切换每页条数时重置为第一页
}
</script>
  
<style scoped>
.ant-tag {
  margin-right: 0;
}

/* 动态生成网格列数的样式 */
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
</style>