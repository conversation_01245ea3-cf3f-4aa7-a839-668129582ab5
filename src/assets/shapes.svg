<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">

  <!-- 流程图 -->
  <!-- 开始/结束 (圆角矩形) -->
  <symbol id="start-end" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M2,2 h12 a2,2 0 0 1 2,2 v8 a2,2 0 0 1 -2,2 h-12 a2,2 0 0 1 -2,-2 v-8 a2,2 0 0 1 2,-2 z"/>
  </symbol>
  
  <!-- 过程 (矩形) -->
  <symbol id="process" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M2,2 h12 v12 h-12 z"/>
  </symbol>

  <!-- 判断 (菱形) -->
  <symbol id="judge" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8,2 L14,8 L8,14 L2,8 z"/>
  </symbol>

  <!-- 输入/输出 (平行四边形) -->
  <symbol id="input-output" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M3,2 L13,2 L11,14 L1,14 z"/>
  </symbol>

  <!-- 文档 (带折角的矩形) -->
  <symbol id="document" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M2,2 h10 v12 h-10 v-12 z M12,2 l-4,4"/>
  </symbol>

  <!-- 数据 (圆柱体) -->
  <symbol id="data" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M3,4 a4,2 0 0 1 10,0 v8 a4,2 0 0 1 -10,0 z M3,4 v8 M13,4 v8"/>
  </symbol>

  <!-- 终止 -->
  <symbol id="terminal" viewBox="0 0 16 8" preserveAspectRatio="none">
    <rect x="1" y="1" width="14" height="6" rx="3"/>
  </symbol>

  <!-- 准备 -->
  <symbol id="prepare" viewBox="0 0 16 10" preserveAspectRatio="none">
    <polygon points="3,1 13,1 16,5 13,9 3,9 0,5"/>
  </symbol>

  <!-- 手动输入 -->
  <symbol id="manual-input" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="2,6 14,2 14,14 2,14"/>
  </symbol>

  <!-- 手动操作 -->
  <symbol id="manual-operation" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="2,2 14,2 12,14 4,14"/>
  </symbol>

  <!-- 合并 -->
  <symbol id="merge" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="2,2 14,2 8,14"/>
  </symbol>

  <!-- 离页连接符 -->
  <symbol id="page-connector" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="2,2 14,2 14,12 8,14 2,12"/>
  </symbol>

  <!-- 卡片 -->
  <symbol id="card" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="4,2 14,2 14,14 2,14 2,4"/>
  </symbol>

  <!-- 汇总连接 -->
  <symbol id="sum-join" viewBox="0 0 16 16" preserveAspectRatio="none">
    <circle cx="8" cy="8" r="7"/>
    <line x1="3" y1="3" x2="13" y2="13" stroke="black" stroke-width="0.2" />
    <line x1="13" y1="3" x2="3" y2="13" stroke="black" stroke-width="0.2" />
  </symbol>

  <!-- 或者 -->
  <symbol id="or" viewBox="0 0 16 16" preserveAspectRatio="none">
    <circle cx="8" cy="8" r="7"/>
    <line x1="1" y1="8" x2="15" y2="8" stroke="black" stroke-width="0.2" />
    <line x1="8" y1="1" x2="8" y2="15" stroke="black" stroke-width="0.2" />
  </symbol>

  <!-- 延期 -->
  <symbol id="delay" viewBox="0 0 16 12" preserveAspectRatio="none">
    <path d="M2 2 H10 A4 4 0 0 1 10 10 H2 Z"/>
  </symbol>

  <!-- 对照 -->
  <symbol id="contrast" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="2,2 14,2 8,8" />
    <polygon points="2,14 14,14 8,8"/>
  </symbol>

  <!-- 排序 -->
  <symbol id="sort" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="8,2 14,8 8,14 2,8"/>
    <line x1="2" y1="8" x2="14" y2="8" stroke="black" stroke-width="0.2" />
  </symbol>
  
  <!-- 基本形状 -->
  <!-- 三角形 -->
  <symbol id="triangle" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8,2 L14,14 L2,14 z"/>
  </symbol>

  <!-- 直角三角形 -->
  <symbol id="right-triangle" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="2,14 2,2 14,14"/>
  </symbol>

  <!-- 梯形 -->
  <symbol id="trapezoid" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="4,4 12,4 14,14 2,14"/>
  </symbol>

  <!-- 圆形 -->
  <symbol id="circle" viewBox="0 0 16 16">
    <path d="M8,2 a6,6 0 0 1 0,12 a6,6 0 0 1 0,-12"/>
  </symbol>

  <!-- 椭圆 -->
  <symbol id="ellipse" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M2,8 a6,4 0 0 1 12,0 a6,4 0 0 1 -12,0"/>
  </symbol>

  <!-- 五边形 -->
  <symbol id="pentagon" viewBox="0 0 16 16">
    <path d="M8,2 L14.09,6.18 L11.82,13.02 L4.18,13.02 L1.91,6.18 Z"/>
  </symbol>

  <!-- 六角形 -->
  <symbol id="hexagon" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon
      points="
        8,2
        14,5
        14,11
        8,14
        2,11
        2,5
      "
    />
  </symbol>

  <!-- L型折线 -->
  <symbol id="L-line" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="2,2 8,2 8,8 14,8 14,14 2,14"/>
  </symbol>

  <!-- 十字 -->
  <symbol id="cross" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon
      points="
      6,2 10,2 10,6 14,6 14,10
      10,10 10,14 6,14 6,10
      2,10 2,6 6,6
    "
    />
  </symbol>

  <!-- 立方体 -->
  <symbol id="cube" viewBox="0 0 16 16" preserveAspectRatio="none">
    <!-- 顶面 -->
    <polygon points="4,4 12,4 16,0 8,0"/>
    <!-- 正面 -->
    <polygon points="4,4 12,4 12,12 4,12"/>
    <!-- 右侧面 -->
    <polygon points="12,4 16,0 16,8 12,12"/>
  </symbol>

  <!-- 双括号 -->
  <symbol id="double-bracket" viewBox="0 0 16 16" preserveAspectRatio="none">
    <!-- 左括号 -->
    <path d="M5,4 Q3.5,4 3.5,6 L3.5,10 Q3.5,12 5,12" fill="none" stroke="currentColor" stroke-width="1"/>
    <!-- 右括号 -->
    <path d="M11,4 Q12.5,4 12.5,6 L12.5,10 Q12.5,12 11,12" fill="none" stroke="currentColor" stroke-width="1"/>
  </symbol>

  <!-- 双大括号 -->
  <symbol id="double-curly-bracket" viewBox="0 0 16 16" preserveAspectRatio="none">
    <!-- 左大括号 -->
    <path d="M6,2
             Q3,2 3,5
             L3,7
             Q3,8 2,8
             Q3,8 3,9
             L3,11
             Q3,14 6,14" fill="none" stroke="currentColor" stroke-width="1"/>
    <!-- 右大括号 -->
    <path d="M10,2
             Q13,2 13,5
             L13,7
             Q13,8 14,8
             Q13,8 13,9
             L13,11
             Q13,14 10,14" fill="none" stroke="currentColor" stroke-width="1"/>
  </symbol>

  <!-- 左中括号 -->
  <symbol id="left-square-bracket" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8,3 Q6,3 6,5 L6,11 Q6,13 8,13" fill="none" stroke="currentColor" stroke-width="1"/>
  </symbol>

  <!-- 右中括号 -->
  <symbol id="right-square-bracket" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8,3 Q10,3 10,5 L10,11 Q10,13 8,13" fill="none" stroke="currentColor" stroke-width="1"/>
  </symbol>

  <!-- 左大括号 -->
  <symbol id="left-curly-bracket" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8,2 Q5,2 5,5 L5,7 Q5,8 4,8 Q5,8 5,9 L5,11 Q5,14 8,14" fill="none" stroke="currentColor" stroke-width="1"/>
  </symbol>

  <!-- 右大括号 -->
  <symbol id="right-curly-bracket" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8,2 Q11,2 11,5 L11,7 Q11,8 12,8 Q11,8 11,9 L11,11 Q11,14 8,14" fill="none" stroke="currentColor" stroke-width="1"/>
  </symbol>

  <!-- 云朵 -->
  <symbol id="cloud" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path
      d="
        M4,12
        Q2.5,10 4.5,9
        Q3.5,7 6,7
        Q6,4 9,5
        Q10,2.5 12,5
        Q14,5.5 13.5,8
        Q16,9 14,12
        Q12,14 8,13.5
        Q4,14 4,12
        Z
      "
    />
  </symbol>

  <!-- 同心圆 -->
  <symbol id="concentric-circles" viewBox="0 0 16 16">
    <circle cx="8" cy="8" r="6.5" fill="none" stroke="currentColor" stroke-width="3"/>
  </symbol>

  <!-- 禁止符号 -->
  <symbol id="ban-sign" viewBox="0 0 16 16">
    <!-- 外圆环 -->
    <circle cx="8" cy="8" r="6.5" fill="none" stroke="currentColor" stroke-width="3"/>
    <!-- 斜矩形，长度足够长，两端与圆环相连 -->
    <rect x="2" y="7" width="12" height="2" rx="1" fill="currentColor" transform="rotate(45 8 8)" />
  </symbol>

  <!-- 图文框 -->
  <symbol id="frame" viewBox="0 0 16 16" preserveAspectRatio="none">
    <rect x="2" y="2" width="12" height="12" fill="none" stroke="currentColor" stroke-width="1"/>
  </symbol>

  <!-- 爱心 -->
  <symbol id="heart" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path
      d="
      M8,14
      C2,9 2,4.5 6,4
      C7.5,4 8,5.5 8,6
      C8,5.5 8.5,4 10,4
      C14,4.5 14,9 8,14
      Z
    "
    />
  </symbol>

  <!-- 箭头形状 -->
  <!-- 右箭头 -->
  <symbol id="arrow-right" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="0,5 9,5 9,0 16,8 9,16 9,11 0,11" />
  </symbol>

  <!-- 左箭头 -->
  <symbol id="arrow-left" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="16,5 7,5 7,0 0,8 7,16 7,11 16,11" />
  </symbol>

  <!-- 上箭头 -->
  <symbol id="arrow-up" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="8,0 16,8 12,8 12,16 4,16 4,8 0,8" />
  </symbol>

  <!-- 下箭头 -->
  <symbol id="arrow-down" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="8,16 0,8 4,8 4,0 12,0 12,8 16,8" />
  </symbol>

  <!-- 左右箭头 -->
  <symbol id="arrow-left-right" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="1,8 6,4.5 6,7 10,7 10,4.5 15,8 10,11.5 10,9 6,9 6,11.5" />
  </symbol>

  <!-- 上下箭头 -->
  <symbol id="arrow-up-down" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="8,1 11.5,6 9,6 9,10 11.5,10 8,15 4.5,10 7,10 7,6 4.5,6" />
  </symbol>

  <!-- 十字箭头 -->
  <symbol id="cross-arrow" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8 0 L10 3 H9 V7 H13 V6 L16 8 L13 10 V9 H9 V13 H10 L8 16 L6 13 H7 V9 H3 V10 L0 8 L3 6 V7 H7 V3 H6 Z"/>
  </symbol>

  <!-- 丁字箭头 -->
  <symbol id="T-arrow" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8 3 L11 7 H9 V10 H14 V8 L16 10.5 14 13 V11 H2 V13 L0 10.5 2 8 V10 H7 V7 H5 Z"/>
  </symbol>

  <!-- 圆角右箭头 -->
  <symbol id="arrow-right-rounded" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M3 14V7.5C3 4 5.5 2 8.5 2H11V0.7L14 3.7L11 6.7V5.5H8.5C6.5 5.5 5 7 5 9V14H3Z"/>
  </symbol>

  <!-- 五边形箭头 -->
  <symbol id="pentagon-arrow" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="2,2 8,2 14,8 8,14 2,14"/>
  </symbol>

  <!-- 燕尾形箭头 -->
  <symbol id="swallowtail-arrow" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="2,2 11,2 14,8 11,14 2,14 5,8"/>
  </symbol>

  <!-- 公式形状 -->
  <!-- 加号 -->
  <symbol id="math-plus" viewBox="0 0 16 16" preserveAspectRatio="none">
    <rect x="7" y="2" width="2" height="12"/>
    <rect x="2" y="7" width="12" height="2"/>
  </symbol>

  <!-- 减号 -->
  <symbol id="math-minus" viewBox="0 0 16 16" preserveAspectRatio="none">
    <rect x="2" y="7" width="12" height="2"/>
  </symbol>

  <!-- 乘号 -->
  <symbol id="math-multiply" viewBox="0 0 16 16" preserveAspectRatio="none">
    <rect x="7" y="2" width="2" height="12" transform="rotate(45 8 8)"/>
    <rect x="7" y="2" width="2" height="12" transform="rotate(-45 8 8)"/>
  </symbol>

  <!-- 除号 -->
  <symbol id="math-divide" viewBox="0 0 16 16" preserveAspectRatio="none">
    <rect x="2" y="7" width="12" height="2"/>
    <circle cx="8" cy="4" r="1.2"/>
    <circle cx="8" cy="12" r="1.2"/>
  </symbol>

  <!-- 等号 -->
  <symbol id="math-equal" viewBox="0 0 16 16" preserveAspectRatio="none">
    <rect x="2" y="5" width="12" height="2"/>
    <rect x="2" y="9" width="12" height="2"/>
  </symbol>

  <!-- 不等号 -->
  <symbol id="math-not-equal" viewBox="0 0 16 16" preserveAspectRatio="none">
    <rect x="2" y="5" width="12" height="2"/>
    <rect x="2" y="9" width="12" height="2"/>
    <rect x="8" y="3" width="2" height="12" transform="rotate(-30 5 9)"/>
  </symbol>

  <!-- 爆炸星 -->
  <symbol id="star-explode" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8 2 L9.2 5.2 L12.7 4.5 L10.5 7.1 L12.9 9.5 L9.5 9.2 L8.7 12.7 L8 9.5 L5.1 12.1 L6.5 8.8 L3.1 8.5 L6.1 6.7 L4.5 3.7 L7.5 5.5 Z"/>
  </symbol>

  <!-- 十字星 -->
  <symbol id="star-cross" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="8,1 9.2,6.8 15,8 9.2,9.2 8,15 6.8,9.2 1,8 6.8,6.8"/>
  </symbol>

  <!-- 五角星 -->
  <symbol id="star-five" viewBox="0 0 16 16" preserveAspectRatio="none">
    <polygon points="8,2 9.9021,6.1803 14.5106,6.9098 10.7553,10.0197 11.8042,14.5902 8,12.1 4.1958,14.5902 5.2447,10.0197 1.4894,6.9098 6.0979,6.1803"/>
  </symbol>

  <!-- 六角星 -->
  <symbol id="star-six" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M8 1 L12 12 L4 12 Z" />
    <path d="M8 15 L4 4 L12 4 Z" />
  </symbol>

  <!-- 八角星 -->
  <symbol id="star-eight" viewBox="0 0 16 16" preserveAspectRatio="none">
    <!-- 正常正方形 -->
    <rect x="3" y="3" width="10" height="10" />
    <!-- 旋转45度的正方形，中心点(8,8) -->
    <rect x="3" y="3" width="10" height="10" transform="rotate(45 8 8)" />
  </symbol>

  <!-- 波浪线旗帜形状 -->
  <symbol id="flag" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M2,2 Q5,5 8,2 Q11,-1 14,2 L14,14 Q11,11 8,14 Q5,17 2,14 Z" />
  </symbol>

  <!-- 标注 -->
  <!-- 矩形标注 -->
  <symbol id="rect-mark" viewBox="0 0 16 16" preserveAspectRatio="none">
    <rect x="3" y="3" width="10" height="8" rx="1"/>
    <polygon points="7,11 9,11 8,13"/>
  </symbol>

  <!-- 圆角矩形标注 -->
  <symbol id="rounded-rect-mark" viewBox="0 0 16 16" preserveAspectRatio="none">
    <rect x="3" y="3" width="10" height="8" rx="3"/>
    <polygon points="7,11 9,11 8,13"/>
  </symbol>

  <!-- 椭圆形标注 -->
  <symbol id="ellipse-mark" viewBox="0 0 16 16" preserveAspectRatio="none">
    <ellipse cx="8" cy="7" rx="5" ry="4"/>
    <polygon points="7,11 9,11 8,13"/>
  </symbol>

  <!-- 云朵标注 -->
  <symbol id="cloud-mark" viewBox="0 0 16 16" preserveAspectRatio="none">
    <path d="M5.5 12 Q4 11.5 4 10 Q2 10 2.5 8 Q2 6 4.5 6 Q5 4 8 4 Q11 4 11.5 6 Q14 6 13.5 8 Q14 10 12 10 Q12 11.5 10.5 12 Q9.5 13 8 12.5 Q6.5 13 5.5 12 Z"/>
    <polygon points="7,12 9,12 8,14"/>
  </symbol>

  <!-- 线性标注 -->
  <symbol id="linear-mark" viewBox="0 0 16 16" preserveAspectRatio="none">
    <line x1="2" y1="14" x2="6" y2="6" stroke="currentColor" stroke-width="0.2"/>
    <rect x="8" y="4" width="6" height="8"/>
  </symbol>
</svg>