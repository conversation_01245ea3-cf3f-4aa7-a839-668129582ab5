<svg width="150" height="150" viewBox="0 0 150 150" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3F76FF" />
      <stop offset="100%" stop-color="#4650E5" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="75" cy="75" r="70" fill="#4C8BF5" />
  
  <!-- 机器人面部 - 简单的女性风格 -->
  <circle cx="75" cy="70" r="40" fill="white" />
  
  <!-- 发型 - 简单的短发 -->
  <path d="M45 55C45 55 35 70 35 85C35 85 45 75 55 75C55 75 50 60 45 55Z" fill="white" />
  <path d="M105 55C105 55 115 70 115 85C115 85 105 75 95 75C95 75 100 60 105 55Z" fill="white" />
  
  <!-- 眼睛 - 大眼睛，更可爱 -->
  <ellipse cx="60" cy="65" rx="8" ry="10" fill="#4C8BF5" />
  <ellipse cx="90" cy="65" rx="8" ry="10" fill="#4C8BF5" />
  
  <!-- 瞳孔 - 大瞳孔 -->
  <circle cx="60" cy="63" r="4" fill="white" />
  <circle cx="90" cy="63" r="4" fill="white" />
  
  <!-- 腮红 -->
  <circle cx="50" cy="75" r="5" fill="#8BB5FF" opacity="0.5" />
  <circle cx="100" cy="75" r="5" fill="#8BB5FF" opacity="0.5" />
  
  <!-- 嘴巴 - 简单的笑容 -->
  <path d="M65 85C65 85 70 90 75 90C80 90 85 85 85 85" stroke="#4C8BF5" stroke-width="2" stroke-linecap="round" />
  
  <!-- 头饰 - 简单的蝴蝶结 -->
  <circle cx="75" cy="35" r="6" fill="white" />
  <path d="M69 35C69 35 65 30 60 35C55 40 60 45 65 45C70 45 72 40 72 40" fill="white" />
  <path d="M81 35C81 35 85 30 90 35C95 40 90 45 85 45C80 45 78 40 78 40" fill="white" />
  
  <!-- 简单的电子元素 - 柔和版 -->
  <circle cx="35" cy="100" r="3" fill="white" />
  <circle cx="115" cy="100" r="3" fill="white" />
  <line x1="35" y1="100" x2="48" y2="100" stroke="white" stroke-width="2" stroke-linecap="round" />
  <line x1="115" y1="100" x2="102" y2="100" stroke="white" stroke-width="2" stroke-linecap="round" />
  
  <!-- 底部装饰元素 -->
  <circle cx="75" cy="120" r="3" fill="white" />
  <line x1="75" y1="105" x2="75" y2="117" stroke="white" stroke-width="2" stroke-linecap="round" />
</svg> 