@font-face {
  font-family: "iconfont"; /* Project id 4823508 */
  src: url('iconfont.woff2?t=1739159067391') format('woff2'),
       url('iconfont.woff?t=1739159067391') format('woff'),
       url('iconfont.ttf?t=1739159067391') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.yss-knowledge:before {
  content: "\e628";
}

.yss-tubiao_qudao:before {
  content: "\e63a";
}

.yss-wenshuzuzhuang:before {
  content: "\e678";
}

.yss-shejiqi:before {
  content: "\e608";
}

.yss-zhishitupu:before {
  content: "\e615";
}

.yss-tongyongnengli:before {
  content: "\e68c";
}

.yss-agent:before {
  content: "\e768";
}

.yss-xiugai07:before {
  content: "\e738";
}

.yss-wenjianjia:before {
  content: "\e60c";
}

.yss-fix:before {
  content: "\e9b9";
}

.yss-shezhi:before {
  content: "\e61b";
}

.yss-user:before {
  content: "\e607";
}

.yss-huodongmulupeizhi:before {
  content: "\e66b";
}

.yss-lifangtilitiduomiantifangkuai2:before {
  content: "\e7fb";
}

.yss-xueyuan-mulu:before {
  content: "\e6c9";
}

