<template>
  <a-modal
    :open="visible"
    title="创建新项目"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirmLoading="loading"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
      class="mt-4"
    >
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="项目名称" name="name">
            <a-input 
              v-model:value="formData.name" 
              placeholder="请输入项目名称"
              :maxlength="50"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="项目类型" name="type">
            <a-select v-model:value="formData.type" placeholder="选择项目类型">
              <a-select-option value="web">Web应用</a-select-option>
              <a-select-option value="mobile">移动应用</a-select-option>
              <a-select-option value="desktop">桌面应用</a-select-option>
              <a-select-option value="api">API服务</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="项目状态" name="status">
            <a-select v-model:value="formData.status" placeholder="选择项目状态">
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="active">进行中</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="项目描述" name="description">
            <a-textarea 
              v-model:value="formData.description" 
              placeholder="请输入项目描述"
              :rows="4"
              :maxlength="500"
              show-count
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="项目负责人" name="owner">
            <a-input 
              v-model:value="formData.owner" 
              placeholder="请输入负责人姓名"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="团队成员" name="team">
            <a-select
              v-model:value="formData.team"
              mode="tags"
              placeholder="输入团队成员姓名"
              :maxTagCount="3"
            >
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="项目标签" name="tags">
            <a-select
              v-model:value="formData.tags"
              mode="tags"
              placeholder="添加项目标签"
              :maxTagCount="5"
            >
              <a-select-option value="AI">AI</a-select-option>
              <a-select-option value="前端">前端</a-select-option>
              <a-select-option value="后端">后端</a-select-option>
              <a-select-option value="移动端">移动端</a-select-option>
              <a-select-option value="数据分析">数据分析</a-select-option>
              <a-select-option value="机器学习">机器学习</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- AI辅助功能 -->
      <a-divider>AI辅助创建</a-divider>
      
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="项目描述（AI生成）">
            <a-textarea 
              v-model:value="aiPrompt" 
              placeholder="描述你想要创建的项目，AI将帮助你生成项目信息..."
              :rows="3"
            />
            <div class="mt-2">
              <a-button 
                type="primary" 
                ghost 
                size="small"
                @click="generateWithAI"
                :loading="aiLoading"
              >
                <RobotOutlined />
                AI生成项目信息
              </a-button>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { RobotOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'

interface Props {
  visible: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:visible', 'confirm'])

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const aiLoading = ref(false)
const aiPrompt = ref('')

const formData = reactive({
  name: '',
  description: '',
  type: 'web',
  status: 'draft',
  owner: '',
  team: [],
  tags: []
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '项目名称长度在2-50个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入项目描述', trigger: 'blur' },
    { min: 10, max: 500, message: '项目描述长度在10-500个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ],
  owner: [
    { required: true, message: '请输入项目负责人', trigger: 'blur' }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    name: '',
    description: '',
    type: 'web',
    status: 'draft',
    owner: '',
    team: [],
    tags: []
  })
  aiPrompt.value = ''
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('confirm', { ...formData })
    emit('update:visible', false)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
}

// AI生成项目信息
const generateWithAI = async () => {
  if (!aiPrompt.value.trim()) {
    return
  }
  
  aiLoading.value = true
  
  try {
    // 模拟AI生成
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟AI生成的结果
    const aiResult = generateMockAIResult(aiPrompt.value)
    
    Object.assign(formData, aiResult)
  } catch (error) {
    console.error('AI生成失败:', error)
  } finally {
    aiLoading.value = false
  }
}

// 模拟AI生成结果
const generateMockAIResult = (prompt: string) => {
  // 这里应该调用真实的AI API
  const keywords = prompt.toLowerCase()
  
  let result = {
    name: '',
    description: '',
    type: 'web' as const,
    tags: [] as string[]
  }
  
  if (keywords.includes('电商') || keywords.includes('购物')) {
    result.name = '电商平台系统'
    result.description = '基于现代技术栈的电商平台，包含商品管理、订单处理、用户管理、支付集成等核心功能'
    result.tags = ['电商', '支付', '用户管理']
  } else if (keywords.includes('客服') || keywords.includes('聊天')) {
    result.name = '智能客服系统'
    result.description = '基于AI的智能客服系统，支持自然语言处理、多轮对话、情感分析等功能'
    result.tags = ['AI', '客服', 'NLP']
  } else if (keywords.includes('数据') || keywords.includes('分析')) {
    result.name = '数据分析平台'
    result.description = '企业级数据分析平台，提供数据可视化、报表生成、预测分析等功能'
    result.tags = ['数据分析', '可视化', 'BI']
  } else {
    result.name = '智能应用系统'
    result.description = '基于用户需求定制的智能应用系统，提供高效、便捷的解决方案'
    result.tags = ['智能', '定制']
  }
  
  if (keywords.includes('移动') || keywords.includes('手机')) {
    result.type = 'mobile'
  }
  
  return result
}
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>
