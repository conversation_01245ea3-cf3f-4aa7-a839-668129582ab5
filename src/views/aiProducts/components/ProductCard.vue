<template>
  <div
    class="bg-area-fill border border-border hover:!bg-primary-light dark:hover:!bg-primary-hover hover:border-primary cursor-pointer hover:shadow-md transition-all relative group p-8"
  >
    <!-- 标题区域 -->
    <div class="flex items-start justify-between mb-3">
      <div class="flex items-start gap-2">
        <div>
          <h3
            class="text-base font-medium text-primary group-hover:text-white"
            >{{ product.name }}</h3
          >
          <p
            class="text-primary text-xs mt-0.5 group-hover:text-white"
            >{{ product.englishName }}</p
          >
        </div>
        <span
          v-if="product.status === '待发布'"
          class="px-1.5 py-0.5 text-xs rounded bg-orange-50 text-orange-500 group-hover:bg-white/20 group-hover:text-white"
        >
          {{ product.status }}
        </span>
        <span
          v-else-if="product.status === '已下架'"
          class="px-1.5 py-0.5 text-xs rounded bg-gray-50 text-gray-500 group-hover:bg-white/20 group-hover:text-white"
        >
          {{ product.status }}
        </span>
      </div>
      <div class="flex gap-1.5">
        <button
          v-for="tag in product.tags.slice(0, 2)"
          :key="tag"
          class="px-2 py-0.5 text-xs border border-border rounded-md group-hover:text-white group-hover:border-white text-custom"
        >
          {{ tag }}
        </button>
        <button
          v-if="product.tags.length > 2"
          class="px-2 py-0.5 text-xs border border-border rounded-md group-hover:text-white group-hover:border-white text-custom"
        >
          ...
        </button>
      </div>
    </div>

    <!-- 描述文本 -->
    <p
      class="text-text-secondary text-xs mb-4 line-clamp-3 group-hover:text-white"
      >{{ product.description }}</p
    >

    <!-- 底部操作区 -->
    <div class="flex items-center gap-2">
      <button @click="handleAction('edit')" class="text-primary group-hover:text-white">
        <EditOutlined />
      </button>
      <button @click="handleAction('undo')" class="text-primary group-hover:text-white">
        <UndoOutlined />
      </button>
      <button @click="handleAction('preview')" class="text-primary group-hover:text-white">
        <BugOutlined />
      </button>
      <button @click="handleAction('history')" class="text-primary group-hover:text-white">
        <HistoryOutlined />
      </button>
    </div>

    <!-- 前往预演按钮 -->
    <a
      :href="product.link"
      target="_blank"
      class="absolute bottom-4 right-4 px-3 bg-primary dark:!bg-[var(--btn-color)] py-1 text-xs text-white hover:text-white rounded transition-colors"
    >
      前往演示
    </a>
  </div>
</template>

<script setup lang="ts">
import { EditOutlined, UndoOutlined, BugOutlined, HistoryOutlined } from '@ant-design/icons-vue'

interface Product {
    id: number
    name: string
    englishName: string
    description: string
    status: '待发布' | '已下架'
    publishTime: string
    tags: string[]
}

const props = defineProps<{
    product: Product
}>()

const emit = defineEmits(['action'])

const handleAction = (type: 'edit' | 'undo' | 'preview' | 'history' | 'play') => {
    emit('action', { type, product: props.product })
}
</script>
