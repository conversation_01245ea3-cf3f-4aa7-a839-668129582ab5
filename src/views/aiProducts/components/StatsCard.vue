<template>
  <div class="stats-card bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6 hover:shadow-lg transition-all duration-300">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">{{ title }}</p>
        <div class="flex items-baseline gap-2">
          <h3 class="text-3xl font-bold text-gray-900 dark:text-white">{{ formattedValue }}</h3>
          <div 
            v-if="trend"
            :class="[
              'flex items-center gap-1 text-xs font-medium px-2 py-1 rounded-full',
              trend.isUp 
                ? 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30' 
                : 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/30'
            ]"
          >
            <component :is="trend.isUp ? 'ArrowUpOutlined' : 'ArrowDownOutlined'" />
            {{ trend.value }}%
          </div>
        </div>
      </div>
      
      <div 
        :class="[
          'w-12 h-12 rounded-xl flex items-center justify-center',
          colorClasses[color]
        ]"
      >
        <component :is="iconComponent" class="text-xl text-white" />
      </div>
    </div>
    
    <!-- 进度条 -->
    <div v-if="progress !== undefined" class="mt-4">
      <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
        <span>完成度</span>
        <span>{{ progress }}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div 
          :class="[
            'h-2 rounded-full transition-all duration-500',
            progressColorClasses[color]
          ]"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  ProjectOutlined,
  PlayCircleOutlined,
  DesktopOutlined,
  FileTextOutlined,
  TeamOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons-vue'

interface Props {
  title: string
  value: number
  icon: string
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red'
  trend?: {
    value: number
    isUp: boolean
  }
  progress?: number
}

const props = defineProps<Props>()

// 图标映射
const iconMap = {
  ProjectOutlined,
  PlayCircleOutlined,
  DesktopOutlined,
  FileTextOutlined,
  TeamOutlined
}

// 颜色样式映射
const colorClasses = {
  blue: 'bg-blue-500',
  green: 'bg-green-500',
  purple: 'bg-purple-500',
  orange: 'bg-orange-500',
  red: 'bg-red-500'
}

const progressColorClasses = {
  blue: 'bg-blue-400',
  green: 'bg-green-400',
  purple: 'bg-purple-400',
  orange: 'bg-orange-400',
  red: 'bg-red-400'
}

// 计算属性
const iconComponent = computed(() => iconMap[props.icon] || ProjectOutlined)

const formattedValue = computed(() => {
  if (props.value >= 1000) {
    return (props.value / 1000).toFixed(1) + 'k'
  }
  return props.value.toString()
})
</script>

<style scoped>
.stats-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>
