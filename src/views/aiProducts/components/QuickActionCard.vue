<template>
  <div 
    class="quick-action-card bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6 cursor-pointer hover:shadow-lg transition-all duration-300 group"
    @click="$emit('click')"
  >
    <div class="flex items-start gap-4">
      <!-- 图标 -->
      <div 
        :class="[
          'w-14 h-14 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110',
          colorClasses[color]
        ]"
      >
        <component :is="iconComponent" class="text-2xl text-white" />
      </div>
      
      <!-- 内容 -->
      <div class="flex-1">
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          {{ title }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
          {{ description }}
        </p>
        
        <!-- 功能特性 -->
        <div v-if="features" class="mt-3 flex flex-wrap gap-2">
          <span 
            v-for="feature in features" 
            :key="feature"
            class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-md"
          >
            {{ feature }}
          </span>
        </div>
      </div>
      
      <!-- 箭头 -->
      <div class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 transition-all duration-300">
        <RightOutlined class="text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" />
      </div>
    </div>
    
    <!-- 底部统计信息 -->
    <div v-if="stats" class="mt-4 pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
      <div class="grid grid-cols-3 gap-4 text-center">
        <div v-for="stat in stats" :key="stat.label">
          <div class="text-lg font-bold text-gray-900 dark:text-white">{{ stat.value }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">{{ stat.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  ProjectOutlined,
  DesktopOutlined,
  FileTextOutlined,
  TeamOutlined,
  RightOutlined,
  BulbOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

interface Props {
  title: string
  description: string
  icon: string
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red'
  features?: string[]
  stats?: Array<{
    label: string
    value: string | number
  }>
}

const props = defineProps<Props>()
const emit = defineEmits(['click'])

// 图标映射
const iconMap = {
  ProjectOutlined,
  DesktopOutlined,
  FileTextOutlined,
  TeamOutlined,
  BulbOutlined,
  SettingOutlined
}

// 颜色样式映射
const colorClasses = {
  blue: 'bg-blue-500',
  green: 'bg-green-500',
  purple: 'bg-purple-500',
  orange: 'bg-orange-500',
  red: 'bg-red-500'
}

// 计算属性
const iconComponent = computed(() => iconMap[props.icon] || ProjectOutlined)
</script>

<style scoped>
.quick-action-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.quick-action-card:active {
  transform: translateY(-2px);
}
</style>
