<template>
  <div class="project-workspace">
    <!-- 项目统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-area-fill border border-border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-text-secondary text-sm">总项目数</p>
            <p class="text-2xl font-bold text-text-primary">{{ projects.length }}</p>
          </div>
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <ProjectOutlined class="text-blue-600" />
          </div>
        </div>
      </div>
      
      <div class="bg-area-fill border border-border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-text-secondary text-sm">进行中</p>
            <p class="text-2xl font-bold text-text-primary">{{ activeProjects }}</p>
          </div>
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <PlayCircleOutlined class="text-green-600" />
          </div>
        </div>
      </div>
      
      <div class="bg-area-fill border border-border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-text-secondary text-sm">草稿</p>
            <p class="text-2xl font-bold text-text-primary">{{ draftProjects }}</p>
          </div>
          <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
            <EditOutlined class="text-yellow-600" />
          </div>
        </div>
      </div>
      
      <div class="bg-area-fill border border-border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-text-secondary text-sm">已归档</p>
            <p class="text-2xl font-bold text-text-primary">{{ archivedProjects }}</p>
          </div>
          <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <InboxOutlined class="text-gray-600" />
          </div>
        </div>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="bg-area-fill border border-border rounded-lg">
      <div class="p-4 border-b border-border">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-text-primary">项目列表</h2>
          <div class="flex items-center gap-2">
            <a-select v-model:value="statusFilter" placeholder="状态筛选" style="width: 120px" allowClear>
              <a-select-option value="active">进行中</a-select-option>
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="archived">已归档</a-select-option>
            </a-select>
            <a-select v-model:value="typeFilter" placeholder="类型筛选" style="width: 120px" allowClear>
              <a-select-option value="web">Web应用</a-select-option>
              <a-select-option value="mobile">移动应用</a-select-option>
              <a-select-option value="desktop">桌面应用</a-select-option>
              <a-select-option value="api">API服务</a-select-option>
            </a-select>
          </div>
        </div>
      </div>
      
      <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ProjectCard
            v-for="project in filteredProjects"
            :key="project.id"
            :project="project"
            @edit="$emit('edit-project', project)"
            @delete="$emit('delete-project', project.id)"
            @view-details="handleViewDetails"
          />
        </div>
        
        <!-- 空状态 -->
        <div v-if="filteredProjects.length === 0" class="text-center py-12">
          <InboxOutlined class="text-4xl text-text-secondary mb-4" />
          <p class="text-text-secondary">暂无项目</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { 
  ProjectOutlined, 
  PlayCircleOutlined, 
  EditOutlined, 
  InboxOutlined 
} from '@ant-design/icons-vue'
import ProjectCard from './ProjectCard.vue'
import type { Project } from '../config/projects'

interface Props {
  projects: Project[]
}

const props = defineProps<Props>()
const emit = defineEmits(['create-project', 'edit-project', 'delete-project'])

// 筛选条件
const statusFilter = ref<string>()
const typeFilter = ref<string>()

// 计算属性
const activeProjects = computed(() => 
  props.projects.filter(p => p.status === 'active').length
)

const draftProjects = computed(() => 
  props.projects.filter(p => p.status === 'draft').length
)

const archivedProjects = computed(() => 
  props.projects.filter(p => p.status === 'archived').length
)

const filteredProjects = computed(() => {
  let filtered = props.projects
  
  if (statusFilter.value) {
    filtered = filtered.filter(p => p.status === statusFilter.value)
  }
  
  if (typeFilter.value) {
    filtered = filtered.filter(p => p.type === typeFilter.value)
  }
  
  return filtered
})

// 事件处理
const handleViewDetails = (project: Project) => {
  console.log('查看项目详情:', project)
  // 这里可以跳转到项目详情页面
}
</script>

<style scoped>
.project-workspace {
  /* 自定义样式 */
}
</style>
