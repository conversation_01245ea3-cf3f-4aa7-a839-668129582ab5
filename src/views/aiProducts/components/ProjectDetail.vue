<template>
  <div class="project-detail">
    <!-- 项目头部信息 -->
    <div class="bg-area-fill border border-border rounded-lg p-6 mb-6">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center gap-3 mb-2">
            <h1 class="text-2xl font-bold text-text-primary">{{ project.name }}</h1>
            <span 
              :class="[
                'px-3 py-1 text-sm rounded-full',
                statusStyles[project.status]
              ]"
            >
              {{ statusLabels[project.status] }}
            </span>
            <span 
              :class="[
                'px-3 py-1 text-sm rounded',
                typeStyles[project.type]
              ]"
            >
              {{ typeLabels[project.type] }}
            </span>
          </div>
          <p class="text-text-secondary mb-4">{{ project.description }}</p>
          
          <!-- 项目信息 -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <div class="text-sm text-text-secondary">负责人</div>
              <div class="font-medium text-text-primary">{{ project.owner }}</div>
            </div>
            <div>
              <div class="text-sm text-text-secondary">团队成员</div>
              <div class="font-medium text-text-primary">{{ project.team.length }}人</div>
            </div>
            <div>
              <div class="text-sm text-text-secondary">创建时间</div>
              <div class="font-medium text-text-primary">{{ formatDate(project.createdAt) }}</div>
            </div>
            <div>
              <div class="text-sm text-text-secondary">最后更新</div>
              <div class="font-medium text-text-primary">{{ formatDate(project.updatedAt) }}</div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex items-center gap-2">
          <a-button @click="$emit('edit-project')">
            <EditOutlined />
            编辑项目
          </a-button>
          <a-dropdown>
            <a-button>
              <MoreOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleArchive">
                  <InboxOutlined class="mr-2" />
                  归档项目
                </a-menu-item>
                <a-menu-item @click="handleExport">
                  <ExportOutlined class="mr-2" />
                  导出项目
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item @click="handleDelete" class="text-red-500">
                  <DeleteOutlined class="mr-2" />
                  删除项目
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
      
      <!-- 进度条 -->
      <div class="mt-4">
        <div class="flex items-center justify-between text-sm mb-2">
          <span class="text-text-secondary">项目进度</span>
          <span class="text-text-primary font-medium">{{ project.progress }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3">
          <div 
            class="bg-primary h-3 rounded-full transition-all"
            :style="{ width: `${project.progress}%` }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 标签页内容 -->
    <a-tabs v-model:activeKey="activeTab" class="project-tabs">
      <!-- 概览 -->
      <a-tab-pane key="overview" tab="概览">
        <ProjectOverview :project="project" />
      </a-tab-pane>
      
      <!-- 原型设计 -->
      <a-tab-pane key="prototypes" tab="原型设计">
        <template #tab>
          <span>
            <DesktopOutlined />
            原型设计 ({{ project.prototypes.length }})
          </span>
        </template>
        <PrototypeManager 
          :prototypes="project.prototypes"
          :project-id="project.id"
          @create-prototype="handleCreatePrototype"
          @edit-prototype="handleEditPrototype"
        />
      </a-tab-pane>
      
      <!-- 文档管理 -->
      <a-tab-pane key="documents" tab="文档管理">
        <template #tab>
          <span>
            <FileTextOutlined />
            文档管理 ({{ project.documents.length }})
          </span>
        </template>
        <DocumentManager 
          :documents="project.documents"
          :project-id="project.id"
          @create-document="handleCreateDocument"
          @edit-document="handleEditDocument"
        />
      </a-tab-pane>
      
      <!-- 需求管理 -->
      <a-tab-pane key="requirements" tab="需求管理">
        <template #tab>
          <span>
            <UnorderedListOutlined />
            需求管理 ({{ project.requirements.length }})
          </span>
        </template>
        <RequirementManager 
          :requirements="project.requirements"
          :project-id="project.id"
          @create-requirement="handleCreateRequirement"
          @edit-requirement="handleEditRequirement"
        />
      </a-tab-pane>
      
      <!-- 团队协作 -->
      <a-tab-pane key="collaboration" tab="团队协作">
        <template #tab>
          <span>
            <TeamOutlined />
            团队协作
          </span>
        </template>
        <TeamCollaboration 
          :project="project"
          @invite-member="handleInviteMember"
          @remove-member="handleRemoveMember"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  EditOutlined, 
  MoreOutlined, 
  InboxOutlined, 
  ExportOutlined, 
  DeleteOutlined,
  DesktopOutlined,
  FileTextOutlined,
  UnorderedListOutlined,
  TeamOutlined
} from '@ant-design/icons-vue'
import type { Project } from '../config/projects'

// 导入子组件（这些组件需要创建）
import ProjectOverview from './ProjectOverview.vue'
import PrototypeManager from './PrototypeManager.vue'
import DocumentManager from './DocumentManager.vue'
import RequirementManager from './RequirementManager.vue'
import TeamCollaboration from './TeamCollaboration.vue'

interface Props {
  project: Project
}

const props = defineProps<Props>()
const emit = defineEmits([
  'edit-project', 
  'archive-project', 
  'delete-project',
  'create-prototype',
  'edit-prototype',
  'create-document',
  'edit-document',
  'create-requirement',
  'edit-requirement',
  'invite-member',
  'remove-member'
])

const activeTab = ref('overview')

// 样式映射
const statusStyles = {
  active: 'bg-green-100 text-green-800',
  draft: 'bg-yellow-100 text-yellow-800',
  archived: 'bg-gray-100 text-gray-800'
}

const statusLabels = {
  active: '进行中',
  draft: '草稿',
  archived: '已归档'
}

const typeStyles = {
  web: 'bg-blue-100 text-blue-800',
  mobile: 'bg-purple-100 text-purple-800',
  desktop: 'bg-indigo-100 text-indigo-800',
  api: 'bg-orange-100 text-orange-800'
}

const typeLabels = {
  web: 'Web应用',
  mobile: '移动应用',
  desktop: '桌面应用',
  api: 'API服务'
}

// 工具函数
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 事件处理
const handleArchive = () => {
  emit('archive-project', props.project.id)
}

const handleExport = () => {
  console.log('导出项目:', props.project)
}

const handleDelete = () => {
  emit('delete-project', props.project.id)
}

const handleCreatePrototype = (data: any) => {
  emit('create-prototype', data)
}

const handleEditPrototype = (prototype: any) => {
  emit('edit-prototype', prototype)
}

const handleCreateDocument = (data: any) => {
  emit('create-document', data)
}

const handleEditDocument = (document: any) => {
  emit('edit-document', document)
}

const handleCreateRequirement = (data: any) => {
  emit('create-requirement', data)
}

const handleEditRequirement = (requirement: any) => {
  emit('edit-requirement', requirement)
}

const handleInviteMember = (memberData: any) => {
  emit('invite-member', memberData)
}

const handleRemoveMember = (memberId: string) => {
  emit('remove-member', memberId)
}
</script>

<style scoped>
.project-detail {
  /* 自定义样式 */
}

.project-tabs :deep(.ant-tabs-content-holder) {
  padding-top: 16px;
}
</style>
