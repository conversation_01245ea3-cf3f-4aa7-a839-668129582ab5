<template>
  <div
    class="project-card bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover:shadow-xl transition-all duration-300 cursor-pointer group overflow-hidden"
    @click="$emit('click')"
  >
    <!-- 顶部渐变条 -->
    <div :class="['h-1', gradientClasses[project.type]]"></div>

    <!-- 卡片内容 -->
    <div class="p-6">
      <!-- 头部信息 -->
      <div class="flex items-start justify-between mb-4">
        <div class="flex items-start gap-3 flex-1">
          <!-- 项目图标 -->
          <div
            :class="[
              'w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0',
              iconBgClasses[project.type]
            ]"
          >
            <component :is="typeIcons[project.type]" class="text-white text-lg" />
          </div>

          <!-- 项目信息 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-1">
              <h3 class="text-lg font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate">
                {{ project.name }}
              </h3>
              <span
                :class="[
                  'px-2 py-1 text-xs rounded-full font-medium flex-shrink-0',
                  statusStyles[project.status]
                ]"
              >
                {{ statusLabels[project.status] }}
              </span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 leading-relaxed">
              {{ project.description }}
            </p>
          </div>
        </div>

        <!-- 操作菜单 -->
        <a-dropdown trigger="click" placement="bottomRight">
          <button
            class="w-8 h-8 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-center transition-colors opacity-0 group-hover:opacity-100"
            @click.stop
          >
            <MoreOutlined class="text-gray-400" />
          </button>
          <template #overlay>
            <a-menu class="min-w-[160px]">
              <a-menu-item @click.stop="$emit('edit', project)">
                <EditOutlined class="mr-2" />
                编辑项目
              </a-menu-item>
              <a-menu-item @click.stop="$emit('view-details', project)">
                <EyeOutlined class="mr-2" />
                查看详情
              </a-menu-item>
              <a-menu-item @click.stop="handlePrototype">
                <DesktopOutlined class="mr-2" />
                原型设计
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item @click.stop="$emit('delete', project.id)" class="text-red-500">
                <DeleteOutlined class="mr-2" />
                删除项目
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>

      <!-- 项目标签 -->
      <div class="flex items-center gap-2 mb-4">
        <span
          :class="[
            'px-2 py-1 text-xs rounded-md font-medium',
            typeTagStyles[project.type]
          ]"
        >
          {{ typeLabels[project.type] }}
        </span>
        <span
          v-for="tag in project.tags.slice(0, 2)"
          :key="tag"
          class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-md"
        >
          {{ tag }}
        </span>
        <span
          v-if="project.tags.length > 2"
          class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-md"
        >
          +{{ project.tags.length - 2 }}
        </span>
      </div>

      <!-- 进度条 -->
      <div class="mb-4">
        <div class="flex items-center justify-between text-sm mb-2">
          <span class="text-gray-600 dark:text-gray-400 font-medium">项目进度</span>
          <span class="text-gray-900 dark:text-white font-bold">{{ project.progress }}%</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
          <div
            :class="[
              'h-2.5 rounded-full transition-all duration-500',
              progressClasses[project.type]
            ]"
            :style="{ width: `${project.progress}%` }"
          ></div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="grid grid-cols-3 gap-4 mb-4">
        <div class="text-center">
          <div class="text-lg font-bold text-gray-900 dark:text-white">{{ project.prototypes?.length || 0 }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">原型</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-bold text-gray-900 dark:text-white">{{ project.documents?.length || 0 }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">文档</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-bold text-gray-900 dark:text-white">{{ project.requirements?.length || 0 }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">需求</div>
        </div>
      </div>

      <!-- 团队和时间信息 -->
      <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
        <div class="flex items-center gap-2">
          <div class="flex -space-x-1">
            <div
              v-for="(member, index) in project.team.slice(0, 3)"
              :key="member"
              class="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-medium border-2 border-white dark:border-gray-800"
              :style="{ zIndex: 10 - index }"
            >
              {{ member.charAt(0) }}
            </div>
            <div
              v-if="project.team.length > 3"
              class="w-6 h-6 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 text-xs font-medium border-2 border-white dark:border-gray-800"
            >
              +{{ project.team.length - 3 }}
            </div>
          </div>
          <span class="text-xs">{{ project.owner }}</span>
        </div>
        <div class="flex items-center gap-1">
          <ClockCircleOutlined class="text-xs" />
          <span class="text-xs">{{ formatDate(project.updatedAt) }}</span>
        </div>
      </div>

      <!-- 快速操作按钮 -->
      <div class="flex items-center gap-2">
        <button
          @click.stop="handlePrototype"
          class="flex-1 px-3 py-2 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-all duration-200 flex items-center justify-center gap-1 font-medium"
        >
          <DesktopOutlined />
          原型
        </button>
        <button
          @click.stop="handleDocument"
          class="flex-1 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-all duration-200 flex items-center justify-center gap-1 font-medium"
        >
          <FileTextOutlined />
          文档
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  MoreOutlined,
  EditOutlined,
  EyeOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  DesktopOutlined,
  FileTextOutlined,
  GlobalOutlined,
  MobileOutlined,
  LaptopOutlined,
  ApiOutlined
} from '@ant-design/icons-vue'
import type { Project } from '../config/projects'

interface Props {
  project: Project
}

const props = defineProps<Props>()
const emit = defineEmits(['edit', 'delete', 'view-details', 'click'])

// 类型图标映射
const typeIcons = {
  web: GlobalOutlined,
  mobile: MobileOutlined,
  desktop: LaptopOutlined,
  api: ApiOutlined
}

// 渐变样式映射
const gradientClasses = {
  web: 'bg-gradient-to-r from-blue-500 to-blue-600',
  mobile: 'bg-gradient-to-r from-purple-500 to-purple-600',
  desktop: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
  api: 'bg-gradient-to-r from-orange-500 to-orange-600'
}

// 图标背景样式
const iconBgClasses = {
  web: 'bg-blue-500',
  mobile: 'bg-purple-500',
  desktop: 'bg-indigo-500',
  api: 'bg-orange-500'
}

// 状态样式映射
const statusStyles = {
  active: 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400',
  draft: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400',
  archived: 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
}

const statusLabels = {
  active: '进行中',
  draft: '草稿',
  archived: '已归档'
}

// 类型标签样式
const typeTagStyles = {
  web: 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400',
  mobile: 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400',
  desktop: 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-400',
  api: 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400'
}

const typeLabels = {
  web: 'Web应用',
  mobile: '移动应用',
  desktop: '桌面应用',
  api: 'API服务'
}

// 进度条样式
const progressClasses = {
  web: 'bg-gradient-to-r from-blue-400 to-blue-500',
  mobile: 'bg-gradient-to-r from-purple-400 to-purple-500',
  desktop: 'bg-gradient-to-r from-indigo-400 to-indigo-500',
  api: 'bg-gradient-to-r from-orange-400 to-orange-500'
}

// 工具函数
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
}

// 事件处理
const handlePrototype = () => {
  console.log('打开原型设计:', props.project)
  // 跳转到原型设计页面
}

const handleDocument = () => {
  console.log('打开文档管理:', props.project)
  // 跳转到文档管理页面
}
</script>

<style scoped>
.project-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.project-card:active {
  transform: translateY(-4px);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 团队头像动画 */
.project-card:hover .team-avatar {
  transform: scale(1.1);
}

/* 按钮悬停效果 */
.project-card button {
  transition: all 0.2s ease;
}

.project-card button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
