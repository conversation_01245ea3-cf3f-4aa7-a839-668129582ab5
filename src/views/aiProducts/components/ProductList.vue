<template>
    <YssTable :columns="columns" :data="products" :show-pagination="false" class="product-list-table">
        <template #bodyCell="{ column, record }">
            <!-- 名称列 -->
            <template v-if="column.key === 'name'">
                <div class="flex items-center gap-3">
                    <div>
                        <h3 class="text-lg font-medium text-text-primary">{{ record.name }}</h3>
                        <p class="text-primary text-sm">{{ record.englishName }}</p>
                    </div>
                    <span v-if="record.status === '待发布'" 
                        class="px-2 py-1 text-xs rounded bg-orange-50 text-orange-500">
                        {{ record.status }}
                    </span>
                    <span v-else-if="record.status === '已下架'" 
                        class="px-2 py-1 text-xs rounded bg-gray-50 text-gray-500">
                        {{ record.status }}
                    </span>
                </div>
            </template>

            <!-- 描述列 -->
            <template v-else-if="column.key === 'description'">
                <p class="text-text-secondary text-sm line-clamp-1">{{ record.description }}</p>
            </template>

            <!-- 标签列 -->
            <template v-else-if="column.key === 'tags'">
                <div class="flex gap-2">
                    <span v-for="tag in record.tags.slice(0, 2)" :key="tag"
                        class="px-3 py-0.5 text-xs border border-border rounded-md">
                        {{ tag }}
                    </span>
                    <span v-if="record.tags.length > 2" 
                        class="px-3 py-0.5 text-xs border border-border rounded-md">
                        ...
                    </span>
                </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'actions'">
                <a :href="record.link"
                   target="_blank"
                   class="text-primary hover:text-primary-hover dark:!text-[var(--grey-auxiliary-color)] text-sm cursor-pointer">
                    前往演示
                </a>
            </template>
        </template>
    </YssTable>
</template>

<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue'
import { YssTable } from '@yss-design/ui'

interface Product {
    id: number
    name: string
    englishName: string
    description: string
    status: '待发布' | '已下架'
    publishTime: string
    tags: string[]
}

const columns: TableColumnsType = [
    {
        title: '产品名称',
        key: 'name',
        width: '35%'
    },
    {
        title: '产品描述',
        key: 'description',
        width: '35%'
    },
    {
        title: '标签',
        key: 'tags',
        width: '20%'
    },
    {
        title: '操作',
        key: 'actions',
        width: '10%'
    }
]

const props = defineProps<{
    products: Product[]
}>()

const emit = defineEmits(['action'])

const handleAction = (type: 'edit' | 'undo' | 'preview' | 'history' | 'play', product: Product) => {
    emit('action', { type, product })
}
</script>

<style scoped>
.product-list-table :deep(.ant-table-cell) {
    vertical-align: middle;
}
</style>