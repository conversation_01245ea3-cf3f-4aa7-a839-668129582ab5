<template>
  <div class="bg-bg-light rounded-md p-3">
    <!-- 卡片标题 -->
    <h3 class="text-[#64748b] text-sm mb-4">{{ cardName }}</h3>

    <!-- 可点击的标题链接 -->
    <div class="group cursor-pointer mb-1.5">
      <h2 class="text-primary-light text-base font-medium dark:!text-primary">{{ title }}</h2>
    </div>

    <!-- 占位文本提示 -->
    <p class="text-xs text-[#94a3b8]">
      {{ content || '点击，可以写一些说明性文本...' }}
    </p>
  </div>
</template>

<script setup lang="ts">
defineProps<{
    cardName: string
    title: string
    content?: string
}>()
</script>

<style scoped>
.group:hover h2 {
    text-decoration: underline;
}
</style>
