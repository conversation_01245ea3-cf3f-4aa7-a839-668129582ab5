<template>
  <div class="project-table">
    <a-table
      :columns="columns"
      :data-source="projects"
      :pagination="paginationConfig"
      :scroll="{ x: 1200 }"
      row-key="id"
      class="modern-table"
    >
      <!-- 项目名称列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <div class="flex items-center gap-3 cursor-pointer" @click="$emit('view', record.id)">
            <div 
              :class="[
                'w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0',
                iconBgClasses[record.type]
              ]"
            >
              <component :is="typeIcons[record.type]" class="text-white" />
            </div>
            <div>
              <div class="font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                {{ record.name }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-[200px]">
                {{ record.description }}
              </div>
            </div>
          </div>
        </template>

        <!-- 状态列 -->
        <template v-else-if="column.key === 'status'">
          <span 
            :class="[
              'px-3 py-1 text-xs rounded-full font-medium',
              statusStyles[record.status]
            ]"
          >
            {{ statusLabels[record.status] }}
          </span>
        </template>

        <!-- 类型列 -->
        <template v-else-if="column.key === 'type'">
          <span 
            :class="[
              'px-2 py-1 text-xs rounded-md font-medium',
              typeTagStyles[record.type]
            ]"
          >
            {{ typeLabels[record.type] }}
          </span>
        </template>

        <!-- 进度列 -->
        <template v-else-if="column.key === 'progress'">
          <div class="flex items-center gap-3">
            <div class="flex-1">
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  :class="[
                    'h-2 rounded-full transition-all duration-500',
                    progressClasses[record.type]
                  ]"
                  :style="{ width: `${record.progress}%` }"
                ></div>
              </div>
            </div>
            <span class="text-sm font-medium text-gray-900 dark:text-white min-w-[40px]">
              {{ record.progress }}%
            </span>
          </div>
        </template>

        <!-- 团队列 -->
        <template v-else-if="column.key === 'team'">
          <div class="flex items-center gap-2">
            <div class="flex -space-x-1">
              <div
                v-for="(member, index) in record.team.slice(0, 3)"
                :key="member"
                class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-medium border-2 border-white dark:border-gray-800"
                :style="{ zIndex: 10 - index }"
                :title="member"
              >
                {{ member.charAt(0) }}
              </div>
              <div 
                v-if="record.team.length > 3"
                class="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 text-xs font-medium border-2 border-white dark:border-gray-800"
                :title="`还有 ${record.team.length - 3} 个成员`"
              >
                +{{ record.team.length - 3 }}
              </div>
            </div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ record.owner }}
            </span>
          </div>
        </template>

        <!-- 统计列 -->
        <template v-else-if="column.key === 'stats'">
          <div class="flex items-center gap-4 text-sm">
            <div class="flex items-center gap-1">
              <DesktopOutlined class="text-blue-500" />
              <span>{{ record.prototypes?.length || 0 }}</span>
            </div>
            <div class="flex items-center gap-1">
              <FileTextOutlined class="text-green-500" />
              <span>{{ record.documents?.length || 0 }}</span>
            </div>
            <div class="flex items-center gap-1">
              <UnorderedListOutlined class="text-orange-500" />
              <span>{{ record.requirements?.length || 0 }}</span>
            </div>
          </div>
        </template>

        <!-- 更新时间列 -->
        <template v-else-if="column.key === 'updatedAt'">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ formatDate(record.updatedAt) }}
          </span>
        </template>

        <!-- 操作列 -->
        <template v-else-if="column.key === 'actions'">
          <div class="flex items-center gap-2">
            <a-tooltip title="查看详情">
              <a-button 
                type="text" 
                size="small"
                @click="$emit('view', record.id)"
              >
                <EyeOutlined />
              </a-button>
            </a-tooltip>
            
            <a-tooltip title="编辑项目">
              <a-button 
                type="text" 
                size="small"
                @click="$emit('edit', record)"
              >
                <EditOutlined />
              </a-button>
            </a-tooltip>

            <a-dropdown trigger="click" placement="bottomRight">
              <a-button type="text" size="small">
                <MoreOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="handlePrototype(record)">
                    <DesktopOutlined class="mr-2" />
                    原型设计
                  </a-menu-item>
                  <a-menu-item @click="handleDocument(record)">
                    <FileTextOutlined class="mr-2" />
                    文档管理
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="$emit('delete', record.id)" class="text-red-500">
                    <DeleteOutlined class="mr-2" />
                    删除项目
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  EyeOutlined,
  EditOutlined,
  MoreOutlined,
  DeleteOutlined,
  DesktopOutlined,
  FileTextOutlined,
  UnorderedListOutlined,
  GlobalOutlined,
  MobileOutlined,
  LaptopOutlined,
  ApiOutlined
} from '@ant-design/icons-vue'
import type { Project } from '../config/projects'

interface Props {
  projects: Project[]
}

const props = defineProps<Props>()
const emit = defineEmits(['edit', 'delete', 'view'])

// 表格列配置
const columns = [
  {
    title: '项目名称',
    key: 'name',
    width: 300,
    fixed: 'left'
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '进度',
    key: 'progress',
    width: 150
  },
  {
    title: '团队',
    key: 'team',
    width: 200
  },
  {
    title: '统计',
    key: 'stats',
    width: 120
  },
  {
    title: '更新时间',
    key: 'updatedAt',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
}

// 类型图标映射
const typeIcons = {
  web: GlobalOutlined,
  mobile: MobileOutlined,
  desktop: LaptopOutlined,
  api: ApiOutlined
}

// 图标背景样式
const iconBgClasses = {
  web: 'bg-blue-500',
  mobile: 'bg-purple-500',
  desktop: 'bg-indigo-500',
  api: 'bg-orange-500'
}

// 状态样式映射
const statusStyles = {
  active: 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400',
  draft: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400',
  archived: 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
}

const statusLabels = {
  active: '进行中',
  draft: '草稿',
  archived: '已归档'
}

// 类型标签样式
const typeTagStyles = {
  web: 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400',
  mobile: 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400',
  desktop: 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-400',
  api: 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400'
}

const typeLabels = {
  web: 'Web应用',
  mobile: '移动应用',
  desktop: '桌面应用',
  api: 'API服务'
}

// 进度条样式
const progressClasses = {
  web: 'bg-blue-400',
  mobile: 'bg-purple-400',
  desktop: 'bg-indigo-400',
  api: 'bg-orange-400'
}

// 工具函数
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
}

// 事件处理
const handlePrototype = (project: Project) => {
  console.log('打开原型设计:', project)
}

const handleDocument = (project: Project) => {
  console.log('打开文档管理:', project)
}
</script>

<style scoped>
.modern-table :deep(.ant-table) {
  background: transparent;
}

.modern-table :deep(.ant-table-thead > tr > th) {
  background: rgba(249, 250, 251, 0.8);
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  font-weight: 600;
}

.modern-table :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
}

.modern-table :deep(.ant-table-tbody > tr:hover > td) {
  background: rgba(59, 130, 246, 0.05);
}

.dark .modern-table :deep(.ant-table-thead > tr > th) {
  background: rgba(31, 41, 55, 0.8);
  border-bottom: 1px solid rgba(75, 85, 99, 0.5);
  color: rgba(243, 244, 246, 1);
}

.dark .modern-table :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
  color: rgba(243, 244, 246, 1);
}

.dark .modern-table :deep(.ant-table-tbody > tr:hover > td) {
  background: rgba(59, 130, 246, 0.1);
}
</style>
