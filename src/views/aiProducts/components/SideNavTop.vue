<template>
  <div class="w-[220px]  bg-area-fill border-border pl-8 pr-8 relative flex flex-col">
    <div class="h-[320px] p-3">
      <GoBookmarkFill
        class="absolute right-2 top-0 text-primary-light"
        size="24px"
      ></GoBookmarkFill>
      <div class="mb-4 mt-4 flex flex-row items-center gap-2">
        <div class="text-base font-bold text-text-primary">产品开发工作台</div>
        <div class="text-base text-[#d2dae4]">{{ totalProjects }}</div>
      </div>
      <!-- 快速操作 -->
      <div class="mb-4">
        <div class="mb-4">
          <button
            @click="$emit('create-project')"
            class="w-full h-[60px] bg-primary-light dark:!bg-[var(--top-menu-fill-color)] dark:!border dark:!border-border text-[16px] font-bold text-white rounded-md p-3 flex items-center justify-center gap-2"
          >
            <PlusOutlined />
            创建新项目
          </button>
        </div>
        <div class="grid grid-cols-2 gap-2 mb-4">
          <button
            @click="$emit('quick-action', 'prototype')"
            class="px-3 py-2 text-sm bg-white border border-border rounded-md hover:border-primary text-text-primary flex items-center justify-center gap-1"
          >
            <DesktopOutlined />
            原型设计
          </button>
          <button
            @click="$emit('quick-action', 'document')"
            class="px-3 py-2 text-sm bg-white border border-border rounded-md hover:border-primary text-text-primary flex items-center justify-center gap-1"
          >
            <FileTextOutlined />
            文档管理
          </button>
        </div>
        <div class="mb-4 text-primary-light dark:!text-primary text-sm">
          <p>在工作台中管理您的产品项目，包括原型设计、文档编写、需求管理等...</p>
        </div>
      </div>
    </div>
    <div class=" flex-1"
      ><div class="gap-2 mt-2 flex flex-col">
        <MenuCard
          v-for="menu in menuList"
          :key="menu.title"
          :card-name="menu.cardName"
          :title="menu.title"
          :content="menu.content"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { GoBookmarkFill } from 'vue-icons-plus/go'
import { PlusOutlined, DesktopOutlined, FileTextOutlined } from '@ant-design/icons-vue'
import MenuCard from './MenuCard.vue'
import { menuList } from '../mock/index'

interface Props {
  totalProjects?: number
}

const props = withDefaults(defineProps<Props>(), {
  totalProjects: 0
})

const emit = defineEmits(['create-project', 'quick-action'])
</script>
