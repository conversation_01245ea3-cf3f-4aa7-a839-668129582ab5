<template>
  <div class="project-overview">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-area-fill border border-border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-text-secondary text-sm">原型数量</p>
            <p class="text-2xl font-bold text-text-primary">{{ project.prototypes.length }}</p>
          </div>
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <DesktopOutlined class="text-blue-600" />
          </div>
        </div>
      </div>
      
      <div class="bg-area-fill border border-border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-text-secondary text-sm">文档数量</p>
            <p class="text-2xl font-bold text-text-primary">{{ project.documents.length }}</p>
          </div>
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <FileTextOutlined class="text-green-600" />
          </div>
        </div>
      </div>
      
      <div class="bg-area-fill border border-border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-text-secondary text-sm">需求数量</p>
            <p class="text-2xl font-bold text-text-primary">{{ project.requirements.length }}</p>
          </div>
          <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
            <UnorderedListOutlined class="text-yellow-600" />
          </div>
        </div>
      </div>
      
      <div class="bg-area-fill border border-border rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-text-secondary text-sm">团队成员</p>
            <p class="text-2xl font-bold text-text-primary">{{ project.team.length }}</p>
          </div>
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <TeamOutlined class="text-purple-600" />
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 最近活动 -->
      <div class="bg-area-fill border border-border rounded-lg">
        <div class="p-4 border-b border-border">
          <h3 class="text-lg font-semibold text-text-primary">最近活动</h3>
        </div>
        <div class="p-4">
          <div class="space-y-4">
            <div v-for="activity in recentActivities" :key="activity.id" class="flex items-start gap-3">
              <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                <component :is="activity.icon" class="text-white text-sm" />
              </div>
              <div class="flex-1">
                <p class="text-text-primary text-sm">{{ activity.description }}</p>
                <p class="text-text-secondary text-xs">{{ formatTime(activity.time) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目标签 -->
      <div class="bg-area-fill border border-border rounded-lg">
        <div class="p-4 border-b border-border">
          <h3 class="text-lg font-semibold text-text-primary">项目标签</h3>
        </div>
        <div class="p-4">
          <div class="flex flex-wrap gap-2">
            <span 
              v-for="tag in project.tags" 
              :key="tag"
              class="px-3 py-1 bg-primary-light text-white text-sm rounded-full"
            >
              {{ tag }}
            </span>
          </div>
          <div class="mt-4">
            <a-button size="small" type="dashed" @click="showAddTag = true">
              <PlusOutlined />
              添加标签
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 需求状态分布 -->
    <div class="mt-6 bg-area-fill border border-border rounded-lg">
      <div class="p-4 border-b border-border">
        <h3 class="text-lg font-semibold text-text-primary">需求状态分布</h3>
      </div>
      <div class="p-4">
        <div class="grid grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ todoCount }}</div>
            <div class="text-sm text-text-secondary">待处理</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-yellow-600">{{ inProgressCount }}</div>
            <div class="text-sm text-text-secondary">进行中</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ doneCount }}</div>
            <div class="text-sm text-text-secondary">已完成</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 团队成员 -->
    <div class="mt-6 bg-area-fill border border-border rounded-lg">
      <div class="p-4 border-b border-border">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-text-primary">团队成员</h3>
          <a-button size="small" type="primary" @click="$emit('invite-member')">
            <UserAddOutlined />
            邀请成员
          </a-button>
        </div>
      </div>
      <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div v-for="member in project.team" :key="member" class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
              <UserOutlined class="text-white" />
            </div>
            <div>
              <div class="font-medium text-text-primary">{{ member }}</div>
              <div class="text-sm text-text-secondary">
                {{ member === project.owner ? '项目负责人' : '团队成员' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加标签模态框 -->
    <a-modal
      v-model:open="showAddTag"
      title="添加标签"
      @ok="handleAddTag"
      @cancel="showAddTag = false"
    >
      <a-input 
        v-model:value="newTag" 
        placeholder="输入新标签"
        @pressEnter="handleAddTag"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  DesktopOutlined,
  FileTextOutlined,
  UnorderedListOutlined,
  TeamOutlined,
  PlusOutlined,
  UserAddOutlined,
  UserOutlined,
  EditOutlined,
  FileAddOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import type { Project } from '../config/projects'

interface Props {
  project: Project
}

const props = defineProps<Props>()
const emit = defineEmits(['invite-member', 'add-tag'])

const showAddTag = ref(false)
const newTag = ref('')

// 计算属性
const todoCount = computed(() => 
  props.project.requirements.filter(r => r.status === 'todo').length
)

const inProgressCount = computed(() => 
  props.project.requirements.filter(r => r.status === 'in-progress').length
)

const doneCount = computed(() => 
  props.project.requirements.filter(r => r.status === 'done').length
)

// 模拟最近活动数据
const recentActivities = computed(() => [
  {
    id: 1,
    description: '创建了新的原型设计',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    icon: DesktopOutlined
  },
  {
    id: 2,
    description: '更新了需求文档',
    time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    icon: FileTextOutlined
  },
  {
    id: 3,
    description: '完成了用户登录功能',
    time: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
    icon: CheckCircleOutlined
  },
  {
    id: 4,
    description: '添加了新的需求',
    time: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
    icon: FileAddOutlined
  }
])

// 工具函数
const formatTime = (timeString: string) => {
  const time = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  if (hours < 1) {
    const minutes = Math.floor(diff / (1000 * 60))
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    const days = Math.floor(hours / 24)
    return `${days}天前`
  }
}

// 事件处理
const handleAddTag = () => {
  if (newTag.value.trim()) {
    emit('add-tag', newTag.value.trim())
    newTag.value = ''
    showAddTag.value = false
  }
}
</script>

<style scoped>
.project-overview {
  /* 自定义样式 */
}
</style>
