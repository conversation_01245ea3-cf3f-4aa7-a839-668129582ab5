export const productList = [
  {
    id: 1,
    name: '语料收集知识化与大模型训推一体化平台',
    englishName: 'CMTT',
    description:
      '以「生态循环」的方式「可持续提升」AI能力的基础设施。集 Collection「语料收集」、Management「管理/知识化」、Tuning「模型调优」、Testing「模型回测」功能为一体，形成知识闭环，进而赋能在此基础上建设的各项场景应用。',
    status: '已发布',
    publishTime: '2024-01-15 10:00:00',
    tags: ['模型训练', '知识管理'],
    link: 'http://*************:8082'
  },
  {
    id: 2,
    name: 'AI搜索',
    englishName: 'AI Search',
    description:
      '基于「CMTT」知识底座的搜索能力。区别于根据「个人素质」提炼「关键词」获取「文件」的传统搜索。智能搜索能够理解「语义」，根据用户的「直接问题」，做出有「客观依据佐证」的「直接回答」，并提供更多「搜索建议」。',
    status: '已发布',
    publishTime: '2024-01-16 10:00:00',
    tags: ['智能搜索', '语义理解'],
    link: 'http://*************:8080'
  },
  {
    id: 3,
    name: '信用风险预警',
    englishName: 'AI Risk',
    description:
      '基于财务指标和「舆情信息」做信用主体风险预警。区别于传统风险模型（以具有「滞后性」的财务、工商数据为特征输入），将「风险评估与舆情相结合」。利用大模型的信息提取捕捉能力+小模型的预测能力+行业大数据，实现比市场和竞争对手更早地发现这些潜在违约风险。',
    status: '已发布',
    publishTime: '2024-01-17 10:00:00',
    tags: ['风险控制', '舆情分析'],
    link: 'https://ai.heartie.cn/risk/'
  },
  {
    id: 4,
    name: '要素抽取',
    englishName: 'AI Elements',
    description:
      '通过LLM从基础语料中「识别和提取关键信息」，帮助企业高效整理海量数据，提升决策支持与信息管理效率。相较于传统「OCR+NLP」方式更具优势：无需依赖特定的OCR模型，格式调整也不用重制模板；LLM自带的语言理解能力可以支持通过优化prompt的方式快速调整，降低传统NLP模型训练成本。',
    status: '已发布',
    publishTime: '2024-01-18 10:00:00',
    tags: ['信息抽取'],
    link: 'http://*************:8102'
  },
  {
    id: 5,
    name: '报告合成',
    englishName: 'AI Reporter',
    description:
      '基于「要素抽取」的文档组装能力，提取有效信息并按规则将其融合为目标文档。区别于「生成式」的不可控和低准确率，或「配置式」的难以扩展与高人工成本，报告合成以类似「文档问答」的形式，通过LLM关联「知识结构」来执行相应要素内容的提取与组装，更具长远价值。',
    status: '已发布',
    publishTime: '2024-01-19 10:00:00',
    tags: ['文档处理', '交互式生成报告'],
    link: 'http://*************:8081'
  },
  {
    id: 6,
    name: '测试平台',
    englishName: 'AI Testplatform',
    description:
      '用于验证LLM在特定任务场景的效果。通过上传测试样本与标注答案，以多维度指标的形式呈现LLM在当前任务的准确率，促进指导能力迭代。',
    status: '已发布',
    publishTime: '2024-01-20 10:00:00',
    tags: ['AI应用测试', '效果评估'],
    link: 'http://*************:8085'
  },
  {
    id: 7,
    name: '物料生成器',
    englishName: 'MaterialGenerator',
    description:
      '基于LLM的自动化内容创作工具。辅助快速生成广告文案、营销内容、产品描述等各类文本，支持多种场景风格定制，帮助企业提高创作效率、降低成本。',
    status: '已发布',
    publishTime: '2024-01-21 10:00:00',
    tags: ['内容生成', '营销工具'],
    link: 'http://*************:8095'
  },
  {
    id: 15,
    name: '消息对接渠道平台',
    englishName: 'AI MessageChannel',
    description:
      '多源消息的中转枢纽与监控平台，支持接入多种消息来源（邮件、各种聊天软件、短信...），聚合上下文会话管理与监控。内置监控面板实时展示AI的解析过程和反馈内容，确保信息处理的透明性与准确性，优化信息管理效率。',
    status: '规划中',
    publishTime: '2024-01-29 10:00:00',
    tags: ['消息管理', '渠道集成'],
    link: 'http://*************:8100'
  },
  {
    id: 9,
    name: '合同问答',
    englishName: 'AI Integration',
    description:
      '基于合同要素信息的智能问答。通过LLM拆解用户问题的查询主体条件，执行数据库查询后把同一维度的数据写入上下文，构建多轮问答。',
    status: '进行中',
    publishTime: '2024-01-23 10:00:00',
    tags: ['智能问答', '合同管理'],
    link: 'http://*************:8093/?type=USER_INFO&userName=%E6%9D%8E%E6%B3%BD%E8%83%9C&tokenValue=xxdddd2d3300dadadadaffadsxx&isKeepChat=true&noRadius=true'
  },
  {
    id: 10,
    name: '私域运营',
    englishName: 'AI Operation',
    description:
      '降低银行私域客服运营成本。实时接入「客服会话」，通过LLM动态监控客户「情绪波动」，为客服人员智能「推荐话术」及「生成工单」管理。会话结束后进行「会话评分」，同时沉淀「营销知识」与「金牌话术」。',
    status: 'POC',
    publishTime: '2024-01-24 10:00:00',
    tags: ['客服运营', '情感分析'],
    link: 'http://*************:8098'
  },
  // {
  //   id: 11,
  //   name: 'AI研发平台',
  //   englishName: 'AI DevelopmentPlatform',
  //   description: 'AI模型研发与部署一体化平台，提供完整的AI应用开发生命周期管理。',
  //   status: '规划中',
  //   publishTime: '2024-01-25 10:00:00',
  //   tags: ['研发平台', 'AI开发'],
  //     // link:"http://*************:"
  // },
  {
    id: 12,
    name: '智能BI报表平台',
    englishName: 'AI BIRpt',
    description:
      'AI融合金融业务理解，辅助用户处理数据需求，包括数据查询、报表生成与图表分析。通过对话框精准识别用户需求，实时获取数据并支持导出，同时支持简单的数据运算和加工。提升数据资产的利用效率，快速响应高时效需求，打破传统开发周期的限制。',
    status: 'POC',
    publishTime: '2024-01-26 10:00:00',
    tags: ['数据分析', '可视化'],
    link: 'http://*************:8099'
  },
  {
    id: 13,
    name: 'XB合规审查',
    englishName: 'AI ComplianceReview',
    description:
      '金融领域报告的智能合规审查。针对报告中的各项要素进行精准分析，智能识别并辅助修正潜在的合规问题，包括时间、数据准确性，业务披露和内容结构完整性等行业普遍关注问题。提升审核效率，减少合规风险，帮助金融机构快速响应监管要求。',
    status: '进行中',
    publishTime: '2024-01-27 10:00:00',
    tags: ['合规审查', 'AI金融'],
    link: 'http://*************:8103/app/pdf'
  },
  {
    id: 14,
    name: '询报价机器人',
    englishName: 'AI Inquiry',
    description:
      '投研领域的交易员智能助理。接入多源聊天渠道，智能识别投资经理「黑话」匹配投资意向，辅助交易员结合询价策略与市场进行询报价沟通，最终促成交易。实时语义识别会话意图与工作阶段，动态归集意向与询报价要素，显著提升交易员工作效率。',
    status: '进行中',
    publishTime: '2024-01-28 10:00:00',
    tags: ['金融交易', '自动询价'],
    link: 'http://*************:8100'
  },
  {
    id: 15,
    name: 'AI门户',
    englishName: 'AI portal',
    description:
      '集成AI工作台、交互框架、智能导航与实验空间的综合平台，致力于为用户提供高效、智能的一站式解决方案。通过统一入口打破工具割裂，让AI能力随取随用。',
    status: '已发布',
    publishTime: '2024-01-29 10:00:00',
    tags: ['AI门户', 'AI工作台'],
    link: 'http://*************:8111'
  }
]
