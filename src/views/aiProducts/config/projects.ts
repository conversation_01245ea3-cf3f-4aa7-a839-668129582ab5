export interface Project {
  id: number
  name: string
  description: string
  status: 'active' | 'archived' | 'draft'
  type: 'web' | 'mobile' | 'desktop' | 'api'
  createdAt: string
  updatedAt: string
  owner: string
  team: string[]
  tags: string[]
  progress: number
  prototypes: Prototype[]
  documents: Document[]
  requirements: Requirement[]
}

export interface Prototype {
  id: number
  name: string
  type: 'wireframe' | 'mockup' | 'interactive'
  url?: string
  thumbnail?: string
  createdAt: string
  updatedAt: string
  status: 'draft' | 'review' | 'approved'
}

export interface Document {
  id: number
  name: string
  type: 'requirement' | 'design' | 'api' | 'user-story'
  content: string
  createdAt: string
  updatedAt: string
  author: string
}

export interface Requirement {
  id: number
  title: string
  description: string
  priority: 'high' | 'medium' | 'low'
  status: 'todo' | 'in-progress' | 'done'
  assignee?: string
  createdAt: string
  updatedAt: string
}

export const projectList: Project[] = [
  {
    id: 1,
    name: '智能客服系统',
    description: '基于AI的智能客服系统，支持多轮对话、情感分析、知识库检索等功能',
    status: 'active',
    type: 'web',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T15:30:00Z',
    owner: '张三',
    team: ['张三', '李四', '王五'],
    tags: ['AI', '客服', 'NLP'],
    progress: 75,
    prototypes: [
      {
        id: 1,
        name: '主界面原型',
        type: 'interactive',
        thumbnail: '/images/prototype1.png',
        createdAt: '2024-01-16T09:00:00Z',
        updatedAt: '2024-01-18T14:00:00Z',
        status: 'approved'
      },
      {
        id: 2,
        name: '对话界面设计',
        type: 'mockup',
        thumbnail: '/images/prototype2.png',
        createdAt: '2024-01-17T11:00:00Z',
        updatedAt: '2024-01-19T16:00:00Z',
        status: 'review'
      },
      {
        id: 3,
        name: '用户登录流程',
        type: 'interactive',
        thumbnail: '/images/prototype3.png',
        createdAt: '2024-01-18T14:00:00Z',
        updatedAt: '2024-01-20T10:00:00Z',
        status: 'approved'
      }
    ],
    documents: [
      {
        id: 1,
        name: '产品需求文档',
        type: 'requirement',
        content: '智能客服系统需求规格说明...',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-16T12:00:00Z',
        author: '张三'
      },
      {
        id: 2,
        name: 'API接口文档',
        type: 'api',
        content: 'RESTful API接口设计文档...',
        createdAt: '2024-01-17T14:00:00Z',
        updatedAt: '2024-01-18T10:00:00Z',
        author: '李四'
      },
      {
        id: 3,
        name: '用户体验设计规范',
        type: 'design',
        content: 'UI/UX设计规范和组件库...',
        createdAt: '2024-01-19T09:00:00Z',
        updatedAt: '2024-01-20T16:00:00Z',
        author: '王五'
      }
    ],
    requirements: [
      {
        id: 1,
        title: '用户登录功能',
        description: '实现用户登录、注册、密码重置功能',
        priority: 'high',
        status: 'done',
        assignee: '王五',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-16T18:00:00Z'
      },
      {
        id: 2,
        title: '智能对话引擎',
        description: '集成大语言模型，实现智能对话功能',
        priority: 'high',
        status: 'in-progress',
        assignee: '李四',
        createdAt: '2024-01-16T09:00:00Z',
        updatedAt: '2024-01-20T15:00:00Z'
      },
      {
        id: 3,
        title: '知识库管理',
        description: '构建和管理客服知识库，支持智能检索',
        priority: 'medium',
        status: 'todo',
        assignee: '王五',
        createdAt: '2024-01-18T10:00:00Z',
        updatedAt: '2024-01-18T10:00:00Z'
      },
      {
        id: 4,
        title: '多渠道接入',
        description: '支持网页、微信、APP等多渠道接入',
        priority: 'medium',
        status: 'in-progress',
        assignee: '张三',
        createdAt: '2024-01-19T14:00:00Z',
        updatedAt: '2024-01-21T09:00:00Z'
      }
    ]
  },
  {
    id: 2,
    name: '数据分析平台',
    description: '企业级数据分析平台，支持多维度数据可视化、报表生成、预测分析',
    status: 'active',
    type: 'web',
    createdAt: '2024-01-10T08:00:00Z',
    updatedAt: '2024-01-22T11:00:00Z',
    owner: '赵六',
    team: ['赵六', '钱七', '孙八'],
    tags: ['数据分析', '可视化', 'BI'],
    progress: 60,
    prototypes: [
      {
        id: 4,
        name: '仪表板设计',
        type: 'wireframe',
        thumbnail: '/images/prototype4.png',
        createdAt: '2024-01-12T10:00:00Z',
        updatedAt: '2024-01-15T16:00:00Z',
        status: 'draft'
      },
      {
        id: 5,
        name: '数据可视化组件',
        type: 'mockup',
        thumbnail: '/images/prototype5.png',
        createdAt: '2024-01-14T13:00:00Z',
        updatedAt: '2024-01-16T11:00:00Z',
        status: 'review'
      }
    ],
    documents: [
      {
        id: 3,
        name: '技术架构文档',
        type: 'design',
        content: '数据分析平台技术架构设计...',
        createdAt: '2024-01-11T09:00:00Z',
        updatedAt: '2024-01-14T15:00:00Z',
        author: '赵六'
      }
    ],
    requirements: [
      {
        id: 3,
        title: '数据源接入',
        description: '支持多种数据源接入，包括数据库、API、文件等',
        priority: 'high',
        status: 'in-progress',
        assignee: '钱七',
        createdAt: '2024-01-11T10:00:00Z',
        updatedAt: '2024-01-20T14:00:00Z'
      }
    ]
  },
  {
    id: 3,
    name: '移动端购物应用',
    description: '电商移动端应用，包含商品浏览、购物车、支付、用户中心等功能',
    status: 'active',
    type: 'mobile',
    createdAt: '2024-01-20T14:00:00Z',
    updatedAt: '2024-01-22T09:00:00Z',
    owner: '周九',
    team: ['周九', '吴十', '李明', '张华'],
    tags: ['电商', '移动端', 'React Native'],
    progress: 45,
    prototypes: [
      {
        id: 6,
        name: '商品列表页面',
        type: 'wireframe',
        thumbnail: '/images/prototype6.png',
        createdAt: '2024-01-21T15:00:00Z',
        updatedAt: '2024-01-22T10:00:00Z',
        status: 'draft'
      }
    ],
    documents: [
      {
        id: 4,
        name: '用户故事',
        type: 'user-story',
        content: '作为一个用户，我希望能够...',
        createdAt: '2024-01-21T10:00:00Z',
        updatedAt: '2024-01-22T09:00:00Z',
        author: '周九'
      }
    ],
    requirements: [
      {
        id: 5,
        title: '商品展示页面',
        description: '设计和实现商品详情展示页面',
        priority: 'medium',
        status: 'todo',
        assignee: '吴十',
        createdAt: '2024-01-21T11:00:00Z',
        updatedAt: '2024-01-21T11:00:00Z'
      },
      {
        id: 6,
        title: '购物车功能',
        description: '实现购物车添加、删除、结算功能',
        priority: 'high',
        status: 'todo',
        assignee: '周九',
        createdAt: '2024-01-22T09:00:00Z',
        updatedAt: '2024-01-22T09:00:00Z'
      }
    ]
  },
  {
    id: 4,
    name: '企业协作平台',
    description: '面向企业的一站式协作平台，包含项目管理、团队沟通、文档协作等功能',
    status: 'active',
    type: 'web',
    createdAt: '2024-01-25T09:00:00Z',
    updatedAt: '2024-02-01T16:30:00Z',
    owner: '陈小明',
    team: ['陈小明', '刘芳', '王强', '赵丽', '孙伟'],
    tags: ['协作', '企业级', 'SaaS'],
    progress: 80,
    prototypes: [
      {
        id: 7,
        name: '项目看板设计',
        type: 'interactive',
        thumbnail: '/images/prototype7.png',
        createdAt: '2024-01-26T10:00:00Z',
        updatedAt: '2024-01-28T14:00:00Z',
        status: 'approved'
      },
      {
        id: 8,
        name: '团队沟通界面',
        type: 'mockup',
        thumbnail: '/images/prototype8.png',
        createdAt: '2024-01-27T15:00:00Z',
        updatedAt: '2024-01-30T11:00:00Z',
        status: 'approved'
      },
      {
        id: 9,
        name: '文档编辑器',
        type: 'interactive',
        thumbnail: '/images/prototype9.png',
        createdAt: '2024-01-29T13:00:00Z',
        updatedAt: '2024-02-01T09:00:00Z',
        status: 'review'
      }
    ],
    documents: [
      {
        id: 5,
        name: '产品规划文档',
        type: 'requirement',
        content: '企业协作平台产品规划和功能设计...',
        createdAt: '2024-01-25T09:00:00Z',
        updatedAt: '2024-01-26T16:00:00Z',
        author: '陈小明'
      },
      {
        id: 6,
        name: '系统架构设计',
        type: 'design',
        content: '微服务架构设计和技术选型...',
        createdAt: '2024-01-27T10:00:00Z',
        updatedAt: '2024-01-29T14:00:00Z',
        author: '王强'
      },
      {
        id: 7,
        name: 'API接口规范',
        type: 'api',
        content: 'RESTful API设计规范和接口文档...',
        createdAt: '2024-01-30T11:00:00Z',
        updatedAt: '2024-02-01T15:00:00Z',
        author: '刘芳'
      }
    ],
    requirements: [
      {
        id: 7,
        title: '项目管理模块',
        description: '实现项目创建、任务分配、进度跟踪功能',
        priority: 'high',
        status: 'done',
        assignee: '陈小明',
        createdAt: '2024-01-25T09:00:00Z',
        updatedAt: '2024-01-28T18:00:00Z'
      },
      {
        id: 8,
        title: '实时通讯功能',
        description: '基于WebSocket的实时消息推送',
        priority: 'high',
        status: 'done',
        assignee: '王强',
        createdAt: '2024-01-26T10:00:00Z',
        updatedAt: '2024-01-30T17:00:00Z'
      },
      {
        id: 9,
        title: '文档协作编辑',
        description: '多人实时协作编辑文档功能',
        priority: 'medium',
        status: 'in-progress',
        assignee: '刘芳',
        createdAt: '2024-01-28T14:00:00Z',
        updatedAt: '2024-02-01T12:00:00Z'
      },
      {
        id: 10,
        title: '权限管理系统',
        description: '基于角色的权限控制系统',
        priority: 'medium',
        status: 'todo',
        assignee: '赵丽',
        createdAt: '2024-01-30T09:00:00Z',
        updatedAt: '2024-01-30T09:00:00Z'
      }
    ]
  },
  {
    id: 5,
    name: '在线教育平台',
    description: 'K12在线教育平台，支持直播授课、作业管理、学习进度跟踪等功能',
    status: 'active',
    type: 'web',
    createdAt: '2024-02-01T08:00:00Z',
    updatedAt: '2024-02-05T17:00:00Z',
    owner: '李教授',
    team: ['李教授', '张老师', '王同学', '刘助教'],
    tags: ['教育', '直播', 'Vue3'],
    progress: 35,
    prototypes: [
      {
        id: 10,
        name: '课程列表页',
        type: 'wireframe',
        thumbnail: '/images/prototype10.png',
        createdAt: '2024-02-02T09:00:00Z',
        updatedAt: '2024-02-03T15:00:00Z',
        status: 'approved'
      },
      {
        id: 11,
        name: '直播教室界面',
        type: 'mockup',
        thumbnail: '/images/prototype11.png',
        createdAt: '2024-02-03T11:00:00Z',
        updatedAt: '2024-02-05T10:00:00Z',
        status: 'review'
      }
    ],
    documents: [
      {
        id: 8,
        name: '教学需求分析',
        type: 'requirement',
        content: '在线教育平台功能需求和用户场景分析...',
        createdAt: '2024-02-01T08:00:00Z',
        updatedAt: '2024-02-02T16:00:00Z',
        author: '李教授'
      },
      {
        id: 9,
        name: '直播技术方案',
        type: 'design',
        content: '基于WebRTC的直播技术实现方案...',
        createdAt: '2024-02-03T10:00:00Z',
        updatedAt: '2024-02-04T14:00:00Z',
        author: '张老师'
      }
    ],
    requirements: [
      {
        id: 11,
        title: '用户注册登录',
        description: '学生和教师注册登录功能',
        priority: 'high',
        status: 'done',
        assignee: '王同学',
        createdAt: '2024-02-01T09:00:00Z',
        updatedAt: '2024-02-02T18:00:00Z'
      },
      {
        id: 12,
        title: '课程管理系统',
        description: '教师创建和管理课程功能',
        priority: 'high',
        status: 'in-progress',
        assignee: '张老师',
        createdAt: '2024-02-02T10:00:00Z',
        updatedAt: '2024-02-05T15:00:00Z'
      },
      {
        id: 13,
        title: '直播功能开发',
        description: '实现直播授课和互动功能',
        priority: 'high',
        status: 'todo',
        assignee: '刘助教',
        createdAt: '2024-02-03T14:00:00Z',
        updatedAt: '2024-02-03T14:00:00Z'
      }
    ]
  },
  {
    id: 6,
    name: 'IoT设备管理系统',
    description: '物联网设备统一管理平台，支持设备监控、数据采集、远程控制等功能',
    status: 'draft',
    type: 'web',
    createdAt: '2024-02-03T10:00:00Z',
    updatedAt: '2024-02-06T14:00:00Z',
    owner: '张工程师',
    team: ['张工程师', '李硬件', '王嵌入式'],
    tags: ['IoT', '物联网', '监控'],
    progress: 20,
    prototypes: [
      {
        id: 12,
        name: '设备监控大屏',
        type: 'wireframe',
        thumbnail: '/images/prototype12.png',
        createdAt: '2024-02-04T13:00:00Z',
        updatedAt: '2024-02-05T16:00:00Z',
        status: 'draft'
      }
    ],
    documents: [
      {
        id: 10,
        name: 'IoT架构设计',
        type: 'design',
        content: '物联网系统整体架构和通信协议设计...',
        createdAt: '2024-02-03T10:00:00Z',
        updatedAt: '2024-02-04T15:00:00Z',
        author: '张工程师'
      }
    ],
    requirements: [
      {
        id: 14,
        title: '设备接入协议',
        description: '支持MQTT、HTTP等多种设备接入协议',
        priority: 'high',
        status: 'todo',
        assignee: '李硬件',
        createdAt: '2024-02-04T09:00:00Z',
        updatedAt: '2024-02-04T09:00:00Z'
      },
      {
        id: 15,
        title: '数据可视化',
        description: '设备数据实时展示和历史数据分析',
        priority: 'medium',
        status: 'todo',
        assignee: '王嵌入式',
        createdAt: '2024-02-05T11:00:00Z',
        updatedAt: '2024-02-05T11:00:00Z'
      }
    ]
  }
]
