<template>
  <div class="document-detail-page">
    <!-- 返回按钮 -->
    <div class="mb-6">
      <a-button @click="goBack" class="flex items-center gap-2">
        <ArrowLeftOutlined />
        返回文档列表
      </a-button>
    </div>

    <!-- 文档详情 -->
    <div class="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 mb-8">
      <div class="flex items-start justify-between mb-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">示例文档</h1>
          <p class="text-gray-600 dark:text-gray-400 text-lg">这是一个示例文档页面</p>
        </div>
        <div class="flex items-center gap-3">
          <a-button>
            <EditOutlined />
            编辑
          </a-button>
          <a-button type="primary">
            <ShareAltOutlined />
            分享
          </a-button>
        </div>
      </div>

      <!-- 文档编辑器区域 -->
      <div class="bg-gray-100 dark:bg-gray-700 rounded-xl p-8 text-center">
        <div class="w-32 h-32 mx-auto mb-6 bg-gradient-to-r from-green-500 to-teal-600 rounded-2xl flex items-center justify-center">
          <FileTextOutlined class="text-6xl text-white" />
        </div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">文档编辑器</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          文档编辑器功能正在开发中，敬请期待
        </p>
        <a-button type="primary" size="large">
          <EditOutlined />
          开始编辑
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeftOutlined,
  EditOutlined,
  ShareAltOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/aiProducts/documents')
}
</script>

<style scoped>
/* 自定义样式 */
</style>
