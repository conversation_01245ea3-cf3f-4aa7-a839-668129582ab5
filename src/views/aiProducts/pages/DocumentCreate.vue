<template>
  <div class="document-create-page min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <!-- 返回按钮 -->
    <div class="mb-6">
      <a-button @click="goBack" class="flex items-center gap-2">
        <ArrowLeftOutlined />
        返回文档列表
      </a-button>
    </div>

    <!-- 页面头部 -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">创建文档</h1>
      <p class="text-gray-600 dark:text-gray-400 text-lg">选择文档类型，快速创建专业文档</p>
    </div>

    <!-- 文档类型选择 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div 
        v-for="docType in documentTypes" 
        :key="docType.type"
        :class="[
          'p-6 rounded-2xl border-2 cursor-pointer transition-all hover:shadow-lg',
          selectedType === docType.type 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : 'border-gray-200 dark:border-gray-600 hover:border-blue-300'
        ]"
        @click="selectedType = docType.type"
      >
        <div class="text-center">
          <div 
            :class="[
              'w-16 h-16 rounded-xl flex items-center justify-center mx-auto mb-4',
              docType.bgColor
            ]"
          >
            <component :is="docType.icon" :class="['text-2xl', docType.iconColor]" />
          </div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ docType.name }}</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ docType.description }}</p>
        </div>
      </div>
    </div>

    <!-- 文档信息表单 -->
    <div class="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-8">
      <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">文档信息</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            文档标题 *
          </label>
          <a-input 
            v-model:value="documentForm.title" 
            placeholder="输入文档标题"
            size="large"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            关联项目
          </label>
          <a-select 
            v-model:value="documentForm.projectId" 
            placeholder="选择关联项目" 
            style="width: 100%"
            size="large"
          >
            <a-select-option 
              v-for="project in projects" 
              :key="project.id" 
              :value="project.id"
            >
              {{ project.name }}
            </a-select-option>
          </a-select>
        </div>
      </div>

      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          文档描述
        </label>
        <a-textarea 
          v-model:value="documentForm.description" 
          placeholder="简要描述文档内容和用途..."
          :rows="3"
          :maxlength="500"
          show-count
        />
      </div>

      <!-- AI辅助生成 -->
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 mb-6">
        <div class="flex items-start gap-4">
          <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center flex-shrink-0">
            <BulbOutlined class="text-white text-xl" />
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">AI辅助生成</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              描述您的需求，AI将为您生成文档大纲和初始内容
            </p>
            <a-textarea 
              v-model:value="aiPrompt" 
              placeholder="描述您想要创建的文档内容，包括主要章节、关键信息等..."
              :rows="4"
              :maxlength="1000"
              show-count
              class="mb-4"
            />
            <a-button type="primary" @click="generateWithAI" :loading="aiLoading">
              <BulbOutlined />
              AI生成大纲
            </a-button>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center justify-end gap-4">
        <a-button size="large" @click="goBack">
          取消
        </a-button>
        <a-button type="primary" size="large" @click="createDocument">
          <FileTextOutlined />
          创建文档
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ArrowLeftOutlined,
  FileTextOutlined,
  DesktopOutlined,
  ApiOutlined,
  UserOutlined,
  BulbOutlined
} from '@ant-design/icons-vue'
import { projectList } from '../config/projects'

const router = useRouter()

// 响应式数据
const selectedType = ref('requirement')
const aiLoading = ref(false)
const aiPrompt = ref('')
const projects = ref(projectList)

const documentForm = reactive({
  title: '',
  projectId: null,
  description: ''
})

// 文档类型配置
const documentTypes = [
  {
    type: 'requirement',
    name: '需求文档',
    description: '产品需求规格说明',
    icon: FileTextOutlined,
    bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    iconColor: 'text-blue-500'
  },
  {
    type: 'design',
    name: '设计文档',
    description: 'UI/UX设计规范',
    icon: DesktopOutlined,
    bgColor: 'bg-purple-100 dark:bg-purple-900/30',
    iconColor: 'text-purple-500'
  },
  {
    type: 'api',
    name: 'API文档',
    description: '接口设计文档',
    icon: ApiOutlined,
    bgColor: 'bg-green-100 dark:bg-green-900/30',
    iconColor: 'text-green-500'
  },
  {
    type: 'user-story',
    name: '用户故事',
    description: '用户需求故事',
    icon: UserOutlined,
    bgColor: 'bg-orange-100 dark:bg-orange-900/30',
    iconColor: 'text-orange-500'
  }
]

// 事件处理
const goBack = () => {
  router.push('/aiProducts/documents')
}

const generateWithAI = async () => {
  if (!aiPrompt.value.trim()) return
  
  aiLoading.value = true
  
  try {
    // 模拟AI生成过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    console.log('AI生成文档大纲:', aiPrompt.value)
    // 这里可以将AI生成的内容填充到表单中
    
  } catch (error) {
    console.error('AI生成失败:', error)
  } finally {
    aiLoading.value = false
  }
}

const createDocument = () => {
  if (!documentForm.title.trim()) {
    return
  }
  
  const newDocument = {
    ...documentForm,
    type: selectedType.value,
    aiPrompt: aiPrompt.value,
    createdAt: new Date().toISOString()
  }
  
  console.log('创建文档:', newDocument)
  
  // 跳转到文档详情页面
  router.push('/aiProducts/documents/1')
}
</script>

<style scoped>
/* 自定义样式 */
</style>
