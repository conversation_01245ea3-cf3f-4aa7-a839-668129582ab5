<template>
  <div class="project-list-page min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">项目管理</h1>
        <p class="text-gray-600 dark:text-gray-400">管理您的所有产品项目，跟踪开发进度</p>
      </div>
      <div class="flex items-center gap-3">
        <a-button @click="showCreateModal = true" type="primary" size="large">
          <PlusOutlined />
          新建项目
        </a-button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6 mb-6">
      <div class="flex items-center gap-4 flex-wrap">
        <!-- 搜索框 -->
        <div class="relative flex-1 min-w-[300px]">
          <SearchOutlined class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索项目名称、描述..."
            class="w-full pl-10 pr-4 py-2.5 bg-white/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
          />
        </div>

        <!-- 状态筛选 -->
        <a-select 
          v-model:value="statusFilter" 
          placeholder="项目状态" 
          style="width: 120px"
          allowClear
        >
          <a-select-option value="active">进行中</a-select-option>
          <a-select-option value="draft">草稿</a-select-option>
          <a-select-option value="archived">已归档</a-select-option>
        </a-select>

        <!-- 类型筛选 -->
        <a-select 
          v-model:value="typeFilter" 
          placeholder="项目类型" 
          style="width: 120px"
          allowClear
        >
          <a-select-option value="web">Web应用</a-select-option>
          <a-select-option value="mobile">移动应用</a-select-option>
          <a-select-option value="desktop">桌面应用</a-select-option>
          <a-select-option value="api">API服务</a-select-option>
        </a-select>

        <!-- 排序 -->
        <a-select 
          v-model:value="sortBy" 
          placeholder="排序方式" 
          style="width: 140px"
        >
          <a-select-option value="updatedAt">最近更新</a-select-option>
          <a-select-option value="createdAt">创建时间</a-select-option>
          <a-select-option value="name">项目名称</a-select-option>
          <a-select-option value="progress">项目进度</a-select-option>
        </a-select>

        <!-- 视图切换 -->
        <div class="flex border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
          <button
            :class="[
              'px-3 py-2 text-sm transition-colors',
              viewMode === 'grid' 
                ? 'bg-blue-500 text-white' 
                : 'bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
            ]"
            @click="viewMode = 'grid'"
          >
            <AppstoreOutlined />
          </button>
          <button
            :class="[
              'px-3 py-2 text-sm transition-colors',
              viewMode === 'list' 
                ? 'bg-blue-500 text-white' 
                : 'bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
            ]"
            @click="viewMode = 'list'"
          >
            <UnorderedListOutlined />
          </button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="总项目"
        :value="projects.length"
        icon="ProjectOutlined"
        color="blue"
        :progress="75"
      />
      <StatsCard
        title="进行中"
        :value="activeProjects"
        icon="PlayCircleOutlined"
        color="green"
        :progress="60"
      />
      <StatsCard
        title="草稿"
        :value="draftProjects"
        icon="EditOutlined"
        color="orange"
        :progress="30"
      />
      <StatsCard
        title="已归档"
        :value="archivedProjects"
        icon="InboxOutlined"
        color="gray"
        :progress="90"
      />
    </div>

    <!-- 项目列表 -->
    <div class="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <ProjectCard
          v-for="project in filteredProjects"
          :key="project.id"
          :project="project"
          @click="navigateToProject(project.id)"
          @edit="handleEditProject"
          @delete="handleDeleteProject"
        />
      </div>

      <!-- 列表视图 -->
      <div v-else>
        <ProjectTable 
          :projects="filteredProjects"
          @edit="handleEditProject"
          @delete="handleDeleteProject"
          @view="navigateToProject"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="filteredProjects.length === 0" class="text-center py-12">
        <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <InboxOutlined class="text-4xl text-gray-400" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无项目</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-4">开始创建您的第一个项目吧</p>
        <a-button type="primary" @click="showCreateModal = true">
          <PlusOutlined />
          创建项目
        </a-button>
      </div>
    </div>

    <!-- 创建项目模态框 -->
    <CreateProjectModal 
      v-model:visible="showCreateModal"
      @confirm="handleCreateProject"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { 
  PlusOutlined, 
  SearchOutlined, 
  AppstoreOutlined, 
  UnorderedListOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'
import ProjectCard from '../components/ProjectCard.vue'
import ProjectTable from '../components/ProjectTable.vue'
import StatsCard from '../components/StatsCard.vue'
import CreateProjectModal from '../components/CreateProjectModal.vue'
import { projectList } from '../config/projects'
import type { Project } from '../config/projects'

const router = useRouter()

// 响应式数据
const projects = ref<Project[]>(projectList)
const searchKeyword = ref('')
const statusFilter = ref<string>()
const typeFilter = ref<string>()
const sortBy = ref('updatedAt')
const viewMode = ref<'grid' | 'list'>('grid')
const showCreateModal = ref(false)

// 计算属性
const activeProjects = computed(() => 
  projects.value.filter(p => p.status === 'active').length
)

const draftProjects = computed(() => 
  projects.value.filter(p => p.status === 'draft').length
)

const archivedProjects = computed(() => 
  projects.value.filter(p => p.status === 'archived').length
)

const filteredProjects = computed(() => {
  let filtered = projects.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(project => 
      project.name.toLowerCase().includes(keyword) ||
      project.description.toLowerCase().includes(keyword)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(p => p.status === statusFilter.value)
  }

  // 类型过滤
  if (typeFilter.value) {
    filtered = filtered.filter(p => p.type === typeFilter.value)
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'progress':
        return b.progress - a.progress
      case 'createdAt':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      case 'updatedAt':
      default:
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    }
  })

  return filtered
})

// 事件处理
const navigateToProject = (projectId: number) => {
  router.push(`/aiProducts/projects/${projectId}`)
}

const handleCreateProject = (projectData: any) => {
  const newProject: Project = {
    id: Date.now(),
    ...projectData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    prototypes: [],
    documents: [],
    requirements: []
  }
  projects.value.unshift(newProject)
  showCreateModal.value = false
}

const handleEditProject = (project: Project) => {
  console.log('编辑项目:', project)
  // 这里可以打开编辑模态框或跳转到编辑页面
}

const handleDeleteProject = (projectId: number) => {
  const index = projects.value.findIndex(p => p.id === projectId)
  if (index > -1) {
    projects.value.splice(index, 1)
  }
}
</script>

<style scoped>
.project-list-page {
  /* 自定义样式 */
}
</style>
