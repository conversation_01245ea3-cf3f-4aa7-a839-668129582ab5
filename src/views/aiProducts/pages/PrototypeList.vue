<template>
  <div class="prototype-list-page min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">原型设计</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">AI驱动的原型设计，快速生成专业界面</p>
      </div>
      <a-button type="primary" @click="navigateToCreate">
        <PlusOutlined />
        创建原型
      </a-button>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6 mb-6">
      <div class="flex items-center gap-4 flex-wrap">
        <!-- 搜索框 -->
        <div class="relative flex-1 min-w-[300px]">
          <SearchOutlined class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索原型名称、描述..."
            class="w-full pl-10 pr-4 py-2.5 bg-white/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
          />
        </div>

        <!-- 项目筛选 -->
        <a-select 
          v-model:value="projectFilter" 
          placeholder="所属项目" 
          style="width: 150px"
          allowClear
        >
          <a-select-option 
            v-for="project in projects" 
            :key="project.id" 
            :value="project.id"
          >
            {{ project.name }}
          </a-select-option>
        </a-select>

        <!-- 状态筛选 -->
        <a-select 
          v-model:value="statusFilter" 
          placeholder="原型状态" 
          style="width: 120px"
          allowClear
        >
          <a-select-option value="draft">草稿</a-select-option>
          <a-select-option value="review">审核中</a-select-option>
          <a-select-option value="approved">已通过</a-select-option>
        </a-select>

        <!-- 类型筛选 -->
        <a-select 
          v-model:value="typeFilter" 
          placeholder="原型类型" 
          style="width: 120px"
          allowClear
        >
          <a-select-option value="wireframe">线框图</a-select-option>
          <a-select-option value="mockup">视觉稿</a-select-option>
          <a-select-option value="interactive">交互原型</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 原型列表 -->
    <div class="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6">
      <!-- 空状态 -->
      <div class="text-center py-20">
        <div class="w-32 h-32 mx-auto mb-6 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-2xl flex items-center justify-center">
          <DesktopOutlined class="text-6xl text-blue-500" />
        </div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">开始您的第一个原型</h2>
        <p class="text-gray-500 dark:text-gray-400 mb-8 max-w-md mx-auto">
          使用我们的AI驱动原型设计工具，通过简单的文字描述就能生成专业的产品原型
        </p>
        
        <!-- 快速开始选项 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto mb-8">
          <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-500 transition-colors cursor-pointer">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <EditOutlined class="text-blue-500 text-xl" />
            </div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-1">线框图</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">快速构建页面结构</p>
          </div>
          
          <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-purple-500 transition-colors cursor-pointer">
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <PictureOutlined class="text-purple-500 text-xl" />
            </div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-1">视觉稿</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">高保真界面设计</p>
          </div>
          
          <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-green-500 transition-colors cursor-pointer">
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <InteractionOutlined class="text-green-500 text-xl" />
            </div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-1">交互原型</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">可点击的交互演示</p>
          </div>
        </div>
        
        <div class="flex items-center justify-center gap-4">
          <a-button type="primary" size="large" @click="navigateToCreate">
            <PlusOutlined />
            创建原型
          </a-button>
          <a-button size="large">
            <PlayCircleOutlined />
            查看示例
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusOutlined,
  SearchOutlined,
  DesktopOutlined,
  BulbOutlined,
  ThunderboltOutlined,
  TeamOutlined,
  EditOutlined,
  PictureOutlined,
  BorderOutlined,
  InteractionOutlined,
  PlayCircleOutlined
} from '@ant-design/icons-vue'
import { projectList } from '../config/projects'
import { computed } from 'vue'

const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const projectFilter = ref<number>()
const statusFilter = ref<string>()
const typeFilter = ref<string>()
const projects = ref(projectList)

// 事件处理
const navigateToCreate = () => {
  router.push('/aiProducts/prototypes/create')
}
</script>

<style scoped>
/* 自定义样式 */
</style>
