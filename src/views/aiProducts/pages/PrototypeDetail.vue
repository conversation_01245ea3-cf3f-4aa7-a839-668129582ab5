<template>
  <div class="prototype-detail-page">
    <!-- 返回按钮 -->
    <div class="mb-6">
      <a-button @click="goBack" class="flex items-center gap-2">
        <ArrowLeftOutlined />
        返回原型列表
      </a-button>
    </div>

    <!-- 原型详情 -->
    <div class="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 mb-8">
      <div class="flex items-start justify-between mb-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">示例原型</h1>
          <p class="text-gray-600 dark:text-gray-400 text-lg">这是一个示例原型页面</p>
        </div>
        <div class="flex items-center gap-3">
          <a-button>
            <EditOutlined />
            编辑
          </a-button>
          <a-button type="primary">
            <PlayCircleOutlined />
            预览
          </a-button>
        </div>
      </div>

      <!-- 原型预览区域 -->
      <div class="bg-gray-100 dark:bg-gray-700 rounded-xl p-8 text-center">
        <div class="w-32 h-32 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
          <DesktopOutlined class="text-6xl text-white" />
        </div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">原型编辑器</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          原型编辑器功能正在开发中，敬请期待
        </p>
        <a-button type="primary" size="large">
          <EditOutlined />
          开始编辑
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeftOutlined,
  EditOutlined,
  PlayCircleOutlined,
  DesktopOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/aiProducts/prototypes')
}
</script>

<style scoped>
/* 自定义样式 */
</style>
