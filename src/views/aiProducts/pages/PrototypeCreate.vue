<template>
  <div class="prototype-create-page min-h-screen bg-white dark:bg-gray-900">
    <!-- 顶部导航栏 - 类似Figma风格 -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <a-button @click="goBack" type="text" size="small" class="flex items-center gap-1 text-gray-600 hover:text-gray-900">
            <ArrowLeftOutlined class="text-sm" />
            <span class="text-sm">返回</span>
          </a-button>
          <div class="h-4 w-px bg-gray-300 dark:bg-gray-600"></div>
          <div class="flex items-center gap-2">
            <div class="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
              <DesktopOutlined class="text-white text-xs" />
            </div>
            <span class="text-sm font-medium text-gray-900 dark:text-white">新建原型</span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <a-button type="text" size="small" class="text-gray-600 hover:text-gray-900">
            <QuestionCircleOutlined class="text-sm" />
            <span class="text-sm ml-1">帮助</span>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 bg-gray-50 dark:bg-gray-900">
      <div class="max-w-7xl mx-auto p-8">
        <!-- 页面标题 -->
        <div class="text-center mb-10">
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-3">选择创建方式</h1>
          <p class="text-gray-600 dark:text-gray-400">选择最适合您的原型创建方式，快速开始设计</p>
        </div>

        <!-- 创建方式卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <!-- 从模板开始 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md hover:border-blue-500 dark:hover:border-blue-400 transition-all cursor-pointer group" @click="showTemplateModal = true">
            <div class="text-center">
              <div class="w-12 h-12 bg-blue-50 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-500 group-hover:text-white transition-all">
                <AppstoreOutlined class="text-xl text-blue-500 group-hover:text-white" />
              </div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">从模板开始</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">选择预设模板快速开始</p>
              <div class="inline-flex items-center px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 text-xs rounded-full">
                <span class="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></span>
                推荐新手
              </div>
            </div>
          </div>

          <!-- AI智能生成 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md hover:border-blue-500 dark:hover:border-blue-400 transition-all cursor-pointer group" @click="showAIModal = true">
            <div class="text-center">
              <div class="w-12 h-12 bg-blue-50 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-500 group-hover:text-white transition-all">
                <BulbOutlined class="text-xl text-blue-500 group-hover:text-white" />
              </div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">AI智能生成</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">描述需求，AI生成原型</p>
              <div class="inline-flex items-center px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400 text-xs rounded-full">
                🔥 热门功能
              </div>
            </div>
          </div>

          <!-- 空白画布 -->
          <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md hover:border-blue-500 dark:hover:border-blue-400 transition-all cursor-pointer group" @click="showBlankModal = true">
            <div class="text-center">
              <div class="w-12 h-12 bg-blue-50 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-500 group-hover:text-white transition-all">
                <EditOutlined class="text-xl text-blue-500 group-hover:text-white" />
              </div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">空白画布</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">从零开始自由创作</p>
              <div class="inline-flex items-center px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                专业设计师
              </div>
            </div>
          </div>
        </div>

        <!-- 常用模板预览 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">常用模板</h2>
            <a-button type="text" size="small" class="text-blue-500 hover:text-blue-600">
              查看全部
              <RightOutlined class="ml-1 text-xs" />
            </a-button>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div
              v-for="template in templates"
              :key="template.id"
              class="group cursor-pointer"
              @click="selectTemplate(template)"
            >
              <div class="aspect-[3/4] bg-gray-50 dark:bg-gray-700 rounded-lg mb-3 flex items-center justify-center border-2 border-transparent group-hover:border-blue-500 transition-all">
                <component :is="template.icon" class="text-2xl text-gray-400 group-hover:text-blue-500" />
              </div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">{{ template.name }}</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">{{ template.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI生成模态框 -->
    <a-modal
      v-model:open="showAIModal"
      title="AI智能生成原型"
      width="600px"
      @ok="handleAIGenerate"
      @cancel="showAIModal = false"
      :confirmLoading="aiLoading"
    >
      <div class="space-y-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            选择项目
          </label>
          <a-select v-model:value="aiForm.projectId" placeholder="选择关联项目" style="width: 100%">
            <a-select-option 
              v-for="project in projects" 
              :key="project.id" 
              :value="project.id"
            >
              {{ project.name }}
            </a-select-option>
          </a-select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            原型名称
          </label>
          <a-input v-model:value="aiForm.name" placeholder="输入原型名称" />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            描述您的想法
          </label>
          <a-textarea 
            v-model:value="aiForm.description" 
            placeholder="详细描述您想要创建的原型，包括功能、布局、风格等..."
            :rows="6"
            :maxlength="1000"
            show-count
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            原型类型
          </label>
          <a-radio-group v-model:value="aiForm.type">
            <a-radio value="wireframe">线框图</a-radio>
            <a-radio value="mockup">视觉稿</a-radio>
            <a-radio value="interactive">交互原型</a-radio>
          </a-radio-group>
        </div>
      </div>
    </a-modal>

    <!-- 模板选择模态框 -->
    <a-modal
      v-model:open="showTemplateModal"
      title="选择模板"
      width="800px"
      :footer="null"
    >
      <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div
          v-for="template in templates"
          :key="template.id"
          class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:border-blue-500 transition-all"
          @click="selectTemplate(template)"
        >
          <div class="aspect-[3/4] bg-gray-100 dark:bg-gray-700 rounded-lg mb-3 flex items-center justify-center">
            <component :is="template.icon" class="text-3xl text-gray-400" />
          </div>
          <h4 class="font-medium text-gray-900 dark:text-white mb-1">{{ template.name }}</h4>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ template.description }}</p>
        </div>
      </div>
    </a-modal>

    <!-- 空白画布模态框 -->
    <a-modal
      v-model:open="showBlankModal"
      title="创建空白原型"
      width="500px"
      @ok="handleBlankCreate"
      @cancel="showBlankModal = false"
    >
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            选择项目
          </label>
          <a-select v-model:value="blankForm.projectId" placeholder="选择关联项目" style="width: 100%">
            <a-select-option
              v-for="project in projects"
              :key="project.id"
              :value="project.id"
            >
              {{ project.name }}
            </a-select-option>
          </a-select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            原型名称
          </label>
          <a-input v-model:value="blankForm.name" placeholder="输入原型名称" />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            画布尺寸
          </label>
          <a-select v-model:value="blankForm.type" placeholder="选择画布尺寸" style="width: 100%">
            <a-select-option value="desktop">桌面端 (1440x900)</a-select-option>
            <a-select-option value="mobile">移动端 (375x812)</a-select-option>
            <a-select-option value="tablet">平板端 (768x1024)</a-select-option>
            <a-select-option value="custom">自定义尺寸</a-select-option>
          </a-select>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeftOutlined,
  BulbOutlined,
  EditOutlined,
  DesktopOutlined,
  MobileOutlined,
  TabletOutlined,
  ShoppingOutlined,
  QuestionCircleOutlined,
  AppstoreOutlined,
  RightOutlined
} from '@ant-design/icons-vue'
import { projectList } from '../config/projects'

const router = useRouter()

// 响应式数据
const showAIModal = ref(false)
const showTemplateModal = ref(false)
const showBlankModal = ref(false)
const aiLoading = ref(false)
const projects = ref(projectList)

const aiForm = reactive({
  projectId: null,
  name: '',
  description: '',
  type: 'wireframe'
})

const blankForm = reactive({
  projectId: null,
  name: '',
  type: 'wireframe'
})

// 模板数据
const templates = ref([
  {
    id: 1,
    name: 'Web应用',
    description: '适用于网站和Web应用的原型模板',
    icon: DesktopOutlined
  },
  {
    id: 2,
    name: '移动应用',
    description: '手机App界面设计模板',
    icon: MobileOutlined
  },
  {
    id: 3,
    name: '平板应用',
    description: '平板设备界面设计模板',
    icon: TabletOutlined
  },
  {
    id: 4,
    name: '电商平台',
    description: '电商网站和应用模板',
    icon: ShoppingOutlined
  }
])

// 事件处理
const goBack = () => {
  router.push('/aiProducts/prototypes')
}

const handleAIGenerate = async () => {
  aiLoading.value = true
  
  try {
    // 模拟AI生成过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    console.log('AI生成原型:', aiForm)
    showAIModal.value = false
    
    // 跳转到原型详情页面
    router.push('/aiProducts/prototypes/1')
  } catch (error) {
    console.error('AI生成失败:', error)
  } finally {
    aiLoading.value = false
  }
}

const selectTemplate = (template) => {
  console.log('选择模板:', template)
  // 跳转到原型编辑器，使用选中的模板
  router.push('/aiProducts/prototypes/1')
}

const handleBlankCreate = () => {
  console.log('创建空白原型:', blankForm)
  showBlankModal.value = false

  // 跳转到原型编辑器
  router.push('/aiProducts/prototypes/1')
}
</script>

<style scoped>
/* 自定义样式 */
</style>
