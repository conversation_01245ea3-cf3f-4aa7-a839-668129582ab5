<template>
  <div class="document-list-page min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">文档中心</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">管理项目文档，支持多人协作编辑</p>
      </div>
      <a-button type="primary" @click="navigateToCreate">
        <PlusOutlined />
        新建文档
      </a-button>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6 mb-6">
      <div class="flex items-center gap-4 flex-wrap">
        <!-- 搜索框 -->
        <div class="relative flex-1 min-w-[300px]">
          <SearchOutlined class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索文档标题、内容..."
            class="w-full pl-10 pr-4 py-2.5 bg-white/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-600 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
          />
        </div>

        <!-- 项目筛选 -->
        <a-select 
          v-model:value="projectFilter" 
          placeholder="所属项目" 
          style="width: 150px"
          allowClear
        >
          <a-select-option 
            v-for="project in projects" 
            :key="project.id" 
            :value="project.id"
          >
            {{ project.name }}
          </a-select-option>
        </a-select>

        <!-- 类型筛选 -->
        <a-select 
          v-model:value="typeFilter" 
          placeholder="文档类型" 
          style="width: 120px"
          allowClear
        >
          <a-select-option value="requirement">需求文档</a-select-option>
          <a-select-option value="design">设计文档</a-select-option>
          <a-select-option value="api">API文档</a-select-option>
          <a-select-option value="user-story">用户故事</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 文档列表 -->
    <div class="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6">
      <!-- 空状态 -->
      <div class="text-center py-20">
        <div class="w-32 h-32 mx-auto mb-6 bg-gradient-to-r from-green-100 to-teal-100 dark:from-green-900/30 dark:to-teal-900/30 rounded-2xl flex items-center justify-center">
          <FileTextOutlined class="text-6xl text-green-500" />
        </div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">开始创建您的第一个文档</h2>
        <p class="text-gray-500 dark:text-gray-400 mb-8 max-w-md mx-auto">
          使用我们的智能文档编辑器，快速创建专业的产品文档
        </p>
        
        <!-- 文档类型选项 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 max-w-4xl mx-auto mb-8">
          <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-500 transition-colors cursor-pointer">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <FileTextOutlined class="text-blue-500 text-xl" />
            </div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-1">需求文档</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">产品需求规格说明</p>
          </div>
          
          <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-purple-500 transition-colors cursor-pointer">
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <DesktopOutlined class="text-purple-500 text-xl" />
            </div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-1">设计文档</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">UI/UX设计规范</p>
          </div>
          
          <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-green-500 transition-colors cursor-pointer">
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <ApiOutlined class="text-green-500 text-xl" />
            </div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-1">API文档</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">接口设计文档</p>
          </div>
          
          <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-orange-500 transition-colors cursor-pointer">
            <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <UserOutlined class="text-orange-500 text-xl" />
            </div>
            <h3 class="font-medium text-gray-900 dark:text-white mb-1">用户故事</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">用户需求故事</p>
          </div>
        </div>
        
        <div class="flex items-center justify-center gap-4">
          <a-button type="primary" size="large" @click="navigateToCreate">
            <PlusOutlined />
            创建文档
          </a-button>
          <a-button size="large">
            <BookOutlined />
            查看模板
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusOutlined,
  SearchOutlined,
  FileTextOutlined,
  FileSearchOutlined,
  TeamOutlined,
  BulbOutlined,
  DesktopOutlined,
  ApiOutlined,
  UserOutlined,
  BookOutlined
} from '@ant-design/icons-vue'
import { projectList } from '../config/projects'
import { computed } from 'vue'

const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const projectFilter = ref<number>()
const typeFilter = ref<string>()
const projects = ref(projectList)

// 事件处理
const navigateToCreate = () => {
  router.push('/aiProducts/documents/create')
}
</script>

<style scoped>
/* 自定义样式 */
</style>
