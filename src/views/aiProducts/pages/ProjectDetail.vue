<template>
  <div class="project-detail-page">
    <!-- 返回按钮 -->
    <div class="mb-6">
      <a-button @click="goBack" class="flex items-center gap-2">
        <ArrowLeftOutlined />
        返回项目列表
      </a-button>
    </div>

    <!-- 项目详情内容 -->
    <div v-if="project">
      <!-- 项目头部信息 -->
      <div class="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-8 mb-8">
        <div class="flex items-start justify-between">
          <div class="flex items-start gap-6 flex-1">
            <!-- 项目图标 -->
            <div 
              :class="[
                'w-20 h-20 rounded-2xl flex items-center justify-center flex-shrink-0',
                iconBgClasses[project.type]
              ]"
            >
              <component :is="typeIcons[project.type]" class="text-white text-3xl" />
            </div>
            
            <!-- 项目信息 -->
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-3">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ project.name }}</h1>
                <span 
                  :class="[
                    'px-3 py-1 text-sm rounded-full font-medium',
                    statusStyles[project.status]
                  ]"
                >
                  {{ statusLabels[project.status] }}
                </span>
                <span 
                  :class="[
                    'px-3 py-1 text-sm rounded-lg font-medium',
                    typeTagStyles[project.type]
                  ]"
                >
                  {{ typeLabels[project.type] }}
                </span>
              </div>
              
              <p class="text-gray-600 dark:text-gray-400 text-lg mb-6 leading-relaxed">
                {{ project.description }}
              </p>
              
              <!-- 项目元信息 -->
              <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">项目负责人</div>
                  <div class="font-semibold text-gray-900 dark:text-white">{{ project.owner }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">团队成员</div>
                  <div class="font-semibold text-gray-900 dark:text-white">{{ project.team.length }} 人</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">创建时间</div>
                  <div class="font-semibold text-gray-900 dark:text-white">{{ formatDate(project.createdAt) }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 mb-1">最后更新</div>
                  <div class="font-semibold text-gray-900 dark:text-white">{{ formatDate(project.updatedAt) }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex items-center gap-3">
            <a-button size="large">
              <EditOutlined />
              编辑项目
            </a-button>
            <a-dropdown>
              <a-button size="large">
                <MoreOutlined />
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <ShareAltOutlined class="mr-2" />
                    分享项目
                  </a-menu-item>
                  <a-menu-item>
                    <ExportOutlined class="mr-2" />
                    导出项目
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item class="text-red-500">
                    <DeleteOutlined class="mr-2" />
                    删除项目
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
        
        <!-- 进度条 -->
        <div class="mt-8">
          <div class="flex items-center justify-between mb-3">
            <span class="text-lg font-semibold text-gray-900 dark:text-white">项目进度</span>
            <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ project.progress }}%</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4">
            <div 
              :class="[
                'h-4 rounded-full transition-all duration-1000',
                progressClasses[project.type]
              ]"
              :style="{ width: `${project.progress}%` }"
            ></div>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <StatsCard
          title="原型数量"
          :value="project.prototypes?.length || 0"
          icon="DesktopOutlined"
          color="blue"
        />
        <StatsCard
          title="文档数量"
          :value="project.documents?.length || 0"
          icon="FileTextOutlined"
          color="green"
        />
        <StatsCard
          title="需求数量"
          :value="project.requirements?.length || 0"
          icon="UnorderedListOutlined"
          color="orange"
        />
        <StatsCard
          title="团队成员"
          :value="project.team.length"
          icon="TeamOutlined"
          color="purple"
        />
      </div>

      <!-- 标签页内容 -->
      <div class="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
        <a-tabs v-model:activeKey="activeTab" size="large" class="project-detail-tabs">
          <a-tab-pane key="overview" tab="项目概览">
            <div class="p-6">
              <ProjectOverview :project="project" />
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="prototypes">
            <template #tab>
              <span class="flex items-center gap-2">
                <DesktopOutlined />
                原型设计 ({{ project.prototypes?.length || 0 }})
              </span>
            </template>
            <div class="p-6">
              <div class="text-center py-12">
                <DesktopOutlined class="text-4xl text-gray-400 mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">原型设计功能</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">即将推出，敬请期待</p>
                <a-button type="primary">
                  <PlusOutlined />
                  创建原型
                </a-button>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="documents">
            <template #tab>
              <span class="flex items-center gap-2">
                <FileTextOutlined />
                文档管理 ({{ project.documents?.length || 0 }})
              </span>
            </template>
            <div class="p-6">
              <div class="text-center py-12">
                <FileTextOutlined class="text-4xl text-gray-400 mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">文档管理功能</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">即将推出，敬请期待</p>
                <a-button type="primary">
                  <PlusOutlined />
                  创建文档
                </a-button>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="requirements">
            <template #tab>
              <span class="flex items-center gap-2">
                <UnorderedListOutlined />
                需求管理 ({{ project.requirements?.length || 0 }})
              </span>
            </template>
            <div class="p-6">
              <div class="text-center py-12">
                <UnorderedListOutlined class="text-4xl text-gray-400 mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">需求管理功能</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">即将推出，敬请期待</p>
                <a-button type="primary">
                  <PlusOutlined />
                  添加需求
                </a-button>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- 项目不存在 -->
    <div v-else class="text-center py-20">
      <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
        <ExclamationCircleOutlined class="text-4xl text-gray-400" />
      </div>
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">项目不存在</h2>
      <p class="text-gray-500 dark:text-gray-400 mb-6">您访问的项目可能已被删除或不存在</p>
      <a-button type="primary" @click="goBack">
        返回项目列表
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  ArrowLeftOutlined,
  EditOutlined,
  MoreOutlined,
  ShareAltOutlined,
  ExportOutlined,
  DeleteOutlined,
  DesktopOutlined,
  FileTextOutlined,
  UnorderedListOutlined,
  TeamOutlined,
  PlusOutlined,
  ExclamationCircleOutlined,
  GlobalOutlined,
  MobileOutlined,
  LaptopOutlined,
  ApiOutlined
} from '@ant-design/icons-vue'
import StatsCard from '../components/StatsCard.vue'
import ProjectOverview from '../components/ProjectOverview.vue'
import { projectList } from '../config/projects'
import type { Project } from '../config/projects'

const route = useRoute()
const router = useRouter()

// 响应式数据
const activeTab = ref('overview')
const project = ref<Project | null>(null)

// 类型图标映射
const typeIcons = {
  web: GlobalOutlined,
  mobile: MobileOutlined,
  desktop: LaptopOutlined,
  api: ApiOutlined
}

// 样式映射
const iconBgClasses = {
  web: 'bg-blue-500',
  mobile: 'bg-purple-500',
  desktop: 'bg-indigo-500',
  api: 'bg-orange-500'
}

const statusStyles = {
  active: 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400',
  draft: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400',
  archived: 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
}

const statusLabels = {
  active: '进行中',
  draft: '草稿',
  archived: '已归档'
}

const typeTagStyles = {
  web: 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400',
  mobile: 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400',
  desktop: 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-400',
  api: 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400'
}

const typeLabels = {
  web: 'Web应用',
  mobile: '移动应用',
  desktop: '桌面应用',
  api: 'API服务'
}

const progressClasses = {
  web: 'bg-blue-400',
  mobile: 'bg-purple-400',
  desktop: 'bg-indigo-400',
  api: 'bg-orange-400'
}

// 工具函数
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })
}

// 事件处理
const goBack = () => {
  router.push('/aiProducts/projects')
}

// 生命周期
onMounted(() => {
  const projectId = Number(route.params.id)
  project.value = projectList.find(p => p.id === projectId) || null
})
</script>

<style scoped>
.project-detail-tabs :deep(.ant-tabs-nav) {
  padding: 0 24px;
  margin-bottom: 0;
}

.project-detail-tabs :deep(.ant-tabs-tab) {
  padding: 16px 24px;
  font-weight: 500;
}

.project-detail-tabs :deep(.ant-tabs-content-holder) {
  padding: 0;
}
</style>
