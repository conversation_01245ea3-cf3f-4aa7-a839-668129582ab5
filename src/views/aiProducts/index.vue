<template>
  <div class="product-workspace h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden">
    <!-- 顶部导航栏 -->
    <div class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div class="flex items-center justify-between">
        <!-- 左侧标题 -->
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
            <RocketOutlined class="text-white text-lg" />
          </div>
          <div>
            <h1 class="text-xl font-bold text-gray-900 dark:text-white">产品工作台</h1>
            <p class="text-sm text-gray-500 dark:text-gray-400">智能化产品开发平台</p>
          </div>
        </div>

        <!-- 右侧操作区 -->
        <div class="flex items-center gap-4">
          <!-- 搜索框 -->
          <div class="relative">
            <SearchOutlined class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索项目、原型、文档..."
              class="w-80 pl-10 pr-4 py-2.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
            />
          </div>

          <!-- 快速操作 -->
          <div class="flex items-center gap-2">
            <a-tooltip title="创建原型">
              <button
                @click="quickCreate('prototype')"
                class="w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors flex items-center justify-center"
              >
                <DesktopOutlined />
              </button>
            </a-tooltip>

            <a-tooltip title="创建文档">
              <button
                @click="quickCreate('document')"
                class="w-10 h-10 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors flex items-center justify-center"
              >
                <FileTextOutlined />
              </button>
            </a-tooltip>

            <a-tooltip title="创建项目">
              <button
                @click="showCreateModal = true"
                class="px-4 py-2.5 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-all flex items-center gap-2 font-medium"
              >
                <PlusOutlined />
                新建项目
              </button>
            </a-tooltip>
          </div>

          <!-- 用户头像 -->
          <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center cursor-pointer">
            <UserOutlined class="text-white" />
          </div>
        </div>
      </div>
    </div>

    <!-- 工作台主要内容 - 一屏显示 -->
    <div class="h-[calc(100vh-80px)] overflow-hidden p-6">
      <!-- 顶部统计区域 -->
      <div class="grid grid-cols-4 gap-4 mb-6">
          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">总项目</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ projects.length }}</p>
              </div>
              <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                <FolderOutlined class="text-white" />
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">进行中</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ activeProjects }}</p>
              </div>
              <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                <PlayCircleOutlined class="text-white" />
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">原型数</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalPrototypes }}</p>
              </div>
              <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                <DesktopOutlined class="text-white" />
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">团队成员</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ totalMembers }}</p>
              </div>
              <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                <TeamOutlined class="text-white" />
              </div>
            </div>
          </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="grid grid-cols-3 gap-6 h-[calc(100%-140px)]">
        <!-- 左侧：项目列表 -->
        <div class="col-span-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white">项目概览</h2>
              <a-button type="link" size="small" @click="navigateTo('/aiProducts/projects')">
                查看全部
              </a-button>
            </div>

            <div class="space-y-3 h-[calc(100%-60px)] overflow-y-auto">
              <div
                v-for="project in projects.slice(0, 6)"
                :key="project.id"
                class="flex items-center gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-colors"
                @click="navigateTo(`/aiProducts/projects/${project.id}`)"
              >
                <div :class="['w-10 h-10 rounded-lg flex items-center justify-center', getProjectIconBg(project.type)]">
                  <component :is="getProjectIcon(project.type)" class="text-white" />
                </div>
                <div class="flex-1 min-w-0">
                  <div class="flex items-center gap-2">
                    <h3 class="font-medium text-gray-900 dark:text-white truncate">{{ project.name }}</h3>
                    <span :class="['px-2 py-0.5 text-xs rounded-full', getStatusStyle(project.status)]">
                      {{ getStatusLabel(project.status) }}
                    </span>
                  </div>
                  <p class="text-sm text-gray-500 dark:text-gray-400 truncate">{{ project.description }}</p>
                  <div class="flex items-center gap-4 mt-1">
                    <span class="text-xs text-gray-400">{{ project.progress }}% 完成</span>
                    <span class="text-xs text-gray-400">{{ project.team.length }}人团队</span>
                  </div>
                </div>
              </div>
          </div>
        </div>

        <!-- 右侧：快速操作和活动 -->
        <div class="space-y-6">
            <!-- 快速操作 -->
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">快速操作</h3>
              <div class="space-y-3">
                <button
                  @click="showCreateModal = true"
                  class="w-full flex items-center gap-3 p-3 text-left hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                >
                  <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <PlusOutlined class="text-white text-sm" />
                  </div>
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white">新建项目</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">创建新的产品项目</p>
                  </div>
                </button>

                <button
                  @click="navigateTo('/aiProducts/prototypes/create')"
                  class="w-full flex items-center gap-3 p-3 text-left hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-lg transition-colors"
                >
                  <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                    <DesktopOutlined class="text-white text-sm" />
                  </div>
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white">创建原型</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">AI辅助原型设计</p>
                  </div>
                </button>

                <button
                  @click="navigateTo('/aiProducts/documents/create')"
                  class="w-full flex items-center gap-3 p-3 text-left hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                >
                  <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <FileTextOutlined class="text-white text-sm" />
                  </div>
                  <div>
                    <p class="font-medium text-gray-900 dark:text-white">写文档</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">创建产品文档</p>
                  </div>
                </button>
              </div>
            </div>

            <!-- 最近活动 -->
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">最近活动</h3>
              <div class="space-y-3">
                <div v-for="activity in recentActivities" :key="activity.id" class="flex items-start gap-3">
                  <div class="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <component :is="activity.icon" class="text-blue-500 text-xs" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-sm text-gray-900 dark:text-white">{{ activity.description }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ formatTime(activity.time) }}</p>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建项目模态框 -->
    <CreateProjectModal
      v-model:visible="showCreateModal"
      @confirm="handleCreateProject"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import CreateProjectModal from './components/CreateProjectModal.vue'
import { projectList } from './config/projects'
import {
    SearchOutlined,
    PlusOutlined,
    RocketOutlined,
    DesktopOutlined,
    FileTextOutlined,
    UserOutlined,
    FolderOutlined,
    PlayCircleOutlined,
    TeamOutlined,
    GlobalOutlined,
    MobileOutlined,
    LaptopOutlined,
    ApiOutlined,
    CheckCircleOutlined,
    FileAddOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

// 响应式数据
const projects = ref(projectList)
const searchKeyword = ref('')
const showCreateModal = ref(false)

// 计算属性
const activeProjects = computed(() =>
  projects.value.filter(p => p.status === 'active').length
)

const totalPrototypes = computed(() =>
  projects.value.reduce((total, project) => total + project.prototypes.length, 0)
)

const totalMembers = computed(() => {
  const allMembers = new Set()
  projects.value.forEach(project => {
    project.team.forEach(member => allMembers.add(member))
  })
  return allMembers.size
})

const recentActivities = computed(() => [
  {
    id: 1,
    description: '张三更新了智能客服系统的原型设计',
    time: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    icon: DesktopOutlined
  },
  {
    id: 2,
    description: '李四完成了数据分析平台的API文档',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    icon: FileTextOutlined
  },
  {
    id: 3,
    description: '王五创建了新的需求：用户权限管理',
    time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
    icon: FileAddOutlined
  },
  {
    id: 4,
    description: '赵六完成了企业协作平台的项目管理模块',
    time: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
    icon: CheckCircleOutlined
  }
])

// 导航方法
const navigateTo = (path: string) => {
  router.push(path)
}

// 快速创建
const quickCreate = (type: string) => {
  switch (type) {
    case 'prototype':
      navigateTo('/aiProducts/prototypes/create')
      break
    case 'document':
      navigateTo('/aiProducts/documents/create')
      break
  }
}

// 工具方法
const getProjectIcon = (type: string) => {
  const iconMap = {
    web: GlobalOutlined,
    mobile: MobileOutlined,
    desktop: LaptopOutlined,
    api: ApiOutlined
  }
  return iconMap[type] || GlobalOutlined
}

const getProjectIconBg = (type: string) => {
  const bgMap = {
    web: 'bg-blue-500',
    mobile: 'bg-purple-500',
    desktop: 'bg-indigo-500',
    api: 'bg-orange-500'
  }
  return bgMap[type] || 'bg-blue-500'
}

const getStatusStyle = (status: string) => {
  const styleMap = {
    active: 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400',
    draft: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400',
    archived: 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
  }
  return styleMap[status] || 'bg-gray-100 text-gray-700'
}

const getStatusLabel = (status: string) => {
  const labelMap = {
    active: '进行中',
    draft: '草稿',
    archived: '已归档'
  }
  return labelMap[status] || '未知'
}

const formatTime = (timeString: string) => {
  const time = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

// 项目管理
const handleCreateProject = (projectData: any) => {
  const newProject = {
    id: Date.now(),
    ...projectData,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    prototypes: [],
    documents: [],
    requirements: []
  }
  projects.value.unshift(newProject)
  showCreateModal.value = false
}
</script>

<style scoped>
.product-workspace {
  min-height: 100vh;
}

/* 自定义滚动条 */
:deep(.ant-layout-content) {
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

:deep(.ant-layout-content)::-webkit-scrollbar {
  width: 6px;
}

:deep(.ant-layout-content)::-webkit-scrollbar-track {
  background: transparent;
}

:deep(.ant-layout-content)::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
  border: transparent;
}

/* 卡片悬停效果 */
.project-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 渐变背景动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-bg {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}
</style>
