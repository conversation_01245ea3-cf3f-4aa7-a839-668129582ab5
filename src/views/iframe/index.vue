<template>
  <div class="w-full h-screen overflow-hidden">
    <!-- 有效平台展示区 -->
    <div v-if="targetUrl" class="w-full h-full">
      <iframe :src="targetUrl" class="w-full h-full border-0" frameborder="0" allowfullscreen>
      </iframe>
    </div>
    <!-- 无效平台提示 -->
    <div v-else class="flex items-center justify-center h-full bg-gray-50">
      <div class="text-center">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">提示</h1>
        <p class="text-gray-600 mb-8">请从正确的入口访问系统</p>
        <a-button type="primary" @click="goToHome">返回首页</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getTargetUrl, getPlatformTitle } from './constant'

const route = useRoute()
const router = useRouter()
const platform = ref('')
const targetUrl = ref('')

// 监听platform变化，更新targetUrl和页面标题
watch(() => route.query.platform, (newPlatform) => {
  if (newPlatform) {
    platform.value = newPlatform
    targetUrl.value = getTargetUrl(newPlatform)
    document.title = getPlatformTitle(newPlatform)
  }
}, { immediate: true })

const goToHome = () => {
  router.push('/')
}
</script>
