export const columns = [
  {
    type: 'selection',
    width: 60,
    align: 'center'
  },
  {
    title: '菜单名称',
    dataIndex: 'name',
    width: 200
  },
  {
    title: '菜单类型',
    dataIndex: 'menuType',
    width: 150,
    customRender: ({ text }) => {
      return text === 0 ? '一级菜单' : '子菜单'
    }
  },
  {
    title: '图标',
    dataIndex: 'icon',
    width: 100
  },
  // {
  //   title: '组件',
  //   dataIndex: 'component',
  //   width: 200
  // },
  {
    title: '路径',
    dataIndex: 'url',
    width: 150
  },
  {
    title: '排序',
    dataIndex: 'sortNo',
    width: 100
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 150,
    slots: { customRender: 'actionColumn' } 
  }
]
export const searchSchema = [
  {
    key: 'name',
    label: '菜单名称',
    type: 'input',
    placeholder: '请输入菜单名称'
  }
]