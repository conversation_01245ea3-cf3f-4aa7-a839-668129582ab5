<template>
  <a-modal
    :visible="visible"
    :title="formData?.id ? '编辑菜单' : '新增菜单'"
    @cancel="handleCancel"
    @ok="handleSubmit"
  >
    <a-form
      ref="formRef"
      :model="form"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <a-form-item
        label="菜单类型"
        name="menuType"
        :rules="[{ required: true, message: '请选择菜单类型' }]"
      >
        <a-radio-group v-model:value="form.menuType">
          <a-radio :value="0">
            一级菜单
          </a-radio>
          <a-radio :value="1">
            子菜单
          </a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item
        v-if="form.menuType === 1"
        label="上级菜单"
        name="parentId"
        :rules="[{ required: true, message: '请选择上级菜单' }]"
      >
        <a-tree-select
          v-model:value="form.parentId"
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="menuTreeData"
          placeholder="请选择上级菜单"
          tree-default-expand-all
          :field-names="{
            children: 'children',
            label: 'name',
            value: 'id'
          }"
        />
      </a-form-item>

      <a-form-item
        label="菜单名称"
        name="name"
        :rules="[{ required: true, message: '请输入菜单名称' }]"
      >
        <a-input
          v-model:value="form.name"
          placeholder="请输入菜单名称"
        />
      </a-form-item>

      <a-form-item
        label="路由地址"
        name="url"
        :rules="[{ required: true, message: '请输入路由地址' }]"
      >
        <a-input
          v-model:value="form.url"
          placeholder="请输入路由地址"
        />
      </a-form-item>
      <a-form-item
        v-if="form.menuType === 0"
        label="所属区域"
        name="region"
        :rules="[{ required: true, message: '请输入所属区域' }]"
      >
        <a-select
          v-model:value="form.region"
          placeholder="请选择所属区域"
          style="width: 100%"
        >
          <a-select-option value="fixedListMap">
            导航菜单
          </a-select-option>
          <a-select-option value="engineerBusinessMenu">
            AI工程师业务模块
          </a-select-option>
          <a-select-option value="iterationBusinessMenu">
            AI迭代质量门业务模块
          </a-select-option>
          <a-select-option value="financeBusinessMenu">
            AI金融业务模块
          </a-select-option>
          <a-select-option value="userConfig">
            用户设置
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item
        label="排序"
        name="sortNo"
      >
        <a-input-number
          v-model:value="form.sortNo"
          :min="0"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item
        label="图标"
        name="icon"
      >
        <a-input
          v-model:value="form.icon"
          placeholder="请输入图标"
        />
      </a-form-item>
      <a-form-item
        label="叶子节点"
        name="isLeaf"
      >
        <a-radio-group v-model:value="form.isLeaf">
          <a-radio :value="0">
            否
          </a-radio>
          <a-radio :value="1">
            是
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="隐藏菜单"
        name="hidden"
      >
        <a-switch v-model:checked="form.hidden" />
      </a-form-item>
      <a-form-item
        label="描述"
        name="description"
      >
        <a-textarea
          v-model:value="form.description"
          placeholder="请输入描述"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import {
  addPermission,
  updatePermission,
  getPermissionList,
  type PermissionModel
} from '@/apis/system'

const props = defineProps<{
  visible: boolean
  formData?: PermissionModel | null
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

const formRef = ref<FormInstance>()
const menuTreeData = ref<PermissionModel[]>([])

const form = reactive<PermissionModel>({
  id: '',
  menuType: 0,
  parentId: '0',
  name: '',
  url: '',
  sortNo: 1,
  icon: '',
  isLeaf: 0,
  hidden: 0,
  region: '',
  description: ''
})

// 获取菜单树数据
const fetchMenuTree = async () => {
  try {
    const response = await getPermissionList({
      column: 'sortNo',
      order: 'asc'
    })
    if (response.code === 200) {
      menuTreeData.value = response.data
    }
  } catch (error) {
    message.error('获取菜单列表失败')
  }
}

// 监听菜单类型变化
watch(
  () => form.menuType,
  (newVal) => {
    if (newVal === 0) {
      form.parentId = '0'
    } else if (newVal === 1 && form.parentId === '0') {
      form.parentId = undefined
    }
    form.isLeaf = newVal === 1 ? 1 : 0
  }
)

// 监听表单数据变化
watch(
  () => props.formData,
  (val) => {
    if (val) {
      Object.assign(form, val)
    } else {
      Object.assign(form, {
        id: '',
        menuType: 0,
        parentId: '0',
        name: '',
        url: '',
        sortNo: 1,
        icon: '',
        isLeaf: 0,
        hidden: 0,
        region: '',
        description: ''
      })
    }
  },
  { deep: true }
)

// 处理取消
const handleCancel = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    const api = form.id ? updatePermission : addPermission
    const response = await api(form)

    if (response.code === 200) {
      message.success(form.id ? '更新成功' : '添加成功')
      emit('success')
      emit('update:visible', false)
      formRef.value?.resetFields()
    }
  } catch (error) {
    message.error('表单验证失败')
  }
}

onMounted(() => {
  fetchMenuTree()
})
</script>

<style lang="scss" scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>