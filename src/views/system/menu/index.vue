<template>
  <div class="menu-container">
    <BaseTable
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :search-schema="searchSchema"
      :expandedKeys="expandedKeys"
      :pagination="{
        total: dataSource.length,
        current: queryParams.pageNo,
        pageSize: queryParams.pageSize,
        onChange: handlePageChange
      }"
      :showPagination="false"
      @search="handleSearch"
      @reset="handleReset"
      @expand="handleExpand"
    >
      <template #actions>
        <div class="action-buttons">
          <a-button
            type="primary"
            @click="handleAdd"
          >
            新增菜单
          </a-button>
          <a-button @click="handleExpandAll">
            {{ isExpandAll ? '折叠全部' : '展开全部' }}
          </a-button>
        </div>
      </template>

      <!-- 菜单名称列 -->
      <template #menuName="{ record }">
        <div class="flex items-center gap-2">
          <component
            :is="record.icon"
            v-if="record.icon"
          />
          <span>{{ record.name }}</span>
        </div>
      </template>
      <!-- 操作列 -->
      <template #actionColumn="{ record }">
        <!-- 修改这里的插槽名称 -->
        <a-space>
          <a-button
            type="link"
            size="small"
            @click="handleEdit(record)"
          >
            编辑
          </a-button>
          <a-button
            type="link"
            size="small"
            @click="handleAddSub(record)"
          >
            添加下级
          </a-button>
          <a-button
            type="link"
            size="small"
            @click="handleDelete(record)"
          >
            删除
          </a-button>
        </a-space>
      </template>
    </BaseTable>

    <!-- 菜单表单 -->
    <MenuForm
      v-model:visible="formVisible"
      :form-data="currentRecord"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  getPermissionList,
  deletePermission,
  type PermissionModel,
  type PermissionQueryDTO
} from '@/apis/system'

import BaseTable from '@/components/BaseTable/index.vue'
import MenuForm from './components/MenuForm.vue'
import { columns, searchSchema } from './columns'

const dataSource = ref<PermissionModel[]>([])
const loading = ref(false)
const formVisible = ref(false)
const currentRecord = ref<PermissionModel | null>(null)
const expandedKeys = ref<(string | number)[]>([])
const allKeys = ref<(string | number)[]>([])
// 计算是否全部展开
const isExpandAll = computed(() => {
  return expandedKeys.value.length === allKeys.value.length
})

// 查询参数
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  name: '',
  column: 'sortNo',
  order: 'asc'
})

// 修改 fetchData 函数，调整参数格式
const fetchData = async (params?: Partial<PermissionQueryDTO>) => {
  loading.value = true
  try {
    // 合并查询参数
    const requestParams = {
      ...queryParams.value,
      ...params
    }

    const response = await getPermissionList(requestParams)
    if (response.code === 200) {
      dataSource.value = response.data
      // 更新所有可展开的节点 key
      allKeys.value = getAllKeys(response.data)
      // 如果是首次加载，默认展开全部
      if (expandedKeys.value.length === 0) {
        expandedKeys.value = [...allKeys.value]
      }
    } else {
      message.error(response.message || '获取数据失败')
      resetState()
    }
  } catch (error) {
    message.error('获取数据失败')
    resetState()
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (params: PermissionQueryDTO) => {
  // 更新查询参数
  queryParams.value = {
    ...queryParams.value,
    ...params,
    pageNo: 1 // 搜索时重置页码
  }
  fetchData()
}

// 重置处理
const handleReset = () => {
  // 重置查询参数
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    name: '',
    column: 'sortNo',
    order: 'asc'
  }
  fetchData()
}

// 分页处理
const handlePageChange = (page: number, pageSize: number) => {
  queryParams.value.pageNo = page
  queryParams.value.pageSize = pageSize
  fetchData()
}

// 新增处理
const handleAdd = (record?: PermissionModel) => {
  currentRecord.value = record
    ? { parentId: record.id, menuType: 1 } // 子菜单
    : { parentId: '0', menuType: 0 } // 一级菜单
  formVisible.value = true
}

// 编辑处理
const handleEdit = (record: PermissionModel) => {
  currentRecord.value = { ...record }
  formVisible.value = true
}
// 删除处理
const handleDelete = (record: PermissionModel) => {
  if (!record?.id) {
    message.error('无效的记录ID')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该菜单吗？如果存在子菜单，将一并删除！',
    async onOk() {
      try {
        const response = await deletePermission(record.id)
        if (response.code === 200) {
          message.success('删除成功')
          fetchData()
        } else {
          message.error(response.message || '删除失败')
        }
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 获取所有可展开的节点 key
const getAllKeys = (data: PermissionModel[]): (string | number)[] => {
  const keys: (string | number)[] = []
  const traverse = (items: PermissionModel[]) => {
    items.forEach((item) => {
      if (item.id) {
        keys.push(item.id)
      }
      if (item.children && item.children.length > 0) {
        traverse(item.children)
      }
    })
  }
  traverse(data)
  return keys
}
// 添加展开事件处理函数
const handleExpand = (expanded: boolean, record: PermissionModel) => {
  if (expanded) {
    // 展开时添加 key
    if (!expandedKeys.value.includes(record.id!)) {
      expandedKeys.value = [...expandedKeys.value, record.id!]
    }
  } else {
    // 折叠时移除 key
    expandedKeys.value = expandedKeys.value.filter((key) => key !== record.id)
  }
}
// 展开/折叠切换
const handleExpandAll = () => {
  if (isExpandAll.value) {
    expandedKeys.value = [] // 折叠全部
  } else {
    expandedKeys.value = [...allKeys.value] // 展开全部
  }
}
// 添加重置状态函数
const resetState = () => {
  dataSource.value = []
  allKeys.value = []
  expandedKeys.value = []
}
// 表单提交成功处理
const handleSuccess = () => {
  fetchData()
}
// 添加新的处理函数
const handleAddSub = (record: PermissionModel) => {
  currentRecord.value = {
    parentId: record.id,
    menuType: 1
  }
  formVisible.value = true
}
// 初始化
fetchData()
</script>

<style lang="scss" scoped>
.menu-container {
  padding: 16px;
  background: var(--global-background-color);
  border-radius: 4px;
  height: calc(100vh - 100px);

  .action-buttons {
    display: flex;
    gap: 8px;

    :deep(.ant-btn) {
      margin: 0;

      .anticon {
        margin-right: 4px;
      }
    }
  }

  // 确保表格区域没有背景色
  :deep(.ant-table-wrapper) {
    background: transparent;
  }

  :deep(.ant-table) {
    background: transparent;
  }

  :deep(.ant-table-container) {
    background: transparent;
  }
}
</style>
