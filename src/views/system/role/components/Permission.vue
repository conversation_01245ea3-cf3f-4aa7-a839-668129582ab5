<template>
  <a-modal
    :visible="visible"
    title="角色权限配置"
    :width="600"
    :footer="null"
    @cancel="handleClose"
  >
    <template v-if="roleInfo">
      <div class="mb-4">
        <span class="font-medium">所拥有的权限</span>
      </div>
      <a-spin :spinning="loading">
        <div class="permission-tree-container">
          <a-tree
            v-model:checkedKeys="checkedKeys"
            v-model:expandedKeys="expandedKeys"
            :tree-data="permissionTree"
            checkable
            :selectable="false"
            :default-expand-all="true"
            @check="handleCheck"
          >
            <template #title="{ title }">
              <span>{{ title }}</span>
            </template>
          </a-tree>
        </div>
      </a-spin>
    </template>

    <div class="modal-footer">
      <a-space>
        <a-button @click="handleClose">
          取消
        </a-button>
        <a-button
          type="primary"
          :loading="saving"
          @click="handleSave"
        >
          仅保存
        </a-button>
        <a-button
          type="primary"
          :loading="saving"
          @click="handleSaveAndClose"
        >
          保存并关闭
        </a-button>
      </a-space>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { TreeProps } from 'ant-design-vue'
import { 
  queryRolePermission, 
  saveRolePermissions,
  queryTreeList,
  type RoleModel,
  type PermissionTreeNode 
} from '@/apis/system'

const props = defineProps<{
  visible: boolean
  roleInfo?: RoleModel | null
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}>()

// 状态定义
const loading = ref(false)
const saving = ref(false)
const checkedKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const permissionTree = ref<PermissionTreeNode[]>([])

// 监听可见性变化
watch(
  () => props.visible,
  async (newVisible) => {
    if (newVisible) {
      await fetchPermissionTree()
      if (props.roleInfo?.id) {
        await fetchRolePermissions(props.roleInfo.id)
      }
    }
  }
)

// 获取权限树
const fetchPermissionTree = async () => {
  loading.value = true
  try {
    const { data, code, message: msg } = await queryTreeList()
    if (code === 200 && data) {
      permissionTree.value = data.treeList.map(item => ({
        key: item.key,
        title: item.slotTitle,
        children: item.children?.map(child => ({
          key: child.key,
          title: child.slotTitle,
          children: child.children || []
        })) || []
      }))
      expandedKeys.value = getAllKeys(permissionTree.value)
    } else {
      message.error(msg || '获取权限数据失败')
    }
  } catch (error) {
    message.error('获取权限数据失败')
  } finally {
    loading.value = false
  }
}

// 获取所有节点的key
const getAllKeys = (tree: PermissionTreeNode[]): string[] => {
  const keys: string[] = []
  const traverse = (nodes: PermissionTreeNode[]) => {
    nodes.forEach(node => {
      if (node.key) {
        keys.push(node.key)
      }
      if (node.children?.length) {
        traverse(node.children)
      }
    })
  }
  traverse(tree)
  return keys
}

// 获取角色权限
const fetchRolePermissions = async (roleId: string) => {
  try {
    const response = await queryRolePermission(roleId)
    if (response.code === 200) {
      checkedKeys.value = response.data
    } else {
      message.error(response.message || '获取角色权限失败')
    }
  } catch (error) {
    message.error('获取角色权限失败')
  }
}

// 处理节点选中
const handleCheck: TreeProps['onCheck'] = (checked) => {
  if (Array.isArray(checked)) {
    checkedKeys.value = checked
  }
}

// 保存权限
const savePermissions = async () => {
  if (!props.roleInfo?.id) {
    message.error('无效的角色ID')
    return false
  }

  saving.value = true
  try {
    const response = await saveRolePermissions({
      roleId: props.roleInfo.id,
      permissionIds: checkedKeys.value.join(',')
    })
    
    if (response.code === 200) {
      message.success('保存成功')
      emit('success')
      return true
    } else {
      message.error(response.message || '保存失败')
      return false
    }
  } catch (error) {
    console.error('保存角色权限失败:', error)
    message.error('保存失败')
    return false
  } finally {
    saving.value = false
  }
}

// 事件处理
const handleClose = () => {
  emit('update:visible', false)
  checkedKeys.value = []
  expandedKeys.value = []
}

const handleSave = async () => {
  await savePermissions()
}

const handleSaveAndClose = async () => {
  const success = await savePermissions()
  if (success) {
    handleClose()
  }
}
</script>

<style lang="scss" scoped>
.permission-tree-container {
  max-height: 500px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;
}

.modal-footer {
  text-align: right;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 16px;
}

:deep(.ant-tree) {
  background: transparent;
}

:deep(.ant-tree-treenode) {
  width: 100%;
  
  .ant-tree-node-content-wrapper {
    flex: 1;
  }
}

:deep(.ant-modal-body) {
  padding: 24px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}
</style> 