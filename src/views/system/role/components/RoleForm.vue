<template>
  <a-modal
    :visible="visible"
    :title="formData ? '编辑角色' : '新增角色'"
    @cancel="handleCancel"
    @ok="handleSubmit"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <a-form-item
        label="角色名称"
        name="roleName"
      >
        <a-input
          v-model:value="form.roleName"
          placeholder="请输入角色名称"
          allow-clear
        />
      </a-form-item>

      <a-form-item
        label="角色编码"
        name="roleCode"
      >
        <a-input
          v-model:value="form.roleCode"
          placeholder="请输入角色编码，为空时自动生成"
          allow-clear
        />
      </a-form-item>

      <a-form-item
        label="角色描述"
        name="description"
      >
        <a-textarea
          v-model:value="form.description"
          placeholder="请输入角色描述"
          :rows="4"
          allow-clear
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { createRole, updateRole, type RoleModel } from '@/apis/system'

const props = defineProps<{
  visible: boolean
  formData?: RoleModel | null
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}>()

const formRef = ref<FormInstance>()
const form = ref<RoleModel>({
  roleName: '',
  roleCode: '',
  description: ''
})

const rules = {
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
}

watch(() => props.formData, (newVal) => {
  if (newVal) {
    form.value = { ...newVal }
  } else {
    form.value = {
      roleName: '',
      roleCode: '',
      description: ''
    }
  }
}, { deep: true })

const handleCancel = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    const api = form.value.id ? updateRole : createRole
    const response = await api(form.value)
    
    if (response.code === 200) {
      message.success(form.value.id ? '更新成功' : '添加成功')
      emit('success')
      handleCancel()
    } else {
      message.error(response.message || (form.value.id ? '更新失败' : '添加失败'))
    }
  } catch (error) {
    console.error('表单提交失败:', error)
    message.error('表单验证失败')
  }
}
</script>

<style lang="scss" scoped>
.drawer-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .ant-form {
    flex: 1;
    overflow-y: auto;
  }
}
:deep(.ant-form) {
  .ant-form-item {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .ant-form-item-label {
    label {
      height: 32px;
      &::before {
        content: '*';
        color: #ff4d4f;
        margin-right: 4px;
      }
    }
  }

  .ant-input-affix-wrapper {
    .ant-input {
      padding-left: 4px;
    }
  }
}

.drawer-footer {
  margin-top: 24px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
  background: #fff;
}
</style>