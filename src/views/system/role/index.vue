<template>
  <div class="role-container">
    <BaseTable
      ref="tableRef"
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :search-schema="searchSchema"
      :total="total"
      :current="current"
      :pageSize="pageSize"
      @search="handleSearch"
      @reset="handleReset"
      @page-change="handlePageChange"
    >
      <template #actions>
        <a-space>
          <a-button
            type="primary"
            @click="handleAdd"
          >
            新增角色
          </a-button>
        </a-space>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-button
            type="link"
            @click="handleAssignPermission(record)"
          >
            授权
          </a-button>
          <a-button
            type="link"
            @click="handleEdit(record)"
          >
            编辑
          </a-button>
          <a-button
            type="link"
            @click="handleDelete(record)"
          >
            删除
          </a-button>
        </a-space>
      </template>
    </BaseTable>

    <!-- 角色表单 -->
    <RoleForm
      v-model:visible="formVisible"
      :form-data="currentRecord"
      @success="handleSuccess"
    />
    <!-- 权限配置抽屉 -->
    <Permission
      v-model:visible="PermissionVisible"
      :role-info="currentRecord"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  getRoleList,
  deleteRole,
  type RoleModel,
  type RoleQueryDTO
} from '@/apis/system'
import BaseTable from '@/components/BaseTable/index.vue'
import RoleForm from './components/RoleForm.vue'
import Permission from './components/Permission.vue'
import { columns, searchSchema } from './columns'

// 状态定义
const tableRef = ref()
const loading = ref(false)
const dataSource = ref<RoleModel[]>([])
const total = ref(0)
const formVisible = ref(false)
const PermissionVisible = ref(false)
const currentRecord = ref<RoleModel | null>(null)
const queryParams = ref<RoleQueryDTO>({
  pageNo: 1,
  pageSize: 10,
  column: 'createTime',
  order: 'desc'
})

// 计算属性
const current = computed(() => queryParams.value.pageNo)
const pageSize = computed(() => queryParams.value.pageSize)

// 获取数据
const fetchData = async (params?: Partial<RoleQueryDTO>) => {
  loading.value = true
  try {
    const response = await getRoleList({
      ...queryParams.value,
      ...params
    })
    
    if (response.code === 200 && response.data) {
      dataSource.value = response.data.records || []
      total.value = response.data.total || 0
      
      // 如果当前页没有数据且不是第一页，则自动跳转到上一页
      if (dataSource.value.length === 0 && queryParams.value.pageNo > 1) {
        handlePageChange({
          current: queryParams.value.pageNo - 1,
          size: queryParams.value.pageSize
        })
      }
    } else {
      message.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取角色数据失败:', error)
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = (params: RoleQueryDTO) => {
  handlePageChange({
    current: 1,
    size: queryParams.value.pageSize,
    ...params
  })
}

const handleReset = () => {
  handlePageChange({
    current: 1,
    size: 10,
    column: 'createTime',
    order: 'desc'
  })
}

const handlePageChange = ({ current, size, ...rest }: { current: number; size?: number } & Partial<RoleQueryDTO>) => {
  queryParams.value = {
    ...queryParams.value,
    ...rest,
    pageNo: current,
    pageSize: rest.pageSize || queryParams.value.pageSize
  }
  fetchData()
}

const handleAdd = () => {
  currentRecord.value = null
  formVisible.value = true
}

const handleEdit = (record: RoleModel) => {
  currentRecord.value = { ...record }
  formVisible.value = true
}

const handleDelete = (record: RoleModel) => {
  if (!record?.id) {
    message.error('无效的记录ID')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该角色吗？',
    async onOk() {
      try {
        const response = await deleteRole(record.id)
        if (response.code === 200) {
          message.success('删除成功')
          // 如果当前页只有一条数据，删除后自动跳转到上一页
          if (dataSource.value.length === 1 && queryParams.value.pageNo > 1) {
            handlePageChange({
              current: queryParams.value.pageNo - 1,
              size: queryParams.value.pageSize
            })
          } else {
            fetchData()
          }
        } else {
          message.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('删除角色失败:', error)
        message.error('删除失败')
      }
    }
  })
}

const handleAssignPermission = (record: RoleModel) => {
  currentRecord.value = { ...record }
  PermissionVisible.value = true
}

const handleSuccess = () => {
  formVisible.value = false
  PermissionVisible.value = false
  fetchData()
}

// 初始化
fetchData()
</script>

<style lang="scss" scoped>
.role-container {
  padding: 16px;
  background-color: var(--global-background-color);
  border-radius: 4px;
  height: 100%;
  
  :deep(.ant-table-wrapper) {
    background: transparent;
  }

  :deep(.ant-table) {
    background: transparent;
  }

  :deep(.ant-table-container) {
    background: transparent;
  }
}
</style>
