<template>
  <div class="package-container">
    <BaseTable
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :search-schema="searchSchema"
      :total="total"
      @search="handleSearch"
      @reset="handleReset"
      @page-change="handlePageChange"
    >
      <template #actions>
        <a-space size="middle">
          <a-button
            type="primary"
            @click="handleAdd"
          >
            <template #icon>
              <PlusOutlined />
            </template>
            新增
          </a-button>
        </a-space>
      </template>

      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="record.status === '开启' ? 'success' : 'error'">
          {{ record.status }}
        </a-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-button
            type="link"
            @click="handleEdit(record)"
          >
            编辑
          </a-button>
          <a-button
            type="link"
            @click="handleDelete(record)"
          >
            删除
          </a-button>
        </a-space>
      </template>
    </BaseTable>

    <!-- 新增/编辑弹窗 -->
    <!-- <PackageForm
      v-model:visible="formVisible"
      :form-data="currentRecord"
      @success="handleSuccess"
    /> -->
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import BaseTable from '@/components/BaseTable/index.vue'
import { columns, searchSchema } from './columns'
// import PackageForm from './components/PackageForm.vue'

// 表格数据
const dataSource = ref<any[]>([])
const loading = ref(false)
const total = ref(0)
const formVisible = ref(false)
const currentRecord = ref(null)

// 获取数据
const fetchData = async (params = {}) => {
  loading.value = true
  try {
    const queryParams = {
      current: params.current || 1,
      size: params.size || 10,
      ...params
    }

    const response = await getPackagePage(queryParams)
    if (response.code === 200) {
      dataSource.value = response.data.records
      total.value = response.data.total
    } else {
      message.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取套餐数据失败:', error)
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (params) => {
  fetchData({
    ...params,
    current: 1
  })
}

// 重置处理
const handleReset = () => {
  fetchData({ current: 1 })
}

// 分页处理
const handlePageChange = ({ current, size, searchForm }) => {
  fetchData({
    current,
    size,
    ...searchForm
  })
}

// 新增处理
const handleAdd = () => {
  currentRecord.value = null
  formVisible.value = true
}

// 编辑处理
const handleEdit = (record) => {
  currentRecord.value = record
  formVisible.value = true
}

// 删除处理
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该套餐吗？',
    async onOk() {
      try {
        const response = await deletePackage(record.id)
        if (response.code === 200) {
          message.success('删除成功')
          fetchData()
        }
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 表单提交成功处理
const handleSuccess = () => {
  formVisible.value = false
  fetchData()
}

// 初始化
fetchData()
</script>

<style lang="scss" scoped>
.package-container {
  padding: 16px;
  background: #fff;
  border-radius: 4px;

  :deep(.ant-space) {
    gap: 12px !important;
  }
}
</style>
