
export const columns = [
  {
    title: '租户名称',
    dataIndex: 'tenantName',
    width: 200
  },
  {
    title: '租户编码',
    dataIndex: 'tenantCode',
    width: 150
  },
  {
    title: '联系人',
    dataIndex: 'contactName',
    width: 120
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    width: 150
  },
  {
    title: '地址',
    dataIndex: 'address'
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '备注',
    dataIndex: 'remark'
  },
  {
    title: '操作',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

export const searchSchema = [
  {
    key: 'tenantName',
    label: '租户名称',
    type: 'input',
    placeholder: '请输入租户名称'
  },
  {
    key: 'tenantCode',
    label: '租户编码',
    type: 'input',
    placeholder: '请输入租户编码'
  },
  {
    key: 'contactName', 
    label: '联系人',
    type: 'input',
    placeholder: '请输入联系人'
  },
  {
    key: 'status',
    label: '状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '开启', value: '开启' },
      { label: '关闭', value: '关闭' }
    ]
  }
]