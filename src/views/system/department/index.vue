<template>
  <div class="dept-container">
    <!-- 左侧部门树 -->
    <div class="dept-left">
      <div class="action-bar">
        <a-space>
          <a-button
            type="primary"
            @click="handleAdd"
          >
            新增
          </a-button>
          <a-button @click="handleAddSub">
            添加下级
          </a-button>
        </a-space>
      </div>

      <div class="search-bar">
        <a-input-search
          v-model:value="keyWord"
          placeholder="搜索记录"
          style="width: 100%"
          @search="handleSearch"
        />
      </div>

      <div class="dept-tree">
        <a-tree
          v-model:selectedKeys="selectedKeys"
          v-model:expandedKeys="expandedKeys"
          v-model:checkedKeys="checkedKeys"
          :tree-data="treeData"
          :field-names="{ title: 'departName', key: 'id' }"
          checkable
          @select="handleSelect"
          @check="handleCheck"
        >
          <template #title="{ departName, id, parentId }">
            <span @click="detailJson = { id, departName, parentId }">{{ departName }}</span>
          </template>
        </a-tree>
      </div>
    </div>

    <!-- 右侧详情组件 -->
    <DeptDetail
      :detailJson="detailJson"
      :tree-data="treeData"
      @success="handleSuccess"
    />

    <!-- 新增/编辑部门弹窗 -->
    <DeptForm
      v-model:visible="formVisible"
      :form-data="currentRecord"
      :tree-data="treeData"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  getDepartTreeSync,
  getDepartList,
  type DepartModel,
  type CreateDepartDTO
} from '@/apis/system'
import DeptForm from './components/DeptForm.vue'
import DeptDetail from './components/DeptDetail.vue'

// 搜索文本
const keyWord = ref('')

// 树形数据
const treeData = ref<DepartModel[]>([])
const selectedKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const checkedKeys = ref<string[]>([])
const detailJson = ref<string[]>([])

// 表单控制
const formVisible = ref(false)
const currentRecord = ref<CreateDepartDTO | null>(null)

// 计算选中的部门ID
const selectedDeptId = computed(() => selectedKeys.value[0])


// 获取部门树形数据
const fetchDeptTree = async () => {
  try {
    const { data, code, message: msg } = await getDepartTreeSync()
    if (code === 200 && data) {
      // 处理返回的数据,添加key和title字段
      const processData = (data: DepartModel[]) => {
        return data.map(item => ({
          ...item,
          key: item.id,
          value: item.id,
          title: item.departName,
          children: item.children ? processData(item.children) : [],
          isLeaf: !item.children || item.children.length === 0
        }))
      }
      
      treeData.value = processData(data)
      
      // 如果没有展开的节点，默认展开第一级
      if (!expandedKeys.value.length && data.length) {
        expandedKeys.value = data.map(item => item.id)
      }
    } else {
      message.error(msg || '获取部门数据失败')
    }
  } catch (error) {
    message.error('获取部门数据失败')
  }
}

// 新增部门
const handleAdd = () => {
  currentRecord.value = {
    departName: '',
    departNameEn: '',
    departNameAbbr: '',
    orgType: '1', // 设置为一级部门
    orgCategory: '1', // 默认为公司类型
    status: '1', // 默认启用
    departOrder: 0 // 默认排序号
  }
  formVisible.value = true
}

// 修改添加子级的处理函数
const handleAddSub = () => {
  const parentId = checkedKeys.value.length ? checkedKeys.value[0] : selectedKeys.value[0]
  
  if (!parentId) {
    message.warning('请先选择或勾选上级部门')
    return
  }

  // 查找父级部门信息
  const parentDept = findDepartById(treeData.value, parentId)
  if (!parentDept) {
    message.error('未找到上级部门信息')
    return
  }

  currentRecord.value = {
    departName: '',
    parentId: parentId,
    departNameEn: '',
    departNameAbbr: '',
    orgType: '2', // 设置为子部门
    orgCategory: parentDept.orgCategory || '1', // 继承父级的机构类别
    status: '1', // 默认启用
    departOrder: 0 // 默认排序号
  }
  formVisible.value = true
}

// 递归查找部门
const findDepartById = (departments: DepartModel[], id: string): DepartModel | null => {
  for (const dept of departments) {
    if (dept.id === id) {
      return dept
    }
    if (dept.children?.length) {
      const found = findDepartById(dept.children, id)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 搜索处理
const handleSearch = async (value: string) => {
  if (value) {
    try {
      const { data, code, msg } = await getDepartList({
        keyWord: value
      })
      if (code === 200 && data) {
        // 更新树形数据
        const processData = (data: DepartModel[]) => {
          return data.map(item => ({
            ...item,
            key: item.id,
            value: item.id,
            title: item.departName,
            isLeaf: !item.children || item.children.length === 0,
            children: item.children ? processData(item.children) : null
          }))
        }
        treeData.value = processData(data)
      } else {
        message.error(msg || '搜索部门失败')
      }
    } catch (error) {
      message.error('搜索部门失败')
    }
  } else {
    await fetchDeptTree()
  }
}
  
// 选择处理
const handleSelect = (keys: string[]) => {
  selectedKeys.value = keys
}
  
// 表单提交成功处理
const handleSuccess = () => {
  formVisible.value = false
  fetchDeptTree()
}
  
// 添加勾选事件处理函数
const handleCheck = (checkedKeysValue: string[]) => {
  checkedKeys.value = checkedKeysValue
}
  
// 添加展开处理函数

// const handleExpand = async (expandedKeys: string[], { expanded, node }: any) => {
//   if (expanded) {
//     const parentId = node.id
//     // 保存当前展开节点的父级ID
//     currentParentId.value = parentId
//     try {
//       const { data, code, message: msg } = await getDepartTreeSync(parentId)
//       if (code === 200 && data) {
//         // 处理返回的数据
//         const processData = (items: DepartModel[]) => {
//           return items.map(item => ({
//             ...item,
//             key: item.id,
//             value: item.id,
//             title: item.departName,
//             children: item.children ? processData(item.children) : []
//           }))
//         }

//         // 更新指定节点的子节点
//         const updateTreeData = (list: DepartModel[], id: string, children: DepartModel[]) => {
//           return list.map(node => {
//             if (node.id === id) {
//               return {
//                 ...node,
//                 children: processData(children)
//               }
//             }
//             if (node.children) {
//               return {
//                 ...node,
//                 children: updateTreeData(node.children, id, children)
//               }
//             }
//             return node
//           })
//         }

//         // 更新树数据
//         treeData.value = updateTreeData(treeData.value, parentId, data)
//       } else {
//         message.error(msg || '获取子部门数据失败')
//       }
//     } catch (error) {
//       console.error('获取子部门数据失败:', error)
//       message.error('获取子部门数据失败')
//     }
//   }
// }
  
// 初始化
fetchDeptTree()
</script>
  
<style lang="scss" scoped>
.dept-container {
  display: flex;
  height: calc(100vh - 120px); // 减去头部和导航的高度
  background: var(--global-background-color);
  padding: 16px;
  gap: 16px;

  .dept-left {
    width: 50%; // 固定左侧宽度
    background: var(--area-fill-color);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);

    .action-bar {
      padding: 16px;
      border-bottom: 1px solid var(--input-border-color);
      
      :deep(.ant-space) {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .ant-btn {
          padding: 4px 8px;
          font-size: 13px;
          height: 28px;

          .anticon {
            font-size: 12px;
          }
        }
      }
    }

    .search-bar {
      padding: 12px 16px;
      border-bottom: 1px solid var(--input-border-color);

      :deep(.ant-input-search) {
        .ant-input {
          height: 32px;
          border-color: var(--input-border-color);
          background-color: var(--input-fill-color);
        }
        .ant-input-group-addon .ant-btn {
          background-color: var(--input-fill-color);
          border-color: var(--input-border-color);
        }
      }
    }

    .dept-tree {
      flex: 1;
      padding: 16px;
      overflow: auto;

      :deep(.ant-tree) {
        .ant-tree-treenode {
          padding: 4px 0;
          
          .ant-tree-node-content-wrapper {
            padding: 0 4px;
          }

          .ant-checkbox-wrapper {
            margin-left: 4px;
          }
        }
      }
    }
  }
}

// 暗色主题适配
:deep([data-theme='dark']) {
  .dept-container {
    background: #1f1f1f;

    .dept-left,
    .dept-right {
      background: #141414;
      box-shadow: none;
    }
  }
}
</style>