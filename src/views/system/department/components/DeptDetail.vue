<template>
  <div class="dept-right">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane
        key="basic"
        tab="基本信息"
      >
        <div class="basic-content">
          <a-form
            ref="formRef"
            :model="form"
            :rules="rules"
          >
            <a-form-item
              label="部门名称"
              name="departName"
            >
              <a-input
                v-model:value="form.departName"
                class="bg-input-fill border-input-border"
                placeholder="请输入部门名称"
              >
                <template #suffix>
                  <QuestionCircleOutlined />
                </template>
              </a-input>
            </a-form-item>

            <a-form-item
              label="上级部门"
              name="parentId"
            >
              <a-tree-select
                v-model:value="form.parentId"
                disabled
                :tree-data="treeData"
                placeholder="请选择上级部门"
                :field-names="{ label: 'departName', value: 'id' }"
                allow-clear
              />
            </a-form-item>

            <a-form-item
              label="状态"
              name="status"
            >
              <a-radio-group v-model:value="form.status">
                <a-radio value="1">
                  正常
                </a-radio>
                <a-radio value="0">
                  禁用
                </a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item
              label="备注"
              name="memo"
            >
              <a-textarea
                v-model:value="form.memo"
                class="bg-input-fill border-input-border"
                placeholder="请输入备注"
                :rows="4"
              />
            </a-form-item>
          </a-form>
        </div>

        <!-- 底部按钮 -->
        <div class="fixed-footer">
          <a-space>
            <a-button @click="handleReset">
              重置
            </a-button>
            <a-button
              type="primary"
              @click="handleSubmit"
            >
              保存
            </a-button>
          </a-space>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import { type DepartMode, updateDepart} from '@/apis/system'

const props = defineProps<{
  detailJson: { id: string; departName: string; parentId: string }
  treeData: DepartModel[]
}>()

const emit = defineEmits(['success'])

// 当前激活的标签页
const activeKey = ref('basic')

// 表单实例
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  departName: '', // 部门名称
  parentId: undefined, // 上级部门
  memo: '', // 备注
  status: '1' // 状态
})

// 递归查找部门
const findDepartById = (departments: DepartModel[], id: string): DepartModel | null => {
  for (const dept of departments) {
    if (dept.id === id) {
      return dept
    }
    if (dept.children?.length) {
      const found = findDepartById(dept.children, id)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 监听选中部门变化
watch(
  () => props.detailJson,
  (newVal) => {
    if (newVal) {
      // 根据ID查找部门详情
      const dept = findDepartById(props.treeData, newVal.id)
      if (dept) {
        // 回填表单数据
        Object.assign(form, {
          departName: dept.departName,
          parentId: dept.parentId,
          memo: dept.memo,
          status: dept.status
        })
      }
    }
  },
  { immediate: true }
)

// 表单校验规则
const rules = {
  departName: [{ required: true, message: '请输入部门名称', trigger: 'blur' }]
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    // 调用更新部门接口
    const params = {
      id: props.detailJson.id,
      departName: form.departName,
      parentId: form.parentId,
      memo: form.memo,
      status: form.status
    }
    const { data, code, message: msg } = await updateDepart(params)
    if (code === 200) {
      message.success('保存成功')
      emit('success')
    } else {
      message.error(msg || '保存失败')
    }
  } catch (error) {
    message.error('保存失败')
  }
}
</script>

<style lang="scss" scoped>
.dept-right {
  flex: 1;
  background: var(--area-fill-color);
  border-radius: 4px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 16px;

  :deep(.ant-tabs) {
    display: flex;
    flex-direction: column;
    height: 100%;

    .ant-tabs-content-holder {
      flex: 1;
      overflow: hidden;
    }

    .ant-tabs-content {
      height: 100%;
    }

    .ant-tabs-tabpane {
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
    }
  }

  // 基本信息内容区
  .basic-content {
    flex: 1;
    overflow: auto;
    padding: 16px 16px 60px;

    :deep(.ant-form) {
      max-width: 600px;
      margin: 0 auto;

      .ant-form-item {
        margin-bottom: 24px;
      }
    }
  }

  // 固定底部按钮样式
  .fixed-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px 16px;
    background: var(--area-fill-color);
    border-top: 1px solid var(--input-border-color);
    text-align: right;
    z-index: 10;

    // 上方阴影效果
    &::before {
      content: '';
      position: absolute;
      top: -10px;
      left: 0;
      right: 0;
      height: 10px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.05), transparent);
      pointer-events: none;
    }
  }
}
</style>