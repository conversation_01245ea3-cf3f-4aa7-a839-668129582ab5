<template>
  <a-modal
    :visible="visible"
    :title="formData?.id ? '编辑部门' : (formData?.parentId ? '添加下级' : '新增部门')"
    :confirmLoading="loading"
    @cancel="handleCancel"
    @ok="handleSubmit"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <!-- 添加下级时显示上级部门 -->
      <template v-if="formData?.parentId">
        <a-form-item label="上级部门">
          <a-tree-select
            v-model:value="form.parentId"
            :tree-data="treeData"
            :field-names="{
              children: 'children',
              label: 'departName',
              value: 'id'
            }"
            placeholder="请选择上级部门"
            :disabled="true"
            tree-default-expand-all
          />
        </a-form-item>
      </template>

      <a-form-item
        label="部门名称"
        name="departName"
      >
        <a-input
          v-model:value="form.departName"
          placeholder="请输入部门名称"
        />
      </a-form-item>

      <a-form-item
        label="部门编码"
        name="orgCode"
      >
        <a-input
          v-model:value="form.orgCode"
          placeholder="请输入部门编码"
        />
      </a-form-item>

      <a-form-item
        label="排序号"
        name="departOrder"
      >
        <a-input-number
          v-model:value="form.departOrder"
          :min="0"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item
        label="机构类型"
        name="orgType"
      >
        <a-select
          v-model:value="form.orgType"
          :disabled="true"
        >
          <a-select-option
            v-if="!formData?.parentId"
            value="1"
          >
            一级部门
          </a-select-option>
          <a-select-option
            v-if="formData?.parentId"
            value="2"
          >
            子部门
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item
        label="状态"
        name="status"
      >
        <a-switch
          v-model:value="form.status"
          :checked="form.status === '1'"
          @change="val => form.status = val ? '1' : '0'"
        />
      </a-form-item>

      <a-form-item
        label="备注"
        name="description"
      >
        <a-textarea
          v-model:value="form.description"
          :rows="4"
          placeholder="请输入备注"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import {
  createDepart,
  updateDepart,
  type DepartModel,
  type CreateDepartDTO
} from '@/apis/system'

const props = defineProps<{
  visible: boolean
  formData: CreateDepartDTO | null
  treeData: DepartModel[]
}>()

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const form = ref<CreateDepartDTO>({
  departName: '',
  parentId: undefined,
  orgCode: '',
  departOrder: 0,
  orgType: '1', // 默认为一级部门
  orgCategory: '1',
  status: '1',
  description: ''
})

// 表单校验规则
const rules = {
  departName: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { max: 50, message: '部门名称不能超过50个字符', trigger: 'blur' }
  ],
  orgCode: [
    { required: true, message: '请输入部门编码', trigger: 'blur' },
    { max: 50, message: '部门编码不能超过50个字符', trigger: 'blur' }
  ],
  departOrder: [
    { required: true, message: '请输入排序号', trigger: 'blur' },
    { type: 'number', message: '排序号必须为数字', trigger: 'blur' }
  ]
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newVal) => {
    if (newVal) {
      // 深拷贝防止互相影响
      form.value = { 
        ...JSON.parse(JSON.stringify(newVal)),
        orgType: newVal.parentId ? '2' : '1', // 根据是否有父级设置机构类型
        status: newVal.status || '1' // 如果没有状态，默认为启用
      }
    } else {
      // 重置表单
      form.value = {
        departName: '',
        parentId: undefined,
        orgCode: '',
        departOrder: 0,
        orgType: '1',
        orgCategory: '1',
        status: '1',
        description: ''
      }
      formRef.value?.resetFields()
    }
  },
  { deep: true, immediate: true }
)

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  // 重置表单
  form.value = {
    departName: '',
    parentId: undefined,
    orgCode: '',
    departOrder: 0,
    orgType: '1',
    orgCategory: '1',
    status: '1',
    description: ''
  }
  formRef.value?.resetFields()
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    const api = form.value.id ? updateDepart : createDepart
    const response = await api(form.value)
    
    if (response.code === 200) {
      message.success('保存成功')
      emit('success')
      handleCancel()
    } else {
      message.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('保存部门失败:', error)
    message.error('保存失败')
  } finally {
    loading.value = false
  }
}
</script>