<template>
  <div class="user-container">
    <BaseTable
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :search-schema="searchSchema"
      :total="total"
      :current="current"
      :pageSize="pageSize"
      @search="handleSearch"
      @reset="handleReset"
      @page-change="handlePageChange"
    >
      <template #actions>
        <div class="action-buttons">
          <a-button
            type="primary"
            @click="handleAdd"
          >
            新增用户
          </a-button>
        </div>
      </template>

      <!-- 用户头像列 -->
      <template #avatar="{ record }">
        <div class="flex items-center gap-2">
          <a-avatar :src="record.avatar" />
          <span>{{ record.name }}</span>
        </div>
      </template>

      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="record.status === '正常' ? 'success' : 'error'">
          {{ record.status }}
        </a-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-button
            type="link"
            :disabled="record.status === 2"
            @click="handleEdit(record)"
          >
            编辑
          </a-button>
          <a-button
            type="link"
            :disabled="record.status === 2"
            @click="handleDelete(record)"
          >
            删除
          </a-button>
          <a-dropdown>
            <a-button
              type="link"
              style="top: -4px;
    position: relative;"
            >
              ...
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a-button
                    type="link"
                    :disabled="record.status === 2"
                    @click="handleChangePassword(record)"
                  >
                    修改密码
                  </a-button>
                </a-menu-item>
                <a-menu-item>
                  <a-button
                    type="link"
                    @click="handleToggleStatus(record)"
                  >
                    {{ record.status === 1 ? '冻结' : '解冻' }}
                  </a-button>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </template>
    </BaseTable>
    <UserForm
      v-model:visible="formVisible"
      :form-data="currentRecord"
      @success="handleSuccess"
    />
    <ChangePasswordModal
      v-model:visible="changePasswordVisible"
      :username="currentUser?.username"
      @success="handlePasswordChanged"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import BaseTable from '@/components/BaseTable/index.vue'
import UserForm from './components/UserForm.vue'
import ChangePasswordModal from './components/ChangePasswordModal.vue'
import { columns, searchSchema } from './columns'
import {
  getUserList,
  deleteUser,
  enableUser,
  type UserModel,
  type UserQueryDTO
} from '@/apis/system'

// 表格数据
const dataSource = ref<UserModel[]>([])
const loading = ref(false)
const total = ref(0)
const current = ref(1)
const pageSize = ref(10)
const formVisible = ref(false)
const currentRecord = ref<UserModel | null>(null)
const searchParams = ref<Partial<UserQueryDTO>>({})

// 获取数据
const fetchData = async (params: Partial<UserQueryDTO> = {}) => {
  loading.value = true
  try {
    const queryParams: UserQueryDTO = {
      current: params.current || current.value,
      pageSize: params.pageSize || pageSize.value,
      userName: params.userName,
      realName: params.realName,
      sex: params.sex,
      status: params.status,
      phone: params.phone
    }

    const response = await getUserList(queryParams)
    if (response.code === 200) {
      dataSource.value = response.data.records
      total.value = response.data.total
      current.value = queryParams.current
      pageSize.value = queryParams.pageSize
    } else {
      message.error(response.message || '获取数据失败')
    }
  } catch (error) {
    message.error('获取数据失败')
    dataSource.value = []
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (params: Partial<UserQueryDTO>) => {
  searchParams.value = params
  fetchData({
    ...params,
    current: 1
  })
}

// 重置处理
const handleReset = () => {
  searchParams.value = {}
  current.value = 1
  pageSize.value = 10
  fetchData()
}

// 分页处理
const handlePageChange = ({ current: newCurrent, pageSize: newPageSize }) => {
  fetchData({
    ...searchParams.value,
    current: newCurrent,
    pageSize: newPageSize
  })
}

// 新增处理
const handleAdd = () => {
  currentRecord.value = null
  formVisible.value = true
}

// 编辑处理
const handleEdit = (record: UserModel) => {
  currentRecord.value = record
  formVisible.value = true
}

// 删除处理
const handleDelete = (record: UserModel) => {
  if (!record.id) return

  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该用户吗？',
    async onOk() {
      try {
        const response = await deleteUser(record.id)
        if (response.code === 200) {
          message.success('删除成功')
          fetchData(searchParams.value)
        }
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 修改密码相关
const changePasswordVisible = ref(false)
const currentUser = ref<UserModel | null>(null)

// 处理修改密码
const handleChangePassword = (record: UserModel) => {
  currentUser.value = record
  changePasswordVisible.value = true
}

// 密码修改成功回调
const handlePasswordChanged = () => {
  message.success('密码修改成功')
  changePasswordVisible.value = false
  currentUser.value = null
}

// 切换状态
const handleToggleStatus = (record: UserModel) => {
  if (!record.id) return

  const newStatus = record.status === 1 ? 2 : 1
  Modal.confirm({
    title: `确认${newStatus === 1 ? '启用' : '禁用'}`,
    content: `确定要${newStatus === 1 ? '启用' : '禁用'}该用户吗？`,
    async onOk() {
      try {
        const response = await enableUser(record.id, newStatus)
        if (response.code === 200) {
          message.success('操作成功')
          fetchData(searchParams.value)
        } else {
          message.error(response.message || '操作失败')
        }
      } catch (error) {
        message.error('操作失败')
      }
    }
  })
}

// 表单提交成功处理
const handleSuccess = () => {
  fetchData(searchParams.value)
  formVisible.value = false
}

// 初始化
fetchData()
</script>

<style lang="scss" scoped>
.user-container {
  padding: 16px;
  background: var(--global-background-color);
  border-radius: 4px;

  .action-buttons {
    display: flex;
    gap: 8px; // 设置按钮之间的间距为 8px

    :deep(.ant-btn) {
      margin: 0; // 清除按钮默认的 margin
    }
  }
}
</style>
