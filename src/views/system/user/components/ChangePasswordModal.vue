<template>
  <a-modal
    :visible="visible"
    title="修改密码"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item
        label="原密码"
        name="oldPwd"
      >
        <a-input-password
          v-model:value="form.oldPwd"
          placeholder="请输入原密码"
        />
      </a-form-item>

      <a-form-item
        label="新密码"
        name="newPwd"
      >
        <a-input-password
          v-model:value="form.newPwd"
          placeholder="请输入新密码"
        />
      </a-form-item>

      <a-form-item
        label="确认新密码"
        name="confirmPwd"
      >
        <a-input-password
          v-model:value="form.confirmPwd"
          placeholder="请再次输入新密码"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { changePassword } from '@/apis/system'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  username: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref<FormInstance>()
const form = reactive({
  oldPwd: '',
  newPwd: '',
  confirmPwd: ''
})

const rules = {
  oldPwd: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
  newPwd: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPwd: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (value && value !== form.newPwd) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ]
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    const response = await changePassword({
      ...form,
      userName: props.username
    })

    if (response.code === 0) {
      message.success('密码修改成功')
      emit('success')
      handleCancel()
    }
  } catch (error) {
    message.error('密码修改失败')
  }
}

const handleCancel = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}
</script> 