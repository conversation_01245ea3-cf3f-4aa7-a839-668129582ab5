<template>
  <a-modal
    :visible="visible"
    :title="formData?.id ? '编辑用户' : '新增用户'"
    :width="600"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <a-form-item
        label="用户账号"
        name="username"
      >
        <a-input
          v-model:value="form.username"
          placeholder="请输入用户账号"
          :maxLength="50"
          :disabled="!!formData?.id"
        />
      </a-form-item>

      <a-form-item
        label="用户姓名"
        name="realname"
      >
        <a-input
          v-model:value="form.realname"
          placeholder="请输入用户姓名"
          :maxLength="50"
        />
      </a-form-item>

      <a-form-item
        label="性别"
        name="sex"
      >
        <a-select
          v-model:value="form.sex"
          placeholder="请选择性别"
          :options="sexOptions"
        >
          <template #suffixIcon>
            <DownOutlined />
          </template>
        </a-select>
      </a-form-item>
      <a-form-item
        v-if="!formData?.id"
        label="密码"
        name="password"
      >
        <a-input-password
          v-model:value="form.password"
          placeholder="请输入密码"
        >
          <template #suffix>
            <a-button
              type="link"
              size="small"
              @click="handleRandomPassword"
            >
              生成
            </a-button>
          </template>
        </a-input-password>
      </a-form-item>

      <a-form-item
        v-if="!formData?.id"
        label="确认密码"
        name="confirmPassword"
      >
        <a-input-password
          v-model:value="form.confirmPassword"
          placeholder="请再次输入密码"
        />
      </a-form-item>
      <a-form-item
        label="角色"
        name="roleIds"
      >
        <a-select
          v-model:value="form.roleIds"
          mode="multiple"
          placeholder="请选择角色"
          :options="roleOptions"
          :loading="roleLoading"
        >
          <template #suffixIcon>
            <SearchOutlined />
          </template>
        </a-select>
      </a-form-item>

      <a-form-item
        label="所属部门"
        name="departIds"
      >
        <a-tree-select
          v-model:value="form.departIds"
          show-search
          multiple
          style="width: 100%"
          placeholder="请选择所属部门"
          :tree-data="deptOptions"
          :loading="deptLoading"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          tree-default-expand-all
          allow-clear
          tree-checkable
          :show-checked-strategy="TreeSelect.SHOW_PARENT"
        >
          <template #suffixIcon>
            <SearchOutlined />
          </template>
        </a-tree-select>
      </a-form-item>
      <a-form-item
        label="邮箱"
        name="email"
      >
        <a-input
          v-model:value="form.email"
          placeholder="请输入邮箱"
          :maxLength="50"
        >
          <template #suffix>
            <QuestionCircleOutlined />
          </template>
        </a-input>
      </a-form-item>

      <a-form-item
        label="手机号码"
        name="phone"
      >
        <a-input
          v-model:value="form.phone"
          placeholder="请输入手机号码"
          :maxLength="11"
        >
          <template #suffix>
            <QuestionCircleOutlined />
          </template>
        </a-input>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { message, TreeSelect } from 'ant-design-vue'
import { QuestionCircleOutlined, SearchOutlined, DownOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import {
  createUser,
  updateUser,
  getRoleList,
  getDepartTreeSync,
  type RoleModel,
  type DepartModel
} from '@/apis/system'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  username: '',
  realname: '',
  password: '',
  confirmPassword: '',
  roleIds: [],
  departIds: [],
  sex: '1',
  email: '',
  phone: ''
})

// 性别选项
const sexOptions = [
  { label: '男', value: 1 },
  { label: '女', value: 2 }
]

// 表单校验规则
const rules = {
  username: [
    { required: true, message: '请输入用户账号', trigger: 'blur' },
    { max: 50, message: '用户账号最多50个字符', trigger: 'blur' }
  ],
  realname: [
    { required: true, message: '请输入用户姓名', trigger: 'blur' },
    { max: 50, message: '用户姓名最多50个字符', trigger: 'blur' }
  ],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (value && value !== form.password) {
          return Promise.reject('两次输入的密码不一致')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  email: [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }],
  phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  roleIds: [{ required: true, message: '请选择角色', trigger: 'change' }],
  departIds: [{ required: true, message: '请选择部门', trigger: 'change' }]
}

// 树选择器配置
const { SHOW_PARENT } = TreeSelect
const deptOptions = ref<any[]>([])
const deptLoading = ref(false)

// 获取部门列表
const fetchDepartList = async () => {
  deptLoading.value = true
  try {
    const response = await getDepartTreeSync({})
    if (response.code === 200) {
      // 递归构建部门树形选项
      const buildDeptOptions = (depts: DepartModel[]): any[] => {
        return depts.map((dept) => ({
          title: dept.departName,
          value: dept.id,
          key: dept.id,
          children: dept.children ? buildDeptOptions(dept.children) : undefined,
          isLeaf: dept.isLeaf === 1
        }))
      }
      deptOptions.value = buildDeptOptions(response.data)
    } else {
      message.error(response.statusText || '获取部门列表失败')
    }
  } catch (error) {
    message.error('获取部门列表失败')
  } finally {
    deptLoading.value = false
  }
}

// 搜索过滤函数
const filterTreeNode = (inputValue: string, treeNode: any) => {
  return treeNode.title.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
}

// 角色选项
const roleOptions = ref<{ label: string; value: string }[]>([])
const roleLoading = ref(false)

// 获取角色列表
const fetchRoleList = async () => {
  roleLoading.value = true
  try {
    const response = await getRoleList({
      pageNo: 1,
      pageSize: 100 // 获取足够多的角色数据
    })
    if (response.code === 200) {
      roleOptions.value = response.data.records.map((role: RoleModel) => ({
        label: role.roleName,
        value: role.id as string
      }))
    } else {
      message.error(response.message || '获取角色列表失败')
    }
  } catch (error) {
    message.error('获取角色列表失败')
  } finally {
    roleLoading.value = false
  }
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    if (val) {
      // 弹窗打开时获取角色和部门数据
      fetchRoleList()
      fetchDepartList()
    }
  }
)

// 组件挂载时获取初始数据
onMounted(() => {
  if (props.visible) {
    fetchRoleList()
    fetchDepartList()
  }
})

// 修改表单提交逻辑
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    const params = {
      ...form,
      departIds: Array.isArray(form.departIds) ? form.departIds.join(',') : form.departIds,
      roleIds: Array.isArray(form.roleIds) ? form.roleIds.join(',') : form.roleIds
    }
    delete params.confirmPassword

    if (props.formData?.id) {
      const response = await updateUser({
        ...params,
        id: props.formData.id
      })
      if (response.code === 200) {
        message.success('编辑成功')
        emit('success')
        handleCancel()
      }
    } else {
      const response = await createUser(params)
      if (response.code === 200) {
        message.success('新增成功')
        emit('success')
        handleCancel()
      }
    }
  } catch (error) {}
}

// 取消
const handleCancel = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

// 生成随机密码
const handleRandomPassword = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let password = ''
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  form.password = password
  form.confirmPassword = password
}

// 监听表单数据变化时处理部门数据
watch(
  () => props.formData,
  (val) => {
    if (val?.id) {
      Object.assign(form, {
        ...val,
        sex: typeof val.sex === 'number' ? val.sex : 1,
        departIds: val.departIds ? val.departIds.split(',') : [], // 将逗号分隔的字符串转为数组
        roleIds: val.roleIds ? val.roleIds.split(',') : []
      })
    } else {
      Object.keys(form).forEach((key) => {
        form[key] = key === 'sex' ? 1 : ''
      })
      form.roleIds = []
      form.departIds = []
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
:deep(.ant-form) {
  .ant-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .ant-form-item-label {
    label {
      height: 32px;
    }
  }
}

.form-footer {
  margin-top: 24px;
  text-align: right;
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
}

:deep(.ant-tree-select) {
  .ant-select-selection-overflow {
    max-height: 80px;
    overflow-y: auto;
  }
}
</style>