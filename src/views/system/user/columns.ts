export const columns = [
  { title: '用户名', dataIndex: 'username' },
  { title: '真实姓名', dataIndex: 'realname' },
  {
    title: '性别',
    dataIndex: 'sex',
    customRender: ({ text }) => {
      const sexMap = {
        0: '未知',
        1: '男',
        2: '女'
      }
      return sexMap[text] || '未知'
    }
  },
  { title: '手机号', dataIndex: 'phone' },
  { title: '邮箱', dataIndex: 'email' },
  { title: '部门', dataIndex: 'deptName' },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => {
      const statusMap = {
        1: '正常',
        2: '冻结'
      }
      return statusMap[text] || '未知'
    }
  },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '操作',  slots: { customRender: 'action' } }
]
export const searchSchema = [
  {
    key: 'userName',
    label: '用户名',
    type: 'input',
    placeholder: '请输入用户名'
  },
  {
    key: 'realName',
    label: '真实姓名',
    type: 'input',
    placeholder: '请输入真实姓名'
  },
  {
    key: 'sex',
    label: '性别',
    type: 'select',
    placeholder: '请选择性别',
    options: [
      { label: '男', value: 1 },
      { label: '女', value: 2 }
    ]
  },
  {
    key: 'phone',
    label: '手机号',
    type: 'input',
    placeholder: '请输入手机号'
  },
  {
    key: 'status',
    label: '状态',
    type: 'select',
    placeholder: '请选择状态',
    options: [
      { label: '正常', value: 1 },
      { label: '冻结', value: 2 }
    ]
  }
]
