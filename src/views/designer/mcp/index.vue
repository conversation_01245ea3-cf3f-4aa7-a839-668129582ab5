<template>
  <div class="h-full bg-global-background">
    <BaseTable
      :search-schema="searchSchema"
      :columns="COLUMNS as any"
      :dataSource="mcpList"
      :loading="loading"
      :total="total"
      :current="currentPage"
      :pageSize="pageSize"
      @search="handleSearch"
      @reset="handleReset"
      @pageChange="handlePageChange"
    >
      <template #type="{ record }">
        <a-tag :color="getTypeColor(record.type)">
          {{ record.type.toUpperCase() }}
        </a-tag>
      </template>

      <!-- 访问地址列 -->
      <template #path="{ record }">
        <div class="flex items-center">
          <a-tooltip title="复制地址">
            <a-button type="link" size="small" @click="copyPath(record.path)">
              <template #icon><CopyOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-popover placement="right" trigger="click" :overlayStyle="{ maxWidth: '500px' }">
            <template #content>
              <div class="p-3 bg-gray-50 rounded-sm break-all max-w-full">
                {{ record.path }}
              </div>
              <div class="mt-2 flex justify-end">
                <a-button type="primary" size="small" @click="copyPath(record.path)">
                  <template #icon><CopyOutlined /></template>
                  复制
                </a-button>
              </div>
            </template>
            <a-tooltip title="查看地址">
              <a-button type="link" size="small">
                <template #icon><EyeOutlined /></template>
              </a-button>
            </a-tooltip>
          </a-popover>
        </div>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-tooltip title="编辑">
            <a-button type="link" @click="handleEdit(record)">
              <template #icon><EditOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="测试MCP" v-if="record.type === 'mcp'">
            <a-button type="link" @click="handleTestMcp(record)">
              <template #icon><LaptopOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="删除">
            <a-button type="link" danger @click="showDeleteConfirm(record)">
              <template #icon><DeleteOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="跳转到画布" v-if="record.type === 'mcp'">
            <a-button type="link" @click="handleGoToCanvas(record)">
              <template #icon><BlockOutlined /></template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>

    <PublishModal ref="publishModalRef" />

    <McpTester v-model:visible="mcpTesterVisible" :mcpData="currentMcpData" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  DeleteOutlined,
  EditOutlined,
  LaptopOutlined,
  CopyOutlined,
  EyeOutlined,
  BlockOutlined
} from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import { BaseTable } from '@/components'
import PublishModal from '../canvas/components/toolbar/publishModal/index.vue'
import { useDesignerStore } from '@/store/designer'
import McpTester from './components/mcpTester/index.vue'
import { getEndpointPage, deleteEndpoint, getFileApi } from '@/apis/designer'

// MCP常量
const TYPE_COLORS = {
  'mcp': 'blue',
}

const PAGINATION = {
  PAGE_SIZES: ['10', '20', '50', '100'],
  DEFAULT_PAGE_SIZE: 10,
  DEFAULT_CURRENT_PAGE: 1,
}

interface McpItem {
  id: string
  project: string
  subsystem: string
  module: string
  description: string
  type: 'mcp'
  config: string
  pathUrl: string
  createTime: string
  updateTime: string
  templateId: string
  reqParam?: string
  path?: string
}

// 搜索配置类型
interface SearchField {
  key: string
  label: string
  type: 'input' | 'select' | 'date'
  placeholder: string
}

// 状态
const loading = ref(false)
const currentPage = ref(PAGINATION.DEFAULT_CURRENT_PAGE)
const pageSize = ref(PAGINATION.DEFAULT_PAGE_SIZE)
const total = ref(0)
const mcpList = ref<McpItem[]>([])

// MCP测试相关
const mcpTesterVisible = ref(false)
const currentMcpData = ref<McpItem>({} as McpItem)

// 路由
const router = useRouter()

// 搜索配置
const searchSchema: SearchField[] = [
  {
    key: 'description',
    label: '描述',
    type: 'input',
    placeholder: '请输入描述关键词'
  },
]

// 定义表格列
const COLUMNS = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    align: 'center',
    customRender: ({ index }: { index: number }) => {
      return (currentPage.value - 1) * pageSize.value + index + 1
    },
  },
  {
    title: '产品/项目',
    dataIndex: 'project',
    width: 150,
    key: 'project',
  },
  {
    title: '子系统',
    dataIndex: 'subsystem',
    width: 150,
    key: 'subsystem',
  },
  {
    title: '模块',
    dataIndex: 'module',
    width: 150,
    key: 'module',
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 200,
    ellipsis: true,
    key: 'description',
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    key: 'type',
    slots: { customRender: 'type' },
  },
  {
    title: '访问地址',
    dataIndex: 'path',
    width: 200,
    ellipsis: true,
    key: 'path',
    slots: { customRender: 'path' },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    key: 'createTime',
    customRender: ({ text }: { text: string }) => text ? new Date(text).toLocaleString() : '',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 160,
    key: 'updateTime',
    customRender: ({ text }: { text: string }) => text ? new Date(text).toLocaleString() : '',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
]

// 表单
const form = reactive({
  project: '',
  subsystem: '',
  module: '',
  description: '',
  type: 'mcp',
  config: '',
  file: null as File | null
})

// 获取数据 - 使用现有的getEndpointPage API
const fetchData = async (params: Record<string, any> = {}) => {
  loading.value = true
  try {
    const { current = 1, pageSize = 10, ...rest } = params
    // API需要使用size而不是pageSize
    const res = await getEndpointPage({
      current,
      size: pageSize,
      type: 'mcp',
      ...rest
    })
    if (res.data) {
      mcpList.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    message.error('获取数据失败：' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (params: Record<string, any>) => {
  currentPage.value = 1
  fetchData({ current: 1, pageSize: pageSize.value, ...params })
}

// 重置处理
const handleReset = () => {
  currentPage.value = 1
  fetchData({ current: 1, pageSize: pageSize.value })
}

// 分页处理
const handlePageChange = (pagination: { current: number; pageSize: number; searchForm?: Record<string, any> }) => {
  currentPage.value = pagination.current
  pageSize.value = pagination.pageSize
  fetchData({
    current: pagination.current,
    pageSize: pagination.pageSize,
    ...pagination.searchForm
  })
}

// 获取类型对应的颜色
const getTypeColor = (type: string) => {
  return TYPE_COLORS[type as keyof typeof TYPE_COLORS] || 'default'
}

// 删除设计 - 使用现有的deleteEndpoint API
const handleDelete = async (row: McpItem) => {
  try {
    await deleteEndpoint(row.id)
    message.success('删除成功')
    fetchData()
  } catch (error) {
    message.error('删除失败')
  }
}

// 显示删除确认对话框
const showDeleteConfirm = (row: McpItem) => {
  Modal.confirm({
    title: '确定要删除吗？',
    content: `将删除 "${row.description}" 设计`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      return handleDelete(row)
    }
  })
}

// 添加发布模态框引用
const publishModalRef = ref<InstanceType<typeof PublishModal>>()

// 添加编辑处理函数
const handleEdit = async (row: McpItem) => {
  // 准备发布模态框需要的数据
  const editData = {
    currentFile: {
      id: row.templateId
    },
    graphData: row.config,
    endpoint: row
  }

  // 打开发布模态框
  publishModalRef.value?.open(editData)
}

// 添加测试MCP按钮的处理函数
const handleTestMcp = (row: McpItem) => {
  if (row.type !== 'mcp') {
    message.warning('只有MCP类型的设计才能进行测试')
    return
  }
  // 先关闭再打开，确保状态变化
  mcpTesterVisible.value = false
  // 使用setTimeout等待DOM更新
  setTimeout(() => {
    currentMcpData.value = row
    mcpTesterVisible.value = true
  }, 0)
}

// 复制访问地址
const copyPath = (path: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(path)
      .then(() => {
        message.success('已复制到剪贴板')
      })
      .catch(() => {
        message.error('复制失败')
      })
  } else {
    // 兼容性处理
    const textarea = document.createElement('textarea')
    textarea.value = path
    document.body.appendChild(textarea)
    textarea.select()
    try {
      document.execCommand('copy')
      message.success('已复制到剪贴板')
    } catch (err) {
      message.error('复制失败')
    }
    document.body.removeChild(textarea)
  }
}

// 跳转到画布
const handleGoToCanvas = async (record: McpItem) => {
  try {
    // 获取设计器store
    const designerStore = useDesignerStore()

    // 获取文件内容并处理
    const res = await getFileApi({id: record.templateId})
    if (res && res.data) {
      const fileContent = res.data

      // 构建标签页数据
      const tab = {
        id: record.templateId,
        name: fileContent.name || record.description || '未命名',
        path: record.path || '',
        directoryId: '',
        content: JSON.parse(fileContent.content)
      }

      // 添加到设计器的标签页中
      designerStore.addTab(tab)

      // 加载到画布
      if (designerStore.modGraph?.fromJSON) {
        designerStore.modGraph.fromJSON(tab.content)
      }

      // 关闭右侧面板
      designerStore.setRightPanelVisiable(false)
    }

    // 跳转到画布页面
    router.push({
      path: '/designer/canvas',
      query: {
        tarId: record.templateId
      }
    })
  } catch (error) {
    console.error('加载画布文件失败:', error)
    message.error('加载画布文件失败')
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.w-full {
  width: 100%;
}

.config-content {
  max-height: 400px;
  overflow-y: auto;
}

.description {
  background-color: #fafafa;
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
