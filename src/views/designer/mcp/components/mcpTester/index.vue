<template>
  <div>
    <a-modal
      :visible="visible"
      title="测试MCP"
      width="800px"
      :footer="null"
      :destroyOnClose="true"
      @cancel="handleCancel"
    >
      <div class="p-4">
        <a-form layout="vertical">
          <a-form-item label="MCP信息">
            <a-descriptions bordered size="small">
              <a-descriptions-item
                label="描述"
                :span="3"
                >{{ mcpData.description }}</a-descriptions-item
              >
              <a-descriptions-item
                label="项目"
                :span="1"
                >{{ mcpData.project }}</a-descriptions-item
              >
              <a-descriptions-item
                label="子系统"
                :span="1"
                >{{ mcpData.subsystem }}</a-descriptions-item
              >
              <a-descriptions-item label="模块" :span="1">{{ mcpData.module }}</a-descriptions-item>
            </a-descriptions>
          </a-form-item>

          <a-form-item label="输入参数">
            <a-textarea
              v-model:value="inputParams"
              placeholder="请输入测试参数 (JSON格式)"
              :auto-size="{ minRows: 5, maxRows: 10 }"
            />
          </a-form-item>

          <div class="flex justify-between mb-4">
            <a-button @click="resetParams">重置参数</a-button>
            <a-button type="primary" @click="executeTest" :loading="loading">执行测试</a-button>
          </div>

          <a-divider />

          <a-form-item label="执行结果">
            <div class="border rounded-sm p-4 bg-gray-50 min-h-[150px] max-h-[300px] overflow-auto">
              <pre v-if="result" class="whitespace-pre-wrap">{{ result }}</pre>
              <div v-else class="text-gray-400">暂无结果</div>
            </div>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mcpData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

// 处理关闭弹窗
const handleCancel = () => {
  emit('update:visible', false)
}

// 输入参数
const inputParams = ref('')
// 执行结果
const result = ref('')
// 加载状态
const loading = ref(false)

// 重置参数
const resetParams = () => {
  try {
    // 如果API有预设参数，则使用预设参数
    if (props.mcpData.reqParam) {
      inputParams.value = JSON.stringify(JSON.parse(props.mcpData.reqParam), null, 2)
    } else {
      // 否则使用默认空对象
      inputParams.value = JSON.stringify({}, null, 2)
    }
  } catch (error) {
    inputParams.value = JSON.stringify({}, null, 2)
  }
}

// 执行测试
const executeTest = async () => {
  loading.value = true
  result.value = ''

  try {
    // 验证输入参数是否为有效JSON
    const params = JSON.parse(inputParams.value)

    // 模拟测试执行
    setTimeout(() => {
      result.value = JSON.stringify({
        success: true,
        timestamp: new Date().toISOString(),
        data: {
          message: 'MCP测试执行成功',
          response: {
            // 模拟测试响应
            result: '这是一个MCP测试响应',
            requestParams: params,
            mcpId: props.mcpData.id,
            executionTime: '100ms'
          }
        }
      }, null, 2)
      loading.value = false
    }, 1000)

    // 实际场景可能需要调用API
    // const res = await testMcpApi({
    //   id: props.mcpData.id,
    //   params
    // })
    // result.value = JSON.stringify(res, null, 2)

  } catch (error) {
    message.error('执行测试失败: ' + (error as Error).message)
    result.value = JSON.stringify({
      success: false,
      message: '执行测试失败',
      error: (error as Error).message
    }, null, 2)
    loading.value = false
  }
}

// 监听mcpData变化，重置参数
watch(
  () => props.mcpData,
  () => {
    resetParams()
  },
  { immediate: true }
)

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    if (!val) {
      emit('update:visible', false)
    }
  }
)
</script>

<style scoped>
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
</style>
