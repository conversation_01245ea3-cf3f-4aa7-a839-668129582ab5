<template>
  <div class="enum-container">
    <BaseTable
      :current="current"
      :pageSize="pageSize"
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :search-schema="searchSchema"
      :total="total"
      @search="handleSearch"
      @reset="handleReset"
      @pageChange="handlePageChange"
    >
      <template #actions>
        <a-button type="primary" @click="handleAdd">新增枚举</a-button>
      </template>

      <template #content="{ text }">
        <template v-if="text && text.length">
          <a-tooltip v-if="text.length > 3" :title="formatContentTooltip(text)">
            <div class="tag-container">
              <a-tag v-for="(item, index) in text.slice(0, 3)" :key="index" color="blue">
                {{ item }}
              </a-tag>
              <a-tag v-if="text.length > 3" class="dark:!text-white dark:!bg-input-fill dark:!border-input-border">...</a-tag>
            </div>
          </a-tooltip>
          <div v-else class="tag-container">
            <a-tag v-for="(item, index) in text" :key="index" color="blue">
              {{ item }}
            </a-tag>
          </div>
        </template>
      </template>

      <template #action="{ record }">
        <a-space>
          <a-tooltip title="编辑">
            <a-button type="link" @click="handleEdit(record)">
              <template #icon><EditOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="删除">
            <a-button type="link" danger @click="handleDelete(record)">
              <template #icon><DeleteOutlined /></template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>

    <!-- 枚举表单 -->
    <EnumForm v-model:visible="formVisible" :form-data="currentRecord" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { message, Modal, Tag, Tooltip } from 'ant-design-vue'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import BaseTable from '@/components/BaseTable/index.vue'
import EnumForm from './components/EnumForm.vue'
import { getDesignerEnumPage, deleteDesignerEnum } from '@/apis/designer'
import { columns, searchSchema } from './constant'

// 表格数据
const dataSource = ref<any[]>([])
const loading = ref(false)
const total = ref(0)
const formVisible = ref(false)
const currentRecord = ref(null)
const current = ref(1)
const pageSize = ref(10)

// 获取数据
const fetchData = async (params = {}) => {
  loading.value = true
  try {
    const queryParams = {
      current: params.current || 1,
      size: params.size || 10,
      ...params
    }

    const response = await getDesignerEnumPage(queryParams)
    if (response.code === 200) {
      // 数据标准化处理
      dataSource.value = (response.data.records || []).map(item => ({
        ...item,
        // 统一转换为标准数组格式
        content: normalizeContent(item.content)
      }))
      total.value = response.data.total          // 获取真实总数
    } else {
      message.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取枚举数据失败:', error)
    message.error('获取数据失败')
    dataSource.value = []
  } finally {
    loading.value = false
  }
}

// 内容标准化方法
const normalizeContent = (content: any): string[] => {
  try {
    if (Array.isArray(content)) return content
    if (typeof content === 'string') return JSON.parse(content)
    return []
  } catch (e) {
    console.error('Content解析异常:', content, e)
    return []
  }
}

// 搜索处理
const handleSearch = (params) => {
  fetchData({
    ...params,
    current: 1
  })
}

// 重置处理
const handleReset = () => {
  fetchData({ current: 1 })
}

// 分页处理
const handlePageChange = ({ current: newCurrent, size: newSize, searchForm }) => {
  current.value = newCurrent // 更新当前页码
  pageSize.value = newSize   // 更新每页大小
  fetchData({
    current: newCurrent,
    size: newSize,
    ...searchForm
  })
}

// 新增处理
const handleAdd = () => {
  currentRecord.value = null
  formVisible.value = true
}

// 编辑处理
const handleEdit = (record) => {
  currentRecord.value = record
  formVisible.value = true
}

// 删除处理
const handleDelete = (record) => {
  if (!record?.id) {
    message.error('无效的记录ID')
    return
  }

  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该枚举吗？',
    async onOk() {
      try {
        const response = await deleteDesignerEnum(record.id)
        if (response.code === 200) {
          message.success('删除成功')
          fetchData()
        } else {
          message.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 表单提交成功处理
const handleSuccess = () => {
  fetchData()
}

// 格式化内容为完整的tooltip显示
const formatContentTooltip = (list: string[]) => {
  if (Array.isArray(list)) {
    return list.join(', ')
  }
  return ''
}

// 初始化
fetchData()
</script>

<style lang="scss" scoped>
.enum-container {
  padding: 0;
  background: var(--global-background-color);
  border-radius: 4px;
}

.tag-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 4px;
  overflow: hidden;

  .ant-tag {
    margin-right: 0;
    white-space: nowrap;
  }
}
</style>
