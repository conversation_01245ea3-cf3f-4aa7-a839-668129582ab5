import dayjs from 'dayjs'

// 表格列定义
export const columns = [
  {
    title: '枚举类型',
    dataIndex: 'type',
    width: 200,
  },
  {
    title: '枚举内容',
    dataIndex: 'content',
    width: 400,
    slots: { customRender: 'content' },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 180,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
  },
  {
    title: '操作',
    width: 120,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
]

// 搜索配置
export const searchSchema = [
  {
    key: 'type',
    label: '枚举类型',
    type: 'input',
    placeholder: '请输入枚举类型',
  },
]
