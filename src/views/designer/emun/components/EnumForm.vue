<template>
  <a-modal
    :visible="visible"
    :title="formData ? '编辑枚举' : '新增枚举'"
    width="600px"
    @cancel="handleCancel"
    @ok="handleSubmit"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <a-form-item label="枚举类型" name="type">
        <a-input v-model:value="form.type" placeholder="请输入枚举类型" />
      </a-form-item>

      <!-- 枚举内容列表 -->
      <a-form-item label="枚举内容" name="content">
        <div class="enum-content">
          <div class="input-area">
            <a-input
              v-model:value="newContent"
              placeholder="请输入内容"
              @pressEnter="handleAddContent"
            >
              <template #suffix>
                <PlusOutlined @click="handleAddContent" />
              </template>
            </a-input>
          </div>

          <div class="tags-area">
            <a-tag
              v-for="(item, index) in form.content"
              :key="item + '_' + index"
              closable
              color="blue"
              @close="removeItem(index)"
            >
              {{ item }}
            </a-tag>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { createDesignerEnum, updateDesignerEnum } from '@/apis/designer'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单实例
const formRef = ref<FormInstance>()
const newContent = ref('')

// 表单数据
const form = ref({
  type: '',
  content: [] as string[]
})

// 表单校验规则
const rules = {
  type: [{ required: true, message: '请输入枚举类型', trigger: 'blur' }],
  content: [{
    type: 'array',
    required: true,
    message: '请至少添加一个内容',
    validator: async (_rule: any, value: string[]) => {
      if (!Array.isArray(value) || value.length === 0) {
        throw new Error('请至少添加一个内容')
      }
      if (!value.every(item => item)) {
        throw new Error('枚举内容不能为空')
      }
      return true
    }
  }]
}

// 监听弹窗显示状态，当打开时重置表单
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      if (props.formData) {
        const content = props.formData.content
        console.log('编辑模式接收到的原始content:', JSON.stringify(content))

        let parsedContent
        try {
          parsedContent = typeof content === 'string' ?
            JSON.parse(content) :
            (Array.isArray(content) ? content : [])
        } catch (e) {
          console.error('Content parsing error:', e)
          parsedContent = []
        }

        console.log('处理后的content数组:', JSON.stringify(parsedContent))

        // 强制使用新数组
        form.value = {
          type: props.formData.type,
          content: [...parsedContent]
        }

        console.log('设置到表单的content:', JSON.stringify(form.value.content))
      } else {
        form.value = {
          type: '',
          content: []
        }
      }
      newContent.value = ''
      formRef.value?.clearValidate()
    }
  }
)

// 添加内容项
const handleAddContent = () => {
  if (!newContent.value.trim()) {
    message.warning('请输入内容')
    return
  }

  // 检查重复
  if (form.value.content.includes(newContent.value.trim())) {
    message.warning('内容已存在')
    return
  }

  // 创建新数组添加内容
  form.value.content = [...form.value.content, newContent.value.trim()]
  newContent.value = ''
}

// 删除内容项
const removeItem = (index: number) => {
  const newContent = form.value.content.filter((_, i) => i !== index)
  form.value.content = [...newContent]
  formRef.value?.validateFields(['content'])
}

// 取消
const handleCancel = () => {
  // 清空表单数据
  form.value = {
    type: '',
    content: []
  }
  // 清空临时输入框
  newContent.value = ''
  // 清除验证状态
  formRef.value?.clearValidate()
  // 关闭弹窗
  emit('update:visible', false)
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    // 确保提交的数据是正确的格式
    const submitData = {
      type: form.value.type,
      content: JSON.stringify([...form.value.content]) // 创建新数组再序列化
    }

    let response
    if (props.formData) {
      response = await updateDesignerEnum({
        ...submitData,
        id: props.formData.id
      })
    } else {
      response = await createDesignerEnum(submitData)
    }

    if (response.code === 200) {
      message.success(props.formData ? '更新成功' : '创建成功')
      emit('success')
      handleCancel()
    } else {
      message.error(response.message || '操作失败')
    }
  } catch (error: any) {
    console.error('表单提交失败:', error)
    message.error(error.message || '表单提交失败')
  }
}
</script>

<style lang="scss" scoped>
.enum-content {
  .input-area {
    margin-bottom: 16px;

    :deep(.anticon) {
      cursor: pointer;
      color: #1890ff;

      &:hover {
        color: #40a9ff;
      }
    }
  }

  .tags-area {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    min-height: 32px;
    padding: 4px 8px;
    background: #fafafa;
    @apply dark:!bg-area-fill dark:!border-input-border;
    border: 1px solid #d9d9d9;
    border-radius: 2px;

    .ant-tag {
      margin: 2px;
      display: inline-flex;
      align-items: center;
    }
  }
}
</style>
