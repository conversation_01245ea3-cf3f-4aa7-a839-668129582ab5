<template>
  <div class="flex flex-col bg-white min-h-full dark:!bg-[var(--background-color)] dark:!text-white">
    <!-- 顶部导航 -->
    <div class="flex items-center px-4 py-2">
      <!-- 左侧和中间区域（带下边框） -->
      <div class="flex-1 flex items-center justify-between border-b pb-2">
        <div class="text-[18px]">
          <a-space>
            <span class="text-[18px]">编排</span>
            <a-select v-model:value="agentMode" style="width: 180px" size="middle">
              <a-select-option value="llm">单 Agent (LLM模式)</a-select-option>
            </a-select>
          </a-space>
        </div>
      </div>

      <!-- 右侧区域（无边框） -->
      <div class="w-[490px] pl-4">
        <a-space>
          <span class="text-[18px]">预览与调试</span>
        </a-space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex flex-1 overflow-hidden">
      <!-- 左侧面板 -->
      <div class="w-[480px] border-r flex-shrink-0">
        <div class="p-4">
          <!-- 固定的标题 -->
          <div class="flex items-center justify-between mb-4">
            <span class="font-medium">人设与回复逻辑</span>
            <a-tag color="purple" class="cursor-pointer" @click="handleOptimize">优化</a-tag>
          </div>

          <div class="space-y-4">
            <a-textarea
              v-model:value="roleDescription"
              :auto-size="{ minRows: 3, maxRows: 6 }"
              placeholder="请输入角色描述"
              :bordered="false"
              class="bg-gray-50 rounded"
            />
          </div>

          <!-- 这里可以添加其他可编辑的部分 -->
        </div>
      </div>

      <!-- 中间工作区 -->
      <div class="flex-1">
        <div class="p-4 h-full">
          <div class="bg-gray-50 rounded-lg h-full">
            <div class="flex items-center justify-between p-3">
              <div class="flex items-center space-x-2">
                <span class="font-medium">工作流</span>
              </div>
              <a-button type="text" class="!p-0">
                <template #icon><PlusOutlined /></template>
              </a-button>
            </div>
            <div class="p-4 bg-white h-[calc(100%-48px)]">
              <p class="text-gray-600 text-sm leading-relaxed">
                工作流支持通过可视化的方式，对插件、大语言模型、代码块等功能进行组合，从而实现复杂、稳定的业务流程编排，例如旅行规划、报告分析等。
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧聊天区 -->
      <div class="flex-shrink-0 mr-2">
        <AIChatContainer
          :key="chatKey"
          :width="480"
          :systemShowChatHeader="true"
          class="bg-white dark:!bg-global-background"
          :sseUrl="`${PREFIX_DEBUG}?stream=true&tabId=${TAB_ID}&aip-session=${AI_TOKEN}`"
          :sendSSEMessage="handleSendSSEMessage"
          :menuList="[]"
          :selectInfo="{
            name: '',
            data: []
          }"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PREFIX_DEBUG } from '@/apis/designer'
import {
PlusOutlined
} from '@ant-design/icons-vue'
import { AIChatContainer } from '@yss/ai-chat-components'
import { v4 as uuidv4 } from 'uuid'
import { ref } from 'vue'

const agentMode = ref('llm')
const roleDescription = ref('你是火影忍者中的主角波涧鸣人。从吊车尾逐渐成长为火之国的火影，有一位妻子和两个孩子。')
const chatKey = ref(0)
const TAB_ID = ref(uuidv4())
const AI_TOKEN = ref(uuidv4())

// 处理发送消息
const handleSendSSEMessage = async ({ question }) => {
  try {
    const formData = new FormData()
    formData.append('data', JSON.stringify({ question }))
    // 这里需要实现实际的消息发送逻辑
    return { code: 200, data: 'Success' }
  } catch (error) {
    console.error('Error sending message:', error)
  }
}

const handleOptimize = () => {
  console.log('优化')
}
</script>

<style scoped>
:deep(.ant-input) {
  background: #f9fafb;
}

:deep(.ant-btn-text) {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn-text:hover) {
  background: rgba(0, 0, 0, 0.04);
}
</style>
