<template>
  <div class="bg-global-background">
    <a-tabs v-model:activeKey="activeTab">
      <a-tab-pane key="endpoints" tab="接入API">
        <EndpointComponent />
      </a-tab-pane>
      <a-tab-pane key="metateam" tab="Agents元团队">
        <SquareComponent />
      </a-tab-pane>
      <a-tab-pane key="mcp" tab="MCP端">
        <template #tab>
          <span>
            <laptop-outlined />
            <span class="ml-1">MCP端</span>
          </span>
        </template>
        <McpComponent />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import EndpointComponent from '../files/index.vue'
import SquareComponent from '../agentSquare/index.vue'
import McpComponent from '../mcp/index.vue'
import { LaptopOutlined } from '@ant-design/icons-vue'

const activeTab = ref('metateam')
</script>
