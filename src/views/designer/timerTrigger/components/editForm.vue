<template>
    <a-modal :visible="visible" :title="formData ? '编辑定时任务' : '新增定时任务'" width="600px" :maskClosable="false"
        @cancel="handleCancel">
        <template #footer>
            <a-button @click="handleCancel">
                取消
            </a-button>
            <a-button type="primary" :loading="loading" @click="handleSubmit">
                确认
            </a-button>
        </template>

        <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
            <!-- 触发器名称 -->
            <a-form-item label="触发器名称" name="triggerName">
                <a-input v-model:value="form.triggerName" placeholder="请输入触发器名称" />
            </a-form-item>

            <!-- 描述信息 -->
            <a-form-item label="描述信息" name="description">
                <a-input v-model:value="form.description" placeholder="请输入描述信息" />
            </a-form-item>

        </a-form>
    </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { updateTimerTrigger } from '@/apis/timer'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    formData: {
        type: Object,
        default: null
    }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref<FormInstance>()
const loading = ref(false)


// 表单数据
const form = reactive({
    id: '',
    triggerName: '',
    description: '',
})

// 表单验证规则
const rules = {}
// 监听表单数据变化
watch(
    () => props.formData,
    (newVal) => {
        if (newVal) {
            // 编辑模式，填充表单
            Object.assign(form, {
                id: newVal.id || '',
                triggerName: newVal.triggerName || '',
                description: newVal.description || '',
            })
        }
    },
    { immediate: true }
)

// 提交表单
const handleSubmit = () => {
    formRef.value?.validate().then(async () => {
        loading.value = true
        try {
            const api = updateTimerTrigger
            const response = await api(form)

            if (response.code === 200) {
              message.success('编辑成功')
              emit('success')
              emit('update:visible', false)
            } else {
              message.error(response.msg || '编辑失败')
            }
        } catch (error) {
            message.error('编辑失败')
        } finally {
            loading.value = false
        }
    })
}

// 取消
const handleCancel = () => {
    formRef.value?.resetFields()
    emit('update:visible', false)
}

// 初始化
onMounted(() => {
})
</script>

<style scoped>
.ant-form-item {
    margin-bottom: 16px;
}
</style>