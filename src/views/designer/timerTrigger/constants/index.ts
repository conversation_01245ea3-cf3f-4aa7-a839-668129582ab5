import dayjs from 'dayjs'

// 状态类型
export const STATUS_COLORS = ['red', 'green']
// 触发时间类型
export const TRIGGER_TYPES = {
    CRON: 'cron',
    PRESET: 'preset',
  } as const
// 类型颜色映射
export const TYPE_COLORS = {
    [TRIGGER_TYPES.CRON]: 'blue',
    [TRIGGER_TYPES.PRESET]: 'orange',
  } as const

// 表格列定义
export const getColumns = (page = { current: 1, pageSize: 10 }) => [
  {
    title: '序号',
    width: 80,
    align: 'center',
    customRender: ({ index }) => {
      // 手动计算全局序号
      return (page.current - 1) * page.pageSize + index + 1
    },
  },
  {
    title: '触发器名称',
    dataIndex: 'triggerName',
    width: 150,
    key: 'triggerName',
  },
  {
    title: '关联的工作流ID',
    dataIndex: 'workflowId',
    width: 150,
    key: 'workflowId',
  },
  {
    title: '执行路径',
    dataIndex: 'path',
    width: 250,
    ellipse: true,
    key: 'path',
  },
  {
    title: '描述信息',
    dataIndex: 'description',
    width: 200,
    ellipse: true,
    key: 'description',
  },
  {
    title: '用户ID',
    dataIndex: 'userId',
    width: 100,
    key: 'userId',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    key: 'status',
    slots: { customRender: 'status' },
  },
  {
    title: '时区',
    dataIndex: 'timezone',
    width: 160,
    key: 'timezone',
  },
  {
    title: '触发时间类型',
    dataIndex: 'triggerTimeType',
    width: 160,
    key: 'triggerTimeType',
    slots: { customRender: 'triggerTimeType' },
  },
  {
    title: 'cron表达式',
    dataIndex: 'cronExpression',
    width: 200,
    ellipsis: true,
    key: 'cronExpression',
  },
  {
    title: '触发时间',
    dataIndex: 'triggerTime',
    width: 200,
    key: 'triggerTime',
  },
  {
    title: '频率类型',
    dataIndex: 'frequencyType',
    width: 100,
    key: 'frequencyType',
  },
  {
    title: '频率天数',
    dataIndex: 'frequencyDay',
    width: 100,
    key: 'frequencyDay',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    key: 'createTime',
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 160,
    key: 'updateTime',
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
]

export const COLUMNS = getColumns()
