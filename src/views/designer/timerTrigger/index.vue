<template>
    <div class="h-full bg-global-background">
        <BaseTable :search-schema="searchSchema" :columns="COLUMNS" :dataSource="timerTriggerList" :loading="loading"
            :total="total" :current="currentPage" :pageSize="pageSize" @search="handleSearch" @reset="handleReset"
            @pageChange="handlePageChange">

            <!-- 状态列 -->
            <template #status="{ record }">
                <a-tag :color="getStatusColor(record.status)">
                    {{ record.status === 1 ? '启用' : '禁用' }}
                </a-tag>
            </template>

            <!-- 触发时间类型列 -->
            <template #triggerTimeType="{ record }">
                <a-tag :color="getTypeColor(record.triggerTimeType)">
                    {{ record.triggerTimeType.toUpperCase() }}
                </a-tag>
            </template>

            <!-- 操作列 -->
            <template #action="{ record }">
                <a-space>
                    <a-tooltip title="编辑">
                        <a-button type="link" @click="handleEdit(record)">
                            <template #icon>
                                <EditOutlined />
                            </template>
                        </a-button>
                    </a-tooltip>
                    <a-tooltip title="删除">
                        <a-button type="link" danger @click="showDeleteConfirm(record)">
                            <template #icon>
                                <DeleteOutlined />
                            </template>
                        </a-button>
                    </a-tooltip>
                    <a-tooltip :title="record.status === 0 ? '启用' : '禁用'">
                        <a-button type="link" :danger="record.status === 1" @click="showStatusChange(record)">
                            <PlayCircleOutlined v-if="record.status === 0" />
                            <PauseCircleOutlined v-else />
                        </a-button>
                    </a-tooltip>
                </a-space>
            </template>

        </BaseTable>

        <!-- 编辑对话框 -->
        <EditForm v-model:visible="formVisible" :form-data="currentRecord" @success="handleSuccess" />
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { BaseTable } from '@/components'
import { COLUMNS, TYPE_COLORS, STATUS_COLORS } from './constants'
import { DeleteOutlined, EditOutlined, PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import EditForm from './components/editForm.vue'
import { getTimerTriggerPage, deleteTimerTrigger, updateTimerTrigger } from '@/apis/timer'

// 获取定时器触发器列表
interface TimerTrigger {
  triggerName: string
  workflowId: string
  path: string
  timezone: string
  description: string
  status: number
  triggerTimeType: string
  cronExpression: string
  triggerTime: string
  frequencyType: string
  frequencyDay: number
  createTime: string
  updateTime: string
}

const timerTriggerList = ref<TimerTrigger[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

const searchSchema = ref([
    {
        key: 'name',
        label: '触发器名称',
        type: 'input',
        placeholder: '请输入触发器名称'
    },
    {
        key: 'status',
        label: '状态',
        type: 'select',
        placeholder: '请选择状态',
        options: [
            { label: '启用', value: 1 },
            { label: '禁用', value: 0 }
        ]
    },
])

// 获取状态对应的颜色
const getStatusColor = (type: number) => {
    return STATUS_COLORS[type as keyof typeof STATUS_COLORS] || 'default'
}

// 获取类型对应的颜色
const getTypeColor = (type: string) => {
  return TYPE_COLORS[type as keyof typeof TYPE_COLORS] || 'default'
}

// 搜索处理
const handleSearch = (params) => {
  console.log(params)
  currentPage.value = 1
  fetchData({ current: 1, pageSize: pageSize.value, ...params })
}

// 重置处理
const handleReset = () => {
  currentPage.value = 1
  fetchData({ current: 1, pageSize: pageSize.value })
}

// 分页处理
const handlePageChange = (pagination) => {
  currentPage.value = pagination.current
  pageSize.value = pagination.pageSize
  fetchData({
    current: pagination.current,
    pageSize: pagination.pageSize,
    ...pagination.searchForm
  })
}

// 编辑弹窗
const formVisible = ref(false)
// 编辑数据
const currentRecord = ref()
// 编辑处理函数
const handleEdit = (record: TimerTrigger) => {
    formVisible.value = true
    currentRecord.value = record
}
// 表单提交成功处理
const handleSuccess = () => {
  fetchData({ current: 1, pageSize: pageSize.value })
  formVisible.value = false
}

// 显示删除确认对话框
const showDeleteConfirm = (record: TimerTrigger) => {
  Modal.confirm({
    title: '确定要删除吗？',
    content: `将删除 "${record.triggerName}" 触发器`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      return handleDelete(record)
    }
  })
}

// 删除
const handleDelete = async (record: TimerTrigger) => {
  try {
    await deleteTimerTrigger(record.id)
    message.success('删除成功')
    fetchData({ current: 1, pageSize: pageSize.value })
  } catch (error) {
    message.error('删除失败')
  }
}

// 显示状态切换对话框
const showStatusChange = (record: TimerTrigger) => {
    Modal.confirm({
    title: '确定要切换状态吗？',
    content: `将切换 "${record.triggerName}" 触发器状态`,
    okText: '确定',
    okType: 'primary',
    cancelText: '取消',
    onOk() {
      return handleStatusChange(record)
    }
  })
}

// 处理状态切换
const handleStatusChange = async (record) => {
  try {
    // 调用API更新状态
    await updateTimerTrigger({
      id: record.id,
      status: record.status === 0 ? 1 : 0
    })
    // // 刷新数据
    fetchData({ current: 1, pageSize: pageSize.value })
    message.success('状态更新成功')
  } catch (error) {
    message.error('状态更新失败')
  }
}

// 获取数据
const fetchData = async (params = {}) => {
  loading.value = true
  try {
    const { current = 1, pageSize = 10, ...rest } = params
    const res = await getTimerTriggerPage({ current, pageSize, ...rest })
    if (res.data) {
      timerTriggerList.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    message.error('获取数据失败：' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
})

</script>
