<template>
  <div class="h-full bg-global-background">
    <BaseTable
      :search-schema="searchSchema"
      :columns="COLUMNS"
      :dataSource="crawlerList"
      :loading="loading"
      :total="total"
      :current="currentPage"
      :pageSize="pageSize"
      :showPagination="true"
      :scroll="{ x: 'max-content' }"
      @search="handleSearch"
      @reset="handleReset"
      @pageChange="handlePageChange"
    >

      <!-- 操作栏 -->
      <template #actions>
        <a-button type="primary" @click="handleAdd">
          <PlusOutlined />
          新增爬虫
        </a-button>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-tooltip :title="record.status === 'active' ? '爬虫运行中' : '启动爬虫'">
            <a-button 
              type="text" 
              size="small" 
              @click="handleStartCrawler(record)"
            >
              <PauseCircleOutlined v-if="record.status === 'active'" class="text-green-500" />
              <PlayCircleOutlined v-else class="text-blue-500" />
            </a-button>
          </a-tooltip>
          <a-tooltip title="编辑">
            <a-button type="text" size="small" @click="handleEdit(record)">
              <EditOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="删除">
            <a-button type="text" size="small" danger @click="handleDeleteConfirm(record)">
              <DeleteOutlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑爬虫' : '新增爬虫'"
      width="800px"
      :centered="true"
      :body-style="{ maxHeight: '70vh', overflow: 'auto' }"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="爬虫名称" name="name" required>
              <a-input v-model:value="formData.name" placeholder="请输入爬虫名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="爬取目标网址" name="crawlUrl" required>
              <a-input v-model:value="formData.crawlUrl" placeholder="请输入目标网址" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="列表点击路径" name="clickListPath" required>
              <a-input v-model:value="formData.clickListPath" placeholder="CSS选择器或XPath" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="下一页路径" name="nextPagePath">
              <a-input v-model:value="formData.nextPagePath" placeholder="翻页按钮选择器（可选）" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="标题获取规则" name="getTitle" required>
              <a-input v-model:value="formData.getTitle" placeholder="文章标题选择器" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发布时间获取规则" name="getPubTime" required>
              <a-input v-model:value="formData.getPubTime" placeholder="发布时间选择器" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="文件获取规则" name="getFile">
              <a-input v-model:value="formData.getFile" placeholder="下载文件链接选择器" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="iframe选择器" name="iframe">
              <a-input v-model:value="formData.iframe" placeholder="iframe选择器（可选）" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="内容获取规则" name="getContent" required>
          <a-textarea 
            v-model:value="formData.getContent" 
            placeholder="正文内容选择器"
            :auto-size="{ minRows: 2, maxRows: 4 }"
          />
        </a-form-item>

        <a-form-item label="爬取函数配置" name="crawlFunction">
          <a-textarea
            v-model:value="formData.crawlFunction"
            placeholder="自定义爬取逻辑（可选）"
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>

        <a-form-item label="主要额外信息" name="mainExtraInfo">
          <a-textarea 
            v-model:value="formData.mainExtraInfo" 
            placeholder="额外提取信息配置"
            :auto-size="{ minRows: 2, maxRows: 4 }"
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="请求头配置" name="headers">
              <a-textarea 
                v-model:value="formData.headers" 
                placeholder="自定义请求头配置（JSON格式）"
                :auto-size="{ minRows: 2, maxRows: 4 }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="代理配置" name="proxy">
              <a-input v-model:value="formData.proxy" placeholder="代理服务器地址" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="超时时间(ms)" name="timeout">
              <a-input-number 
                v-model:value="formData.timeout" 
                :min="1000" 
                :max="60000" 
                :step="1000"
                style="width: 100%"
                placeholder="请求超时时间"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="重试次数" name="retryCount">
              <a-input-number 
                v-model:value="formData.retryCount" 
                :min="0" 
                :max="10" 
                :step="1"
                style="width: 100%"
                placeholder="失败重试次数"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="最大页数" name="maxPages">
              <a-input-number 
                v-model:value="formData.maxPages" 
                :min="1" 
                :max="100" 
                :step="1"
                style="width: 100%"
                placeholder="最大爬取页数"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="描述" name="description">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="爬虫功能描述"
            :auto-size="{ minRows: 2, maxRows: 4 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 删除确认弹窗 -->
    <a-modal
      v-model:open="deleteModalVisible"
      title="删除确认"
      :centered="true"
      @ok="handleDelete"
      @cancel="handleDeleteCancel"
    >
      <p>确定要删除爬虫 <strong>{{ deleteRecord?.name }}</strong> 吗？</p>
      <p class="text-red-500">此操作不可撤销，请谨慎操作。</p>
    </a-modal>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons-vue'
import { BaseTable } from '@/components'
import {
  getCrawlerPage,
  createCrawler,
  updateCrawler,
  deleteCrawler,
  startCrawler,
  type CrawlerConfig
} from '@/apis/crawler'

// 表格列定义
const COLUMNS = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
  },
  {
    title: '爬虫名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: '目标网址',
    dataIndex: 'crawlUrl',
    key: 'crawlUrl',
    width: 300,
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 120,
    slots: { customRender: 'action' },
  },
]

// 搜索配置
const searchSchema = ref([
  {
    key: 'name',
    label: '爬虫名称',
    type: 'input' as const,
    placeholder: '请输入爬虫名称'
  },
])

// 使用从API文件导入的类型

// 数据状态
const crawlerList = ref<CrawlerConfig[]>([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 弹窗状态
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 删除确认状态
const deleteModalVisible = ref(false)
const deleteRecord = ref<CrawlerConfig | null>(null)

// 表单数据
const formData = reactive<CrawlerConfig>({
  name: '',
  crawlUrl: '',
  clickListPath: '',
  nextPagePath: '',
  getTitle: '',
  getPubTime: '',
  getFile: '',
  getContent: '',
  crawlFunction: '',
  mainExtraInfo: '',
  iframe: '',
  headers: "",
  proxy: "",
  timeout: 30000,
  retryCount: 3,
  maxPages: 10,
  description: ""
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入爬虫名称', trigger: 'blur' }],
  crawlUrl: [{ required: true, message: '请输入目标网址', trigger: 'blur' }],
  clickListPath: [{ required: true, message: '请输入列表点击路径', trigger: 'blur' }],
  getTitle: [{ required: true, message: '请输入标题获取规则', trigger: 'blur' }],
  getPubTime: [{ required: true, message: '请输入发布时间获取规则', trigger: 'blur' }],
  getContent: [{ required: true, message: '请输入内容获取规则', trigger: 'blur' }],
}



// 工具函数



// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    crawlUrl: '',
    clickListPath: '',
    nextPagePath: '',
    getTitle: '',
    getPubTime: '',
    getFile: '',
    getContent: '',
    crawlFunction: '',
    mainExtraInfo: '',
    iframe: '',
    headers: "",
    proxy: "",
    timeout: 30000,
    retryCount: 3,
    maxPages: 10,
    description: ""
  })
}

// 事件处理函数
const handleSearch = (params: any) => {
  currentPage.value = 1
  fetchData(params)
}

const handleReset = () => {
  currentPage.value = 1
  fetchData()
}

const handlePageChange = ({ current, pageSize: size, searchForm }: { current: number; pageSize: number; searchForm: any }) => {
  currentPage.value = current
  pageSize.value = size
  fetchData(searchForm)
}

const handleAdd = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

const handleEdit = (record: CrawlerConfig) => {
  isEdit.value = true
  Object.assign(formData, record)
  modalVisible.value = true
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (isEdit.value) {
      // 更新爬虫
      await handleUpdateCrawler(formData)
      message.success('更新成功')
    } else {
      const { id, ...createData } = formData
      await handleCreateCrawler(createData)
      message.success('创建成功')
    }

    modalVisible.value = false
    resetForm()
    fetchData()
  } catch (error) {
    message.error((error as Error).message)
  }
}

const handleDeleteConfirm = (record: CrawlerConfig) => {
  deleteRecord.value = record
  deleteModalVisible.value = true
}

const handleDelete = async () => {
  try {
    if (!deleteRecord.value?.id) {
      message.error('爬虫ID不存在')
      return
    }

    await handleDeleteCrawler(deleteRecord.value.id)
    message.success('删除成功')
    deleteModalVisible.value = false
    deleteRecord.value = null
    fetchData()
  } catch (error) {
    message.error((error as Error).message)
  }
}

const handleDeleteCancel = () => {
  deleteModalVisible.value = false
  deleteRecord.value = null
}


const handleStartCrawler = async (record: CrawlerConfig) => {
  try {
    if (!record.id) {
      message.error('爬虫ID不存在')
      return
    }

    const response: any = await startCrawler({
      id: record.id,
      status: record.status == 'active' ? 'inactive' : 'active'
    })

    if (response?.code === 200) {
      message.success('状态修改成功')
      fetchData()
    } else {
      message.error(response?.message || '状态修改失败')
    }
  } catch (error) {
    message.error('启动失败：' + (error as Error).message)
  }
}



// API 函数
const fetchData = async (params: any = {}) => {
  loading.value = true
  try {
    const response: any = await getCrawlerPage({
      current: currentPage.value,
      pageSize: pageSize.value,
      ...params
    })

    if (response?.code === 200) {
      crawlerList.value = response.data?.records || []
      total.value = response.data?.total || 0
    } else {
      message.error(response?.message || '获取数据失败')
    }

  } catch (error) {
    message.error('获取数据失败：' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

const handleCreateCrawler = async (data: CrawlerConfig) => {
  try {
    const response: any = await createCrawler(data)
    if (response?.code !== 200) {
      throw new Error(response?.message || '创建失败')
    }
  } catch (error) {
    throw new Error('创建失败：' + (error as Error).message)
  }
}

const handleUpdateCrawler = async (data: CrawlerConfig) => {
  try {
    const response: any = await updateCrawler(data)
    if (response?.code !== 200) {
      throw new Error(response?.message || '更新失败')
    }
  } catch (error) {
    throw new Error('更新失败：' + (error as Error).message)
  }
}

const handleDeleteCrawler = async (id: number) => {
  try {
    const response: any = await deleteCrawler(id)
    if (response?.code !== 200) {
      throw new Error(response?.message || '删除失败')
    }
  } catch (error) {
    throw new Error('删除失败：' + (error as Error).message)
  }
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>


