# YSS 爬虫系统 API 接口文档

## 基础信息

- **服务地址**: `http://10.0.76.31:7001`
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "data": {},
  "message": "success"
}
```

## 1. 爬虫设置管理 `/crawler`

### 1.1 获取爬虫设置列表

**接口地址**: `GET /crawler/getList`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | number | 否 | 1 | 页码 |
| pageSize | number | 否 | 10 | 每页数量 |

**请求示例**:
```bash
curl -X GET "http://10.0.76.31:7001/crawler/getList?page=1&pageSize=10"
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "title": "示例爬虫",
        "crawl_url": "https://example.com",
        "click_list_path": ".list-item",
        "get_title": ".title",
        "get_pub_time": ".pub-time",
        "get_content": ".content",
        "next_page_path": ".next-page",
        "iframe": "",
        "crawl_function": "",
        "main_extra_info": "",
        "crawl_bread_crumbs": "",
        "crawl_content_title": ""
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

### 1.2 添加爬虫设置

**接口地址**: `POST /crawler/add`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| title | string | 是 | 爬虫任务名称，用于标识不同的爬虫配置 | "新闻网站爬虫" |
| crawl_url | string | 是 | 要爬取的目标网站URL | "https://example.com/news" |
| click_list_path | string | 是 | 列表页面中每个条目的CSS选择器或XPath | ".news-item" 或 "//div[@class='news-item']" |
| get_title | string | 是 | 获取文章标题的CSS选择器或XPath | ".title" 或 "//h1[@class='title']" |
| get_pub_time | string | 是 | 获取发布时间的CSS选择器或XPath | ".pub-time" 或 "//span[@class='time']" |
| get_file | string | 否 | 获取附件文件的CSS选择器或XPath | ".attachment a" |
| get_content | string | 是 | 获取文章内容的CSS选择器或XPath | ".content" 或 "//div[@class='article-body']" |
| next_page_path | string | 否 | 下一页按钮的CSS选择器或XPath | ".next-page" 或 "//a[text()='下一页']" |
| iframe | string | 否 | 如果内容在iframe中，指定iframe的选择器 | "#content-frame" |
| crawl_function | string | 否 | 自定义爬取函数名称（高级功能） | "customCrawlFunction" |
| main_extra_info | string | 否 | 额外信息提取规则，JSON格式 | '{"author": ".author", "tags": ".tags"}' |
| crawl_bread_crumbs | string | 否 | 面包屑导航的CSS选择器或XPath | ".breadcrumb" |
| crawl_content_title | string | 否 | 内容页标题选择器（与列表页标题不同时使用） | ".article-title" |

**请求示例**:
```bash
curl -X POST "http://10.0.76.31:7001/crawler/add" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "示例爬虫",
    "crawl_url": "https://example.com",
    "click_list_path": ".list-item",
    "get_title": ".title",
    "get_pub_time": ".pub-time",
    "get_content": ".content",
    "next_page_path": ".next-page",
    "iframe": "",
    "crawl_function": "",
    "main_extra_info": "",
    "crawl_bread_crumbs": "",
    "crawl_content_title": ""
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "data": []
}
```

### 1.3 更新爬虫设置

**接口地址**: `POST /crawler/update`

**请求参数**: 同添加接口的所有参数，但需要额外包含 `id` 字段
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 要更新的爬虫设置ID |
| 其他参数 | - | - | 参考添加接口的所有参数 |

**请求示例**:
```bash
curl -X POST "http://10.0.76.31:7001/crawler/update" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "title": "更新后的爬虫",
    "crawl_url": "https://example.com",
    "click_list_path": ".list-item",
    "get_title": ".title",
    "get_pub_time": ".pub-time",
    "get_content": ".content",
    "next_page_path": ".next-page",
    "iframe": "",
    "crawl_function": "",
    "main_extra_info": "",
    "crawl_bread_crumbs": "",
    "crawl_content_title": ""
  }'
```

### 1.4 删除爬虫设置

**接口地址**: `POST /crawler/delete`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 爬虫设置ID |

**请求示例**:
```bash
curl -X POST "http://10.0.76.31:7001/crawler/delete" \
  -H "Content-Type: application/json" \
  -d '{"id": 1}'
```

### 1.5 启动爬虫

**接口地址**: `GET /crawler/start`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| id | number | 是 | 要启动的爬虫设置ID，对应爬虫配置表的主键 | 1 |
| batch_id | string | 否 | 批次标识符，用于区分不同批次的爬取任务 | "batch_20241201_001" |

**请求示例**:
```bash
curl -X GET "http://10.0.76.31:7001/crawler/start?id=1&batch_id=batch_001"
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "queueStatus": {
      "queued": 0,
      "running": 1,
      "completed": 0
    },
    "message": "任务已开始执行"
  }
}
```

## 2. 晓赢社区模块 `/xiaoying`

### 2.1 启动晓赢爬虫

**接口地址**: `GET /xiaoying/start`

**请求示例**:
```bash
curl -X GET "http://10.0.76.31:7001/xiaoying/start"
```

**响应示例**:
```json
{
  "code": 200,
  "data": "success"
}
```

### 2.2 回答问题

**接口地址**: `POST /xiaoying/answer`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| answer | string | 是 | 要回答的内容文本 | "这是我对这个问题的详细回答..." |
| id | string | 是 | 晓赢社区中问题的唯一标识符 | "question_123456" |

**请求示例**:
```bash
curl -X POST "http://10.0.76.31:7001/xiaoying/answer" \
  -H "Content-Type: application/json" \
  -d '{
    "answer": "这是我的回答内容",
    "id": "question_123"
  }'
```

**响应示例**:
```json
{
  "code": 200,
  "data": "success"
}
```

### 2.3 发布帖子

**接口地址**: `POST /xiaoying/publish`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| title | string | 是 | 帖子标题 | "关于投资理财的一些心得分享" |
| content | string | 是 | 帖子正文内容，支持富文本 | "今天想和大家分享一些投资理财的经验..." |
| type | Array<string> | 是 | 帖子分类标签数组 | ["投资", "理财", "经验分享"] |

**请求示例**:
```bash
curl -X POST "http://10.0.76.31:7001/xiaoying/publish" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "帖子标题",
    "content": "帖子内容",
    "type": ["技术", "讨论"]
  }'
```

## 3. 香港日历模块 `/hongkong`

### 3.1 启动香港日历爬虫

**接口地址**: `GET /hongkong/start`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| beginDate | string | 是 | 爬取开始日期，格式为YYYY-MM-DD | "2024-01-01" |
| endDate | string | 是 | 爬取结束日期，格式为YYYY-MM-DD | "2024-12-31" |
| isSync | boolean | 否 | 是否同步执行，true=等待完成后返回结果，false=异步执行立即返回 | true |

**请求示例**:
```bash
curl -X GET "http://10.0.76.31:7001/hongkong/start?beginDate=2024-01-01&endDate=2024-12-31&isSync=true"
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "events": [
      {
        "date": "2024-01-01",
        "title": "元旦",
        "type": "香港公眾假期"
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有POST请求需要设置 `Content-Type: application/json`
2. 爬虫任务为异步执行，启动后会返回任务状态
3. 日期格式统一使用 `YYYY-MM-DD`
4. 服务运行在端口 7001，确保网络连通性
5. 日志文件位于项目根目录的 `logs/` 文件夹下
