<template>
  <div class="h-full bg-global-background">
    <BaseTable
      :search-schema="searchSchema"
      :columns="COLUMNS"
      :dataSource="designList"
      :loading="loading"
      :total="total"
      :current="currentPage"
      :pageSize="pageSize"
      @search="handleSearch"
      @reset="handleReset"
      @pageChange="handlePageChange"
    >
      <!-- 类型列 -->
      <template #type="{ record }">
        <a-tag :color="getTypeColor(record.type)">
          {{ record.type.toUpperCase() }}
        </a-tag>
      </template>

      <!-- 版本列 -->
      <template #version="{ record }">
        <div class="flex items-center gap-2">
          <a-tag :color="getVersionColor(record.currentVersion)" size="small">
            {{ record.currentVersion || 'v1.0.0' }}
          </a-tag>
          <a-button type="link" size="small" @click="handleVersionSettings(record)">
            <template #icon><SettingOutlined /></template>
          </a-button>
        </div>
      </template>

      <!-- 访问地址列 -->
      <template #path="{ record }">
        <div class="flex items-center">
          <a-tooltip title="复制地址">
            <a-button type="link" size="small" @click="copyPath(record.path)">
              <template #icon><CopyOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-popover placement="right" trigger="click" :overlayStyle="{ maxWidth: '500px' }">
            <template #content>
              <div class="p-3 bg-gray-50 rounded-sm break-all max-w-full">
                {{ record.path }}
              </div>
              <div class="mt-2 flex justify-end">
                <a-button type="primary" size="small" @click="copyPath(record.path)">
                  <template #icon><CopyOutlined /></template>
                  复制
                </a-button>
              </div>
            </template>
            <a-tooltip title="查看地址">
              <a-button type="link" size="small">
                <template #icon><EyeOutlined /></template>
              </a-button>
            </a-tooltip>
          </a-popover>
        </div>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-tooltip title="编辑">
            <a-button type="link" @click="handleEdit(record)">
              <template #icon><EditOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="测试API" v-if="record.type === 'api'">
            <a-button type="link" @click="handleTestApi(record)">
              <template #icon><ApiOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="测试Agent" v-if="record.type === 'agent'">
            <a-button type="link" @click="handleTestAgent(record)">
              <template #icon><RobotOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="删除">
            <a-button type="link" danger @click="showDeleteConfirm(record)">
              <template #icon><DeleteOutlined /></template>
            </a-button>
          </a-tooltip>
          <!-- <a-tooltip title="跳转到画布" v-if="record.type === 'api'">
            <a-button type="link" @click="handleGoToCanvas(record, 'edit')">
              <template #icon><BlockOutlined /></template>
            </a-button>
          </a-tooltip> -->
          <a-tooltip title="跳转查看画布" v-if="record.type === 'api'">
            <a-button type="link" @click="handleGoToCanvas(record, 'readonly')">
              <template #icon><BlockOutlined /></template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>

    <!-- 发布/编辑对话框 -->
    <PublishModal ref="publishModalRef" />

    <!-- API测试组件 -->
    <ApiTester v-model:visible="apiTesterVisible" :apiData="currentApiData" />

    <!-- Agent测试组件 -->
    <AgentTester v-model:visible="agentTesterVisible" :agentData="currentAgentData" />

    <!-- API版本管理组件 -->
    <ApiVersionManager
      v-model:visible="versionManagerVisible"
      :apiData="currentApiData"
      @version-updated="handleVersionUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  DeleteOutlined,
  EditOutlined,
  ApiOutlined,
  CopyOutlined,
  EyeOutlined,
  RobotOutlined,
  BlockOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import {
  TYPE_COLORS,
  CONFIG_TYPES,
  PAGINATION,
  COLUMNS
} from './constants'
import { getEndpointPage, deleteEndpoint, getFileApi } from '@/apis/designer'
import { BaseTable } from '@/components'
import ApiTester from './comopnents/apiTester/index.vue'
import AgentTester from './comopnents/agentTester/index.vue'
import PublishModal from '../canvas/components/toolbar/publishModal/index.vue'
import ApiVersionManager from './components/ApiVersionManager.vue'
import { useDesignerStore } from '@/store/designer'

interface DesignItem {
  id: string
  project: string
  subsystem: string
  module: string
  description: string
  type: 'api' | 'agent' | 'cot'
  config: string
  pathUrl: string
  createTime: string
  updateTime: string
  templateId: string
  reqParam?: string
  path?: string
}

// 路由
const router = useRouter()

// 搜索配置
const searchSchema = [
  {
    key: 'description',
    label: '描述',
    type: 'input',
    placeholder: '请输入描述关键词'
  },
]

// 状态
const loading = ref(false)
const currentPage = ref(PAGINATION.DEFAULT_CURRENT_PAGE)
const pageSize = ref(PAGINATION.DEFAULT_PAGE_SIZE)
const total = ref(0)
const designList = ref<DesignItem[]>([])

// API测试相关
const apiTesterVisible = ref(false)
const currentApiData = ref({})

// Agent测试相关
const agentTesterVisible = ref(false)
const currentAgentData = ref({})

// 版本管理相关
const versionManagerVisible = ref(false)

// 表单
const form = reactive({
  project: '',
  subsystem: '',
  module: '',
  description: '',
  type: '',
  configType: CONFIG_TYPES.INPUT,
  template: '',
  config: '',
  file: null as File | null
})

// 获取数据
const fetchData = async (params = {}) => {
  loading.value = true
  try {
    const { current = 1, pageSize = 10, ...rest } = params
    const res = await getEndpointPage({ current, size: pageSize, ...rest })
    if (res.data) {
      // 为每个API添加版本信息
      designList.value = (res.data.records || []).map(item => ({
        ...item,
        currentVersion: item.currentVersion || 'v1.0.0', // 默认版本
        totalVersions: item.totalVersions || 1 // 版本总数
      }))
      total.value = res.data.total || 0
    }
  } catch (error) {
    message.error('获取数据失败：' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (params) => {
  currentPage.value = 1
  fetchData({ current: 1, pageSize: pageSize.value, ...params })
}

// 重置处理
const handleReset = () => {
  currentPage.value = 1
  fetchData({ current: 1, pageSize: pageSize.value })
}

// 分页处理
const handlePageChange = (pagination) => {
  currentPage.value = pagination.current
  pageSize.value = pagination.pageSize
  fetchData({
    current: pagination.current,
    pageSize: pagination.pageSize,
    ...pagination.searchForm
  })
}

// 获取类型对应的颜色
const getTypeColor = (type: string) => {
  return TYPE_COLORS[type as keyof typeof TYPE_COLORS] || 'default'
}


// 删除设计
const handleDelete = async (row: DesignItem) => {
  try {
    await deleteEndpoint(row.id)
    message.success('删除成功')
    fetchData()
  } catch (error) {
    message.error('删除失败')
  }
}

// 显示删除确认对话框
const showDeleteConfirm = (row: DesignItem) => {
  Modal.confirm({
    title: '确定要删除吗？',
    content: `将删除 "${row.description}" 设计`,
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      return handleDelete(row)
    }
  })
}

// 添加发布模态框引用
const publishModalRef = ref<InstanceType<typeof PublishModal>>()

// 添加编辑处理函数
const handleEdit = async (row: DesignItem) => {
  // 准备发布模态框需要的数据
  const editData = {
    currentFile: {
      id: row.templateId
    },
    graphData: row.config,
    endpoint: row
  }

  // 打开发布模态框
  publishModalRef.value?.open(editData)
}

// 添加测试API按钮的处理函数
const handleTestApi = (row: DesignItem) => {
  if (row.type !== 'api') {
    message.warning('只有API类型的设计才能进行测试')
    return
  }
  // 先关闭再打开，确保状态变化
  apiTesterVisible.value = false
  // 使用nextTick等待DOM更新
  setTimeout(() => {
    currentApiData.value = row
    apiTesterVisible.value = true
  }, 0)
}

// 添加测试Agent按钮的处理函数
const handleTestAgent = (row: DesignItem) => {
  if (row.type !== 'agent') {
    message.warning('只有Agent类型的设计才能进行测试')
    return
  }
  // 先关闭再打开，确保状态变化
  agentTesterVisible.value = false
  // 使用nextTick等待DOM更新
  setTimeout(() => {
    currentAgentData.value = row
    agentTesterVisible.value = true
  }, 0)
}

// 复制访问地址
const copyPath = (path: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(path)
      .then(() => {
        message.success('已复制到剪贴板')
      })
      .catch(() => {
        message.error('复制失败')
      })
  } else {
    // 兼容性处理
    const textarea = document.createElement('textarea')
    textarea.value = path
    document.body.appendChild(textarea)
    textarea.select()
    try {
      document.execCommand('copy')
      message.success('已复制到剪贴板')
    } catch (err) {
      message.error('复制失败')
    }
    document.body.removeChild(textarea)
  }
}

// 跳转到画布
const handleGoToCanvas = async (record: DesignItem, mode: 'edit' | 'readonly') => {
  try {
    // 获取设计器store
    const designerStore = useDesignerStore()

    // 获取文件内容并处理（类似目录组件的handleNodeClick逻辑）
    const res = await getFileApi({id: record.templateId})
    if (res && res.data) {
      const fileContent = res.data

      // 构建标签页数据 - 使用接口返回的name字段
      const tab = {
        id: record.templateId,
        name: fileContent.name || record.description || '未命名',
        path: record.path || '',
        directoryId: '', // 设置一个空值，因为这里可能没有
        content: JSON.parse(fileContent.content)
      }

      // 添加到设计器的标签页中
      designerStore.addTab(tab)

      // 加载到画布
      if (designerStore.modGraph?.fromJSON) {
        designerStore.modGraph.fromJSON(tab.content)
      }

      // 关闭右侧面板
      designerStore.setRightPanelVisiable(false)
    }

    if (mode === 'readonly') {
      // 只读模式：打开新标签页
      const route = router.resolve({
        path: '/designer/canvas',
        query: {
          tarId: record.templateId,
          mode: 'readonly'
        }
      })
      window.open(route.href, '_blank')
    } else {
      // 编辑模式：在当前页面跳转
      router.push({
        path: '/designer/canvas',
        query: {
          tarId: record.templateId,
          mode: mode
        }
      })
    }
  } catch (error) {
    console.error('加载画布文件失败:', error)
    message.error('加载画布文件失败')
  }
}

// 获取版本颜色
const getVersionColor = (version: string) => {
  if (!version) return 'default'
  if (version.startsWith('v2.')) return 'blue'
  if (version.startsWith('v1.')) return 'green'
  return 'default'
}

// 版本设置
const handleVersionSettings = (record: DesignItem) => {
  currentApiData.value = record
  versionManagerVisible.value = true
}

// 版本更新回调
const handleVersionUpdated = () => {
  fetchData()
  message.success('版本更新成功')
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.w-full {
  width: 100%;
}

.config-content {
  max-height: 400px;
  overflow-y: auto;
}

.description {
  background-color: #fafafa;
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
