// 设计类型
export const DESIGN_TYPES = {
  API: 'api',
  AGENT: 'agent',
  COT: 'cot',
} as const

// 类型颜色映射
export const TYPE_COLORS = {
  [DESIGN_TYPES.API]: 'green',
  [DESIGN_TYPES.AGENT]: 'orange',
  [DESIGN_TYPES.COT]: 'blue',
} as const

// 配置方式
export const CONFIG_TYPES = {
  INPUT: 'input',
  FILE: 'file',
} as const

// 分页配置
export const PAGINATION = {
  PAGE_SIZES: ['10', '20', '50', '100'],
  DEFAULT_PAGE_SIZE: 10,
  DEFAULT_CURRENT_PAGE: 1,
} as const

// localStorage key
export const STORAGE_KEY = 'design_list'

import dayjs from 'dayjs'

// 表格列定义
export const getColumns = (page = { current: 1, pageSize: 10 }) => [
  {
    title: '序号',
    width: 80,
    align: 'center',
    customRender: ({ index }) => {
      // 手动计算全局序号
      return (page.current - 1) * page.pageSize + index + 1
    },
  },
  {
    title: '产品/项目',
    dataIndex: 'project',
    width: 150,
    key: 'project',
  },
  {
    title: '子系统',
    dataIndex: 'subsystem',
    width: 150,
    key: 'subsystem',
  },
  {
    title: '模块',
    dataIndex: 'module',
    width: 150,
    key: 'module',
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 200,
    ellipsis: true,
    key: 'description',
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    key: 'type',
    slots: { customRender: 'type' },
  },
  {
    title: '版本',
    dataIndex: 'version',
    width: 120,
    key: 'version',
    slots: { customRender: 'version' },
  },
  {
    title: '访问地址',
    dataIndex: 'path',
    width: 200,
    ellipsis: true,
    key: 'path',
    slots: { customRender: 'path' },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    key: 'createTime',
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 160,
    key: 'updateTime',
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
]

// 定时器选择工作流列定义
export const getTimerColumns = (page = { current: 1, pageSize: 10 }) => [
  {
    title: '序号',
    width: 80,
    align: 'center',
    customRender: ({ index }) => {
      // 手动计算全局序号
      return (page.current - 1) * page.pageSize + index + 1
    },
  },
  {
    title: '产品/项目',
    dataIndex: 'project',
    width: 150,
    key: 'project',
  },
  {
    title: '子系统',
    dataIndex: 'subsystem',
    width: 150,
    key: 'subsystem',
  },
  {
    title: '模块',
    dataIndex: 'module',
    width: 150,
    key: 'module',
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 200,
    ellipsis: true,
    key: 'description',
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    key: 'type',
    slots: { customRender: 'type' },
  },
  {
    title: '访问地址',
    dataIndex: 'path',
    width: 600,
    ellipsis: true,
    key: 'path',
  }
]

// 为了兼容现有代码，也导出 COLUMNS 常量
export const COLUMNS = getColumns()

// 导出定时器选择工作流列
export const TIMER_COLUMNS = getTimerColumns()