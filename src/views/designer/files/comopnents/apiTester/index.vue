<template>
  <a-modal
    :visible="visible"
    @update:visible="updateVisible"
    title="API测试"
    width="800px"
    :footer="null"
  >
    <div class="api-test-container">
      <!-- 使用ApiTest组件 -->
      <ApiTest
        ref="apiTestRef"
        :endpoint-url="apiUrl"
        :config-data="configData"
        :req-param-data="reqParamData"
        :template-id="templateId"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import ApiTest from '../../../canvas/components/toolbar/apiTest/index.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  apiData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

const apiTestRef = ref<any>(null)

// 计算API URL - 直接使用path或pathUrl
const apiUrl = computed(() => {
  if (!props.apiData) return ''
  // 优先使用pathUrl字段
  if (props.apiData.pathUrl) return props.apiData.pathUrl
  // 兼容使用path字段
  if (props.apiData.path) return props.apiData.path
  return ''
})

// 计算templateId
const templateId = computed(() => {
  return props.apiData?.templateId || ''
})

// 配置数据
const configData = ref('')
// reqParam数据
const reqParamData = ref('')

// 监听apiData变化
watch(() => props.apiData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 更新配置数据
    configData.value = newVal.config || '{}'
    // 更新reqParam数据
    reqParamData.value = newVal.reqParam || '{}'
  }
}, { immediate: true, deep: true })

// 监听visible变化
const updateVisible = (val: boolean) => {
  emit('update:visible', val)
  // 当面板关闭时，重置组件状态
  if (!val && apiTestRef.value?.reset) {
    apiTestRef.value.reset()
  }
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  // 当面板打开时，重置组件状态
  if (newVal && apiTestRef.value?.reset) {
    apiTestRef.value.reset()
  }
}, { immediate: true })
</script>

<style scoped>
.api-test-container {
  padding: 10px;
}

.w-full {
  width: 100%;
}

.break-all {
  word-break: break-all;
}
</style>
