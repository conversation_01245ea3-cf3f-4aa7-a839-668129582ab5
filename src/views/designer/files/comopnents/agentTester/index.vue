<template>
  <div v-if="visible" class="fixed inset-0  bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white w-[450px] rounded-[5px] shadow-lg flex flex-col">
      <div class="flex flex-col h-[600px]">
        <div class="flex-1 h-full">
          <AIChatContainer
            ref="chatContainer"
            :key="chatKey"
            :width="'100%'"
            :maxHeight="'600px'"
            :systemShowChatHeader="true"
            class="bg-white h-full dark:!bg-area-fill"
            :sseUrl="`${agentForm.path}?stream=true&tabId=${TAB_ID}&aip-session=${AI_TOKEN}`"
            :sendSSEMessage="handleSendSSEMessage"
            :menuList="[]"
            :selectInfo="{
              name: '',
              data: []
            }"
            @closeChat="handleCloseChat"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { AIChatContainer } from '@yss/ai-chat-components'
import { v4 as uuidv4 } from 'uuid'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  agentData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

// 配置聊天容器所需的变量
const chatKey = ref(0)
const TAB_ID = ref(uuidv4())
const AI_TOKEN = ref(uuidv4())

const agentForm = reactive({
  path: ''
})

// 监听agentData变化
watch(() => props.agentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    agentForm.path = newVal.path || ''
    // 每次打开时重新生成聊天会话ID
    TAB_ID.value = uuidv4()
    AI_TOKEN.value = uuidv4()
    chatKey.value++ // 强制刷新聊天组件
  }
}, { immediate: true, deep: true })


// 发送消息到SSE
const handleSendSSEMessage = async ({ question }: { question: string }) => {
  try {
    // 构建消息数组
    const messages = [{
      role: 'user',
      content: question
    }]

    // 发送请求到Agent路径
    const response = await fetch(agentForm.path, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        sessionId: AI_TOKEN.value,
        tabId: TAB_ID.value,
        messages
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
  } catch (error) {
    return {
      code: 500,
      data: null,
      error: error instanceof Error ? error.message : '发送消息时发生未知错误'
    }
  }
}

const handleCloseChat = () => {
  emit('update:visible', false); // 关闭模态框
}
</script>
