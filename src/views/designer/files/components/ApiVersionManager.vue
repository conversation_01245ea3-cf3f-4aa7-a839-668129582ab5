<template>
  <a-modal
    v-model:visible="visible"
    :title="`API版本管理 - ${apiData?.project}/${apiData?.module}`"
    width="1000px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="api-version-manager">
      <!-- 顶部操作栏 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="flex items-center gap-4">
          <h3 class="text-lg font-medium">版本列表</h3>
          <a-tag color="blue">共 {{ versions.length }} 个版本</a-tag>
          <a-tag v-if="currentVersion" color="green">
            当前版本: {{ currentVersion.version }}
          </a-tag>
        </div>
        <div class="flex gap-2">
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </div>
      </div>

      <!-- 版本列表 -->
      <div class="version-list">
        <a-spin :spinning="loading">
          <div class="space-y-3 max-h-96 overflow-y-auto">
            <div
              v-for="version in versions"
              :key="version.id"
              class="version-card border rounded-lg p-4 hover:shadow-md transition-shadow"
              :class="{ 
                'border-blue-500 bg-blue-50': version.isCurrent,
                'border-green-500 bg-green-50': version.isLatest && !version.isCurrent
              }"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="font-semibold text-lg">{{ version.version }}</span>
                    <a-tag v-if="version.isCurrent" color="blue">当前版本</a-tag>
                    <a-tag v-if="version.isLatest" color="green">最新版本</a-tag>
                    <a-tag :color="getStatusColor(version.status)">
                      {{ getStatusText(version.status) }}
                    </a-tag>
                  </div>
                  
                  <p class="text-gray-600 mb-2">{{ version.description }}</p>
                  
                  <div class="text-sm text-gray-500 space-y-1">
                    <div>发布时间: {{ formatDate(version.publishTime) }}</div>
                    <div>发布者: {{ version.publisher }}</div>
                    <div>访问路径: {{ version.path }}</div>
                  </div>
                </div>
                
                <div class="flex flex-col gap-2 ml-4">
                  <a-button
                    v-if="!version.isCurrent"
                    type="primary"
                    size="small"
                    @click="handleActivateVersion(version)"
                  >
                    激活版本
                  </a-button>
                  <span v-else class="text-green-600 font-medium">当前激活</span>

                  <a-button
                    size="small"
                    @click="handleOpenCanvas(version)"
                  >
                    打开画布
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>
    </div>


  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  ReloadOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

interface ApiVersion {
  id: string
  version: string
  description: string
  status: 'active' | 'inactive' | 'deprecated'
  isCurrent: boolean
  isLatest: boolean
  publishTime: string
  publisher: string
  path: string
  config: any
}

interface ApiData {
  id: string
  project: string
  subsystem: string
  module: string
  description: string
  type: string
}

const props = defineProps<{
  visible: boolean
  apiData?: ApiData
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'version-updated': []
}>()

// 数据状态
const loading = ref(false)
const versions = ref<ApiVersion[]>([])
const currentVersion = ref<ApiVersion | null>(null)

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 加载版本列表
const loadVersions = async () => {
  if (!props.apiData?.id) return
  
  loading.value = true
  try {
    // 模拟API调用
    const mockVersions: ApiVersion[] = [
      {
        id: '1',
        version: 'v1.2.0',
        description: '新增用户权限验证功能',
        status: 'active',
        isCurrent: true,
        isLatest: true,
        publishTime: '2024-01-15 14:30:00',
        publisher: '张三',
        path: `/api/${props.apiData.module}/v1.2.0`,
        config: {}
      },
      {
        id: '2',
        version: 'v1.1.0',
        description: '优化响应速度，修复已知问题',
        status: 'inactive',
        isCurrent: false,
        isLatest: false,
        publishTime: '2024-01-10 09:15:00',
        publisher: '李四',
        path: `/api/${props.apiData.module}/v1.1.0`,
        config: {}
      },
      {
        id: '3',
        version: 'v1.0.0',
        description: '初始版本发布',
        status: 'deprecated',
        isCurrent: false,
        isLatest: false,
        publishTime: '2024-01-05 16:45:00',
        publisher: '王五',
        path: `/api/${props.apiData.module}/v1.0.0`,
        config: {}
      }
    ]
    
    versions.value = mockVersions
    currentVersion.value = mockVersions.find(v => v.isCurrent) || null
  } catch (error) {
    message.error('加载版本列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新版本列表
const handleRefresh = () => {
  loadVersions()
}

// 打开画布
const handleOpenCanvas = (version: ApiVersion) => {
  // 构建画布路径，包含版本信息和模板ID
  const templateId = props.apiData?.templateId || props.apiData?.id
  const canvasPath = `/designer/canvas?tarId=${templateId}&version=${version.version}&mode=edit`

  // 在新标签页中打开画布
  window.open(canvasPath, '_blank')

  message.info(`正在打开 ${version.version} 版本的画布`)
}

// 激活版本
const handleActivateVersion = (version: ApiVersion) => {
  Modal.confirm({
    title: '确认激活版本',
    content: `确定要激活版本 ${version.version} 吗？激活后此版本将成为当前访问版本。`,
    async onOk() {
      // 创建新的版本数组来触发响应式更新
      const newVersions = versions.value.map(v => ({
        ...v,
        isCurrent: v.id === version.id
      }))

      // 更新版本列表
      versions.value = newVersions

      // 更新当前版本
      currentVersion.value = newVersions.find(v => v.isCurrent) || null

      // 强制触发响应式更新
      await nextTick()

      message.success(`版本 ${version.version} 已激活`)
      emit('version-updated')
    }
  })
}



// 关闭弹窗
const handleCancel = () => {
  visible.value = false
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap = {
    active: 'green',
    inactive: 'blue',
    deprecated: 'red'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    active: '活跃',
    inactive: '非活跃',
    deprecated: '已废弃'
  }
  return textMap[status] || status
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.apiData) {
    loadVersions()
  }
})
</script>

<style scoped>
.api-version-manager {
  min-height: 400px;
}

.version-card {
  transition: all 0.2s ease;
}

.version-card:hover {
  transform: translateY(-1px);
}
</style>
