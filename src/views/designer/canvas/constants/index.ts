export const homeMenus = [
  {
    title: 'AI产品经理',
    description: '产品版本库、NPDP规划、需求治理、L4-L5业务模型生成、核心功能验证、原型MVP',
    icon: '产品图标路径',
    link: 'http://product.example.com',
  },
  {
    title: 'AI工程师',
    description: 'API编排、COT编排、Agent设计、MetaTeam管理、VL原型生成、算法库管理',
    icon: '工程师图标路径',
    link: '/designer/list',
  },
  {
    title: 'AI迭代质量门',
    description: '产品、POC、项目质量过程，投产迭代流程，CICD工具，环境资源数据库',
    icon: '质量门图标路径',
    link: 'http://*************:8085/#/testmanagement',
  },
  {
    title: '金融AI运行时',
    description:
      '语料治理、私有化知识库管理、甲方大模型训练、推理模型版本迭代、能力评测、运行环境监控、日志收集、运维工具箱',
    icon: '运行时图标路径',
    link: 'http://runtime.example.com',
  },
]

// 内嵌抽屉的统一宽度
export const EmbeddedDrawerWidth = 550

// 试运行结果面板配置
export const RESULT_PANEL = {
  MAX_HEIGHT: 500, // 结果面板最大高度（px）
}