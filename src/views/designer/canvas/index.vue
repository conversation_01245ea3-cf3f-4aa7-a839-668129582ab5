<template>
  <div class="relative w-full h-full flex">
    <!-- 展开按钮 -->
    <div
      v-if="treeWidth === 0 && mode !== 'readonly'"
      class="absolute left-0 top-1/2 -translate-y-1/2 bg-area-fill text-custom p-2 rounded-r cursor-pointer shadow-md hover:bg-[var(--area-hover-color)] z-50"
    >
      <MenuUnfoldOutlined class="text-xl" @click="expandPanel" />
    </div>

    <Splitpanes class="default-theme" @resize="resize">
      <!-- 在非只读模式下显示目录树 -->
      <Pane v-if="mode !== 'readonly'" :size="treeWidth">
        <div class="flex flex-col h-full bg-global-background relative">
          <!-- 版本信息显示 -->
          <div v-if="currentVersion" class="absolute top-2 right-2 z-10">
            <a-tag color="blue" size="small">
              版本: {{ currentVersion }}
            </a-tag>
          </div>

          <DirectoryTree
            class="directory-tree flex-1"
            :treeData="treeData"
            :refreshData="getFileTreeData"
            @change-width="changeWidth"
          ></DirectoryTree>
        </div>
      </Pane>
      <!-- 画布区域，只读模式下占满全屏 -->
      <Pane :size="mode === 'readonly' ? 100 : designerWidth" min-size="50">
        <div class="w-full h-full overflow-hidden relative">
          <!-- 只读模式下的版本显示 -->
          <div v-if="currentVersion && mode === 'readonly'" class="absolute top-2 left-2 z-10">
            <a-tag color="blue" size="small">
              版本: {{ currentVersion }}
            </a-tag>
          </div>

          <div v-if="designerStore.currentFile?.id && mode !== 'readonly'" class="flex w-full">
            <FileTabs class="flex-1" />
          </div>
          <div class="canvas-container bg-area-fill" v-if="designerStore.currentFile?.id">
            <Canvas
              class="data-model-mapping-index"
              height="calc(100vh - 41px)"
              :refreshData="getFileTreeData"
              :treeData="treeData"
            ></Canvas>
          </div>
          <!-- 默认提示界面 -->
          <div
            v-if="!designerStore.currentFile?.id"
            class="w-full h-screen flex items-center justify-center bg-area-fill"
          >
            <div>
              <div
                class="py-5 text-5xl font-bold text-gray-300 [text-shadow:2px_2px_4px_rgba(0,0,0,0.5),-2px_-2px_4px_rgba(255,255,255,0.5)]">
                知行合一
              </div>
              <div class="text-sm text-center text-custom"> 打开目录文件，开始设计 </div>
            </div>
          </div>
        </div>
      </Pane>
    </Splitpanes>
    <generateNode v-if="designerStore.generateNodePanelVisiable" :visible="designerStore.generateNodePanelVisiable" />
    <generateScript v-if="designerStore.generateScriptPanelVisiable" :visible="designerStore.generateScriptPanelVisiable" />
  </div>
</template>

<script setup lang="ts">
import { Splitpanes, Pane } from 'splitpanes'
import { FileTabs } from '@/components'
import DirectoryTree from './components/siderbar/directory/index.vue'
import Canvas from './components/canvas/index.vue'
import { getFileTree } from '@/apis/designer'
import { useDesignerStore } from '@/store/designer'
import { useAppStore } from '@/store/app'
import { useSystemVariablesStore } from '@/store/systemVariables'
import bus from '@/utils/bus'
import './utils/specialNodeRegistry'
import './utils/specialShapeRegistry'
import '@/graph/index'
import { MenuUnfoldOutlined, LeftOutlined } from '@ant-design/icons-vue'
import { registerNodeGroups } from './utils/nodeRegistry'
import { updateNodeGroups } from './utils/configs'
import { useRouter, useRoute } from 'vue-router'
import { registerSpecialShape } from './utils/specialShapeRegistry'
import specialShapesNode from './components/specialShapes/specialShapes.vue'
import generateNode from './components/generateNode/index.vue'
import generateScript from './components/generateScript/index.vue'

const props = defineProps({
  tarId: {
    type: String,
    default: ''
  }
})
const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
const route = useRoute()
const designerStore = useDesignerStore()
const appStore = useAppStore()
const systemVariablesStore = useSystemVariablesStore()
const router = useRouter()

const mode = ref('edit')
const currentVersion = ref('')

const treeWidth = ref(25)
const designerWidth = ref(75)



const changeWidth = (width) => {
  treeWidth.value = width
  designerWidth.value = 100 - width
}

const resize = (e) => {
  treeWidth.value = e[0].size
  designerWidth.value = e[1].size
}

const reselectTreeData = (nodes, level) => {
  if (!nodes || !Array.isArray(nodes)) return []
  level++
  return nodes.map(item => {
    if (!item || !item.name) return null
    const combinedChildren = [
      ...(Array.isArray(item.children) ? item.children : []),
      ...(Array.isArray(item.contents) ? item.contents : [])
    ]

    return {
      children: combinedChildren.length > 0 ? reselectTreeData(combinedChildren, level) : [],
      path: item.path || '',
      content: item.content || '',
      directoryId: item.directoryId || '',
      label: item.name,
      level,
      dir: Array.isArray(item.children),
      id: item.id || '',
      key: item.id || '',
      name: item.name,
      parentId: item.parentId,
      // canvases: item.canvases // Maintain original canvases field
      contents: item.contents // 原canvases字段，修改了字段名
    }
  }).filter(Boolean)
}

const treeData = ref<DirectoryData[]>([])
const loadingTreeData = ref(false)

const getFileTreeData = async () => {
  // 防止重复加载
  if (loadingTreeData.value) return

  loadingTreeData.value = true
  try {
    const res = await getFileTree({ userId: userInfo.id, type: 'canvas' })
    if (res.code !== 200) {
      return
    }
    if (!res.data) {
      treeData.value = []
      return
    }
    const projectData = reselectTreeData(res.data, 0)
    treeData.value = projectData
  } catch (error) {
    console.error('Failed to load file tree:', error)
  } finally {
    loadingTreeData.value = false
  }
}

// 只在组件挂载时加载一次
onMounted(() => {
  // 从URL或store中读取mode状态
  const modeFromQuery = route.query.mode as string
  if (modeFromQuery === 'readonly' || modeFromQuery === 'edit') {
    appStore.setMode(modeFromQuery)
    mode.value = modeFromQuery
  } else {
    appStore.setMode('edit')
    mode.value = 'edit'
  }

  // 处理版本参数
  const versionFromQuery = route.query.version as string
  if (versionFromQuery) {
    currentVersion.value = versionFromQuery
    console.log(`正在加载版本: ${versionFromQuery}`)
    // 这里可以根据版本加载对应的画布配置
    // 例如：loadCanvasForVersion(versionFromQuery)
  }

  // 初始化系统变量
  const canvasId = route.query.tarId as string || 'default'
  systemVariablesStore.setCurrentCanvasId(canvasId)
  systemVariablesStore.initializeDefaultVariables()

  if (mode.value != 'readonly') {
    getFileTreeData()
  }
})

// 提供树数据和刷新方法给子组件
provide('refreshDirectoryTree', getFileTreeData)
provide('treeData', treeData)

const receiveMessage = (msg) => {
  try {
    if (!msg) return
    if (msg.data instanceof Object) {
      return
    }
    const data = JSON.parse(msg.data || '{}')
    if (data.dataType) {
      bus.emit(data.dataType, data)
    }
  } catch (e) {
    console.error(e)
  }
}

// 获取并注册节点
const initializeNodes = async () => {
  try {
    const res = await getFileTree({ userId: userInfo.id, type: 'node' })

    if (res.code === 200 && res.data) {
      // 获取所有节点组
      const allGroups = updateNodeGroups(reselectTreeData(res.data, 0))

      // 将 node 组和 shape 组分离
      const shapeGroups = {}
      const nodeGroups = {}

      // 根据content_type分组
      Object.entries(allGroups).forEach(([key, group]: [string, any]) => {
        const content_type = group.content_type;
        if (content_type === 'shape') {
          shapeGroups[group.id] = group
        } else {
          nodeGroups[group.id] = group
        }
      })

      // 存储节点
      designerStore.setNodeGroups(nodeGroups)
      // 注册节点
      registerNodeGroups(nodeGroups)

      // 存储形状
      designerStore.setShapeGroups(shapeGroups)
      // 注册特殊形状节点,只需注册一个
      try {
        registerSpecialShape({
          shape: 'special-shape',
          width: 40,
          height: 40,
          component: specialShapesNode,
          data: {
            shapeId: '',
            label: ''
          }
        });
      } catch (e) {
        console.error('特殊形状节点注册失败', e);
      }
    }
  } catch (error) {
    console.error('Failed to load node configs:', error)
  }
}

// 在需要的地方设置一个标志，以避免过早恢复
const tabsRestored = ref(false);

// 标签数据变化监听器
watch(
  () => {
    // 转换为数组以便进行深度监听
    return Array.from(designerStore.tabData.entries())
      .map(([key, value]) => ({
        key,
        id: value.id,
        name: value.name
      }));
  },
  () => {
    // 只有在不是初始恢复时才保存
    if (tabsRestored.value) {
      designerStore.serializeTabData();
    }
  },
  { deep: true }
);

// 标签数量变化监听器
watch(
  () => designerStore.tabData.size,
  () => {
    if (tabsRestored.value) {
      designerStore.serializeTabData();
    }
  }
);

// 安全地恢复标签的函数
const safeRestoreTabs = async () => {
  try {
    // 首先从URL参数获取tarId
    const tarId = route.query.tarId as string;
    
    if (tarId) {
      try {
        // 优先通过tarId加载文件
        const content = await designerStore.loadFileById(tarId);
        // 设置当前文件内容
        designerStore.setCurrentFile({
          id: tarId,
          content: content,
        });
      } catch (error) {
        console.error('通过tarId加载文件失败:', error);
      }
    } else {
      // 如果没有tarId，则尝试从缓存恢复标签数据
      console.log('未找到tarId参数，尝试从缓存恢复标签');
      const success = await designerStore.deserializeTabData();
      
      tabsRestored.value = true;

      // 确保UI更新
      if (designerStore.currentFile && designerStore.currentFile.id) {
        // 确保modGraph已初始化
        if (!designerStore.modGraph || !designerStore.modGraph.fromJSON) {
          // 稍后重试加载内容
          setTimeout(() => {
            if (designerStore.modGraph?.fromJSON) {
              try {
                // 强制渲染画布
                designerStore.modGraph.fromJSON(designerStore.currentFile.content);
              } catch (error) {
                console.error("加载内容到画布失败:", error);
              }
            }
          }, 1000);
        } else {
          try {
            // 直接渲染画布
            designerStore.modGraph.fromJSON(designerStore.currentFile.content);
          } catch (error) {
            console.error("加载内容到画布失败:", error);
          }
        }

        // 确保当前文件信息正确
        nextTick(() => {
          // 触发视图更新
          designerStore.setCurrentFile({ ...designerStore.currentFile });
        });
      }
    }
  } catch (error) {
    console.error('标签恢复过程中出错:', error);
  }
};

// onMounted钩子
onMounted(async () => {
  await initializeNodes();
  window.addEventListener('message', receiveMessage, false);

  // 延迟恢复标签，确保其他初始化已完成
  setTimeout(async () => {
    try {
      await safeRestoreTabs();
    } catch (error) {
      console.error('恢复标签过程中出错:', error);
    }
  }, 800); // 增加延迟时间，确保其他组件已初始化
});
onBeforeUnmount(() => {
  window.removeEventListener('message', receiveMessage)
})

const expandPanel = () => {
  treeWidth.value = 25
  designerWidth.value = 75
}

// 明确定义组件的事件
const emit = defineEmits(['debug'])
</script>

<style scoped>
/* 只保留分割线样式，因为这个是 splitpanes 组件特定的样式 */
:deep(.splitpanes__splitter) {
  background: linear-gradient(0deg, var(--splitpanes-splitter-color), var(--splitpanes-splitter-color));
  border: 1px solid var(--splitpanes-splitter-color) !important;
}

:deep(.splitpanes__splitter)::before {
  background-color: var(--splitpanes-splitter-btn) !important;
}

:deep(.splitpanes__splitter)::after {
  background-color: var(--splitpanes-splitter-btn) !important;
}

.canvas-container {
  width: 100%;
  height: 100%;
  overflow: auto;
}

/* 调整树节点整体对齐 */
:deep(.ant-tree-treenode) {
  display: flex;
  align-items: center;
  min-height: 32px;
  /* 确保最小高度 */
}

/* 展开/折叠图标区域 */
:deep(.ant-tree-switcher) {
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  line-height: 24px;
}

/* 文件夹图标区域 */
:deep(.ant-tree-icon__customize) {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  line-height: 1;
}

/* 修复Ant Design图标默认对齐 */
:deep(.ant-tree-switcher .anticon),
:deep(.ant-tree-icon__customize .anticon) {
  vertical-align: middle;
  font-size: 16px;
}

/* 文字标签对齐 */
:deep(.ant-tree-title) {
  line-height: 24px;
}

:deep(.x6-graph) {
  width: 100% !important;
  /* 强制设置画布宽度 */
  height: 100% !important;
}
</style>
