// 特殊节点定义
import PlaceholderNode from '../../components/specialNodes/placeholderNode/index.vue'

export default {
  component: PlaceholderNode,
  type: 'placeholder-node',
  data: {
    type: 'placeholder-node',
    properties: {
      fontSize: 14,
      color: '#333',
    },
  },
  width: 250,
  height: 160,
  ports: {
    groups: {
      right: {
        position: 'right',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
      left: {
        position: 'left',
        attrs: {
          circle: {
            r: 3,
            magnet: true,
            stroke: '#5F95FF',
            strokeWidth: 1,
            fill: '#fff',
          },
        },
      },
    },
    items: [
      { group: 'right', id: 'right' },
      { group: 'left', id: 'left' },
    ],
  },
}
