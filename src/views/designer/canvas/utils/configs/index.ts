// 定义基础节点组配置
export const nodeGroups: Record<string, any> = {}

// 更新节点组的方法
export const updateNodeGroups = (records: any[]) => {
  // 递归查找所有 groupNode
  const findGroupNodes = (node: any, parent: any = null, result: any[] = []) => {
    if (!node) return result

    const isGroupNode =
      node.children?.length > 0 && node.children.every((child) => !child.children?.length)

      if (isGroupNode) {
        const nodeCopy = {
          ...node,
          parent: undefined
        }
        result.push(nodeCopy)
      }

    // 递归处理子节点
    if (node.children?.length) {
      node.children.forEach((child) => findGroupNodes(child, node, result))
    }

    return result
  }

  if (!Array.isArray(records)) {
    console.warn('records 不是数组')
    return {}
  }

  const allGroupNodes = records.reduce((acc: any[], rootNode) => {
    if (rootNode) {
      return [...acc, ...findGroupNodes(rootNode)]
    }
    return acc
  }, [])
  const groups = {}

  allGroupNodes.forEach((groupNode) => {
    if (!groupNode) return

    const groupKey = groupNode.name || ''
    const path = groupNode.path || ''
    if (!groupKey) return

    // 从第一个子节点获取 content_type
    const firstChild = groupNode.children?.[0]
    let content_type = 'node'
    if (firstChild?.content) {
      try {
        const content =
          typeof firstChild.content === 'string'
            ? JSON.parse(firstChild.content)
            : firstChild.content
        content_type = content?.content_type || 'node'
      } catch (e) {
        console.warn('解析content失败:', e)
      }
    }

    // 处理子节点
    const nodes = {}
    groupNode.children?.forEach((child) => {
      const content = JSON.parse(child.content)
      const content_type = content?.content_type
      if (!child?.id) return
      if (content_type === 'shape') {
        nodes[child.id] = {
          ...child,
          ...content,
        }
      } else {
        nodes[content.type] = {
          ...child,
          ...content,
        }
      }
    })

    // 使用 groupKey 作为键添加到 groups
    if (!groups[groupKey]) {
      groups[groupKey] = {
        id: groupKey,
        title: `${path}节点`,
        description: `${path}相关节点`,
        content_type,
        nodes,
      }
    }
  })

  return groups
}

// 导出分组配置
export default nodeGroups
