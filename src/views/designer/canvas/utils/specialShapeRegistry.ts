import { register } from '@antv/x6-vue-shape'
import { register as registerHtmlShape } from 'x6-html-shape'
import specialShapesNode from '../components/specialShapes/specialShapes.vue'
import { useDesignerStore } from '@/store/designer'
import { storeToRefs } from 'pinia'
import createRender from 'x6-html-shape/dist/vue'

const { isSafari } = storeToRefs(useDesignerStore())

// 定义特殊形状配置
export const specialShapes = {
  specialShape: {
    shape: 'special-shape',
    width: 40,
    height: 40,
    component: specialShapesNode,
    data: {
      id: '',
      label: '',
      type: 'special-shape' // 确保type字段存在且值为'special-shape'
    }
  },
}

// 根据浏览器类型选择注册方法
Object.values(specialShapes).forEach((shape) => {
  if (isSafari.value) {
    registerHtmlShape({
      ...shape,
      render: createRender(shape.component)
    })
  } else {
    register(shape)
  }
})

// 导出注册函数
export const registerSpecialShape = (config) => {
  if (isSafari) {
    registerHtmlShape({
      ...config,
      render: createRender(config.component)
    })
  } else {
    register(config)
  }
}