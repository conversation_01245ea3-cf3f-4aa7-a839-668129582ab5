import { register } from '@antv/x6-vue-shape'
import ResultNode from '../components/node/ResultNode/index.vue'
import { portGroups } from '../constants/portConfig'
import { useDesignerStore } from '@/store/designer'
import { useAppStore } from '@/store/app'
import { storeToRefs } from 'pinia'
import { register as registerHtmlShape } from 'x6-html-shape'
import createRender from 'x6-html-shape/dist/vue'
// import createTeleportRender from "x6-html-shape/dist/teleport";
const { isSafari } = storeToRefs(useDesignerStore())
const appStore = useAppStore()

interface NodeComponent {
  type: string
  field: string
  label: string
  noPreview?: boolean
  props?: Record<string, any>
}

interface NodeConfig {
  id: string
  type: string
  title: string
  icon: string
  description?: string
  ports: {
    left?: boolean
    right?: boolean
    top?: boolean
    bottom?: boolean
    [key: string]: boolean | undefined
  }
  components?: NodeComponent[]
  height?: string
}

interface NodeGroup {
  id: string
  title: string
  description: string
  nodes: Record<string, NodeConfig>
}

const getHeight = (config: NodeConfig): number => {
  // 优先使用配置中的height属性
  if (config.height !== undefined) {
    return Number(config.height);
  }

  // 否则使用计算方式
  const renderComponents = (config.components || []).filter((item) => !item.noPreview)
  const isStart = config.type.includes('start')
  const calculatedHeight = ((isStart ? 2 : 0) + renderComponents.length) * 25.2 + 67;
  return calculatedHeight;
}

// 注册节点组
export const registerNodeGroups = (nodeGroups: Record<string, NodeGroup>): void => {
  // 边框颜色
  const BORDER_COLOR = appStore.theme === 'dark' ? '#303030' : 'transparent'
  Object.values(nodeGroups).forEach((group) => {
    Object.values(group.nodes).forEach((config) => {
      // 构造默认数据
      const defaultData: Record<string, any> = {
        type: config.type,
        inputs: {},
        outputs: {},
        properties: {},
        ports: config.ports
      }

      if (isSafari.value) {
        
        const rendervue = createRender(ResultNode)
        registerHtmlShape({
          icon: config.icon,
          shape: config.type,
          width: 358,
          height: getHeight(config),
          render: rendervue,
          ports: {
            groups: portGroups,
            items: config.ports
          },
          data: defaultData,
          attrs: {
            body: {
              stroke: BORDER_COLOR,
              strokeWidth: 1,
            }
          }
        })
      } else {
        register({
          icon: config.icon,
          shape: config.type,
          width: 358,
          height: getHeight(config),
          component: ResultNode,
          ports: {
            groups: portGroups,
            items: config.ports
          },
          data: defaultData,
          attrs: {
            body: {
              stroke: BORDER_COLOR,
              strokeWidth: 1,
            }
          }
        })
      }
    })
  })
}
