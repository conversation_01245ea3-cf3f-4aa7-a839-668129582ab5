<template>
  <a-modal
    :visible="visible"
    title="生成脚本"
    @cancel="handleCancel"
    @ok="handleSubmit"
    @update:visible="(val) => emit('update:visible', val)"
    width="90%"
    class="generate-script-modal"
  >
    <div class="flex h-[700px] bg-gray-50 rounded-lg">
      <!-- 左侧节点配置 -->
      <div class="w-1/2 p-6 bg-area-fill rounded-l-lg shadow-sm overflow-y-auto">
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          class="space-y-4"
        >
          <a-form-item name="generate" class="mb-6">
            <a-button
              type="primary"
              :loading="loading"
              @click="handleGenerate"
              class="w-full h-10 text-base"
              >生成脚本</a-button
            >
          </a-form-item>
          <a-form-item label="prompt" name="prompt">
            <a-textarea
              v-model:value="form.chat"
              placeholder="请输入"
              :rows="10"
              class="rounded border-gray-200"
            />
          </a-form-item>
        </a-form>
      </div>

      <!-- 中间节点JSON -->
      <div class="w-1/2 p-6 bg-area-fill border-x border-gray-100 overflow-y-auto">
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
        >
          <a-form-item label="生成脚本" name="content" class="mb-0">
            <JsonEditorVue
              v-model="form.content"
              :main-menu-bar="false"
              :navigation-bar="false"
              :status-bar="false"
              :expandedOnStart="true"
              mode="text"
              class="json-editor rounded border border-gray-200"
              @change="handleJsonChange"
            />
          </a-form-item>
        </a-form>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import JsonEditorVue from 'json-editor-vue'
import { useDesignerStore } from '@/store/designer'
import { generateNodeConfig } from '@/apis/designer'

const designerStore = useDesignerStore()

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref<FormInstance>()
const form = ref({
  chat: '',
  content: '{}'
})

const loading = ref(false)

// 添加预览配置
const previewConfig = ref<any>(null)

// 添加展开预览的状态和方法
const expandVisible = ref(false)
const handleExpand = () => {
  expandVisible.value = true
}

const rules = {
  chat: [{ required: true, message: '请输入' }],
}

// 处理 JSON 变化
const handleJsonChange = () => {
  try {
    console.log('json改变')
  } catch (error) {

  }
}

// 监听表单内容变化
watch(() => form.value.content, () => {
  handleJsonChange()
}, { immediate: true })

const getConfig = async () => {
  try {
    loading.value = true
    const data = {
      "requestId": "req-001",
      "modelName": "Qwen3-8B",
      "systemPrompt": "你是一个脚本生成器，请根据需求生成脚本。",
      "chat": form.value.chat,
      "temperature": 0.7,
      "maxTokens": 3000
    }
    const res = await generateNodeConfig(data)
    const result = JSON.parse(JSON.stringify(res.data.result))
    form.value.content = result
  } catch (error) {
    message.error('生成脚本失败')
  } finally {
    loading.value = false
  }
}

// 取消
const handleCancel = () => {
  designerStore.setGenerateScriptPanelVisiable(false)
}

// 提交
const handleSubmit = async () => {
  console.log('保存', form.value)
  designerStore.setGenerateScriptPanelVisiable(false)
}

const handleGenerate = () => {
  getConfig()
}
</script>

<style scoped>
.json-editor {
  height: 520px;
  @apply border border-gray-200 rounded;
}

.input-output-editor {
  height: 150px;
}

/* 自定义滚动条样式 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #d4d4d4 #f5f5f5;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f5f5f5;
  @apply rounded;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded;
}

.preview-content {
  min-height: 300px;
}

:deep(.ant-form-item-label > label) {
  @apply text-gray-600 font-medium;
}

:deep(.ant-input), :deep(.ant-input-textarea) {
  @apply rounded border-gray-200;
}

:deep(.ant-input:hover), :deep(.ant-input-textarea:hover) {
  @apply border-blue-400;
}

:deep(.ant-input:focus), :deep(.ant-input-textarea:focus) {
  @apply border-blue-500 shadow-none;
}

:deep(.ant-btn-primary) {
  @apply bg-blue-500 border-blue-500 hover:bg-blue-600 hover:border-blue-600;
}
</style>
