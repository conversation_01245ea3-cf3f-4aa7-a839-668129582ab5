<template>
  <div class="cataloguepanel-container w-full">
    <div
      class="absolute top-[7px] left-[-5px] flex items-center pl-4 h-8 w-10 z-50 cursor-pointer"
      @click="handleReturn"
    >
      <LeftOutlined class="text-custom" />
    </div>
    <a-tabs v-model:activeKey="activeName" class="w-full custom-tabs">
      <a-tab-pane key="projectfile" tab="资源">
        <div class="tree-wrapper">
          <YssTree
            :data="treeData"
            :default-expand-all="false"
            :context-menu="contextmenu"
            :dark="isDark"
            :nodeClick="handleNodeClick"
            :draggable="true"
            :show-search="true"
            :enter-button="false"
            search-placeholder="请输入文件名"
            @search="handleSearch"
            @nodeDrop="handleNodeDrop"
            v-model:expanded-keys="expandedKeys"
            @node-expand="handleNodeExpand"
          >
            <template #title-icon="{ node }">
              <FolderOutlined style="color: #409eff; margin-left: 8px;" v-if="node?.dir" />
              <FileOutlined v-else />
            </template>
          </YssTree>
        </div>
      </a-tab-pane>
    </a-tabs>
    <div class="fold-expand text-custom">
      <MenuFoldOutlined class="text-xl cursor-pointer" @click="foldToggle" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { MenuFoldOutlined, FolderOutlined, FileOutlined, LeftOutlined } from '@ant-design/icons-vue'
import { getFileApi, renameApi, deleteApi, createFileApi, mkdirApi, saveFileApi, deleteCanvasApi } from '@/apis/designer'
import { useDesignerStore } from '@/store/designer'
import { message, Modal } from 'ant-design-vue'
import { onMounted, ref, watch } from 'vue'
import { YssTree } from '@yss-design/ui'
import { useAppStore } from '@/store/app'

const appStore = useAppStore()

// 定义目录数据类型
interface DirectoryData {
  id: string
  label: string
  path: string
  dir: boolean
  children?: DirectoryData[]
  directoryId?: string
  parentId?: string
  level?: number
}

// 定义内容类型
interface Content {
  data: {
    content: string
  }
  mode?: string
}

const designerStore = useDesignerStore()

const props = defineProps({
  treeData:{
    type: Array<DirectoryData>,
    default:()=>[]
  },
  refreshData:{
    type:Function,
    default: new Function()
  },
  targetId: {
    type: String,
    default: ''
  }
})
const emits = defineEmits(['changeWidth'])
const router = useRouter()
const isDark = computed<boolean>(() => appStore.theme === 'dark')

const foldToggle = () => {
  // 修改折叠逻辑，确保右侧面板宽度正确
  emits('changeWidth', 0)
}
const dynamicExpandedKeys = ref<string[]>([])
const activeName = ref('projectfile')
// 获取userid
const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))

const handleReturn = () => {
  // 替换当前路由（而不是添加新路由）
  router.replace('/designer/files')
}

// 修改初始化检查函数，添加超时机制
const ensureModGraphReady = () => {
  if (!designerStore.modGraph) {
    designerStore.initModGraph()
  }

  let attempts = 0;
  const maxAttempts = 10; // 最多尝试10次

  return new Promise((resolve) => {
    const checkGraph = () => {
      attempts++;
      if (designerStore.modGraph && typeof designerStore.modGraph.fromJSON === 'function') {
        resolve(true)
      } else if (attempts < maxAttempts) {
        setTimeout(checkGraph, 100)
      } else {
        // 超时后也resolve，让流程继续
        resolve(false)
      }
    }
    checkGraph()
  })
}

// 修改 handleNodeClick 函数
const handleNodeClick = async(data:DirectoryData) => {
  if(data.dir) {
    return
  }
  // 切换标签时关闭节点配置面板
  designerStore.setRightPanelVisiable(false)
  try {
    const res = await getFileApi({id:data.id})
    const fileContent = res as unknown as Content
    const tab = {
      id: data.id,
      directoryId: data.directoryId,
      name: data.label,
      path: data.path,
      content: JSON.parse(fileContent.data.content)
    }
    designerStore.addTab(tab)

    // 尝试确保 modGraph 准备好，但不阻塞流程
    const isReady = await ensureModGraphReady()

    if (isReady && designerStore.modGraph?.fromJSON) {
      designerStore.modGraph.fromJSON(tab.content)
    }

    designerStore.setMode(res['mode'] || 'all')
  } catch (error) {
    console.error('Error in handleNodeClick:', error)
    message.error('操作失败，请重试')
  }
}

// 处理节点拖拽
const handleNodeDrop = async ({ dragNode, dropNode, dropPosition }) => {
  try {
    // 构建移动节点的参数，拖动到文件下面时给它移动到文件所在的文件夹下
    const params = {
      id: dragNode.dataRef.id,
      name: dragNode.dataRef.label,
      parentId: dropNode.id,
      path: `${dropNode.path}/${dragNode.label}`,
      level: dropNode.dataRef.level,
      directoryId: dropNode.dataRef.dir ? dropNode.dataRef.id : dropNode.dataRef.directoryId,
      userId: userInfo.value.id,
    }
    let res;
    if (dragNode.dataRef.dir) {
      // 如果是文件夹，调用重命名API
      res = await renameApi(params);
    } else {
      // 如果是文件，调用保存文件API
      res = await saveFileApi(params);
    }

    if (res.code === 200) {
      message.success('移动成功')
      // 刷新树数据
      props.refreshData()
    } else {
      message.error('移动失败')
    }
  } catch (error) {
    console.error('节点移动失败:', error)
    message.error('移动失败')
  }
}

const contextmenu = [
  {
    code: 'addFile',
    label: '新建文件',
    minWidth: 0,
    callback: async (data) => {
      const params = {
        path: data.label,
        modelName: 'test',
      }
      const res = await createFileApi({
        name: data.name,
        content: JSON.stringify(params),
        directoryId: data.parentId,
        userId: userInfo.value.id
      })
      if(res.code === 200){
        message.success('创建成功')
        props.refreshData()
      }
    }
  },
  {
    code: 'addDir',
    label: '新建文件夹',
    callback: async(data) => {
      const res = await mkdirApi({ name: data.name, parentId: data.parentId, userId: userInfo.value.id, type: 'canvas' })
      if(res.code === 200){
        message.success('创建成功')
        props.refreshData()
      }
    }
  },
  {
    code: 'rename',
    label: '重命名',
    callback: async(data) => {
      let res;
      const params = {
        userId: userInfo.value.id,
        ...data
      }
      if (data.dir === true) {
        // 如果是文件夹，调用重命名API
        res = await renameApi(params);
      } else {
        // 如果是文件，调用保存文件API
        res = await saveFileApi(params);
      }
      if(res.code === 200){
        message.success('重命名成功')
      }
      props.refreshData() // 失败也要刷新
    }
  },
  {
    label: '删除',
    code: 'delete',
    callback: (data) => {
      const name = data.label
      const text = data.dir ? `文件夹【${name}】` : `文件【${name}】`
      Modal.confirm({
        title: '提示',
        content: `确定要删除${text}吗?`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          let res;
          const params = {
            id: data.id,
            userId: userInfo.value.id
          }
          if (data.dir) {
            // 如果是文件夹，调用删除目录API
            res = await deleteApi(params);
          } else {
            // 如果是文件，调用删除画布API
            res = await deleteCanvasApi(params);
            if(res.code === 200){
              designerStore.tabData.delete(data.id);
              // 清空画布
              designerStore.modGraph?.clearCells?.();
            }
          }
          if(res.code === 200){
            message.success('删除成功')
            props.refreshData()
          }
        }
      })
    }
  }
]

function findDirectoryById(data: DirectoryData[], id: string): DirectoryData | null {
  for(let key in data) {
    if (data[key].id.endsWith(`/${id}`)) {
      return data[key]
    }
    const foundInChildren = findDirectoryById(data[key].children, id)
      if (foundInChildren) {
        return foundInChildren
    }
  }
  return null
}

const defaultOPen = () => {
  if (!props.targetId || props.treeData.length === 0) return
  const tar = findDirectoryById(props.treeData, props.targetId)
  if (!tar) return
  handleNodeClick(tar)
}

// 监听树数据变化
watch(
  ()=>props.treeData,
  (newData)=>{
    if (newData && newData.length > 0) {
      // 短暂延迟，确保DOM已更新
      setTimeout(directExpandAll, 200)
    }
  }
)

// 展开节点
const expandedKeys = ref<string[]>([]);
const handleNodeExpand = (keys: string[]) => {
  expandedKeys.value = keys;
};

// 在组件挂载时进行初始化
onMounted(() => {
  // 初始化模型图
  ensureModGraphReady()

  // 检查DOM和应用到组件上
  // setTimeout(directExpandAll, 800)
  setTimeout(() => {
    // 默认展开第一层
    const firstLevelKeys = props.treeData.map((node) => node.id);
    expandedKeys.value = [...new Set([...expandedKeys.value, ...firstLevelKeys])];
  }, 800)
})

// 切换tab时，展开当前目录
watch(
  () => designerStore.currentFile,
  (newVal, oldVal) => {
    if(newVal && newVal?.id !== oldVal?.id){
      handleExpandCurrentNode(newVal.directoryId)
    }
  }
)

// 查找节点路径
const findNodePath = (nodes: DirectoryData[], targetId: string) => {
  const path: string[] = [];

  const find = (nodes: DirectoryData[], targetId: string) => {
    for (const node of nodes) {
      if (node.id === targetId) {
        path.push(node.id);
        return true;
      }
      if (node.children) {
        path.push(node.id);
        if (find(node.children, targetId)) {
          return true;
        }
        path.pop();
      }
    }
    return false;
  };

  find(nodes, targetId);
  return path;
};

const handleExpandCurrentNode = (nodeId: string) => {
  const path = findNodePath(props.treeData, nodeId);
  expandedKeys.value = [...new Set([...expandedKeys.value, ...path])];
};

// 直接操作DOM强制展开
const directExpandAll = () => {
  // 获取所有可折叠的元素
  const expandButtons = document.querySelectorAll('.ant-tree-switcher.ant-tree-switcher_close')

  // 点击所有折叠的按钮
  expandButtons.forEach((btn) => {
    ;(btn as HTMLElement).click()
  })

  // 再次检查是否都已展开
  setTimeout(() => {
    const remainingButtons = document.querySelectorAll('.ant-tree-switcher.ant-tree-switcher_close')

    // 如果仍有未展开的节点，再次展开
    if (remainingButtons.length > 0) {
      remainingButtons.forEach((btn) => {
        ;(btn as HTMLElement).click()
      })
    }
  }, 100)
}

const flattenTreeData = (data) => {
  let res: string[] = [];
  if (data.length == 0) return res;
  for (let i = 0; i < data.length; i++) {
    if (data[i].children.length > 0) {
      res = res.concat(flattenTreeData(data[i].children));
    }
    else {
      res.push(data[i]?.id || '');
    }
  }
  return res;
}

const handleSearch = (value: string) => {
  if (value) {
      const filterDataId = flattenTreeData(props.treeData);
      if (filterDataId && filterDataId.length > 0) {
        filterDataId.forEach((item) => {
          handleExpandCurrentNode(item);
        });
      }
    } else {
      if (props.treeData && props.treeData.length > 0) {
        const firstLevelKeys = props.treeData.map((node) => node.id);
        expandedKeys.value = Array.from(new Set(firstLevelKeys));
        handleExpandCurrentNode(designerStore.currentFile.directoryId)
      }
    }
}
</script>

<style lang="scss" scoped>
.cataloguepanel-container {
  display: flex;
  padding-left:10px;
  border-radius: 16px;
  justify-content: space-between;
  align-items: flex-start;
  box-sizing: border-box;
  background-color: var(--area-fill-color);
  padding-left: 16px;
  height: calc(100vh - 10px);
  margin-bottom:10px;
  box-sizing: border-box;
  overflow: hidden;
  @apply dark:!rounded-none;

  :deep(.ant-tabs) {
    .ant-tabs-nav {
      border-bottom: none !important;
      &::before {
        border-bottom: none !important;
      }
    }
    .ant-tabs-nav-wrap {
      border-bottom: none !important;
    }
    .ant-tabs-nav-list {
      border-bottom: none !important;
    }
  }

  .tree-wrapper {
    padding: 5px 5px 16px 5px;  // 树组件四周内边距，上方不加
    margin-top: 0;  // 去掉顶部外边距
    height: 100vh; // 设置为100vh
    overflow-y: auto; // 添加垂直滚动条
    overflow-x: hidden; // 隐藏水平滚动条
    padding-bottom: 100px; // 添加底部内边距
    :deep(svg){
      @apply dark:!text-white;
    }
  }

  .tab {
    flex: 1;
    :deep(.el-tabs__header) {
      margin: 0;
    }
  }

  .fold-expand {
    position: relative;
    background:var(--area-fill-color);
    height:40px;
    line-height:40px;
    width:40px;
    cursor:pointer;
  }

  // 上下padding 加了16px
  .custom-tabs{
    height: calc(100vh - 42px);
  }

  :deep(.ant-input-search) {
    .ant-input-search-button {
      display: none !important;
    }
    .ant-input-affix-wrapper {
      border-right-width: 0px !important;
      border-radius: 6px !important;
      background-color: var(--left-nav-bar-fill-color) !important;
    }
    .ant-input {
      border-radius: 6px !important;
      background-color: var(--left-nav-bar-fill-color) !important;
    }
  }
}
</style>
