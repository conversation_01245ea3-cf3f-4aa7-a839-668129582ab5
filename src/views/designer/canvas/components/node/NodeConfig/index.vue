<template>
  <EmbeddedDrawer
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="nodeConfig?.title"
    :width="550"
    @close="handleClose"
    :mask="false"
    :maskClosable="false"
    :allow-fullscreen="true"
    @fullscreen-change="handleFullscreenChange"
  >
    <!-- 内容区 -->
    <div
      class="description px-4 py-4 pt-0 bg-area-fill dark:!text-white text-gray-500 text-sm border-b z-10"
    >
      {{ nodeConfig?.description }}
    </div>

    <!-- 配置内容区 -->
    <div class="config-content" :class="{ 'fullscreen-config': isFullscreen }">
      <template v-for="(item, index) in nodeConfig?.components" :key="index">
        <!-- 主组件 -->
        <div class="p-4 border-b relative bg-area-fill" :class="{ 'fullscreen-component': isFullscreen }" v-show="shouldShowComponent(item)">
          <div class="mb-4 text-sm text-gray-900 dark:!text-white">
            {{ item.label }}
          </div>

          <!-- 对contentParams类型做特殊处理 -->
          <component
            v-if="isCustomComponent(item.type) && item.type === 'contentParams'"
            :is="getConfigComponent(item.type, item)"
            :key="`contentParams-${index}`"
            v-model="getContentParamsData(item).value"
            :default-params="item.props?.defaultParams || []"
            v-bind="item.props || {}"
            @update:modelValue="(val) => handleContentParamsChange(val, item)"
            class="w-full"
          />

          <!-- 其他自定义组件 -->
          <component
            v-else-if="isCustomComponent(item.type)"
            :is="getConfigComponent(item.type, item)"
            :key="`custom-${index}`"
            v-model="formData[item.field]"
            :default-params="item.props?.defaultParams || []"
            v-bind="item.props || {}"
            @update:modelValue="(val) => handleComponentChange(item.field, val)"
            class="w-full"
          />

          <!-- 变量增强输入组件 -->
          <VariableInput
            v-else-if="item.type === 'a-input' || item.type === 'a-textarea'"
            :key="`variable-${index}`"
            v-model:value="formData[item.field]"
            :input-type="item.type === 'a-textarea' ? 'textarea' : 'input'"
            v-bind="item.props || {}"
            @update:modelValue="(val) => handleComponentChange(item.field, val)"
            @manage-variables="showSystemVariables = true"
            class="w-full"
          />

          <!-- 标准组件 -->
          <component
            v-else
            :is="item.type"
            :key="`antd-${index}`"
            v-model:value="formData[item.field]"
            v-bind="item.props || {}"
            @change="(val) => handleComponentChange(item.field, val?.target?.value ?? val)"
            class="w-full"
          />

          <!-- 子组件 -->
          <template v-if="item.children">
            <div
              v-for="(child, childIndex) in item.children"
              :key="`${index}-${childIndex}`"
              class="mt-4 ml-4"
              v-show="shouldShowChild(child)"
            >
              <div class="mb-2 text-sm text-gray-900 dark:!text-white">
                {{ child.label }}
              </div>

              <component
                :is="getConfigComponent(child.type, child)"
                v-if="isCustomComponent(child.type)"
                :key="`custom-child-${childIndex}`"
                v-model="formData[child.field]"
                v-bind="child.props || {}"
                :config="nodeConfig"
                @update:modelValue="(val) => handleComponentChange(child.field, val)"
                class="w-full"
              />
              <component
                :is="child.type"
                v-else
                :key="`antd-child-${childIndex}`"
                v-model:value="formData[child.field]"
                v-bind="child.props || {}"
                @change="(val) => handleComponentChange(child.field, val?.target?.value ?? val)"
                class="w-full"
              />

              <!-- 递归渲染子组件的子组件 -->
              <template v-if="child.config.children">
                <div
                  v-for="(grandChild, grandChildIndex) in child.config.children"
                  :key="`${index}-${childIndex}-${grandChildIndex}`"
                  class="mt-4 ml-4"
                  v-show="shouldShowChild(grandChild)"
                >
                  <div class="mb-2 text-sm text-gray-900 dark:!text-white">
                    {{ grandChild.label }}
                  </div>

                  <component
                    :is="getConfigComponent(grandChild.type, grandChild)"
                    v-if="isCustomComponent(grandChild.type)"
                    :key="`custom-grandchild-${grandChildIndex}`"
                    v-model="formData[grandChild.field]"
                    v-bind="grandChild.props || {}"
                    :config="nodeConfig"
                    @update:modelValue="(val) => handleComponentChange(grandChild.field, val)"
                    class="w-full"
                  />
                  <component
                    :is="grandChild.type"
                    v-else
                    :key="`antd-grandchild-${grandChildIndex}`"
                    v-model:value="formData[grandChild.field]"
                    v-bind="grandChild.props || {}"
                    @change="(val) => handleComponentChange(grandChild.field, val?.target?.value ?? val)"
                    class="w-full"
                  />
                </div>
              </template>
            </div>
          </template>
        </div>
      </template>
    </div>
  </EmbeddedDrawer>

  <!-- 系统变量管理 -->
  <SystemVariables v-model:visible="showSystemVariables" />
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { getConfigComponent } from '../../configRender'
import { useDesignerStore } from '@/store/designer'
import {EmbeddedDrawer} from '@/components'
import { getDesignerEnumList } from '@/apis/designer'
import VariableInput from '../../toolbar/systemVariables/VariableInput.vue'
import SystemVariables from '../../toolbar/systemVariables/index.vue'

interface NodeGroup {
  nodes: Record<string, any>;
  [key: string]: any;
}

const props = defineProps({
  visible: Boolean,
})
const emit = defineEmits(['update:visible'])

// Store
const designerStore = useDesignerStore()

// 系统变量管理状态
const showSystemVariables = ref(false)

// 获取当前节点配置
const nodeConfig = computed(() => {
  const currentNode = designerStore.currentNode
  if (!currentNode) return null

  const nodeData = currentNode.getData()
  if (!nodeData?.type) return null

  // 使用 store 中的 nodeGroups
  const nodeGroups = designerStore.nodeGroups || {}
  for (const group of Object.values(nodeGroups) as NodeGroup[]) {
    const config = group.nodes[nodeData.type]
    if (config) return config
  }

  return null
})

// 表单数据
const formData = ref<Record<string, any>>({})

// 添加一个标志来追踪组件是否已挂载
const isMounted = ref(false)

// 全屏状态
const isFullscreen = ref(false)

// 添加状态来存储枚举选项
const enumOptions = ref<{ label: string; value: any }[]>([])

// 为contentParams专门创建一个响应式对象
const contentParamsDataMap = ref<Map<string, Record<string, any>>>(new Map())

// 添加计算属性来处理contentParams数据
const getContentParamsData = (component: any) => {
  return computed({
    get: () => contentParamsDataMap.value.get(component.label || component.type) || {},
    set: (val) => {
      contentParamsDataMap.value.set(component.label || component.type, val)
      handleContentParamsChange(val, component)
    }
  })
}

// 数据管理器 - 处理startNode和普通节点的差异
const nodeDataManager = {
  // 获取字段值
  getFieldValue(nodeData: any, field: string): any {
    // 处理startNode的特殊情况
    if (nodeData.type === 'startNode') {
      // 直接返回outputs数组
      if (field === 'outputs') {
        return Array.isArray(nodeData.outputs) ? nodeData.outputs : []
      }

      // 如果field是commonsParams相关字段(headers, commons, data)，从outputs对象中获取
      if (['headers', 'commons', 'data'].includes(field)) {
        return nodeData.outputs?.[field] || []
      }

      // 其他字段从outputs中获取
      return nodeData.outputs?.[field]
    }

    // 处理普通节点的字段
    if (field === 'inputs' || field === 'outputs') {
      return nodeData[field] || []
    }

    return nodeData.properties?.[field]
  },

  // 设置字段值
  setFieldValue(nodeData: any, field: string, value: any): void {
    console.log(`设置字段 ${field}`, value, typeof value);

    // 特殊处理startNode节点
    if (nodeData.type === 'startNode') {
      // 如果是outputs字段，直接替换为数组
      if (field === 'outputs') {
        // 确保outputs是数组
        if (Array.isArray(value)) {
          nodeData.outputs = value;
        } else {
          console.warn(`字段 outputs 期望数组值，但收到:`, value);
          // 保留原有数组
          if (!Array.isArray(nodeData.outputs)) {
            nodeData.outputs = [];
          }
        }
      }
      // 如果是inputs字段，确保是对象
      else if (field === 'inputs') {
        nodeData.inputs = value || {};
      }
      // 如果是commonsParams相关字段，存入outputs对象的对应属性中
      else if (['headers', 'commons', 'data'].includes(field)) {
        if (!nodeData.outputs) {
          nodeData.outputs = {};
        }
        // 确保值是数组
        if (Array.isArray(value)) {
          nodeData.outputs[field] = value;
        } else {
          console.warn(`字段 ${field} 期望数组值，但收到:`, value);
          nodeData.outputs[field] = Array.isArray(nodeData.outputs[field]) ? nodeData.outputs[field] : [];
        }
      }
      // 处理其他字段，存入outputs对象
      else {
        if (!nodeData.outputs) {
          nodeData.outputs = {};
        }
        nodeData.outputs[field] = value;
      }
    }
    // 处理普通节点
    else {
      // 处理inputs和outputs字段
      if (field === 'inputs' || field === 'outputs') {
        // 确保是数组
        if (Array.isArray(value)) {
          nodeData[field] = value;
        } else {
          console.warn(`字段 ${field} 期望数组值，但收到:`, value);
          nodeData[field] = Array.isArray(nodeData[field]) ? nodeData[field] : [];
        }
      }
      // 处理其他字段
      else {
        if (!nodeData.properties) {
          nodeData.properties = {};
        }
        nodeData.properties[field] = value;
      }
    }
  }
}

// 修改获取枚举值的方法
const fetchEnumOptions = async (enumKey: string) => {
  try {
    const response = await getDesignerEnumList(enumKey)
    if (response?.data?.[0]?.content) {
      // 将 content 数组转换为 label-value 格式
      enumOptions.value = response.data[0].content.map((item: string) => ({
        label: item,
        value: item
      }))
    }
  } catch (error) {
    console.error('获取枚举值失败:', error)
    enumOptions.value = []
  }
}

// 组件挂载时设置标志
onMounted(() => {
  isMounted.value = true
})

// 组件卸载前清除标志
onBeforeUnmount(() => {
  isMounted.value = false
})

// 初始化表单数据
const initializeFormData = () => {
  // 重置数据
  formData.value = {}

  console.log("初始化表单数据")

  // 获取当前节点
  const currentNode = designerStore.currentNode
  if (!currentNode) return

  // 获取节点数据
  const nodeData = currentNode.getData()
  console.log("节点数据:", nodeData)

  // 递归处理组件及其子组件
  const processComponent = (component: any) => {
    if (component.type === 'contentParams') {
      // 为contentParams获取数据
      const data = getContentParamsValues(component)
      contentParamsDataMap.value.set(component.label || component.type, data)
      console.log('设置contentParams表单数据:', data)
    } else if (component.type === 'commonsParams') {
      // 为commonsParams组件处理数据
      const fieldName = component.field || component.type
      if (fieldName) {
        if (nodeData.type === 'startNode') {
          formData.value[fieldName] = {
            headers: Array.isArray(nodeData.outputs?.headers) ? nodeData.outputs.headers : [],
            commons: Array.isArray(nodeData.outputs?.commons) ? nodeData.outputs.commons : [],
            data: Array.isArray(nodeData.outputs?.data) ? nodeData.outputs.data : []
          }
        } else {
          const data = nodeData.properties?.[fieldName] || {}
          formData.value[fieldName] = {
            headers: Array.isArray(data.headers) ? data.headers : [],
            commons: Array.isArray(data.commons) ? data.commons : [],
            data: Array.isArray(data.data) ? data.data : []
          }
        }
        console.log(`设置commonsParams字段 ${fieldName} 值:`, formData.value[fieldName])
      }
    } else if (component.type === 'inputs') {
      // 处理inputs字段
      if (nodeData.type === 'startNode') {
        formData.value[component.field] = nodeData.inputs || {};
      } else {
        formData.value[component.field] = Array.isArray(nodeData.inputs) ? nodeData.inputs : [];
      }
    } else if (component.type === 'outputs') {
      // 确保outputs始终是数组
      formData.value[component.field] = Array.isArray(nodeData.outputs) ? nodeData.outputs : [];
    } else {
      // 处理其他组件
      const fieldName = component.field || component.type
      if (fieldName) {
        formData.value[fieldName] = nodeDataManager.getFieldValue(nodeData, fieldName) || component.default || ''
        console.log(`设置字段 ${fieldName} 值:`, formData.value[fieldName])
      }
    }

    // 处理子组件
    if (component.children && Array.isArray(component.children)) {
      component.children.forEach(child => {
        if (child.config && Array.isArray(child.config.children)) {
          child.config.children.forEach(grandChild => {
            const fieldName = grandChild.field;
            if (
              nodeData.properties &&
              nodeData.properties.config &&
              typeof nodeData.properties.config === 'object'
            ) {
              formData.value[fieldName] =
                nodeData.properties.config[fieldName] ||
                grandChild.default ||
                '';
            } else {
              formData.value[fieldName] = grandChild.default || '';
            }
          });
        }
        // 递归处理子节点本身
        processComponent(child);
      });
    }
  }

  // 处理所有组件
  if (nodeConfig.value?.components?.length > 0) {
    nodeConfig.value.components.forEach(processComponent)
  }
}

// 获取commonsParams的值
const getCommonsParamsValues = (nodeData: any, field: string) => {
  // 如果是startNode，从outputs中获取对应字段
  if (nodeData.type === 'startNode') {
    // 获取outputs中对应的字段
    const commonsData = nodeData.outputs?.[field] || {}

    // 确保是符合预期的数据结构
    const result = {
      headers: Array.isArray(commonsData.headers) ? commonsData.headers : [],
      commons: Array.isArray(commonsData.commons) ? commonsData.commons : [],
      data: Array.isArray(commonsData.data) ? commonsData.data : []
    }

    // 调试日志
    console.log(`startNode ${field} 数据:`, result)
    return result
  } else {
    // 普通节点，从properties中获取
    const result = nodeData.properties?.[field] || {
      headers: [],
      commons: [],
      data: []
    }

    // 确保每个字段都是数组
    if (!Array.isArray(result.headers)) result.headers = []
    if (!Array.isArray(result.commons)) result.commons = []
    if (!Array.isArray(result.data)) result.data = []

    // 调试日志
    console.log(`普通节点 ${field} 数据:`, result)
    return result
  }
}

// 在watch中修改
watch([() => props.visible, () => designerStore.currentNode], ([newVisible, newNode]) => {
  console.log("visible或currentNode变化:", newVisible, newNode !== null)

  if (newVisible && newNode) {
    console.log("通过watch调用initializeFormData")
    initializeFormData()

    // 确保contentParams组件拿到最新的数据
    if (nodeConfig.value?.components) {
      const contentParamsComponent = nodeConfig.value.components.find(c => c.type === 'contentParams')
      if (contentParamsComponent) {
        console.log("立即获取contentParams数据")
        contentParamsDataMap.value.set(contentParamsComponent.label || contentParamsComponent.type, getContentParamsValues(contentParamsComponent))
      }
    }
  }
}, { immediate: true })

// 判断组件是否显示
const shouldShowComponent = (item: any) => {
  if (!item.show) return true
  return item.show(formData.value)
}

// 判断是否为自定义组件
const isCustomComponent = (type: string) => {
  return !type.startsWith('a-')  // 如果不是 ant-design 组件就是自定义组件
}

// 更新子组件显示判断逻辑
const shouldShowChild = (child: any) => {
  if (!child.showOn) return true

  // 处理单个条件
  if (!Array.isArray(child.showOn)) {
    const { field, value } = child.showOn
    return formData.value[field] === value
  }

  // 处理多个条件（AND）
  return child.showOn.every(condition => {
    const { field, value } = condition
    return formData.value[field] === value
  })
}

// 关闭抽屉
const handleClose = () => {
  isFullscreen.value = false
  emit('update:visible', false)
}

// 处理全屏状态变化
const handleFullscreenChange = (fullscreen: boolean) => {
  isFullscreen.value = fullscreen
}

// 处理组件值变化
const handleComponentChange = (field: string, value: any) => {
  if (value instanceof Event) return
  const currentNode = designerStore.currentNode
  if (!currentNode) return

  console.log("handleComponentChange:", field, value)

  try {
    // 获取当前节点数据
    const nodeData = currentNode.getData()

    // 添加调试日志
    console.log(`处理字段 ${field} 变化，值:`, value);
    console.log(`节点类型:`, nodeData.type);

    // 检查是否是contentParams的数据
    const isContentParamsData = field === undefined && typeof value === 'object' && value !== null && !Array.isArray(value)

    if (isContentParamsData) {
      // 处理contentParams数据
      if (!nodeData.properties) {
        nodeData.properties = {};
      }

      // 确保layout_properties存在
      if (!nodeData.layout_properties) {
        nodeData.layout_properties = {};
      }

      // 处理每个键值对
      Object.entries(value).forEach(([key, val]) => {
        if (key && key !== 'undefined') {
          // 检查是否带有_quote或_default后缀
          if (key.endsWith('_quote') || key.endsWith('_default')) {
            // 带后缀的存入layout_properties
            nodeData.layout_properties[key] = val;
            console.log(`设置layout属性 ${key}=${val}`);
          } else {
            // 无后缀的存入properties
            nodeData.properties[key] = val;
            console.log(`设置属性 ${key}=${val}`);
          }
        }
      })
    }
    // 检查是否是commonsParams结构
    else if (field === 'outputs' && typeof value === 'object' && value !== null && !Array.isArray(value) &&
             ('headers' in value || 'commons' in value || 'data' in value)) {
      console.log('检测到outputs是commonsParams格式，特殊处理:', value);

      // 对于startNode节点
      if (nodeData.type === 'startNode') {
        // 将commonsParams数据保存到正确的位置
        if (!nodeData.outputs) {
          nodeData.outputs = {};
        }

        // 确保每个字段都是数组
        nodeData.outputs.headers = Array.isArray(value.headers) ? value.headers : [];
        nodeData.outputs.commons = Array.isArray(value.commons) ? value.commons : [];
        nodeData.outputs.data = Array.isArray(value.data) ? value.data : [];

        console.log('保存到startNode.outputs中:', nodeData.outputs);
      } else {
        // 对于普通节点，保存到properties中
        if (!nodeData.properties) {
          nodeData.properties = {};
        }
        nodeData.properties.outputs = {
          headers: Array.isArray(value.headers) ? value.headers : [],
          commons: Array.isArray(value.commons) ? value.commons : [],
          data: Array.isArray(value.data) ? value.data : []
        };
      }
    }
    else {
      // 递归查找 children.config.children
      let found = false
      if (nodeConfig.value?.components) {
        for (const item of nodeConfig.value.components) {
          if (item.children) {
            for (const child of item.children) {
              if (child.config && child.config.children) {
                for (const grandChild of child.config.children) {
                  if (grandChild.field === field) {
                    found = true
                  }
                }
              }
            }
          }
        }
      }

      if (found) {
        // 直接将children.config.children的值保存在 properties.config 下
        if (!nodeData.properties) nodeData.properties = {}
        if (typeof nodeData.properties.config !== 'object' || nodeData.properties.config === null || Array.isArray(nodeData.properties.config)) {
          nodeData.properties.config = {}
        }
        nodeData.properties.config[field] = value
      } else {
        // 普通字段直接保存
        nodeDataManager.setFieldValue(nodeData, field, value)
      }
    }

    // 更新节点数据
    currentNode.setData(nodeData)
    currentNode.trigger('change:data', {
      data: nodeData
    })

    // 更新store
    designerStore.setCurrentNodeData(nodeData)

    // 输出最终节点数据，便于调试
    console.log('保存后的节点数据:', JSON.parse(JSON.stringify(currentNode.getData())))
  } catch (error) {
    console.error('更新节点数据时出错:', error)
  }
}

// 专门为contentParams处理变更
const handleContentParamsChange = (value: any, component: any) => {
  console.log("contentParams值变化:", value);

  // 获取当前节点
  const currentNode = designerStore.currentNode;
  if (!currentNode) return;

  // 获取节点数据
  const nodeData = currentNode.getData();

  // 确保对象存在
  if (!nodeData.properties) {
    nodeData.properties = {};
  }
  if (!nodeData.layout_properties) {
    nodeData.layout_properties = {};
  }

  // 收集所有字段的值，方便后续统一处理
  const fieldsData = new Map();

  // 处理传入的参数值
  if (typeof value === 'object' && value !== null) {
    // 提取所有参数信息
    Object.entries(value).forEach(([key, val]) => {
      if (!key) return;

      // 判断字段类型
      const isQuote = key.endsWith('_quote');
      const isDefault = key.endsWith('_default');

      if (isQuote || isDefault) {
        // 获取基础字段名
        const baseField = key.substring(0, key.length - (isQuote ? '_quote'.length : '_default'.length));

        // 保存带后缀的字段到layout_properties
        nodeData.layout_properties[key] = val;
        console.log(`设置${isQuote ? '引用' : '默认'}值 ${key} = ${val} (到layout_properties)`);

        // 收集字段信息
        if (!fieldsData.has(baseField)) {
          fieldsData.set(baseField, { quote: '', default: '' });
        }

        if (isQuote) {
          fieldsData.get(baseField).quote = val;
        } else {
          fieldsData.get(baseField).default = val;
        }

        // 同时检查properties中是否有错误存储的_quote字段，如果有则移除
        if ((key.endsWith('_quote') || key.endsWith('_default')) && key in nodeData.properties) {
          console.log(`从properties中移除错误存储的 ${key}`);
          delete nodeData.properties[key];
        }
      }
      else {
        fieldsData.set(key, { quote: '', default: '', val });
        // 对于普通字段，检查是否是已知的基础字段，如果不是则直接更新
        const quoteField = `${key}_quote`;
        const defaultField = `${key}_default`;

        // 如果不是通过quote或default管理的字段，直接更新
        if (!(quoteField in value) && !(defaultField in value)) {
          nodeData.properties[key] = val;
          console.log(`设置字段值 ${key} = ${val} (到properties)`);
        }
      }
    });
  }

  // 根据收集的信息更新原始值
  fieldsData.forEach((data, field) => {
    if (data.val !== '') {
      nodeData.properties[field] = data.val;
      console.log(`使用值更新 ${field} = ${data.val}`);
    } else if (data.quote !== '') {
      // 如果有引用值，使用引用值
      nodeData.properties[field] = data.quote;
      console.log(`使用引用值更新 ${field} = ${data.quote}`);
    } else if (data.default !== '') {
      // 如果引用值为空但有默认值，使用默认值
      nodeData.properties[field] = data.default;
      console.log(`使用默认值更新 ${field} = ${data.default}`);
    } else {
      // 两者都为空，设为空字符串
      nodeData.properties[field] = '';
      console.log(`清空字段 ${field} = ''`);
    }
  });

  // 清理一下数据，确保所有_quote和_default字段都在layout_properties中
  if (nodeData.properties) {
    Object.keys(nodeData.properties).forEach(key => {
      if (key.endsWith('_quote') || key.endsWith('_default')) {
        // 将这些字段移动到layout_properties
        nodeData.layout_properties[key] = nodeData.properties[key];
        console.log(`修复: 将 ${key} 从properties移动到layout_properties`);
        delete nodeData.properties[key];
      }
    });
  }

  // 更新Map中的数据
  contentParamsDataMap.value.set(component.label || component.type, value);

  // 更新节点数据
  currentNode.setData(nodeData);
  currentNode.trigger('change:data', {
    data: nodeData
  });

  // 更新store
  designerStore.setCurrentNodeData(nodeData);
}

// 获取contentParams值
const getContentParamsValues = (component: any) => {
  console.log("获取contentParams值 - component:", component);

  const result: Record<string, any> = {};
  const componentKey = component.label || component.type; // 使用label或type作为唯一标识

  // 获取当前节点数据
  const currentNode = designerStore.currentNode;
  if (!currentNode) return result;

  const nodeData = currentNode.getData();
  console.log("当前节点数据:", nodeData);

  // 确保properties和layout_properties存在
  const properties = nodeData?.properties || {};
  const layoutProperties = nodeData?.layout_properties || {};

  // 使用默认参数获取属性
  if (component.props?.defaultParams) {
    component.props.defaultParams.forEach((param: any) => {
      if (param.field) {
        const field = param.field;
        const quoteField = `${field}_quote`;
        const defaultField = `${field}_default`;

        // 1. 获取各种值
        const originalValue = properties[field] || '';
        const quoteValue = layoutProperties[quoteField] || '';
        const defaultValue = layoutProperties[defaultField] || param.default || '';

        // 2. 设置结果
        // 原始值保持不变
        result[field] = originalValue;
        // quote值与原始值保持一致 - 除非原始值为空且有默认值
        if (quoteValue) {
          result[quoteField] = quoteValue;
        } else if (originalValue === defaultValue && defaultValue !== '') {
          result[quoteField] = '';
        } else {
          result[quoteField] = originalValue;
        }

        // 默认值不变
        result[defaultField] = defaultValue;

        console.log(`字段 ${field}:`);
        console.log(`  - 原始值: ${originalValue}`);
        console.log(`  - 引用值(quote): ${result[quoteField]}`);
        console.log(`  - 默认值: ${defaultValue}`);
      }
    });
  }

  console.log("contentParams当前值:", result);
  return result;
}
</script>

<style lang="scss" scoped>
.config-content {
  overflow: visible !important;
  height: calc(100% - 60px);
  position: relative;
}

.fullscreen-config {
  height: calc(100vh - 120px) !important;
  overflow-y: auto;
  padding: 0 20px;
}

.fullscreen-component {
  padding: 24px !important;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 确保参数组件在小屏时不出现横向滚动条 */
.config-content {
  overflow-x: auto;
  min-width: 0; /* 允许flex子元素收缩 */
}

/* 为所有参数组件添加最小宽度保护 */
:deep(.flex-param-header),
:deep(.flex-param-row) {
  min-width: 0; /* 允许flex子元素收缩 */
}

:deep(.flex-param-name),
:deep(.flex-param-type),
:deep(.flex-param-value),
:deep(.flex-param-default),
:deep(.flex-param-desc),
:deep(.flex-param-length),
:deep(.flex-param-actions) {
  min-width: 0; /* 允许flex子元素收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.description {
  overflow-y: auto;
}
</style>
