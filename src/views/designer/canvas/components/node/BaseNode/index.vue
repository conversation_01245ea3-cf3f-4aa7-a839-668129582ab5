<template>
  <div
    class="flex flex-col w-full h-full rounded-lg shadow-md relative overflow-hidden"
    @click="handleNodeClick"
  >
    <!-- 节点标题 -->
    <NodeTitle
      :graphNode="node"
      :nodeData="{
        icon: nodeConfig.icon,
        type: nodeConfig.type,
        options: {
          name: nodeConfig.title || nodeConfig.name || ''
        }
      }"
      bgGradient="linear-gradient(#f2f2ff 0%, rgb(252,252,255) 100%)"
      @handleExpand="handleExpand"
    />
    <!-- 自定义预览组件 -->
    <template v-if="previewComponents.length > 0">
      <component
        v-for="(previewComponent, index) in previewComponents"
        :key="index"
        :is="getPreviewComponent(previewComponent.type)"
        :nodeConfig="nodeConfig"
      />
    </template>
    <BaseContent v-else :nodeConfig="nodeConfig" />
    <!-- 端口组件 -->
    <NodePorts ref="nodePortsRef" :node="node" />
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed } from 'vue'
import type { Node } from '@antv/x6'
import NodeTitle from '../NodeTitle/index.vue'
import BaseContent from '../BaseContent/index.vue'
import NodePorts from '../NodePorts/index.vue'
import { useDesignerStore } from '@/store/designer'
import { debounce } from '@/utils/tools'
import { getPreviewComponent } from '../../previewConfig'
const designerStore = useDesignerStore()
const getNode = inject('getNode') as Function
const node = ref<Node>(getNode())
const nodePortsRef = ref()

// 从节点数据中获取配置
const nodeConfig = ref({
  title: '',
  type: '',
  icon: '',
  ...node.value.getData()
})

// 是否需要自定义预览
const previewComponents = computed(() => nodeConfig.value.previewComponents ?? [])

const updateNodeConfig = debounce(() => {
  nodeConfig.value = {...nodeConfig.value, ...node.value.getData()}
}, 100)

node.value.on('change:data', (t: Record<string, unknown>) => {
  if (!(t as Record<string, unknown>).data) return
  updateNodeConfig()
})

const panning = ref(false)
// 监听节点移动事件
node.value.on('change:position', ({ current, previous }) => {
  panning.value = true
})
const handleNodeClick = (e: MouseEvent) => {
  if (!panning.value && (!e.ctrlKey && !e.metaKey)) {
    designerStore.setRightPanelVisiable(true)
  }
  if (!panning.value && nodeConfig.value.type === 'scriptGeneration') {
    designerStore.setRightPanelVisiable(false)
    designerStore.setGenerateScriptPanelVisiable(true)
  }
  panning.value = false
}

const handleExpand = () => {
  const nodeData = node.value.getData()
  if (nodeData.type === 'workflowNode') {
    const currentStatus = nodeData.expandStatus
    const { previewComponents, ...restData } = nodeData
    restData.resizing = true
    const newData = {
      ...restData,
      previewComponents: currentStatus ? [{ 
        type: 'subprocess',
      }] : []
    }
    node.value.prop('data', null)
    node.value.prop('data', newData)
    nodeConfig.value = {
      ...nodeConfig.value,
      ...newData
    }
    
    const { width } = node.value.size()
    if (currentStatus) {
      node.value.resize(width, 400)
    } else {
      node.value.resize(width, 100)
    }
    designerStore.setCurrentNodeData(newData)
  }
}

// designerStore.modGraph?.on('node:click', (args) => {
//   handleNodeClick(args.e)
// })
</script>
