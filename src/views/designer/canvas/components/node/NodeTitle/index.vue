<template>
  <div
    class="flex h-[52px] items-center justify-between px-3 py-2 title-header pointer-events-auto dark:!bg-area-fill dark:!bg-none"
    :style="{ background: bgGradient }"
  >
    <!-- 左侧：图标和标题 -->
    <div class="flex items-center gap-2 flex-1 dark:!text-white">
      <div class="w-6 h-6 flex items-center justify-center">
        <svg class="icon">
          <use :xlink:href="`${iconsUrl}#${validateType(nodeData.type)}`"></use>
        </svg>
      </div>

      <!-- 标题（可编辑） -->
      <div @dblclick="editLabel" class="flex-1 flex items-center">
        <input
          v-if="titleContenteditable"
          :value="tempName"
          ref="labelInput"
          class="input-base no-outline"
          style="border:0 !important; outline:0 !important; box-shadow:none !important;"
          @blur="unableLabel"
          @input="handleInput"
          @compositionstart="onCompositionStart"
          @compositionend="onCompositionEnd"
        />
        <span v-else class="text-[16px]">{{ nodeData.options.name }}</span>
      </div>
    </div>

    <!-- 右侧：操作按钮 -->
    <div class="flex items-center gap-1">
      <!-- 调试按钮 -->
      <Button
        v-if="!hiddenDebug"
        type="text"
        size="small"
        class="action-btn flex items-center justify-center"
        @click.stop="handleDebug"
      >
        <template #icon>
          <BugOutlined class="dark:!text-[var(--text-secondary)]" />
        </template>
      </Button>

      <!-- 更多菜单 -->
      <Dropdown :trigger="['hover']" placement="bottomRight">
        <Button
          type="text"
          class="more-menu w-[38px] h-[28px] flex items-center justify-center"
          @click.stop="() => {}"
        >
          <div class="flex items-center gap-1">
            <div class="w-1 h-1 rounded-full bg-gray-400"></div>
            <div class="w-1 h-1 rounded-full bg-gray-400"></div>
            <div class="w-1 h-1 rounded-full bg-gray-400"></div>
          </div>
        </Button>
        <template #overlay>
          <Menu class="!z-50">
            <MenuItem key="rename" @click.stop="editLabel">
              <span>重命名</span>
            </MenuItem>
            <MenuItem key="duplicate" @click.stop="() => {}">
              <span>创建副本</span>
            </MenuItem>
            <MenuItem key="expand" @click.stop="handleExpand" v-if="isShowExpandMenuItem">
              <span>{{ expandLabel }}</span>
            </MenuItem>
            <MenuItem key="editSubProcess" @click.stop="editSubProcess" v-if="isShowEditMenuItem">
              <span>编辑子流程</span>
            </MenuItem>
            <MenuItem key="delete" @click.stop="handleRemove">
              <span>删除</span>
            </MenuItem>
          </Menu>
        </template>
      </Dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed, inject, onMounted, watch } from 'vue'
import { useDesignerStore } from '@/store/designer'
import { useTryRunStore } from '@/store/tryRun'
import {
  BugOutlined,
} from '@ant-design/icons-vue'
import {
  Dropdown,
  Menu,
  MenuItem,
  Button,
} from 'ant-design-vue'

import type { Node } from '@antv/x6'
import iconsUrl from '@/assets/icons.svg?url'
import { validateIconType } from '@/utils/utils'
import { getFileApi } from '@/apis/designer'

const props = defineProps<{
  nodeData: any,
  graphNode: any,
  bgGradient?: string
}>()

const validateType = (type) => {
  return validateIconType(type, iconsUrl)
}

const getNode = inject('getNode') as Function
const node = ref<Node>(getNode())
const hiddenDebug = computed(() => {
  const nodeData = node.value.getData()
  return !!nodeData?.metadata?.skipDebug
})

const bgGradient = computed(() =>
  props.bgGradient || 'linear-gradient(#f2f2ff 0%, var(--coz-bg-plus) 100%)'
)

const titleContenteditable = ref(false)
const labelInput = ref()
const designerStore = useDesignerStore()
const tryRunStore = useTryRunStore()
const isComposing = ref(false)
const tempName = ref('')

// 标题编辑
const editLabel = () => {
  tempName.value = props.nodeData.options.name
  titleContenteditable.value = true
  nextTick(() => {
    labelInput.value?.focus()
  })
}

const unableLabel = () => {
  if (isComposing.value) {
    return
  }

  // 获取当前节点的完整数据
  const currentData = props.graphNode.getData()

  // 构建新的节点数据
  const newData = {
    ...currentData,
    title: tempName.value,
    properties: {
      ...currentData.properties,
      name: tempName.value
    }
  }

  // 更新本地数据
  props.nodeData.options.name = tempName.value

  // 更新节点数据
  props.graphNode.setData(newData)

  // 更新 store 中的数据
  designerStore.setCurrentNodeData(newData)

  // 触发图节点的数据变更事件
  props.graphNode.trigger('change:data', {
    data: newData
  })

  titleContenteditable.value = false
}

const emit = defineEmits(['handleExpand'])

// 是否展示展开选项
const isShowExpandMenuItem = computed(() => {
  const nodeData = props.graphNode.getData()
  return nodeData.type === 'workflowNode'
})

const isExpanded = ref(false)
// 监听节点数据变化
watch(() => props.graphNode.getData(), (newData) => {
  isExpanded.value = newData.expandStatus === true
}, { immediate: true, deep: true })

const expandLabel = computed(() => {
  return isExpanded.value ? '收起子流程' : '展开子流程'
})

// 子流程展开状态设置
const setExpandStatus =  (status) => {
  const current = props.graphNode.getData()
  if (current.type == 'workflowNode') {
    const newData = {
      ...current,
      expandStatus: status,
    }
    props.graphNode.setData(newData)
    designerStore.setCurrentNodeData(newData)
    isExpanded.value = status
    props.graphNode.trigger('change:data', {
      data: newData
    })
  }
}

// 子流程展开
const handleExpand = () => {
  const currentStatus = props.graphNode.getData().expandStatus
  const newStatus = !currentStatus
  setExpandStatus(newStatus)
  emit('handleExpand')
}

// 是否展示编辑子流程菜单
const isShowEditMenuItem = computed(() => {
  const current = props.graphNode.getData()
  return current.type == 'workflowNode'
})
// 点击编辑子流程，跳转到子流程画布
const editSubProcess = async() => {
  console.log('editSubProcess')
  const current = props.graphNode.getData()
  designerStore.setRightPanelVisiable(false)
  try {
    const res = await getFileApi({id:current.properties?.processId})
    const tab = {
      id: res?.data?.id,
      directoryId: res?.data?.directoryId,
      name: res?.data?.name,
      content: JSON.parse(res?.data?.content)
    }
    designerStore.addTab(tab)

    if (designerStore.modGraph?.fromJSON) {
      designerStore.modGraph.fromJSON(tab.content)
      // 使用nextTick确保内容渲染后再调整视图
      nextTick(() => {
        // 确保节点在视图中居中显示
        designerStore.modGraph.centerContent()
        // 调整画布缩放以适应内容
        designerStore.modGraph.zoomToFit({ padding: 20 })
      })
    }

    designerStore.setMode(res['mode'] || 'all')
  } catch (error) {
    console.error('Error in handleNodeClick:', error)
  }
}

// 删除节点
const handleRemove = () => {
  designerStore.modGraph.removeCell(props.graphNode)
}

// 监听节点数据变化
const nodeListener = () => {
  props.graphNode.on('change:data', ({ current }) => {
    props.nodeData.value = current
  })
}

// 添加处理debug点击的方法
const handleDebug = () => {
  designerStore.setRightPanelVisiable(false)
  tryRunStore.isDirectDebug = false // 设置为直接调试模式
  tryRunStore.setVisible(true) // 打开试运行抽屉
}

// 处理输入法事件的方法
const handleInput = (e: any) => {
  if (!isComposing.value) {
    tempName.value = e.target.value
  }
}

const onCompositionStart = () => {
  isComposing.value = true
}

const onCompositionEnd = (e: any) => {
  isComposing.value = false
  tempName.value = e.target.value
}

onMounted(() => {
  nodeListener()
  // 初始化状态为false
  const nodeData = props.graphNode.getData()
  if (nodeData.type === 'workflowNode' && nodeData.expandStatus === undefined) {
    setExpandStatus(false)
  }
})
</script>

<style lang="scss" scoped>
.title-header {
  .action-btn {
    @apply opacity-80 hover:opacity-100;

    &:hover {
      background: rgba(0,0,0,0.04);
    }
  }
}

.more-menu {
  @apply transition-colors duration-200;

  &:hover {
    background: rgba(0,0,0,0.04);

    .rounded-full {
      @apply bg-gray-600;
    }
  }
}

.input-base {
  background: transparent !important;
  border: 0 !important;
  outline: 0 !important;
  box-shadow: none !important;
  padding: 2px 4px;
  font-size: 16px;
  color: #333;
  min-width: 120px;
  width: auto;
  caret-color: #4d53e8;
  -webkit-appearance: none;
  border-radius: 0;

  &:focus {
    outline: 0 !important;
    border: 0 !important;
    box-shadow: none !important;
    -webkit-appearance: none;
  }

  &:hover {
    outline: 0 !important;
    border: 0 !important;
  }
}

:deep(.anticon) {
  @apply text-gray-600;
}

:deep(.ant-dropdown-menu-item) {
  @apply py-2 px-4;
}

.action-btn {
  @apply opacity-80 hover:opacity-100 h-[28px] !p-0 w-[38px];

  &:hover {
    background: rgba(0,0,0,0.04);
  }
}
</style>
