<template>
  <div class="absolute w-full h-full pointer-events-none">
    <!-- 左侧端口组 -->
    <div class="absolute top-0 bottom-0 -left-2 flex flex-col justify-around py-2.5">
      <div v-for="port in leftPorts" :key="port.id" class="flex items-center">
        <div
          :id="port.id"
          class="w-2 h-2 rounded-full bg-white border-2 border-blue-400 cursor-pointer pointer-events-auto hover:bg-blue-400"
        />
      </div>
    </div>

    <!-- 右侧端口组 -->
    <div class="absolute top-0 bottom-0 -right-2 flex flex-col justify-around py-2.5">
      <div v-for="port in rightPorts" :key="port.id" class="flex items-center">
        <div
          :id="port.id"
          class="w-2 h-2 rounded-full bg-white border-2 border-blue-400 cursor-pointer pointer-events-auto hover:bg-blue-400"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { generateUUID } from '@/utils/tools'
import { useDesignerStore } from '@/store/designer'

interface Props {
  node: any // X6 Node 实例
}

const props = defineProps<Props>()

const designerStore = useDesignerStore()

// 使用 ref 存储端口数据
const ports = computed(() => {
  const allPorts = props.node.getPorts() || []
  return allPorts
})

const leftPorts = computed(() => {
  return ports.value.filter((port: any) => port.group === 'left') || []
})

const rightPorts = computed(() => {
  return ports.value.filter((port: any) => port.group === 'right') || []
})


// 监听端口数量变化
watch(
  () => designerStore.nodePorts.get(props.node.id),
  (newCount) => {
    if (newCount !== undefined) {
      // 更新右侧端口
      updateRightPorts(newCount)
    }
  }
)

// 更新右侧端口的方法
const updateRightPorts = (count: number) => {
  // 先移除所有右侧端口
  const currentPorts = props.node.getPorts()
  currentPorts.forEach((port: any) => {
    if (port.group === 'right') {
      props.node.removePort(port.id)
    }
  })

  // 添加新的右侧端口
  for (let i = 0; i < count; i++) {
    props.node.addPort({
      id: `${props.node.id}-right-${i}`,
      group: 'right',
      attrs: {
        circle: {
          magnet: true,
          stroke: '#69b1ff',
          strokeWidth: 2,
          fill: '#fff',
          r: 4,
        }
      }
    })
  }
}

// 添加单个端口
const addPort = (group: 'left' | 'right') => {
  const portId = `port-${group}-${generateUUID()}`
  props.node.addPort({
    id: portId,
    group,
    attrs: {
      circle: {
        magnet: true,
        stroke: '#69b1ff',
        strokeWidth: 2,
        fill: '#fff',
        r: 4,
      }
    }
  })
}

// 删除单个端口
const removePort = (portId: string) => {
  props.node.removePort(portId)
}

// 更新单个端口
const updatePort = (portId: string, group: 'left' | 'right') => {
  props.node.updatePort(portId, {
    group,
    attrs: {
      circle: {
        magnet: true,
        stroke: '#69b1ff',
        strokeWidth: 2,
        fill: '#fff',
        r: 4,
      }
    }
  })
}

defineExpose({
  addPort,
  removePort,
  updatePort
})
</script>
