<template>
  <div class="flex items-center gap-1.5">
    <div
      v-if="!isString"
      ref="containerRef"
      class="relative overflow-hidden flex items-center gap-1.5 pr-3 "
      :class="isOverflow ? 'fade-right' : ''"
    >
      <div
        v-for="(item, index) in (Array.isArray(content) ? content : [])"
        v-show="index <= firstOverflowIndex"
        :key="index + '_output'"
        class="max-w-[100%]"
      >
        <div
          ref="mainConRefs"
          class="main_con max-w-[100%] break-words flex items-center rounded-[4px] cursor-pointer gap-[2px] px-[4px] py-[1px] select-none whitespace-nowrap"
          :class="[
            item[params.value] ? 'bg-[#EFF0F8] dark:!bg-input-fill' : 'bg-warn-bg',
          ]"
          :style="{ color: item[params.value] ? 'rgba(32, 41, 69, 0.62)' : 'var(--warn-text)' }"
        >
          <Tooltip placement="topLeft">
            <template #title>
              {{ item[params.type] }}
            </template>
            <span
              class="text-text-label text-[10px] shrink-0 grow-0 max-w-[20px] leading-[18px] overflow-hidden whitespace-nowrap mr-px dark:!text-[var(--grey-auxiliary-color)]"
            >{{ typeLabelFormat(item[params.type], item[params.value]) }}</span>
            <span
              class="overflow-hidden overflow-x-hidden overflow-y-hidden truncate whitespace-nowrap select-none leading-[16px]"
              :style="{ color: item[params.value] ? 'rgba(15, 21, 40, 0.82)' : 'var(--warn-text)' }"
              :class="[
                item[params.value] ? 'dark:!text-white' : ''
              ]"
            >{{ item[params.value] || '未定义' }}</span>
            <span class="text-[10px] whitespace-nowrap select-none leading-[16px] dark:!text-[var(--grey-auxiliary-color)]">{{ descriptionFormat(item.description, item[params.type], item[params.value]) }}</span>
          </Tooltip>
        </div>
      </div>
    </div>
    <div
      v-if="isOverflow && typeof content == 'object'"
      class="more-icon bg-[#EFF0F8] rounded-[3px] w-[18px] h-[18px] text-[10px] leading-[12px] text-center cursor-pointer ml-auto relative dark:!bg-input-fill dark:!border dark:!border-border dark:!text-white"
      @mouseleave="handleMouseLeave"
    >
      <span 
        class="absolute top-0 left-0 w-full h-full" 
        @mouseenter="handleMouseEnter"
      >...</span>
      <div 
        class="more-con fixed bg-white shadow-[0_0_10px_0_rgba(0,0,0,0.2)] flex-wrap gap-1"
        :style="moreConStyle"
      >
        <div
          v-for="(item, index) in (Array.isArray(content) ? content : [])"
          :key="index + '_output_2'"
          class="max-w-[100%]"
        >
          <div
            ref="mainConRefs"
            class="main_con max-w-[100%] break-words flex items-center rounded-[4px] cursor-pointer gap-[2px] px-[4px] py-[1px] select-none whitespace-nowrap"
            :class="[
              item[params.value] ? 'bg-[#EFF0F8]' : 'bg-warn-bg',
            ]"
            :style="{ color: item[params.value] ? 'rgba(32, 41, 69, 0.62)' : 'var(--warn-text)' }"
          >
            <Tooltip placement="topLeft">
              <template #title>
                {{ item[params.type] }}
              </template>
              <span
                class="text-text-label text-[10px] shrink-0 grow-0 max-w-[20px] leading-[18px] overflow-hidden whitespace-nowrap mr-px"
              >{{ typeLabelFormat(item[params.type], item[params.value]) }}</span>
              <span
                class="overflow-hidden overflow-x-hidden overflow-y-hidden truncate whitespace-nowrap select-none leading-[16px]"
                :style="{ color: item[params.value] ? 'rgba(15, 21, 40, 0.82)' : 'var(--warn-text)' }"
              >{{ item[params.value] || '未定义' }}</span>
              <span class="text-[10px] whitespace-nowrap select-none leading-[16px] dark:!text-[var(--grey-auxiliary-color)]">{{ descriptionFormat(item.description, item[params.type], item[params.value]) }}</span>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
    <div 
      v-if="isString" 
      class="relative overflow-hidden flex items-center pr-3"
    >
      <span
        class="overflow-hidden overflow-x-hidden overflow-y-hidden truncate whitespace-nowrap select-none leading-[16px] px-[4px] py-[1px] text-[12px]"
        :style="{ color: !!content ? 'rgba(15, 21, 40, 0.82)' : 'var(--warn-text)' }"
        :class="[
          !!content ? 'bg-[#EFF0F8] dark:!bg-input-fill dark:!text-white' : 'bg-warn-bg',
        ]"
      >{{ content || '未定义' }}</span>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ContentMain'
}
</script>

<script setup lang="ts">
import { Tooltip } from 'ant-design-vue'
interface ContentItem {
  type?: string
  name?: string
  [key: string]: unknown
}

type ContentType = string | string[] | ContentItem[] | Record<string, unknown>

interface Props {
  content: ContentType
  params?: {
    type: string
    value: string
    [key: string]: unknown
  }
}

const props = withDefaults(defineProps<Props>(), {
  params: () => ({ type: 'type', value: 'value' })
})

// 参数描述文本的最大长度
const DESCRIPTION_MAX_VALUE = 5

const containerRef = ref()
const mainConRefs = ref([])
const firstOverflowIndex = ref(Infinity)
const isOverflow = ref(false)
const moreConStyle = ref({
  display: 'none',
  top: '0px',
  right: '0px',
  width: '280px',
  padding: '8px 4px',
  borderRadius: '8px',
  border: '1px solid #F4F6F8',
  zIndex: 2000
})

const isString = computed(() => !Array.isArray(props.content))

// 当前rem值
const remInPixels = () => {
  return parseFloat(getComputedStyle(document.documentElement).fontSize)
} 

const checkOverflow = () => {
  if (!containerRef.value || !mainConRefs.value.length) return
  const containerWidth = containerRef.value.clientWidth
  let currentWidth = 0
  // 重新初始化firstOverflowIndex
  firstOverflowIndex.value = Infinity

  for (let i = 0; i < mainConRefs.value.length; i++) {
    const element = mainConRefs.value[i]
    const elementWidth = (element as HTMLElement).offsetWidth
    const gap = elementWidth > 0 ? remInPixels()*0.375 : 0
    if (currentWidth + elementWidth > containerWidth) {
      firstOverflowIndex.value = i
      isOverflow.value = true
      return
    }
    currentWidth += elementWidth + gap
  }
  isOverflow.value = false
}

watch(() => props.content, () => {
  nextTick(checkOverflow)
}, { deep: true })

const typeLabelFormat = (type: string, value: string) => {
  if (!type || !value) return ''
  if (type.startsWith('Array<')) {
    return `[${type.substring(6, type.length - 1).substring(0, 3)}]`
  } else {
    return type.substring(0, 3)
  }
}

const descriptionFormat = (description: string, type: string, value: string)=>{
  if (!type || !value || !description) return ''
  const curOffDescription = description.length > DESCRIPTION_MAX_VALUE ? description.substring(0,5)+'...':description
  return '(' + curOffDescription +')'
}

const handleMouseEnter = (event) => {
  // 获取点击事件的clientX和clientY
  const clickX = event.clientX
  const clickY = event.clientY

  // 找到class为node-wrapper的祖先元素
  let nodeWrapper = event.target
  while (nodeWrapper && !nodeWrapper.classList.contains('node-wrapper')) {
    nodeWrapper = nodeWrapper.parentElement
  }

  if (!nodeWrapper) return
  // 获取node-wrapper相对于视口的位置
  const rect = nodeWrapper.getBoundingClientRect()

  // 计算相对于node-wrapper的点击位置
  const mouseY = clickY - rect.top

  // 更新more-con的位置
  moreConStyle.value = {
    ...moreConStyle.value,
    display: 'flex',
    top: `${mouseY}px`,
    right: '10px'
  }
}

const handleMouseLeave = () => {
  moreConStyle.value = {
    ...moreConStyle.value,
    display: 'none'
  }
}

onMounted(() => {
  checkOverflow()
})
</script>

<style scoped lang="scss">
.fade-right {
  -webkit-mask-image: linear-gradient(to right, black 80%, transparent 100%);
  mask-image: linear-gradient(to right, black 80%, transparent 100%);
}
.more-icon {
  &:hover {
    .more-con {
      // 移除这里的display: flex注释，因为我们现在用JavaScript控制显示
    }
  }
  .more-con {
    // 移除这里的固定样式，因为我们现在用JavaScript控制
  }
}
</style>
