<template>
  <div class="text-xs grid gap-[8px] grid-cols-[auto_minmax(0,1fr)] p-0 px-3 pb-3 w-full">
    <template 
      v-for="component in renderComponent" 
      :key="(component as Record<string, unknown>).field"
    >
      <div
        class="self-center text-right text-[12px] font-normal leading-[16px] mr-0.5 whitespace-nowrap text-text-label dark:!text-[var(--grey-auxiliary-color)]"
      >
        {{ (component as Record<string, unknown>) .label }}
      </div>
      <div class="text-[12px] relative">
        <!-- 右侧内容主窗口 -->
        <ContentMain 
          :content="getContent(component)" 
          :params="getParams(component)" 
        />
      </div>
    </template>
  </div>
</template>

<script lang="ts">
export default {
  name: 'BaseContent'
}
</script>

<script setup lang="ts">
import ContentMain from './components/ContentMain.vue'

const props =defineProps({
  nodeConfig: {
    type: Object,
    required: true,
    default: () => ({
      components: [],
      inputs: [],
      outputs: []
    })
  }
})

const renderComponent = computed(() => {
  const list: unknown[] = []

  // 检查components是否存在
  if (!props.nodeConfig.components || !Array.isArray(props.nodeConfig.components)) {
    console.warn('节点配置中缺少components数组:', props.nodeConfig)
    return list
  }
  
  props.nodeConfig.components.forEach(component => {
    // 定时器组件不显示
    if(component.type === 'timer') return
    if (props.nodeConfig.type.includes('start') && component.field === 'outputs') {
      for(let key in props.nodeConfig.outputs) {
        if (props.nodeConfig.outputs[key].length === 0) continue
        list.push({
          type: key,
          field: key,
          label: key
        })
      }
    } else if (!component.noPreview) {
      list.push(component)
    }
  })
  return list
})

const getContent = (component) => {
  if (component.field === 'inputs') {
    if (Object.keys(props.nodeConfig.inputs).length === 0) return []
    return props.nodeConfig.inputs
  }

  if(props.nodeConfig.type.includes('start')) {
    if (props.nodeConfig.outputs[component.field]) return props.nodeConfig.outputs[component.field]
  }

  if (component.field === 'outputs') {
    if (Object.keys(props.nodeConfig.outputs).length === 0) return []
    return props.nodeConfig.outputs
  }

  if (component.type === 'dynamicInputList') {
    const dynamicInputListData = props.nodeConfig.properties?.[component.field]
    return dynamicInputListData || []
  }

  if(component.type === 'contentParams'){
    return (component.props.defaultParams || []).map((params)=>{
      return {
        ...params,
        description: props.nodeConfig.properties[params.field] || params.description
      }
    })
  }

  // if (props.nodeConfig.outputs[component.field]) return props.nodeConfig.outputs[component.field]

  // 文本类
  return props.nodeConfig.properties[component.field] || ''
}

const getParams = (component) => {
  if (props.nodeConfig.outputs[component.field]) return { type: 'type', value: 'name' }
  if(!props.nodeConfig.type.includes('start') && component.field === 'inputs') return { type: 'type', value: 'name' }
  if (component.field === 'inputs') return { type: 'value', value: 'name' }
  if (component.type === 'dynamicInputList') return { value: 'value' }
  if(!props.nodeConfig.type.includes('start') && component.field === 'outputs')  return { type: 'type', value: 'name' }
  if(component.type === 'contentParams') return {type: 'type', value: 'field'}

  // 为其他情况提供默认值
  return { type: 'type', value: 'name' }
}
</script>

<style scoped lang="scss"></style>
