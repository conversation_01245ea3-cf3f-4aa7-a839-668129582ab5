<template>
  <div class="node-wrapper">
    <!-- 基础节点 -->
    <BaseNode :node="node" />

    <!-- 结果面板  -->
    <div
      v-if="hasDebugResult && !hiddenDebug"
      class="absolute left-0 right-0 mt-4 z-20 pointer-events-auto dark:!border dark:!border-border"
    >
      <!-- 状态栏 -->
      <div
        class="flex items-center justify-between px-3 py-1.5 bg-white dark:!bg-global-background transition-[border-radius] duration-300 ease-in-out shadow-sm"
        :class="{ 'rounded-t-md': isExpanded, 'rounded-md': !isExpanded }"
      >
        <div class="flex items-center gap-1.5">
          <component :is="statusIcon" class="text-sm" :class="statusIconColor" />
          <span class="text-sm text-gray-700 dark:!text-text-secondary">{{ statusText }}</span>
          <span class="text-xs px-2 py-0.5 rounded" :class="timeTagColor"
            >{{ executionTime }}ms</span
          >
        </div>
        <div
          class="flex items-center gap-1 px-2 py-1 rounded hover:bg-gray-100 dark:hover:!bg-primary-hover cursor-pointer"
          style="min-height: 28px; min-width: 80px;"
          @click.stop="toggleExpand"
        >
          <span class="text-sm text-gray-500 dark:!text-text-secondary">{{ isExpanded ? '收起' : '展开' }}结果</span>
          <DownOutlined
            class="transition-transform duration-300 text-gray-400 text-sm"
            :class="{ 'rotate-180': isExpanded }"
          />
        </div>
      </div>

      <!-- 展开的内容 -->
      <Transition
        enter-active-class="transition-all duration-300 ease-out"
        leave-active-class="transition-all duration-300 ease-in"
        enter-from-class="transform scale-y-0 opacity-0"
        enter-to-class="transform scale-y-100 opacity-100"
        leave-from-class="transform scale-y-100 opacity-100"
        leave-to-class="transform scale-y-0 opacity-0"
      >
        <div
          v-show="isExpanded"
          class="bg-white dark:!bg-global-background rounded-b-md origin-top shadow-md pointer-events-auto"
        >
          <div
            class="p-4 overflow-y-auto custom-scrollbar"
            :style="{ maxHeight: `${RESULT_PANEL.MAX_HEIGHT}px` }"
          >
            <!-- 输入区域 -->
            <div v-if="nodeDebugData?.inputs" class="mb-4">
              <div class="flex items-center gap-2 mb-2">
                <span class="text-sm text-gray-700 dark:!text-text-secondary">输入</span>
                <CopyOutlined
                  class="text-gray-400 text-sm cursor-pointer"
                  @click.stop="copyContent(nodeDebugData.inputs)"
                />
                <FullscreenOutlined
                  class="text-gray-400 text-sm cursor-pointer"
                  @click.stop="toggleFullscreenInput"
                />
              </div>
              <div class="bg-gray-50 dark:!bg-primary-hover px-3 py-2 rounded-md">
                <pre
                  class="text-sm whitespace-pre-wrap dark:!text-text-primary break-all break-words overflow-wrap-anywhere"
                  >{{ JSON.stringify(nodeDebugData.inputs, null, 2) }}</pre
                >
              </div>
            </div>

            <!-- 输出区域 -->
            <div v-if="nodeDebugData?.outputs" class="mb-4">
              <div class="flex items-center gap-2 mb-2">
                <span class="text-sm text-gray-700 dark:!text-text-secondary">输出</span>
                <CopyOutlined
                  class="text-gray-400 text-sm cursor-pointer"
                  @click.stop="copyContent(status === 'error' ? nodeDebugData?.errorMessage : nodeDebugData?.outputs)"
                />
                <FullscreenOutlined
                  class="text-gray-400 text-sm cursor-pointer"
                  @click.stop="toggleFullscreenOutput"
                />
              </div>
              <div class="bg-gray-50 dark:!bg-primary-hover px-3 py-2 rounded-md">
                <pre
                  v-if="status === 'error'"
                  class="text-sm text-red-500"
                  >{{nodeDebugData?.errorMessage}}</pre
                >
                <pre
                  v-else
                  class="text-sm whitespace-pre-wrap dark:!text-text-primary break-all break-words overflow-wrap-anywhere"
                  >{{ JSON.stringify(nodeDebugData.outputs, null, 2) }}</pre
                >
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 全屏输入区域 -->
    <Teleport to="body">
      <div v-if="isFullscreenInput && nodeDebugData?.inputs" class="fixed inset-0 z-[99] bg-white dark:!bg-global-background">
        <div class="h-full flex flex-col">
          <div class="flex items-center justify-between p-4 border-b">
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-700 dark:!text-text-secondary">输入</span>
              <CopyOutlined
                class="text-gray-400 text-sm cursor-pointer"
                @click.stop="copyContent(nodeDebugData.inputs)"
              />
            </div>
            <div class="flex items-center gap-2">
              <FullscreenExitOutlined
                class="text-gray-400 text-sm cursor-pointer"
                @click.stop="toggleFullscreenInput"
              />
            </div>
          </div>
          <div class="flex-1 p-4 overflow-auto">
            <div class="bg-gray-50 dark:!bg-global-background p-4 rounded-md h-full">
              <pre
                class="text-sm whitespace-pre-wrap dark:!text-text-primary break-all break-words overflow-wrap-anywhere"
                >{{ JSON.stringify(nodeDebugData.inputs, null, 2) }}</pre
              >
            </div>
          </div>
        </div>
      </div>
    </Teleport>

    <!-- 全屏输出区域 -->
    <Teleport to="body">
      <div
        v-if="isFullscreenOutput && nodeDebugData?.outputs"
        class="fixed inset-0 z-[99] bg-white dark:!bg-global-background"
      >
        <div class="h-full flex flex-col">
          <div class="flex items-center justify-between p-4 border-b">
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-700 dark:!text-text-secondary">输出</span>
              <CopyOutlined
                class="text-gray-400 text-sm cursor-pointer"
                @click.stop="copyContent(status === 'error' ? nodeDebugData?.errorMessage : nodeDebugData?.outputs)"
              />
            </div>
            <div class="flex items-center gap-2">
              <FullscreenExitOutlined
                class="text-gray-400 text-sm cursor-pointer"
                @click.stop="toggleFullscreenOutput"
              />
            </div>
          </div>
          <div class="flex-1 p-4 overflow-auto">
            <div class="bg-gray-50 dark:!bg-global-background p-4 rounded-md h-full">
              <pre
                v-if="status === 'error'"
                class="text-sm text-red-500"
                >{{nodeDebugData?.errorMessage}}</pre
              >
              <pre
                v-else
                class="text-sm whitespace-pre-wrap dark:!text-text-primary break-all break-words overflow-wrap-anywhere"
                >{{ JSON.stringify(nodeDebugData.outputs, null, 2) }}</pre
              >
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, computed } from 'vue'
import type { Node } from '@antv/x6'
import { message } from 'ant-design-vue'
import { useTryRunStore } from '@/store/tryRun'
import BaseNode from '../BaseNode/index.vue'
import { RESULT_PANEL } from '@/views/designer/canvas/constants/index'
import { useDesignerStore } from '@/store/designer'
import {
  DownOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  CopyOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons-vue'


const getNode = inject('getNode') as Function
const node = ref<Node>(getNode())
const tryRunStore = useTryRunStore()
const designerStore = useDesignerStore()
const isFullscreenInput = ref(false)
const isFullscreenOutput = ref(false)

// 跳过调试
const hiddenDebug = computed(() => {
  const nodeData = node.value.getData()
  return !!nodeData?.metadata?.skipDebug
})

// 获取节点调试数据
const nodeDebugData = computed(() => {
  return tryRunStore.getNodeDebugResult(node.value.id)
})

// 判断是否有调试结果
const hasDebugResult = computed(() => {
  return !!nodeDebugData.value
})

// 计算节点状态
const status = computed(() => {
  const nodeStatus = nodeDebugData.value?.status
  return nodeStatus === 'completed' ? 'success' : nodeStatus === 'failed' ? 'error' : 'running'
})

// 计算执行时间
const executionTime = computed(() => {
  if (!nodeDebugData.value?.startTime || !nodeDebugData.value?.endTime) return '0'
  return (nodeDebugData.value.endTime - nodeDebugData.value.startTime).toFixed(0)
})

// 展开/收起状态
const isExpanded = ref(false)
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
  designerStore.setRightPanelVisiable(false)
}

const copyContent = async (content) => {
  const text = typeof content === 'object' && content != null 
    ? JSON.stringify(content, null, 2) 
    : String(content ?? '');

  // 现代API检测
  const isClipboardAPIAvailable = () => {
    try {
      return !!navigator.clipboard?.writeText;
    } catch (e) {
      return false;
    }
  };

  // execCommand方案
  const legacyCopy = () => {
    return new Promise<boolean>((resolve) => {
      const container = document.body.appendChild(document.createElement('div'));
      container.style.cssText = 'position:fixed; left:-9999px; top:10px;';

      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.cssText = 'opacity:0; width:1px; height:1px;';
      
      container.appendChild(textarea);
      textarea.focus({ preventScroll: true });  // 关键修复：确保获得焦点
      textarea.select();

      try {
        resolve(document.execCommand('copy'));
      } catch (e) {
        resolve(false);
      } finally {
        document.body.removeChild(container);
      }
    });
  };

  try {
    // 智能选择复制策略
    let success = false;
    
    if (isClipboardAPIAvailable()) {
      try {
        await navigator.clipboard.writeText(text);
        success = true;
      } catch (e) {
        // 捕获权限拒绝等异常
      }
    }

    if (!success) {
      success = await legacyCopy();
    }

    if (success) {
      message.success('已复制到剪贴板');
    } else {
      // 终极降级方案：显示可选中文本框
      const fallbackUI = document.createElement('div');
      fallbackUI.style.cssText = 'position:fixed; left:10px; top:10px; z-index:9999;';
      fallbackUI.innerHTML = `<textarea style="width:200px;height:100px;">${text}</textarea>`;
      document.body.appendChild(fallbackUI);
      fallbackUI.querySelector('textarea')?.select();
      
      message.warning('请手动复制内容');
    }
  } catch (err) {
    console.error('复制失败:', err);
    message.error('复制失败');
  } finally {
    designerStore.setRightPanelVisiable(false);
  }
};

// 切换全屏
const toggleFullscreenInput = () => {
  isFullscreenInput.value = !isFullscreenInput.value;
  designerStore.setRightPanelVisiable(false);
}

const toggleFullscreenOutput = () => {
  isFullscreenOutput.value = !isFullscreenOutput.value;
  designerStore.setRightPanelVisiable(false);
}

// 运行状态和时间
const statusIcon = computed(() => ({
  success: CheckCircleOutlined,
  error: CloseCircleOutlined,
  running: LoadingOutlined
}[status.value]))

const statusIconColor = computed(() => ({
  'text-green-500': status.value === 'success',
  'text-red-500': status.value === 'error',
  'text-blue-500': status.value === 'running'
}))

// 状态文本
const statusText = computed(() => ({
  success: '运行成功',
  error: '运行失败',
  running: '运行中'
}[status.value]))

// 运行时间 tag 颜色
const timeTagColor = computed(() => ({
  'bg-green-50 text-green-600': status.value === 'success',
  'bg-red-50 text-red-600': status.value === 'error',
  'bg-blue-50 text-blue-600': status.value === 'running'
}))
</script>

<style scoped>
.node-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: var(--global-background-color);
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #E5E7EB transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #E5E7EB;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #D1D5DB;
}
</style>
