<template>
  <ResponsiveLayout class-prefix="table-editor">
    <div class="table-editor-container">
      <div class="bg-area-fill border rounded-md overflow-x-auto table-wrapper">
        <table class="w-full border-collapse">
          <thead>
            <tr class="border-b bg-gray-50 dark:!bg-area-fill">
              <th class="w-10 p-1 text-center dark:text-[var(--text-secondary)]">#</th>
              <th
                v-for="(column, index) in columns"
                :key="`col-${index}`"
                class="p-1 table-column border-r"
              >
                <div class="relative group">
                  <a-input
                    v-model:value="column.title"
                    placeholder="列1"
                    size="small"
                    @change="updateModelValue"
                    class="w-full !py-1"
                    :bordered="false"
                    :disabled="isUsingFixedColumns"
                  />
                  <a-button
                    v-if="!isUsingFixedColumns"
                    type="text"
                    class="absolute top-0 right-0 h-full w-6 flex items-center justify-center text-gray-400 hover:text-red-500 transition-colors"
                    @click="removeColumn(index)"
                  >
                    <DeleteOutlined />
                  </a-button>
                </div>
              </th>
              <!-- 添加列按钮 -->
              <th v-if="!isUsingFixedColumns" class="w-12 p-1 border-l">
                <div
                  class="flex items-center justify-center text-blue-500 hover:text-blue-600 cursor-pointer h-full"
                  @click="addColumn"
                >
                  <PlusOutlined />
                </div>
              </th>
            </tr>
          </thead>
        <tbody>
          <tr
            v-for="(row, rowIndex) in tableData"
            :key="`row-${rowIndex}`"
            class="border-b hover:bg-gray-50 dark:hover:!bg-input-fill"
          >
            <td class="p-1 text-center text-gray-500 dark:!text-[var(--text-secondary)] text-xs">{{ rowIndex + 1 }}</td>
            <td
              v-for="(column, colIndex) in columns"
              :key="`cell-${rowIndex}-${colIndex}`"
              class="p-0 border-r relative"
            >
              <div class="cell-content">
                <template v-if="column.type === 'number'">
                  <a-input-number
                    v-model:value="row[column.key]"
                    :min="0"
                    :bordered="false"
                    class="w-full"
                    @change="updateModelValue"
                    placeholder="点击编辑(数字)"
                />
                </template>
                <template v-else>
                <a-textarea
                  v-model:value="row[column.key]"
                  :bordered="false"
                  :auto-size="{ minRows: 1, maxRows: 1 }"
                  class="cell-textarea !py-1"
                  @change="updateModelValue"
                  placeholder="点击编辑内容"
                />
              </template>
                <a-button
                  v-if="row[column.key]"
                  type="text"
                  class="edit-button absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0 flex items-center justify-center bg-blue-50 text-blue-500 border border-blue-200 rounded-md shadow-sm"
                  @click="openEditModal(row, column)"
                >
                  <EditOutlined style="font-size: 14px" />
                </a-button>
              </div>
            </td>
            <td class="p-1 text-center w-10">
              <a-button
                type="text"
                class="text-gray-400 hover:text-red-500 h-5 w-5 p-0 flex items-center justify-center"
                @click="removeRow(rowIndex)"
              >
                <DeleteOutlined style="font-size: 12px" />
              </a-button>
            </td>
          </tr>

          <!-- 没有数据时的提示 -->
          <tr v-if="columns.length > 0 && tableData.length === 0" class="border-b">
            <td :colspan="columns.length + 2" class="p-3 text-center text-gray-400 text-sm">
              暂无数据
            </td>
          </tr>

          <!-- 添加行按钮 -->
          <tr>
            <td
              :colspan="columns.length + 2"
              class="p-0"
              :class="{'opacity-50 cursor-not-allowed': columns.length === 0}"
            >
              <div
                class="flex items-center justify-center py-1.5 text-blue-500 hover:text-blue-600 hover:bg-gray-50 dark:hover:bg-input-fill cursor-pointer"
                @click="addRow"
              >
                <PlusOutlined class="mr-1" />
                <span class="text-sm">添加行</span>
              </div>
            </td>
          </tr>

          <!-- 没有列时的提示 -->
          <tr v-if="columns.length === 0 && !isUsingFixedColumns">
            <td class="p-4 text-center text-gray-400 text-sm"> 请先添加表格列 </td>
          </tr>
        </tbody>
        </table>
      </div>

      <!-- 数据计数 -->
      <div class="mt-1.5 text-right">
        <span class="text-xs text-gray-400">{{ columns.length }} 列 | {{ tableData.length }} 行</span>
      </div>

      <!-- 编辑模态框 -->
      <EditModal
        v-if="editModalVisible"
        :title="currentColumn?.title || '编辑内容'"
        :value="currentInputValue"
        @update:value="currentInputValue = $event"
        @close="editModalVisible = false"
        @ok="handleModalOk"
      />
    </div>
  </ResponsiveLayout>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import {
  PlusOutlined,
  DeleteOutlined,
  ExpandAltOutlined,
  EditOutlined,
  ExpandOutlined,
  CompressOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';
import EditModal from './EditModal.vue';
import ResponsiveLayout from '../ResponsiveLayout.vue';

interface Column {
  title: string;
  key: string;
  type: string;
  defaultValue?: any;
}

interface TableEditorData {
  columns: Column[];
  tableData: Record<string, any>[];
}

const props = defineProps({
  modelValue: {
    type: [Object, Array, String] as any,
    default: () => ({})
  },
  columns: {
    type: Array as () => Column[],
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue']);

// 表头列
const columns = ref<Column[]>([]);

// 表格数据
const tableData = ref<Record<string, any>[]>([]);

// 编辑模态框相关
const editModalVisible = ref(false);
const currentRow = ref<Record<string, any> | null>(null);
const currentColumn = ref<Column | null>(null);
const currentInputValue = ref<string>('');
const isMaximized = ref(false);

// 打开编辑模态框
const openEditModal = (row: Record<string, any>, column: Column) => {
  currentRow.value = row;
  currentColumn.value = column;
  currentInputValue.value = row[column.key] || '';
  editModalVisible.value = true;
};

// 处理模态框确认
const handleModalOk = () => {
  if (currentRow.value && currentColumn.value) {
    currentRow.value[currentColumn.value.key] = currentInputValue.value;
    updateModelValue();
  }
  editModalVisible.value = false;
};

// 判断是否使用固定列
const isUsingFixedColumns = computed(() => {
  return props.columns && props.columns.length > 0;
});

// 初始化组件数据
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 处理不同类型的模型值
    if (typeof newValue === 'string') {
      // 如果是字符串，初始化为空数据
      columns.value = isUsingFixedColumns.value ? [...props.columns] : [];
      tableData.value = [];
    }
    // 如果是数组，假设是表格数据数组
    else if (Array.isArray(newValue)) {
      if (isUsingFixedColumns.value) {
        // 使用提供的列，将数组转换为表格数据
        columns.value = [...props.columns];
        // 尝试将数组映射成表格数据
        tableData.value = newValue.map(item => {
          const row: Record<string, any> = {};
          columns.value.forEach(col => {
            row[col.key] = item[col.key] || '';
          });
          return row;
        });
      } else {
        // 如果没有提供列，则尝试从数组第一项推断列
        if (newValue.length > 0 && typeof newValue[0] === 'object') {
          // 从第一个对象创建列
          const firstItem = newValue[0];
          const inferredColumns: Column[] = [];
          Object.keys(firstItem).forEach((key, index) => {
            inferredColumns.push({
              title: key,
              key: key,
              type: 'string'
            });
          });
          columns.value = inferredColumns;
          // 直接使用数组作为表格数据
          tableData.value = [...newValue] as Record<string, any>[];
        } else {
          // 空数组或无法推断列时，初始化为空表格
          columns.value = [];
          tableData.value = [];
        }
      }
    }
    // 如果是TableEditorData对象
    else if (newValue.columns !== undefined && newValue.tableData !== undefined) {
      columns.value = newValue.columns || [];
      tableData.value = newValue.tableData || [];
    }
    // 其他对象形式，初始化为空数据
    else {
      if (isUsingFixedColumns.value) {
        // 如果提供了固定列，则使用固定列
        columns.value = [...props.columns];
      } else {
        columns.value = [];
      }
      tableData.value = [];
    }
  } else {
    // 处理null或undefined的情况
    columns.value = isUsingFixedColumns.value ? [...props.columns] : [];
    tableData.value = [];
  }
}, { immediate: true, deep: true });

// 监听固定列变化
watch(() => props.columns, (newFixedColumns) => {
  if (newFixedColumns && newFixedColumns.length > 0) {
    // 如果提供了新的固定列，则替换当前列
    columns.value = [...newFixedColumns];

    // 调整表格数据以匹配新的列结构
    updateRowsForColumns();

    // 更新表单值
    updateModelValue();
  }
}, { deep: true });

// 更新行数据以匹配列结构
const updateRowsForColumns = () => {
  if (columns.value.length === 0) return;

  // 更新每一行，确保有所有当前列的键
  tableData.value.forEach(row => {
    const newRow: Record<string, any> = {};
    columns.value.forEach(column => {
      // 保留原有数据或使用空字符串
      newRow[column.key] = row[column.key] !== undefined ? row[column.key] : '';
    });
    // 用新的行数据覆盖旧的
    Object.keys(row).forEach(key => {
      if (!(key in newRow)) {
        delete row[key];
      }
    });
    Object.keys(newRow).forEach(key => {
      row[key] = newRow[key];
    });
  });
};

// 添加列 (只有在非固定列模式下可用)
const addColumn = () => {
  if (isUsingFixedColumns.value) return;

  const newColumnIndex = columns.value.length + 1;
  columns.value.push({
    title: `列${newColumnIndex}`,
    key: `column${newColumnIndex}`,
    type: 'string'
  });

  // 如果已有数据行，给每一行添加新列的值
  tableData.value.forEach(row => {
    row[`column${newColumnIndex}`] = '';
  });

  updateModelValue();
};

// 移除列 (只有在非固定列模式下可用)
const removeColumn = (index: number) => {
  if (isUsingFixedColumns.value) return;
  if (index < 0 || index >= columns.value.length) return;

  const keyToRemove = columns.value[index].key;
  columns.value.splice(index, 1);

  // 从所有行数据中移除该列
  tableData.value.forEach(row => {
    delete row[keyToRemove];
  });

  updateModelValue();
};

// 添加行
const addRow = () => {
  if (columns.value.length === 0) return;

  const newRow: Record<string, any> = {};

  // 为每一列创建对应的字段
  columns.value.forEach(column => {
    newRow[column.key] = column?.defaultValue || '';
  });

  tableData.value.push(newRow);
  updateModelValue();
};

// 移除行
const removeRow = (index: number) => {
  tableData.value.splice(index, 1);
  updateModelValue();
};

// 更新数据并触发事件
const updateModelValue = () => {
  const result = {
    columns: columns.value,
    tableData: tableData.value
  };

  // 将数据发送给父组件
  emit('update:modelValue', result);
};

// 切换模态框最大化状态
const toggleMaximize = () => {
  isMaximized.value = !isMaximized.value;
};
</script>

<style lang="scss" scoped>
@import '../responsive-layout.scss';

.table-editor-container {
  width: 100%;
}

.table-wrapper {
  overflow-x: auto;
  
  table {
    border-spacing: 0;
    border-collapse: collapse;
    width: 100%;
  }
}

.table-column {
  min-width: 140px;
  height: 40px;
  display: table-cell !important;
}

:deep(table) {
  th {
    height: 40px;
    background-color: var(--color-fill-2);
    font-weight: 500;
    text-align: left;
    display: table-cell !important;
    padding: 8px;
    vertical-align: middle;
    
    .ant-input {
      height: 32px;
      line-height: 32px;
    }
  }

  thead {
    display: table-header-group !important;
  }

  tr {
    display: table-row !important;
  }
}

/* 全屏模式下的表格样式 */
:global(.table-editor-fullscreen) {
  .table-wrapper {
    max-height: calc(100vh - 200px);
    overflow: auto;
  }

  .table-column {
    min-width: 200px;
  }

  .table-editor-container {
    padding: 20px;
  }
}

/* 覆盖ant-design输入框默认样式 */
:deep(.ant-input) {
  transition: all 0.2s;
}

:deep(.ant-input:hover) {
  background-color: #f5f5f5;
}

:deep(.ant-input:focus) {
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

:deep(.cell-textarea) {
  resize: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  min-height: 38px;
  padding: 8px 70px 8px 8px !important;
}

:deep(.ant-input) {
  padding-right: 70px !important;
}

:deep(.ant-input-textarea textarea) {
  padding-right: 70px !important;
}

:deep(.ant-input-textarea-show-count::after) {
  display: none;
}

.cell-content {
  position: relative;
}

.edit-button {
  z-index: 5;
  right: 4px !important;
}

/* 去掉hover效果 */
:deep(.edit-button:hover),
:deep(.edit-button:focus),
:deep(.edit-button:active) {
  background-color: rgba(239, 246, 255, var(--tw-bg-opacity)) !important;
  color: rgba(59, 130, 246, var(--tw-text-opacity)) !important;
  border-color: rgba(191, 219, 254, var(--tw-border-opacity)) !important;
}

/* 模态框样式 */
.edit-modal-content {
  border-radius: 4px;
}

:deep(.ant-modal-title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
  padding: 12px 16px;
}

/* 自定义模态框头部样式 */
.custom-modal-header {
  background-color: #fafafa;
}

:deep(.ant-modal-content) {
  padding-top: 0;
}

:deep(.ant-modal-body) {
  padding-top: 0;
}
</style>
