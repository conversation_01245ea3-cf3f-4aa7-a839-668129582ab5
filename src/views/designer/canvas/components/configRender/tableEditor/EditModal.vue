<template>
  <transition name="modal-fade">
    <div class="fixed inset-0 z-50 flex items-center justify-center">
      <div class="absolute inset-0 bg-gray-500 opacity-20"></div>
      <transition name="modal-content">
        <div
          class="bg-area-fill rounded-md shadow-lg overflow-hidden flex flex-col relative z-10"
          :class="isMaximized ? 'w-full h-full' : 'w-[1000px] max-h-[90vh]'"
          :style="isMaximized ? { margin: 0 } : { margin: '20px' }"
        >
          <!-- 头部 -->
          <div class="flex items-center justify-between px-4 py-3 border-b border-gray-200">
            <h3 class="text-base font-medium text-gray-800 dark:!text-white">{{ title }}</h3>
            <div class="flex items-center">
              <button
                class="ml-2 w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700 focus:outline-none"
                @click="toggleMaximize"
                title="最大化"
              >
                <svg
                  v-if="!isMaximized"
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M8 3H5a2 2 0 0 0-2 2v3"></path>
                  <path d="M21 8V5a2 2 0 0 0-2-2h-3"></path>
                  <path d="M3 16v3a2 2 0 0 0 2 2h3"></path>
                  <path d="M16 21h3a2 2 0 0 0 2-2v-3"></path>
                </svg>
                <svg
                  v-else
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M8 3v3a2 2 0 0 1-2 2H3"></path>
                  <path d="M21 8h-3a2 2 0 0 1-2-2V3"></path>
                  <path d="M3 16h3a2 2 0 0 1 2 2v3"></path>
                  <path d="M16 21v-3a2 2 0 0 1 2-2h3"></path>
                </svg>
              </button>
              <button
                class="ml-2 w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700 focus:outline-none"
                @click="$emit('close')"
                title="关闭"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          </div>

          <!-- 内容 -->
          <div
            class="flex-1 px-5 py-4 overflow-auto bg-area-fill"
            :style="isMaximized ? 'height: calc(100vh - 120px);' : 'max-height: calc(90vh - 120px);'"
          >
            <div class="flex justify-between items-center mb-3">
              <div class="text-sm text-gray-500 dark:!text-[var(--text-secondary)]">
                提示：支持多行文本编辑，Ctrl+Enter 保存，ESC 取消
              </div>
              <div class="text-xs text-gray-400 dark:!text-[var(--text-secondary)]">
                {{ value.length || 0 }} 字符 | {{ getLineCount() }} 行
              </div>
            </div>
            <textarea
              :value="value"
              @input="onInput"
              @keydown="handleKeydown"
              class="w-full h-full border border-gray-300 rounded-md px-3 py-2 text-gray-700 dark:!text-white dark:!bg-input-fill focus:outline-none focus:border-blue-500 resize-vertical"
              :style="isMaximized ? 'height: calc(100vh - 200px);' : 'height: 450px; min-height: 300px;'"
              placeholder="请输入内容，支持多行文本编辑..."
            ></textarea>
          </div>

          <!-- 底部 -->
          <div class="px-5 py-3 bg-gray-50 dark:!bg-input-fill border-t border-gray-200 flex justify-between items-center">
            <div class="text-xs text-gray-500 dark:!text-[var(--text-secondary)]">
              快捷键：Ctrl+Enter 保存，ESC 取消
            </div>
            <div class="flex gap-2">
              <button
                class="px-4 py-2 rounded-md text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:outline-none transition-colors"
                @click="$emit('close')"
              >
                取消
              </button>
              <button
                class="px-4 py-2 rounded-md text-white bg-blue-500 hover:bg-blue-600 focus:outline-none transition-colors"
                @click="$emit('ok')"
              >
                保存
              </button>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script lang="ts">
export default {
  name: 'EditModal',
  props: {
    title: {
      type: String,
      default: '编辑内容'
    },
    value: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'ok', 'update:value'],
  data() {
    return {
      isMaximized: false
    }
  },
  methods: {
    toggleMaximize() {
      this.isMaximized = !this.isMaximized;
    },
    onInput(e) {
      this.$emit('update:value', e.target.value);
    },
    handleKeydown(e) {
      // Ctrl+Enter 保存
      if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        this.$emit('ok');
      }
      // ESC 取消
      else if (e.key === 'Escape') {
        e.preventDefault();
        this.$emit('close');
      }
    },
    getLineCount() {
      if (!this.value) return 1;
      return this.value.split('\n').length;
    }
  },
  mounted() {
    // 自动聚焦到文本框
    this.$nextTick(() => {
      const textarea = this.$el.querySelector('textarea');
      if (textarea) {
        textarea.focus();
        // 将光标移到文本末尾
        textarea.setSelectionRange(textarea.value.length, textarea.value.length);
      }
    });
  }
}
</script>

<style scoped>
/* 模态框淡入淡出效果 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

/* 内容缩放效果 */
.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  transform: scale(0.9);
  opacity: 0;
}
</style>
