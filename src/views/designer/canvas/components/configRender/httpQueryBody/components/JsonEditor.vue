<template>
  <div class="json-editor">
    <!-- @vue-ignore -->
    <JsonEditorVue
      v-model="value"
      :main-menu-bar="false"
      :navigation-bar="false"
      :status-bar="false"
      :expandedOnStart="true"
      :stringified="false"
      mode="text"
      class="json-editor"
    />
  </div>
</template>

<script setup lang="ts">
import JsonEditorVue from 'json-editor-vue'

const value = defineModel()
</script>

<style scoped>
.json-editor {
  height: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}
</style>
