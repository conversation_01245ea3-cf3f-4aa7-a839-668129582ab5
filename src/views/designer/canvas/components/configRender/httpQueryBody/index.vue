<template>
  <div class="w-full">
    <a-select
      :options="options"
      v-model:value="data.bodyType"
      class="w-full"
      @change="handleTypeChange"
    />
    <!-- 根据type渲染不同组件 -->
    <div class="mt-4">
      <component :is="componentMap[data.bodyType]" v-model="data.bodyData" />
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入各个子组件
import JsonEditor from './components/JsonEditor.vue'
import FormDataEditor from './components/FormDataEditor.vue'
import RawTextEditor from './components/RawTextEditor.vue'
import FileUpload from '../fileUpload/index.vue'
import UrlEncodedForm from './components/UrlEncodedForm.vue'

const componentMap = {
  json: JsonEditor,
  'form-data': FormDataEditor,
  'x-www-form-urlencoded': UrlEncodedForm,
  'raw-text': RawTextEditor,
  binary: FileUpload
}



const props = defineProps<{ options: any[], modelValue: any }>()

const data = ref({ bodyType: '', bodyData: undefined })
watch(() => props.modelValue, (newVal) => {
  data.value = newVal || { bodyType: '', bodyData: undefined }
},{deep:true,immediate:true})


const emit = defineEmits(['update:modelValue'])
watch(data, (newVal) => emit('update:modelValue', newVal),{deep:true})

const handleTypeChange = () => {
  data.value.bodyData = undefined
}

</script>
