<template>
  <ResponsiveLayout class-prefix="out-params">
    <div class="w-full">
      <div class="flex-param-header text-gray-500 text-[12px] mb-4">
        <div class="flex-param-name dark:text-[var(--grey-auxiliary-color)]">参数名</div>
        <div class="flex-param-type dark:text-[var(--grey-auxiliary-color)]">参数类型</div>
        <div class="flex-param-value dark:text-[var(--grey-auxiliary-color)]">参数值</div>
        <div class="flex-param-desc dark:text-[var(--grey-auxiliary-color)]">参数描述</div>
        <div class="flex-param-actions">操作</div>
      </div>

      <parameter-item
        v-for="(param, index) in parameters"
        :key="index"
        :param="param"
        :typeOptions="typeOptions"
        @remove="removeParam(index)"
        @update:param="updateParam(index, $event)"
      />

      <!-- 添加按钮 -->
      <a-button
        type="link"
        @click="addParam"
        class="param-add-btn text-blue-600 flex items-center justify-center !h-[32px] !w-[32px] !p-0"
      >
        <PlusOutlined class="text-[16px]" />
      </a-button>
    </div>
  </ResponsiveLayout>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { PropType } from 'vue'
import ParameterItem from './ParameterItem.vue'
import { defaultTypeOptions } from './constants'
import ResponsiveLayout from '../ResponsiveLayout.vue'

const props = defineProps({
  modelValue: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  typeOptions: {
    type: Array as PropType<any[]>,
    default: () => defaultTypeOptions
  }
})
const emit = defineEmits(['update:modelValue'])

const parameters = ref<any[]>(Array.isArray(props.modelValue) ? props.modelValue : [])

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== parameters.value) {
    parameters.value = Array.isArray(newVal) ? newVal : []
  }
}, { deep: true })

// 监听内部值变化
watch(parameters, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

const addParam = () => {
  console.log('parameters.value:', parameters.value)
  console.log('typeof parameters.value:', typeof parameters.value)
  parameters.value.push({
    name: '',
    type: 'String',
    expanded: false,
    children: []
  })
}

const removeParam = (index: number) => {
  parameters.value.splice(index, 1)
}

const updateParam = (index: number, newParam: any) => {
  parameters.value[index] = newParam
}
</script>

<style lang="scss">
@import '../responsive-layout.scss';
</style>

<style scoped>
.param-add-btn {
  position: absolute !important;
  right: 28px;
  top: 15px;
}

/* Flex布局样式 */
.flex-param-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.flex-param-name {
  flex: 2;
  min-width: 80px;
}

.flex-param-type {
  flex: 1;
  min-width: 70px;
}

.flex-param-value {
  flex: 2.5;
  min-width: 120px;
}

.flex-param-desc {
  flex: 1.5;
  min-width: 80px;
}

.flex-param-actions {
  flex: 0.5;
  min-width: 60px;
  text-align: center;
}

/* 全屏模式下的flex比例调整 */
.fullscreen-drawer {
  .flex-param-name {
    flex: 2.5;
    min-width: 200px;
  }

  .flex-param-type {
    flex: 1.2;
    min-width: 120px;
  }

  .flex-param-value {
    flex: 3;
    min-width: 250px;
  }

  .flex-param-desc {
    flex: 2;
    min-width: 150px;
  }

  .flex-param-actions {
    flex: 0.8;
    min-width: 100px;
  }
}
</style>
