<template>
  <div class="relative">
    <div class="flex-param-row">
      <!-- 参数名称输入 -->
      <div class="flex-param-name">
        <div class="flex items-center gap-2">
          <!-- 展开/折叠按钮 - 只有 Object 类型才显示 -->
          <div v-if="isObject" class="cursor-pointer w-4" @click="toggleExpand">
            <RightOutlined v-if="!isExpanded" class="text-gray-400 text-[14px]" />
            <DownOutlined v-else class="text-gray-400 text-[14px]" />
          </div>
          <a-input v-model:value="param.name" :spellcheck="false" placeholder="请输入" class="flex-1" />
        </div>
      </div>

      <!-- 参数类型选择 -->
      <div class="flex-param-type">
        <a-select
          v-model:value="param.type"
          class="!w-full"
          :dropdown-match-select-width="false"
          @change="handleTypeChange"
        >
          <a-select-option v-for="type in typeOptions" :key="type.value" :value="type.value">
            {{ type.label }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 参数值输入 -->
      <div class="flex-param-value">
        <param-ref :param-type="param.type" v-model="param.value" />
      </div>

      <!-- 描述输入 -->
      <div class="flex-param-desc">
        <a-input
          v-model:value="param.description"
          placeholder="描述"
          class="!h-[32px] text-[14px]"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="flex-param-actions">
        <div class="flex items-center justify-center gap-1">
          <!-- 子参数添加按钮 - 只在展开的 Object 类型时显示 -->
          <a-button
            v-if="isObject && isExpanded"
            type="link"
            @click="addChild"
            class="action-btn text-blue-600"
          >
            <PlusOutlined class="text-[14px]" />
          </a-button>

          <!-- 删除按钮 -->
          <a-button type="text" @click="$emit('remove')" class="action-btn text-gray-400">
            <CloseOutlined class="text-[14px]" />
          </a-button>
        </div>
      </div>
    </div>

    <!-- 子参数区域 -->
    <div v-if="isExpanded && isObject" class="pl-8 border-l border-l-gray-200 ml-2 mt-2">
      <parameter-item
        v-for="(child, index) in param.children"
        :key="index"
        :param="child"
        :typeOptions="typeOptions"
        @remove="removeChild(index)"
        @update:param="updateChild(index, $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { RightOutlined, DownOutlined, PlusOutlined, CloseOutlined } from '@ant-design/icons-vue'
import ParamRef from '../paramRef/index.vue'

const props = defineProps(['param', 'typeOptions'])
const emit = defineEmits(['remove', 'update:param'])

// 只有 Object 类型才能添加子项
const isObject = computed(() => props.param.type === 'Object' || props.param.type === 'Array')

// UI 状态，不影响实际数据
const isExpanded = ref(props.param.type === 'Object')

// 判断是否显示变量值输入框
const showValueInput = computed(() =>
  props.param.type !== 'Object' && props.param.type !== 'Array'
)

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

const handleTypeChange = (value: string) => {
  if (value === 'Object') {
    if (!props.param.children) {
      props.param.children = []
    }
    isExpanded.value = true  // 当类型改为 Object 时自动展开
  } else {
    props.param.children = undefined
  }
  emit('update:param', props.param)
}

const addChild = () => {
  if (!props.param.children) {
    props.param.children = []
  }
  props.param.children.push({
    name: '',
    type: 'String',
    value: '',
    description: '',
    expanded: false,
    children: []
  })
  emit('update:param', props.param)
}

const removeChild = (index: number) => {
  props.param.children?.splice(index, 1)
  emit('update:param', props.param)
}

const updateChild = (index: number, newChild: any) => {
  if (props.param.children) {
    props.param.children[index] = newChild
    emit('update:param', props.param)
  }
}
</script>

<style lang="scss">
@import '../responsive-layout.scss';
</style>

<style scoped>
/* Flex布局样式 */
.flex-param-row {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  min-height: 48px;
}

.flex-param-name {
  flex: 2;
  min-width: 80px;
}

.flex-param-type {
  flex: 1;
  min-width: 70px;
}

.flex-param-value {
  flex: 2.5;
  min-width: 120px;
}

.flex-param-desc {
  flex: 1.5;
  min-width: 80px;
}

.flex-param-actions {
  flex: 0.5;
  min-width: 60px;
}

/* 全屏模式下的flex比例调整 */
:global(.fullscreen-drawer) .flex-param-row {
  gap: 16px;
  padding: 12px 0;

  .flex-param-name {
    flex: 2.5;
    min-width: 200px;
  }

  .flex-param-type {
    flex: 1.2;
    min-width: 120px;
  }

  .flex-param-value {
    flex: 3;
    min-width: 250px;
  }

  .flex-param-desc {
    flex: 2;
    min-width: 150px;
  }

  .flex-param-actions {
    flex: 0.8;
    min-width: 100px;
  }
}

:deep(.ant-select-selector) {
  height: 32px !important;
  line-height: 32px !important;
}

:deep(.ant-select-selection-item) {
  line-height: 30px !important;
  font-size: 14px !important;
}

.action-btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
  flex-shrink: 0;
}

:deep(.ant-input) {
  display: flex !important;
  align-items: center !important;
}

:deep(.ant-input-affix-wrapper) {
  padding: 0 11px !important;
  height: 32px !important;
}

:deep(.ant-select-selector) {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
}
</style>
