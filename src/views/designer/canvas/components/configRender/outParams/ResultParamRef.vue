<template>
  <div ref="containerRef">
    <a-input v-model:value="inputValue" placeholder="参数值" class="text-[12px]">
      <template #suffix>
        <SettingOutlined
          class="text-[14px] cursor-pointer hover:text-blue-500"
          @click.stop="showReferenceSelect"
        />
      </template>
    </a-input>

    <Teleport to="body">
      <div
        v-if="isDropdownVisible"
        class="fixed z-[9999] bg-white dark:!bg-area-fill border border-gray-200 dark:!border-border rounded-md shadow-lg overflow-y-auto max-h-[400px]"
        :style="dropdownStyle"
        @click.stop
      >
        <div v-if="resultParams.length" class="divide-y divide-gray-100 p-2">
          <div
            v-for="param in resultParams"
            :key="param.name"
            class="py-2 px-1 cursor-pointer hover:bg-gray-50 text-[12px] flex items-center justify-between"
            @click="selectReference(param.name)"
          >
            <span>{{ param.name }}</span>
            <span class="text-gray-400">({{ param.type }})</span>
          </div>
        </div>
        <div v-else class="py-8 px-4 text-center text-gray-400 text-[12px]">暂无可用参数</div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { SettingOutlined } from '@ant-design/icons-vue'
import { useDesignerStore } from '@/store/designer'

const designerStore = useDesignerStore()

const props = defineProps<{
  modelValue: string,
}>()

const emit = defineEmits(['update:modelValue'])

const containerRef = ref<HTMLElement | null>(null)
const isDropdownVisible = ref(false)

const inputValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})


const dropdownStyle = computed(() => {
  const rect = containerRef.value?.getBoundingClientRect()
  return {
    top: `${(rect?.bottom || 0) + 2}px`,
    left: `${rect?.left || 0}px`,
    width: `${rect?.width || 0}px`
  }
})

const showReferenceSelect = (event: MouseEvent) => {
  event.stopPropagation()
  isDropdownVisible.value = true
}

const selectReference = (paramName: string) => {
  inputValue.value = `{current.${paramName}}`
  isDropdownVisible.value = false
}

const closeDropdown = (e: MouseEvent) => {
  const target = e.target as HTMLElement
  if (!target.closest('.ant-input-suffix')) {
    isDropdownVisible.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeDropdown)
})

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown)
})

const resultParams = computed(() => {
  const currentNode = designerStore.currentNode
  if (!currentNode) return []

  const nodeData = currentNode.getData()
  console.log('nodeData:', nodeData)
  if (!nodeData?.components) return []

  // 直接从节点数据中找到 result 组件的配置
  const resultConfig = nodeData.components.find(comp => comp.field === 'result')
  console.log('resultConfig:', resultConfig)
  return resultConfig?.props?.defaultParams?.data || []
})
</script>

<style scoped>
:deep(.ant-input-suffix) {
  cursor: pointer;
}
</style>
