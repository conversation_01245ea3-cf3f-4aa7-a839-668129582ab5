<template>
  <div class="w-full">
    <!-- 上传字段列表 -->
    <div class="flex flex-col gap-2">
      <div 
        v-for="(field, index) in fields" 
        :key="index"
        class="flex items-center gap-2"
      >
        <!-- 字段名称输入 -->
        <a-input
          v-model:value="field.name"
          placeholder="输入文件字段名"
          class="!w-[220px] !h-[24px] text-[14px]"
          :maxLength="20"
        >
          <template #suffix>{{ field.name.length }}/20</template>
        </a-input>

        <!-- 删除按钮 -->
        <a-button 
          type="text"
          @click="removeField(index)"
          class="!h-[24px] !w-[24px] !p-0"
        >
          <CloseOutlined class="text-gray-400" />
        </a-button>
      </div>
    </div>

    <!-- 添加按钮 -->
    <a-button 
      type="link"
      @click="addField"
      class="upload-add-btn text-blue-600 flex items-center justify-center !h-[24px] !w-[24px] !p-0"
    >
      <PlusOutlined class="text-lg" />
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { PropType } from 'vue'
import { PlusOutlined, CloseOutlined } from '@ant-design/icons-vue'

interface UploadField {
  name: string
}

const props = defineProps({
  modelValue: {
    type: Array as PropType<UploadField[]>,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const fields = ref<UploadField[]>(props.modelValue)

// 添加字段
const addField = () => {
  fields.value.push({
    name: ''
  })
}

// 删除字段
const removeField = (index: number) => {
  fields.value.splice(index, 1)
}

// 监听字段变化
watch(fields, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  fields.value = newVal
}, { deep: true })
</script>

<style scoped>
.upload-add-btn {
  position: absolute !important;
  right: 28px;
  top: 15px;
}
</style> 