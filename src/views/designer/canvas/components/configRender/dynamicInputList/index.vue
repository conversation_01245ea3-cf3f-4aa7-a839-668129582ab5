<template>
  <div class="dynamic-input-list">
    <a-button type="link" @click="addInput" class="add-btn">
      <template #icon>
        <PlusOutlined />
      </template>
    </a-button>
    <div class="input-list">
      <draggable
        :list="inputList"
        item-key="id"
        handle=".drag-handle"
        animation="300"
        ghost-class="input-ghost-class"
        chosen-class="input-chosen-class"
        :force-fallback="true"
        @end="onEnd"
      >
        <template #item="{element}">
          <div class="input-item w-full flex flex-row gap-2">
            <HolderOutlined
              class="drag-handle drag-icon cursor-move text-gray-400 hover:text-gray-600"
            />
            <param-ref style="width: 80%" param-type="type" v-model="element.value" />
            <MinusCircleOutlined class="delete-icon" @click="removeInput(element.id)" />
          </div>
        </template>
      </draggable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { PlusOutlined, MinusCircleOutlined, HolderOutlined } from '@ant-design/icons-vue'
import draggable from 'vuedraggable'
import ParamRef from '../paramRef/index.vue'

interface InputItem {
  id: number
  value: string
}

const props = defineProps({
  modelValue: {
    type: Array as PropType<InputItem[]>,
    default: () => []
  },
  inputProps: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['update:modelValue'])


const inputList = ref<InputItem[]>([])

watch(() => props.modelValue, (newVal) => {
  if (newVal !== inputList.value) {
    inputList.value = Array.isArray(newVal) ? newVal : []
  }
}, { deep: true })

watch(inputList, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })


const addInput = () => {
  inputList.value.push({
    value: '',
    id: Date.now()
  })
}

const removeInput = (id: number) => {
  inputList.value = inputList.value.filter(item => item.id !== id)
}

const onEnd = () => {
  emit('update:modelValue', inputList.value)
}
</script>

<style scoped>
.dynamic-input-list {
  position: relative;
}
.dynamic-input-list .header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.dynamic-input-list .add-btn {
  position: absolute !important;
  right: 12px;
  top: -36px;
}

.dynamic-input-list .input-list .input-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.dynamic-input-list .input-list .delete-icon {
  margin-left: 8px;
  cursor: pointer;
  color: #666;
  font-size: 16px;
  padding: 6px;
}

.dynamic-input-list .input-list .drag-icon {
  color: #666;
}


.dynamic-input-list .input-list .delete-icon:hover {
  background-color: #F0F0F0;
  border-radius: 4px;
}
/* 拖拽时的样式 */
.input-ghost-class {
  background-color: #f5f5f5 !important;
  border: 1px dashed #ccc !important;
}

.input-chosen-class {
  background-color: #e6f7ff !important;
  border: 1px solid #91d5ff !important;
}

.input-drag-class {
  background-color: #fafafa !important;
  opacity: 0.8 !important;
}
</style>
