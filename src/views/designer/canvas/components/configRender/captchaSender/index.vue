<template>
  <div class="phone-input-node">
    <a-form-item
      :label="config?.label || '手机号码'"
      :name="config?.field || 'phoneNumber'"
      :validate-status="phoneError ? 'error' : ''"
      :help="phoneError ? errorMessage : ''"
    >
      <div class="input-container">
        <a-select
          v-model:value="countryCode"
          class="country-select"
          :options="countryOptions"
          :loading="!countryOptions?.length"
          placeholder="选择国家"
          @change="handleCountryChange"
        />
        <a-input
          v-model:value="phoneNumber"
          :placeholder="config?.placeholder || '请输入手机号码'"
          :max-length="getMaxLength()"
          @blur="validatePhoneNumber"
          @input="handleInput"
        />
      </div>
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import type { SelectProps } from 'ant-design-vue'
import { useDesignerStore } from '@/store/designer'

interface PhoneConfig {
  label?: string
  field?: string
  placeholder?: string
  required?: boolean
  // 新增国际化配置支持
  countryOptions?: SelectProps['options']
}

interface CountryOption {
  value: string
  label: string
}

const props = defineProps<{
  modelValue: {
    type: String,
    default: ''
  }, 
  config?: {
    default: '',
    type: PhoneConfig
  }
}>()

const emit = defineEmits(['update:modelValue', 'validation-change'])

// 设计器状态
const designerStore = useDesignerStore()

// 国家选项（可配置）
const countryOptions = computed<SelectProps['options']>(() => 
  props.config?.countryOptions || [
    { value: '+86', label: '中国 (+86)' },
    {value: '+1', label: '美国 (+1)'},
    {value: '+81', label: '日本 (+81)'}
  ]
)

// 修改后的计算属性
const currentCountryLabel = computed(() => {
  const country = countryOptions.value?.find(c => c.value === countryCode.value)
  return country?.label || '所选国家'
})



// 响应式状态
const countryCode = ref('+86')
const phoneNumber = ref('')
const phoneError = ref(false)
const errorMessage = ref('')

// 修改后的错误信息
watch(phoneError, (newVal) => {
  if (newVal) {
    errorMessage.value = `无效的${currentCountryLabel.value}号码`
  } else {
    errorMessage.value = ''
  }
})

// 手机号长度规则
const PHONE_LENGTH: Record<string, number> = {
  '+86': 11,   // 中国
  '+1': 10,    // 美国
  '+81': 10    // 日本
}

// 输入处理
const handleInput = (e: Event) => {
  const input = e.target as HTMLInputElement
  phoneNumber.value = input.value.replace(/\D/g, '')
  emitUpdate()
}

// 国家区号变化处理
const handleCountryChange = () => {
  phoneNumber.value = ''
  validatePhoneNumber()
  emitUpdate()
}

// 验证逻辑
const validatePhoneNumber = () => {
  const rules: Record<string, RegExp> = {
    '+86': /^1[3-9]\d{9}$/,
    '+1': /^\d{10}$/,
    '+81': /^0\d{9}$/
  }

  if (!phoneNumber.value) {
    phoneError.value = !!props.config?.required
    errorMessage.value = props.config?.required ? '必填字段' : ''
    return
  }

  const validator = rules[countryCode.value] || /\d+/
  phoneError.value = !validator.test(phoneNumber.value)
  errorMessage.value = phoneError.value 
    ? `无效的${countryOptions.value?.find(c => c.value === countryCode.value)?.label}号码`
    : ''

  emit('validation-change', !phoneError.value)
}

// 值更新处理
const emitUpdate = () => {
  const fullNumber = countryCode.value + phoneNumber.value
  if (fullNumber !== props.modelValue) {
    emit('update:modelValue', fullNumber)
  }
}

// 动态长度限制
const getMaxLength = () => PHONE_LENGTH[countryCode.value] || 20

// 初始化解析
const parseInitialValue = () => {
  if (!props.modelValue) return
  
  // 匹配最长国家代码
  const codes = countryOptions.value?.map(c => c.value.toString()) || []
  const matchedCode = codes.sort((a, b) => b.length - a.length)
    .find(code => props.modelValue.startsWith(code))

  if (matchedCode) {
    countryCode.value = matchedCode
    phoneNumber.value = props.modelValue.slice(matchedCode.length)
  }
}

// 响应外部变化
watch(() => props.modelValue, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== countryCode.value + phoneNumber.value) {
    parseInitialValue()
  }
})

// 初始化
parseInitialValue()
</script>

<style scoped>
.phone-input-node {
  width: 100%;
  position: relative;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.country-select {
  width: 120px;
  flex-shrink: 0;
}

:deep(.ant-input) {
  transition: all 0.3s;
  flex-grow: 1;
}

:deep(.ant-input:hover) {
  border-color: #4096ff;
}

:deep(.ant-select-selector) {
  border-radius: 6px !important;
}
</style>