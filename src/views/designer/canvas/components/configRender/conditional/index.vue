<template>
  <div>
    <!-- 标题和添加按钮 -->
    <div class="flex justify-between items-center mb-4">
      <span class="text-gray-900 dark:!text-white">条件分支</span>
      <button class="text-blue-500 hover:bg-blue-50 p-2 rounded-lg" @click="addCondition">
        <PlusOutlined />
      </button>
    </div>

    <!-- 条件分支列表 -->
    <div class="space-y-4">
      <div
        v-for="(condition, index) in conditions"
        :key="condition.id"
        class="bg-white rounded-lg border border-gray-200 p-4 dark:!bg-area-fill"
        draggable="true"
        @dragstart="handleDragStart($event, index)"
        @dragover.prevent
        @drop="handleDrop($event, index)"
      >
        <!-- 条件头部 -->
        <div class="flex justify-between items-center mb-4">
          <div class="flex items-center space-x-2">
            <MenuOutlined class="text-gray-400 cursor-move" />
            <span
              class="text-gray-600 dark:!text-[var(--text-secondary)]"
              >{{ getConditionTitle(index) }}</span
            >
            <span class="bg-gray-100 px-2 py-1 rounded text-sm text-gray-500"
              >优先级 {{ index + 1 }}</span
            >
          </div>
          <button
            :class="['text-gray-400 hover:text-gray-600', { 'cursor-not-allowed': disableRemoveCondition }]"
            @click="removeCondition(index)"
          >
            <MinusCircleOutlined />
          </button>
        </div>

        <!-- 变量组列表 -->
        <div class="space-y-4">
          <div class="flex items-center">
            <!-- 只有多行时显示最左侧的且 -->
            <template v-if="condition.groups?.length > 1">
              <a-select
                v-model:value="condition.mainLogic"
                class="w-20 mr-2"
                size="middle"
                :options="LOGIC_OPERATORS"
              />
            </template>

            <div class="flex-1 space-y-2">
              <div
                v-for="(group, groupIndex) in condition.groups"
                :key="group.id"
                class="flex items-center space-x-2"
              >
                <!-- 等于选择器 -->
                <a-select
                  v-model:value="group.operator"
                  class="w-20"
                  size="middle"
                  placeholder="请选择"
                  :options="OPERATORS"
                  label-in-value
                  option-label-prop="symbol"
                  :dropdown-match-select-width="false"
                >
                </a-select>

                <!-- 两个输入框 -->
                <div class="flex-1 flex flex-col space-y-2">
                  <ParamRef v-model="group.value1" placeholder="请选择" />
                  <ParamRef v-model="group.value2" placeholder="输入或引用参数值" />
                </div>

                <!-- 只显示减号（在有多行时） -->
                <template v-if="condition.groups.length > 1">
                  <button
                    class="flex items-center justify-center w-8 h-8"
                    @click="removeVariableGroup(index, groupIndex)"
                  >
                    <MinusCircleOutlined />
                  </button>
                </template>
              </div>
            </div>
          </div>

          <!-- 添加变量组按钮 -->
          <button
            class="w-full py-2 px-4 border border-dashed border-gray-300 
                         text-blue-500 hover:border-blue-500 rounded-lg 
                         flex items-center justify-center space-x-1 mt-2"
            @click="addVariableGroup(index)"
          >
            <PlusOutlined />
            <span>新增</span>
          </button>
        </div>
      </div>

      <!-- 默认的否则条件框 -->
      <div class="bg-white rounded-lg border border-gray-200 p-4 dark:!bg-area-fill">
        <div class="flex items-center space-x-2">
          <MenuOutlined class="text-gray-400" />
          <span class="text-gray-600 dark:!text-[var(--text-secondary)]">否则</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { PlusOutlined, MinusCircleOutlined, MenuOutlined } from '@ant-design/icons-vue'
import { generateUUID } from '@/utils/tools'
import ParamRef from '../paramRef/index.vue'
import { OPERATORS, LOGIC_OPERATORS } from './constant'
import { useDesignerStore } from '@/store/designer'
import {type Node,type Graph} from '@antv/x6'
import lodash from 'lodash-es'

const props = defineProps<{
  modelValue?: any[]
}>()

const emit = defineEmits(['update:modelValue', 'change'])

const designerStore = useDesignerStore()

const conditions = ref([{
  id: generateUUID(),
  mainLogic: 'and',
  groups: [{
    id: generateUUID(),
    operator: '',
    value1: '',
    value2: ''
  }]
}])

// 初始化数据
const initializeData = () => {
  if (!props.modelValue?.length) return
  // 排除否则分支
  const list = props.modelValue.filter((item) => !item.defaultNode)
  conditions.value = list.map((item) => {
    // 从条件数组中提取 mainLogic
    let mainLogic = 'and'; // 默认值
    if (item.conditions.length > 1 && item.conditions[1].logic) {
      mainLogic = item.conditions[1].logic.toLowerCase();
    }

    // 处理每个条件
    const groups = item.conditions.map((condition: any) => {
      // 查找匹配的操作符
      const operatorObj = OPERATORS.find(op => op.value === condition.operator)

      return {
        id: condition.id ?? generateUUID(),
        operator: operatorObj?.value || condition.operator,
        value1: condition.leftValue,
        value2: condition.rightValue
      };
    });

    return {
      id: item.id ?? generateUUID(),
      mainLogic,
      groups
    };
  });
};
watch(() => props.modelValue, (newVal, oldVal) => {
  if (!newVal || !Array.isArray(newVal)) return
  if (newVal.length === 0) return
  const firstItem = newVal[0]
  if (!firstItem?.conditions) return
  if(lodash.isEqual(newVal,oldVal)) return
  initializeData()
} ,{immediate:true,deep:true})


// 格式化数据
const formatConditions = (conditions: any[]) => {
  if (!conditions || !Array.isArray(conditions)) {
    return [];
  }

  // 获取当前节点数据，用于保留现有的nextNode
  const currentNode = designerStore.currentNode
  const currentNodeData = currentNode?.getData() || {}
  const currentBranches = currentNodeData?.properties?.branches || []

  // 过滤掉defaultNode，只保留条件分支
  const nonDefaultBranches = currentBranches.filter(branch => !branch.defaultNode)

  return conditions.map((condition, index) => {
    // 处理每个条件组
    const formattedGroups = condition.groups.map((group: any, groupIndex: number) => {
      return {
        id: group.id,
        leftValue: group.value1,
        operator: group.operator?.label || group.operator,
        rightValue: group.value2,
        // 只有当不是第一个条件时才添加 logic
        ...(groupIndex > 0 ? { logic: condition.mainLogic.toUpperCase() } : {})
      };
    });

    // 查找对应的原分支以保留nextNode
    // 如果是现有分支，使用原有的nextNode
    let nextNode = ''
    if (index < nonDefaultBranches.length) {
      nextNode = nonDefaultBranches[index]?.nextNode || ''
    }

    return {
      id: condition.id,
      conditions: formattedGroups,
      nextNode: nextNode,  // 保留原有的nextNode值或使用空字符串
      priority: index + 1,
      defaultNode: false
    };
  });
};

const currentNode = designerStore.currentNode as Node

const defaultNode = { id: generateUUID(), conditions: [], nextNode: '', priority: 0, defaultNode: true }
watch(conditions, (newVal) => {
  // 获取当前节点数据和分支信息
  const currentNodeData = currentNode?.getData() || {}
  const existingBranches = currentNodeData?.properties?.branches || []

  // 生成新的分支配置，但保留原有nextNode
  let branches = formatConditions(newVal)

  // 对于每个分支，确保保留原来的nextNode
  branches = branches.map((branch, index) => {
    // 如果是现有分支，保留原有的nextNode
    if (index < existingBranches.length && !existingBranches[index].defaultNode) {
      branch.nextNode = existingBranches[index].nextNode || ''
    }
    return branch
  })

  // 处理默认分支
  const oldDefaultBranch = existingBranches.find(b => b.defaultNode)
  defaultNode.priority = branches.length + 1
  defaultNode.nextNode = oldDefaultBranch?.nextNode || ''
  branches.push({...defaultNode})

  emit('update:modelValue', branches)
},
  { deep: true }
)

// 更新 store 中的端口数量
watch(() => conditions.value.length, (newVal) => {
  const currentNode = designerStore.currentNode as Node
  if (currentNode) {
    designerStore.updateNodePorts(currentNode.id, newVal + 1) // +1 是因为还有一个"否则"分支
  }
})

const getConditionTitle = (index: number) => {
  if (index === 0) return '如果'
  if (index === conditions.value.length - 1) return '否则如果'
  return '否则如果'
}

const addCondition = () => {
  // 获取当前节点数据
  const currentData = currentNode?.getData() || {}
  if (!currentData.properties?.branches) return

  // 查找当前默认分支
  const branches = [...currentData.properties.branches]
  const defaultIndex = branches.findIndex(b => b.defaultNode)
  if (defaultIndex < 0) return

  // 保存当前默认分支并修改为普通分支
  const oldDefaultBranch = branches[defaultIndex]
  oldDefaultBranch.defaultNode = false

  // 创建新的默认分支
  const newDefaultBranch = {
    id: generateUUID(),
    conditions: [],
    nextNode: '',
    priority: branches.length + 1,
    defaultNode: true
  }

  // 更新分支数组 - 先删除旧默认分支，再添加回数组，最后添加新默认分支
  branches.splice(defaultIndex, 1)
  branches.push(oldDefaultBranch, newDefaultBranch)

  // 更新优先级
  branches.forEach((branch, index) => {
    branch.priority = index + 1
  })

  emit('update:modelValue', branches)

  // 更新节点数据
  currentData.properties.branches = branches
  currentNode.setData(currentData)

  // 刷新UI条件列表显示
  setTimeout(() => {
    initializeData()
  }, 100)
}

const disableRemoveCondition = computed(() => conditions.value.length === 1)
const removeCondition = (index: number) => {
  if(disableRemoveCondition.value) return
  conditions.value.splice(index, 1)
}

const addVariableGroup = (conditionIndex: number) => {
  if (!conditions.value[conditionIndex]) return

  conditions.value[conditionIndex].groups.push({
    id: generateUUID(),
    operator: '',
    value1: '',
    value2: ''
  })
}

const removeVariableGroup = (conditionIndex: number, groupIndex: number) => {
  if (conditions.value[conditionIndex].groups.length > 1) {
    conditions.value[conditionIndex].groups.splice(groupIndex, 1)
  }
}

// 拖拽相关方法
let dragIndex = -1

const handleDragStart = (e: DragEvent, index: number) => {
  dragIndex = index
  if (e.dataTransfer) {
    e.dataTransfer.effectAllowed = 'move'
  }
}

const handleDrop = (e: DragEvent, index: number) => {
  e.preventDefault()
  if (dragIndex === index) return

  // 交换位置
  const temp = conditions.value[dragIndex]
  conditions.value[dragIndex] = conditions.value[index]
  conditions.value[index] = temp

  // 重置拖拽索引
  dragIndex = -1

  // 触发变更事件
  emit('change', conditions.value)
}
</script>

<style lang="scss" scoped>
.condition-selector {
  .condition-block {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;

    .condition-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .condition-title {
        font-weight: 500;
      }
    }
  }

  .variable-groups {
    .variable-group {
      margin-bottom: 8px;
    }

    .logic-select {
      width: 80px;
      margin-bottom: 8px;
    }

    .variable-condition {
      display: flex;
      align-items: center;
      gap: 8px;

      .param-select {
        flex: 1;
      }

      .operator-select {
        width: 120px;
      }
    }
  }
}

.cursor-move {
  cursor: move;
}
</style>

<style scoped>
.ant-select-selection-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保所有选择器内容居中 */
:deep(.ant-select-selector) {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
