export const OPERATORS = [
  { value: 'eq', label: '等于', symbol: '=' },
  { value: 'neq', label: '不等于', symbol: '!=' },
  { value: 'gt', label: '大于', symbol: '>' },
  { value: 'gte', label: '大于等于', symbol: '≥' },
  { value: 'lt', label: '小于', symbol: '<' },
  { value: 'lte', label: '小于等于', symbol: '≤' },
  { value: 'contains', label: '包含', symbol: '⊃' },
  { value: 'not_contains', label: '不包含', symbol: '⊅' },
]

export const LOGIC_OPERATORS = [
  { value: 'and', label: '且' },
  { value: 'or', label: '或' },
]
