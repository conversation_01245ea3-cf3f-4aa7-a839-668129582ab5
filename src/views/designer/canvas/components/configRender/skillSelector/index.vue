<template>
  <div class="w-full">
    <!-- 技能列表 -->
    <div class="space-y-3">
      <!-- 知识库组 -->
      <div class="space-y-3">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white">知识库</h4>
        
        <!-- 动态渲染知识库技能 -->
        <div
          v-for="skill in knowledgeSkills"
          :key="skill.id"
          class="flex items-center justify-between"
        >
          <span class="text-xs text-gray-600 flex items-center">
            <component :is="skill.icon" class="mr-2" />
            {{ skill.name }}
          </span>
          <a-button type="text" size="small" @click="removeSkill(skill.id)">
            <DeleteOutlined class="text-red-400" />
          </a-button>
        </div>

        <!-- 添加知识库技能按钮 -->
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-600 flex items-center">
            <PlusOutlined class="mr-2" />
            添加知识库技能
          </span>
          <a-button type="text" size="small" @click="showSkillModal('knowledge')">
            <PlusOutlined />
          </a-button>
        </div>
      </div>

      <!-- 记忆组 -->
      <div class="space-y-3">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white">记忆</h4>
        
        <!-- 动态渲染记忆技能 -->
        <div
          v-for="skill in memorySkills"
          :key="skill.id"
          class="flex items-center justify-between"
        >
          <span class="text-xs text-gray-600 flex items-center">
            <component :is="skill.icon" class="mr-2" />
            {{ skill.name }}
          </span>
          <a-switch 
            v-model:checked="skill.enabled" 
            size="small" 
            @change="(checked) => toggleSkill(skill.id, checked)"
          />
        </div>

        <!-- 添加记忆技能按钮 -->
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-600 flex items-center">
            <PlusOutlined class="mr-2" />
            添加记忆技能
          </span>
          <a-button type="text" size="small" @click="showSkillModal('memory')">
            <PlusOutlined />
          </a-button>
        </div>
      </div>

      <!-- 时间工具组 -->
      <div class="space-y-3">
        <h4 class="text-sm font-medium text-gray-900 dark:text-white flex items-center">
          <ClockCircleOutlined class="mr-2 text-orange-500" />
          时间
        </h4>
        
        <!-- 动态渲染时间技能 -->
        <div
          v-for="skill in timeSkills"
          :key="skill.id"
          class="flex items-center justify-between"
        >
          <span class="text-xs text-gray-600 flex items-center">
            <component :is="skill.icon" class="mr-2" />
            {{ skill.name }}
          </span>
          <a-switch 
            v-model:checked="skill.enabled" 
            size="small" 
            @change="(checked) => toggleSkill(skill.id, checked)"
          />
        </div>

        <!-- 添加时间技能按钮 -->
        <div class="flex items-center justify-between">
          <span class="text-xs text-gray-600 flex items-center">
            <PlusOutlined class="mr-2" />
            添加时间技能
          </span>
          <a-button type="text" size="small" @click="showSkillModal('time')">
            <PlusOutlined />
          </a-button>
        </div>
      </div>

      <!-- 工作流组 -->
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white flex items-center">
            <ShareAltOutlined class="mr-2 text-green-500" />
            工作流
          </h4>
          <a-button type="text" size="small" @click="showWorkflowModal">
            <PlusOutlined />
          </a-button>
        </div>
        
        <!-- 已添加的工作流 -->
        <div class="space-y-2">
          <div
            v-for="workflow in addedWorkflows"
            :key="workflow.id"
            class="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
          >
            <span>{{ workflow.name }}</span>
            <a-button type="text" size="small" @click="removeWorkflow(workflow)">
              <DeleteOutlined class="text-red-400" />
            </a-button>
          </div>
          <div v-if="addedWorkflows.length === 0" class="text-gray-400 text-xs text-center py-2">
            暂无工作流
          </div>
        </div>
      </div>
    </div>

    <!-- 技能选择弹窗 -->
    <a-modal
      v-model:open="skillModalVisible"
      :title="`选择${getSkillTypeLabel(currentSkillType)}技能`"
      :footer="null"
      width="600px"
      @cancel="skillModalVisible = false"
    >
      <div class="max-h-96 overflow-y-auto">
        <div v-if="availableSkills.length > 0" class="grid grid-cols-2 gap-4">
          <div
            v-for="skill in availableSkills"
            :key="skill.id"
            class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
            :class="{ 'border-blue-500 bg-blue-50': selectedSkills.includes(skill.id) }"
            @click="toggleSkillSelection(skill.id)"
          >
            <div class="flex items-center space-x-3">
              <component :is="skill.icon" class="text-lg" :class="skill.iconColor" />
              <div>
                <div class="font-medium">{{ skill.name }}</div>
                <div class="text-sm text-gray-500">{{ skill.description }}</div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          <div class="text-lg mb-2">🎉</div>
          <div>所有{{ getSkillTypeLabel(currentSkillType) }}技能都已添加</div>
          <div class="text-sm mt-1">您可以在左侧管理已添加的技能</div>
        </div>
      </div>
      <div v-if="availableSkills.length > 0" class="flex justify-end space-x-2 mt-4">
        <a-button @click="skillModalVisible = false">取消</a-button>
        <a-button type="primary" @click="confirmSkillSelection" :disabled="selectedSkills.length === 0">
          确认添加 {{ selectedSkills.length > 0 ? `(${selectedSkills.length})` : '' }}
        </a-button>
      </div>
      <div v-else class="flex justify-end mt-4">
        <a-button @click="skillModalVisible = false">关闭</a-button>
      </div>
    </a-modal>

    <!-- 工作流选择弹窗 -->
    <workflow-selector
      ref="workflowModal"
      :addedWorkflowIds="addedWorkflowIds"
      @add="handleAddWorkflow"
      @remove="handleRemoveWorkflow"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  PlusOutlined,
  ShareAltOutlined,
  DeleteOutlined,
  FileTextOutlined,
  TableOutlined,
  PictureOutlined,
  CodeOutlined,
  DatabaseOutlined,
  BranchesOutlined,
  FolderOutlined,
  ClockCircleOutlined,
  FieldTimeOutlined,
  NumberOutlined,
  SwapOutlined,
  GlobalOutlined,
  CalendarOutlined
} from '@ant-design/icons-vue'
import WorkflowSelector from './WorkflowSelector.vue'

// 定义技能接口
interface Skill {
  id: string
  name: string
  description: string
  icon: string
  iconColor: string
  enabled?: boolean
}

interface Workflow {
  id: string
  name: string
  description?: string
}

interface ModelValue {
  knowledgeSkills: Skill[]
  memorySkills: Skill[]
  timeSkills: Skill[]
  workflows: Workflow[]
}

const props = defineProps({
  modelValue: {
    type: Object as () => ModelValue,
    default: () => ({
      knowledgeSkills: [],
      memorySkills: [],
      timeSkills: [],
      workflows: []
    })
  }
})

const emit = defineEmits(['update:modelValue'])

// 技能数据
const knowledgeSkills = ref<Skill[]>([])
const memorySkills = ref<Skill[]>([])
const timeSkills = ref<Skill[]>([])
const addedWorkflows = ref<Workflow[]>([])

// 弹窗状态
const skillModalVisible = ref(false)
const currentSkillType = ref<'knowledge' | 'memory' | 'time'>('knowledge')
const selectedSkills = ref<string[]>([])

// 工作流相关
const workflowModal = ref()
const addedWorkflowIds = computed(() => addedWorkflows.value.map(w => w.id))

// 可选技能配置
const skillOptions: Record<string, Skill[]> = {
  knowledge: [
    { id: 'text', name: '文本', description: '处理文本内容', icon: 'FileTextOutlined', iconColor: 'text-blue-500' },
    { id: 'table', name: '表格', description: '处理表格数据', icon: 'TableOutlined', iconColor: 'text-green-500' },
    { id: 'image', name: '图片', description: '处理图片内容', icon: 'PictureOutlined', iconColor: 'text-purple-500' },
    { id: 'document', name: '文档', description: '处理文档内容', icon: 'FileTextOutlined', iconColor: 'text-orange-500' },
    { id: 'video', name: '视频', description: '处理视频内容', icon: 'PictureOutlined', iconColor: 'text-red-500' }
  ],
  memory: [
    { id: 'variable', name: '变量', description: '存储临时变量', icon: 'CodeOutlined', iconColor: 'text-blue-500' },
    { id: 'database', name: '数据库', description: '连接数据库', icon: 'DatabaseOutlined', iconColor: 'text-green-500' },
    { id: 'longTerm', name: '长期记忆', description: '长期记忆存储', icon: 'BranchesOutlined', iconColor: 'text-purple-500' },
    { id: 'fileBox', name: '文件盒子', description: '文件存储管理', icon: 'FolderOutlined', iconColor: 'text-orange-500' }
  ],
  time: [
    { id: 'getCurrentTime', name: '获取当前时间', description: '获取系统当前时间', icon: 'FieldTimeOutlined', iconColor: 'text-blue-500' },
    { id: 'getTimestamp', name: '获取时间戳', description: '获取时间戳', icon: 'NumberOutlined', iconColor: 'text-green-500' },
    { id: 'timestampConvert', name: '时间戳转换', description: '时间戳格式转换', icon: 'SwapOutlined', iconColor: 'text-purple-500' },
    { id: 'timezoneConvert', name: '时区转换', description: '不同时区转换', icon: 'GlobalOutlined', iconColor: 'text-orange-500' },
    { id: 'weekdayCalculate', name: '星期几计算', description: '计算星期几', icon: 'CalendarOutlined', iconColor: 'text-red-500' }
  ]
}

// 当前可选技能
const availableSkills = computed(() => {
  const baseSkills = skillOptions[currentSkillType.value] || []
  
  // 根据当前技能类型，过滤掉已添加的技能
  if (currentSkillType.value === 'knowledge') {
    return baseSkills.filter(skill => 
      !knowledgeSkills.value.some(existing => existing.id === skill.id)
    )
  } else if (currentSkillType.value === 'memory') {
    return baseSkills.filter(skill => 
      !memorySkills.value.some(existing => existing.id === skill.id)
    )
  } else if (currentSkillType.value === 'time') {
    return baseSkills.filter(skill => 
      !timeSkills.value.some(existing => existing.id === skill.id)
    )
  }
  
  return baseSkills
})

// 初始化数据
const initializeData = () => {
  if (props.modelValue) {
    knowledgeSkills.value = props.modelValue.knowledgeSkills || []
    memorySkills.value = props.modelValue.memorySkills || []
    timeSkills.value = props.modelValue.timeSkills || []
    addedWorkflows.value = props.modelValue.workflows || []
  }
}

// 监听props变化
watch(() => props.modelValue, initializeData, { immediate: true, deep: true })

// 发送更新
const emitUpdate = () => {
  emit('update:modelValue', {
    knowledgeSkills: knowledgeSkills.value,
    memorySkills: memorySkills.value,
    timeSkills: timeSkills.value,
    workflows: addedWorkflows.value
  })
}

// 显示技能选择弹窗
const showSkillModal = (type: 'knowledge' | 'memory' | 'time') => {
  currentSkillType.value = type
  selectedSkills.value = []
  skillModalVisible.value = true
}

// 获取技能类型标签
const getSkillTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    knowledge: '知识库',
    memory: '记忆',
    time: '时间'
  }
  return labels[type] || ''
}

// 切换技能选择
const toggleSkillSelection = (skillId: string) => {
  const index = selectedSkills.value.indexOf(skillId)
  if (index > -1) {
    selectedSkills.value.splice(index, 1)
  } else {
    selectedSkills.value.push(skillId)
  }
}

// 确认技能选择
const confirmSkillSelection = () => {
  const skillsToAdd = availableSkills.value.filter(skill => 
    selectedSkills.value.includes(skill.id)
  )

  if (currentSkillType.value === 'knowledge') {
    // 过滤掉已存在的技能
    const newSkills = skillsToAdd.filter(skill => 
      !knowledgeSkills.value.some(existing => existing.id === skill.id)
    )
    knowledgeSkills.value.push(...newSkills)
  } else if (currentSkillType.value === 'memory') {
    // 过滤掉已存在的技能
    const newSkills = skillsToAdd.filter(skill => 
      !memorySkills.value.some(existing => existing.id === skill.id)
    )
    memorySkills.value.push(...newSkills.map(skill => ({ ...skill, enabled: false })))
  } else if (currentSkillType.value === 'time') {
    // 过滤掉已存在的技能
    const newSkills = skillsToAdd.filter(skill => 
      !timeSkills.value.some(existing => existing.id === skill.id)
    )
    timeSkills.value.push(...newSkills.map(skill => ({ ...skill, enabled: false })))
  }

  skillModalVisible.value = false
  emitUpdate()
}

// 移除技能
const removeSkill = (skillId: string) => {
  knowledgeSkills.value = knowledgeSkills.value.filter(skill => skill.id !== skillId)
  memorySkills.value = memorySkills.value.filter(skill => skill.id !== skillId)
  timeSkills.value = timeSkills.value.filter(skill => skill.id !== skillId)
  emitUpdate()
}

// 切换技能开关
const toggleSkill = (skillId: string, enabled: boolean) => {
  const memorySkill = memorySkills.value.find(skill => skill.id === skillId)
  if (memorySkill) {
    memorySkill.enabled = enabled
  }
  
  const timeSkill = timeSkills.value.find(skill => skill.id === skillId)
  if (timeSkill) {
    timeSkill.enabled = enabled
  }
  
  emitUpdate()
}

// 显示工作流弹窗
const showWorkflowModal = () => {
  if (workflowModal.value) {
    workflowModal.value.show()
  }
}

// 添加工作流
const handleAddWorkflow = (workflow: Workflow) => {
  if (!addedWorkflows.value.find(w => w.id === workflow.id)) {
    addedWorkflows.value.push(workflow)
    emitUpdate()
  }
}

// 移除工作流
const removeWorkflow = (workflow: Workflow) => {
  const index = addedWorkflows.value.findIndex(w => w.id === workflow.id)
  if (index !== -1) {
    addedWorkflows.value.splice(index, 1)
    if (workflowModal.value) {
      workflowModal.value.updateWorkflowStatus(workflow.id, false)
    }
    emitUpdate()
  }
}

// 从弹窗移除工作流
const handleRemoveWorkflow = (workflow: Workflow) => {
  removeWorkflow(workflow)
}
</script>

<style scoped>
:deep(.ant-switch) {
  min-width: 28px;
}

:deep(.ant-btn) {
  border-radius: 6px;
}

.skill-item {
  transition: all 0.3s ease;
}

.skill-item:hover {
  background-color: #f5f5f5;
}
</style> 