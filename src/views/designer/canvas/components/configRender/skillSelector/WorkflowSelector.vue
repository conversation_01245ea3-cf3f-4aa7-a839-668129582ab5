<template>
  <a-modal
    v-model:open="visible"
    title="添加工作流"
    :footer="null"
    width="800px"
    class="workflow-modal"
    @cancel="handleCancel"
  >
    <div class="flex h-[500px]">
      <!-- 右侧内容区域 -->
      <div class="flex-1 p-4">
        <!-- 筛选区域 -->
        <a-input-search
          v-model:value="name"
          placeholder="搜索"
          class="mb-4"
          @search="handleSearch"
        >
          <template #prefix>
            <SearchOutlined class="text-gray-400" />
          </template>
        </a-input-search>

        <!-- 工作流列表 -->
        <div class="min-h-[300px] relative">
          <div
            v-if="loading"
            class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75"
          >
            <a-spin />
          </div>
          <div class="overflow-y-auto h-[370px]">
            <div
              v-for="item in filteredWorkflows"
              :key="item.id"
              class="workflow-item"
            >
              <div class="flex items-center p-4 hover:bg-gray-50 dark:hover:!bg-primary-hover rounded cursor-pointer">
                <div class="w-10 h-10 bg-green-50 rounded flex items-center justify-center mr-4">
                  <ShareAltOutlined class="text-green-500 text-lg" />
                </div>
                <div class="flex-1">
                  <div class="flex items-center justify-between">
                    <div>
                      <div class="flex items-center gap-2">
                        <h3 class="text-base font-medium">
                          {{ item.name }}
                        </h3>
                        <a-tag
                          color="success"
                          class="flex items-center"
                        >
                          <CheckCircleOutlined class="mr-1" />
                          已发布
                        </a-tag>
                      </div>
                      <div class="text-gray-500 text-sm mt-1">
                        {{ item.description }}
                      </div>
                    </div>
                    <a-button
                      :type="item.added ? 'default' : 'primary'"
                      size="middle"
                      class="min-w-[88px] workflow-btn"
                      :class="{ 'added': item.added }"
                      @click="handleAdd(item)"
                    >
                      <span>{{ getButtonText(item) }}</span>
                    </a-button>
                  </div>
                  <div class="text-gray-400 text-sm mt-2">
                    发布于 {{ item.publishDate }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-4 flex justify-end">
            <a-pagination
              v-model:current="current"
              v-model:pageSize="pageSize"
              :total="total"
              :show-total="total => `共 ${total} 条`"
              :show-size-changer="true"
              @change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  SearchOutlined,
  ShareAltOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import { getCanvasPage } from '@/apis/designer'

interface Workflow {
  id: string
  name: string
  description: string
  type: string
  status: string
  publishDate: string
  added: boolean
}

interface CanvasPageParams {
  current?: number
  size?: number
  name?: string
  type?: string
  status?: string
}

const props = defineProps<{
  addedWorkflowIds: string[]
}>()

const emit = defineEmits(['add', 'remove'])

const visible = ref(false)
const name = ref('')
const selectedStatus = ref('published')
const workflows = ref<Workflow[]>([])
const current = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 监听 addedWorkflowIds 变化，更新工作流状态
watch(
  () => props.addedWorkflowIds,
  (newIds) => {
    workflows.value.forEach((workflow) => {
      workflow.added = newIds.includes(workflow.id)
    })
  },
  { immediate: true }
)

// 过滤工作流列表
const filteredWorkflows = computed(() => workflows.value)

// 获取工作流列表数据
const fetchWorkflows = async () => {
  try {
    loading.value = true
    const params = {
      current: current.value,
      size: pageSize.value,
      name: name.value
    }

    const response = await getCanvasPage(params)
    if (response.code === 200 && response.data) {
      workflows.value = response.data.records.map((record: any) => ({
        id: record.id,
        name: record.name || '未命名工作流',
        description: record.description || '暂无描述',
        type: record.type || '',
        status: record.status || 'unPublished',
        publishDate: record.updateTime || new Date().toISOString(),
        added: props.addedWorkflowIds.includes(record.id)
      }))
      total.value = response.data.total
    }
  } catch (error) {
    console.error('获取工作流列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = async (value: string) => {
  name.value = value
  current.value = 1
  await fetchWorkflows()
}

// 分页处理
const handlePageChange = async (page: number, size: number) => {
  current.value = page
  pageSize.value = size
  await fetchWorkflows()
}

// 显示弹窗
const show = async () => {
  visible.value = true
  await fetchWorkflows()
}

// 处理添加工作流
const handleAdd = (workflow: Workflow) => {
  if (workflow.added) {
    workflow.added = false
    emit('remove', workflow)
  } else {
    workflow.added = true
    emit('add', workflow)
  }
}

// 处理取消
const handleCancel = () => {
  visible.value = false
}

// 获取按钮文字
const getButtonText = (workflow: Workflow) => {
  return workflow.added ? '已添加' : '添加'
}

// 更新工作流状态
const updateWorkflowStatus = (workflowId: string, added: boolean) => {
  const workflow = workflows.value.find((w) => w.id === workflowId)
  if (workflow) {
    workflow.added = added
  }
}

defineExpose({
  show,
  updateWorkflowStatus
})
</script>

<style scoped>
.workflow-modal :deep(.ant-modal-content) {
  padding: 0;
}

.workflow-modal :deep(.ant-modal-header) {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0;
}

.workflow-modal :deep(.ant-modal-body) {
  padding: 0;
}

.workflow-item {
  border-bottom: 1px solid #f0f0f0;
}

.workflow-item:hover {
  background-color: #f5f5f5;
}

.workflow-item .w-10 {
  background-color: #ecfdf5 !important;
}

:deep(.ant-select-selector) {
  background-color: transparent !important;
}

:deep(.ant-tag) {
  margin-right: 0;
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
}

.text-gray-500 {
  font-size: 13px;
}

.text-gray-400 {
  font-size: 12px;
}

.workflow-btn {
  transition: all 0.3s;
  position: relative;
}

.workflow-btn.added {
  background-color: white;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.88);
  @apply dark:!bg-global-background dark:!border-border;
}

.workflow-btn.added:hover {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: white !important;
}

.workflow-btn.added span {
  display: inline-block;
  transition: opacity 0.3s;
}

.workflow-btn.added:hover span {
  opacity: 0;
}

.workflow-btn.added:hover::before {
  content: '从智能体移除';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: white;
}
</style> 