<template>
  <div class="w-full">
    <a-radio-group 
      :value="selectedValue"
      @update:value="(val) => {
        selectedValue = val;
        emit('update:modelValue', val);
      }"
      class="w-full"
    >
      <div class="flex flex-wrap items-center gap-x-6 gap-y-3">
        <a-radio 
          v-for="option in options" 
          :key="option.value" 
          :value="option.value"
          class="!flex items-center !m-0"
        >
          {{ option.label }}
        </a-radio>
      </div>
    </a-radio-group>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { PropType } from 'vue'

interface RadioOption {
  label: string
  value: string | number
}

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  options: {
    type: Array as PropType<RadioOption[]>,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])
const selectedValue = ref(props.modelValue)

// 监听内部值变化
watch(selectedValue, (newVal) => {
  emit('update:modelValue', newVal)
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  selectedValue.value = newVal
})
</script>

<style scoped>
:deep(.ant-radio-wrapper) {
  margin-right: 0 !important;
  white-space: nowrap;
}
</style> 