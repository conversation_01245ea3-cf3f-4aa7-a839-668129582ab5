<template>
    <div class="crawler-config">
        <a-form-item class="w-full">
            <div class="w-full">
                <a-input :value="settingsIdVal" placeholder="选择一个爬虫，当触发器被激活时，将会执行该爬虫。" readonly
                    class="cursor-not-allowed bg-gray-100" @click.stop="showCrawlerSelection" />
            </div>
            <a-modal :open="isShowCrawlerSelection" @cancel="handleCancel" title="选择爬虫" width="1000px">
                <crawlerSelection ref="crawlerSelectionRef" />
                <template #footer>
                    <a-button type="primary" @click="handleOk">确认</a-button>
                </template>
            </a-modal>
            <div v-if="selectedCrawlerTitle" class="mt-2 text-gray-500 text-sm break-all">
                选择的爬虫: {{ selectedCrawlerTitle }}
            </div>
        </a-form-item>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import crawlerSelection from './crawlerSelection.vue'
import type { CrawlerConfig } from '@/apis/crawler'
import { useDesignerStore } from '@/store/designer'

const designerStore = useDesignerStore()

// 触发时间类型
interface TimerConfig {
    settingsId: string
}
const props = defineProps({
    modelValue: {
        type: Object as PropType<TimerConfig>,
        default: () => ({})
    },
})
const emit = defineEmits(['update:modelValue'])

const settingsIdVal = ref(props.modelValue?.settingsId || '')

// 状态
const isShowCrawlerSelection = ref(false)
const crawlerSelectionRef = ref()
const selectedCrawler = ref<CrawlerConfig | null>(null)


const selectedCrawlerTitle = computed(() => {
    if (selectedCrawler.value) {
        return `${selectedCrawler.value.name} - ${selectedCrawler.value.crawlUrl}`
    }
    return ''
})

// 方法
const showCrawlerSelection = () => {
    isShowCrawlerSelection.value = true
}

const handleCancel = () => {
    isShowCrawlerSelection.value = false
}

const updateField = (field, value) => {
    emit('update:modelValue', Object.assign(designerStore.currentNodeData?.properties, {
        [field]: value
    }))
}

const handleOk = () => {
    if (crawlerSelectionRef.value?.selectedRow) {
        settingsIdVal.value = crawlerSelectionRef.value.selectedRow?.id
        selectedCrawler.value = crawlerSelectionRef.value.selectedRow
        updateField('settingsId', settingsIdVal.value)
    }
    isShowCrawlerSelection.value = false
}

onMounted(() => {
    const val = designerStore.currentNodeData?.properties
    if (val && Object.keys(val).length > 0) {
        settingsIdVal.value = val?.settingsId || null
    }
})

// 暴露方法
defineExpose({
    selectedCrawler
})
</script>

<style scoped>
.crawler-config {
    width: 100%;
}

.w-full {
    width: 100%;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

.bg-gray-100 {
    background-color: #f3f4f6;
}

.mt-2 {
    margin-top: 0.5rem;
}

.text-gray-500 {
    color: #6b7280;
}

.text-sm {
    font-size: 0.875rem;
}

.break-all {
    word-break: break-all;
}
</style>