
// 爬虫选择列定义
export const getCrawlerColumns = (page = { current: 1, pageSize: 10 }) => [
    {
      title: '序号',
      width: 80,
      align: 'center',
      customRender: ({ index }) => {
        // 手动计算全局序号
        return (page.current - 1) * page.pageSize + index + 1
      },
    },
    {
      title: '爬虫名称',
      dataIndex: 'title',
      width: 200,
      key: 'title',
    },
    {
      title: '目标网址',
      dataIndex: 'crawl_url',
      width: 300,
      ellipsis: true,
      key: 'crawl_url',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      key: 'status',
      customRender: ({ text }) => {
        const statusMap = {
          0: { text: '未启动', color: 'default' },
          1: { text: '运行中', color: 'processing' },
          2: { text: '已完成', color: 'success' },
          3: { text: '失败', color: 'error' }
        }
        const status = statusMap[text] || statusMap[0]
        return h('a-tag', { color: status.color }, status.text)
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
      key: 'createTime',
      customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    }
  ]
  
  // 导出爬虫选择列
  export const CRAWLER_COLUMNS = getCrawlerColumns()