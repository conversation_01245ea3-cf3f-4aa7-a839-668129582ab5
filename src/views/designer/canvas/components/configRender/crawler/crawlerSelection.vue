<template>
    <div class="h-full bg-global-background">
      <BaseTable
        :search-schema="searchSchema"
        :columns="COLUMNS"
        :dataSource="crawlerList"
        :loading="loading"
        :total="total"
        :current="currentPage"
        :pageSize="pageSize"
        :showPagination="true"
        :scroll="{ x: 'max-content' }"
        :row-selection="rowSelection"
        :table-props="{
          rowKey: record => record.id,
          rowSelection: rowSelection
        }"
        @search="handleSearch"
        @reset="handleReset"
        @pageChange="handlePageChange"
      >

      </BaseTable>
  
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { message } from 'ant-design-vue'
  import BaseTable from '@/components/BaseTable/index.vue'
  import {
    getCrawlerPage,
    type CrawlerConfig
  } from '@/apis/crawler'
  
  // 表格列定义
  const COLUMNS = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '爬虫名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '目标网址',
      dataIndex: 'crawlUrl',
      key: 'crawlUrl',
      width: 300,
      ellipsis: true,
    },
  ]
  
  // 搜索配置
  const searchSchema = ref([
    {
      key: 'name',
      label: '爬虫名称',
      type: 'input' as const,
      placeholder: '请输入爬虫名称'
    },
  ])
  
  // 使用从API文件导入的类型
  
  // 数据状态
  const crawlerList = ref<CrawlerConfig[]>([])
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  
  // 事件处理函数
  const handleSearch = (params: any) => {
    currentPage.value = 1
    fetchData(params)
  }
  
  const handleReset = () => {
    currentPage.value = 1
    fetchData()
  }
  
  const handlePageChange = ({ current, pageSize: size, searchForm }: { current: number; pageSize: number; searchForm: any }) => {
    currentPage.value = current
    pageSize.value = size
    fetchData(searchForm)
  }
  
  // API 函数
  const fetchData = async (params: any = {}) => {
    loading.value = true
    try {
      const response: any = await getCrawlerPage({
        page: currentPage.value,
        pageSize: pageSize.value,
        ...params
      })
  
      if (response?.code === 200) {
        crawlerList.value = response.data?.records.filter((item) => item.status == 'active') || []
        total.value = response.data?.total || 0
      } else {
        message.error(response?.message || '获取数据失败')
      }
  
    } catch (error) {
      message.error('获取数据失败：' + (error as Error).message)
    } finally {
      loading.value = false
    }
  }

  // 选择表单配置
  const selectedRowKeys = ref([]);
  // 选择行
  const selectedRow = ref(null)
  const rowSelection = computed(() => ({
    type: 'radio',
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys) => {
      selectedRowKeys.value = keys;
    },
    onSelect: (record) => {
      selectedRow.value = record
    }
  }));
  
  defineExpose({
    selectedRowKeys,
    selectedRow
  });
  
  // 初始化
  onMounted(() => {
    fetchData()
  })
  </script>
  
  
  