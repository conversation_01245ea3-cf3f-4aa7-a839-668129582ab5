<template>
  <div class="w-full relative">
    <div class="pl-4">
      <div class="text-gray-500 text-[14px] flex mb-2">
        <span class="w-[220px] text-[12px] dark:text-[var(--grey-auxiliary-color)]">变量名</span>
        <span class="w-[160px] text-[12px] dark:text-[var(--grey-auxiliary-color)]">变量类型</span>
        <span class="w-[200px] text-[12px] dark:text-[var(--grey-auxiliary-color)]">描述</span>
      </div>

      <div v-for="(param, index) in parameters.data" :key="index" class="flex items-center mb-2">
        <span class="w-[220px] text-[12px] dark:text-[var(--grey-auxiliary-color)]">{{ param.name }}</span>
        <span class="w-[160px] text-[12px] dark:text-[var(--grey-auxiliary-color)]">{{ param.type }}</span>
        <span class="w-[200px] text-[12px] dark:text-[var(--grey-auxiliary-color)]">{{ param.description }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { PropType } from 'vue'

interface Parameter {
  name: string
  type: string
  description?: string
}

interface ParameterGroups {
  data: Parameter[]
}

const props = defineProps({
  modelValue: {
    type: Object as PropType<ParameterGroups>,
    default: () => ({
      data: []
    })
  },
  defaultParams: {
    type: Object as PropType<ParameterGroups>,
    default: () => ({
      data: []
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const parameters = ref<ParameterGroups>(props.defaultParams)

watch(() => props.defaultParams, (newVal) => {
  parameters.value = newVal
}, { deep: true })

watch(parameters, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })
</script>
