import type * as Monaco from 'monaco-editor'

export const registerGroovy = (monaco: typeof Monaco) => {
  monaco.languages.register({ id: 'groovy' })

  monaco.languages.setMonarchTokensProvider('groovy', {
    defaultToken: '',
    tokenPostfix: '.groovy',

    keywords: [
      'class', 'interface', 'trait', 'enum', 'package', 'import', 'extends',
      'def', 'void', 'null', 'true', 'false', 'new', 'super', 'this',
      'if', 'else', 'for', 'while', 'do', 'break', 'continue', 'return',
      'try', 'catch', 'finally', 'throw', 'throws', 'assert', 'synchronized'
    ],

    typeKeywords: [
      'boolean', 'char', 'byte', 'short', 'int', 'long', 'float', 'double',
      'String', 'Object', 'List', 'Map', 'Set'
    ],

    operators: [
      '=', '>', '<', '!', '~', '?', ':', '==', '<=', '>=', '!=', '&&', '||',
      '++', '--', '+', '-', '*', '/', '&', '|', '^', '%', '<<', '>>', '>>>',
      '+=', '-=', '*=', '/=', '&=', '|=', '^=', '%=', '<<=', '>>=', '>>>='
    ],

    symbols: /[=><!~?:&|+\-*\/\^%]+/,
    escapes: /\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,

    tokenizer: {
      root: [
        [/[a-zA-Z_$][\w$]*/, {
          cases: {
            '@typeKeywords': 'type',
            '@keywords': 'keyword',
            '@default': 'identifier'
          }
        }],
        [/[ \t\r\n]+/, ''],
        [/\/\*/, 'comment', '@multiLineComment'],
        [/\/\/.*$/, 'comment'],
        [/"([^"\\]|\\.)*$/, 'string.invalid'],
        [/'([^'\\]|\\.)*$/, 'string.invalid'],
        [/"/, 'string', '@string."'],
        [/'/, 'string', '@string.\''],
        [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
        [/0[xX][0-9a-fA-F]+/, 'number.hex'],
        [/\d+/, 'number'],
        [/[{}()\[\]]/, '@brackets'],
        [/@symbols/, {
          cases: {
            '@operators': 'operator',
            '@default': ''
          }
        }]
      ],

      multiLineComment: [
        [/[^/*]+/, 'comment'],
        [/\*\//, 'comment', '@pop'],
        [/[/*]/, 'comment']
      ],

      string: [
        [/[^\\"']+/, 'string'],
        [/@escapes/, 'string.escape'],
        [/\\./, 'string.escape.invalid'],
        [/["']/, {
          cases: {
            '$#==$S2': { token: 'string', next: '@pop' },
            '@default': 'string'
          }
        }]
      ]
    }
  })
}