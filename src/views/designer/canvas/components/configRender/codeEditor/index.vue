<template>
  <div class="editor-wrapper" :class="{ 'is-fullscreen': isFullscreen }">
    <div class="editor-header" :class="{ 'header-fullscreen': isFullscreen }">
      <div class="title">{{currentLanguage}}</div>
      <div class="flex-spacer"></div>
      <span v-if="isFullscreen" class="fullscreen-tip">
        <RollbackOutlined /> 按ESC键或点击按钮退出全屏
      </span>
      <a-button type="text" class="fullscreen-btn" @click="toggleFullscreen">
        <template #icon>
          <FullscreenExitOutlined v-if="isFullscreen" />
          <FullscreenOutlined v-else />
        </template>
      </a-button>
    </div>
    <div ref="editorContainer" class="monaco-editor-container"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import loader from '@monaco-editor/loader'
import { FullscreenOutlined, FullscreenExitOutlined, RollbackOutlined } from '@ant-design/icons-vue'
import { useDesignerStore } from '@/store/designer'
import type * as Monaco from 'monaco-editor'
import { registerGroovy } from './languages/groovy'

interface Props {
  modelValue: string
  language: string
}

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  language: {
    type: String,
    default: 'javascript'
  }
})
const emit = defineEmits(['update:modelValue'])

const editorContainer = ref<HTMLElement | null>(null)
const isFullscreen = ref(false)
// 改为普通变量声明
let editor: Monaco.editor.IStandaloneCodeEditor | null = null
let monacoInstance: typeof Monaco | null = null

// Store
const designerStore = useDesignerStore()

// 计算当前语言
const currentLanguage = computed(() => {
  const configLanguage = (designerStore.currentNodeData?.properties?.language || designerStore.currentNodeData?.properties?.dbType) ?? props.language
  const lang = configLanguage === 'js' ? 'javascript' : configLanguage
  return lang
})

const initMonaco = async () => {
  try {
    monacoInstance = await loader.init()
    // 注册 Groovy 语言支持
    registerGroovy(monacoInstance)
    return true
  } catch (error) {
    console.error('Monaco initialization failed:', error)
    return false
  }
}

const createEditor = () => {
  if (!editorContainer.value || !monacoInstance) return

  editor = monacoInstance.editor.create(editorContainer.value, {
    value: props.modelValue,
    language: currentLanguage.value,
    theme: 'vs-dark',
    automaticLayout: true,
    minimap: {
      enabled: false
    },
    scrollBeyondLastLine: false,
    fontSize: 14,
    tabSize: 2,
    contextmenu: false,
    quickSuggestions: false
  })

  // 监听内容变化
  editor.onDidChangeModelContent(() => {
    const value = editor?.getValue()
    emit('update:modelValue', value)
  })

  // 添加自定义键盘事件监听
  editor.addCommand(monacoInstance.KeyCode.Escape, () => {
    if (isFullscreen.value) {
      toggleFullscreen()
    }
  })
}

const initEditor = async () => {
  const initialized = await initMonaco()
  if (initialized) {
    createEditor()
  }
}

onMounted(() => initEditor())

// 监听语言变化
watch(currentLanguage, () => monacoInstance?.editor.setModelLanguage(editor?.getModel()!, currentLanguage.value))

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  // 调整编辑器大小以适应新的容器尺寸
  setTimeout(() => {
    editor?.layout()
  }, 100)
}

// 添加键盘事件监听
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && isFullscreen.value) {
    toggleFullscreen()
  }
}

onMounted(() => {
  window.addEventListener('keydown', handleKeyDown)
})

onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
  }
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.editor-wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
}

.editor-wrapper.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: #1e1e1e;
  padding: 16px;
}

.editor-header {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  gap: 8px;
  background-color: rgb(30, 30, 30);
  border: 1px solid #ccc;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
}

.header-fullscreen {
  background-color: transparent;
  border: none;
}

.title {
  color: #fff;
  font-size: 12px;
}

.fullscreen-tip {
  color: #8c8c8c;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.flex-spacer {
  flex: 1;
}

.fullscreen-btn {
  color: #fff;
  padding: 0;
}

.fullscreen-btn:hover {
  background-color: transparent !important;
  color: #fff !important;
  @apply dark:!bg-area-fill;
}

.monaco-editor-container {
  width: 100%;
  height: 300px;
  border: 1px solid #ccc;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.is-fullscreen .monaco-editor-container {
  height: calc(100vh - 64px);
  border: none;
  border-radius: 8px;
  flex: 1;
}

.is-fullscreen .editor-header {
  border-radius: 8px;
}
</style>
