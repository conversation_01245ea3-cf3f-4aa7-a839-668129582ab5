<template>
  <div class="file-upload">
    <a-input
      @click="onSelectFile"
      readonly
      v-model:value="inputValue"
      placeholder="点击上传文件"
      class="w-full text-[12px] cursor-pointer"
      :spellcheck="false"
    >
    </a-input>
  </div>
</template>

<script setup lang="ts">
import { selectFile } from '@/utils/selectFile'
import { uploadFile } from '@/apis/designer'
import { message } from 'ant-design-vue'

const inputValue = defineModel()

// 选择文件
const onSelectFile = async () => {
  const [file] = await selectFile()

  // 上传文件
  const formData = new FormData()
  formData.append('file', file)
  try {
    const { data } = await uploadFile(formData)
    inputValue.value = data.url
  } catch (error) {
    message.error('文件上传失败！')
  }
}
</script>
