<template>
  <div class="w-full">
    <a-form-item
      :label="config.label || '验证码'"
      :name="config.field || 'captchaCode'"
      :rules="mergedRules"
      :validateFirst="true"
      :validateStatus="errorMessage ? 'error' : ''"
      :help="errorMessage"
    >
      <div class="flex flex-col gap-3">
        <VerificationInput
          ref="verificationInputRef"
          :model-value="codeArray"
          :length="config.codeLength || 6"
          :numeric-only="config.numericOnly"
          :disabled="isLocked"
          :auto-focus="config.autoFocus"
          @update:model-value="handleCodeUpdate"
          @complete="handleComplete"
        />
        
        <!-- 剩余尝试次数提示 -->
        <div v-if="maxAttempts > 0" class="text-gray-500 text-xs text-right">
          剩余尝试次数：{{ maxAttempts - attemptCount }}
        </div>
      </div>
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { Form } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form/interface'
import VerificationInput from '@/views/login/components/VerificationInput.vue'

type VerificationStatus = 'init' | 'verifying' | 'success' | 'error'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  config: {
    type: Object,
    default: () => ({
      autoVerify: true,
      apiVerify: true,
      verifyUrl: '/api/captcha/verify',
      codeLength: 6,
      numericOnly: true,
      maxAttempts: 5,
      lockTime: 30,
      autoFocus: true,
      successRedirect: ''
    })
  }
})

const emit = defineEmits(['update:modelValue', 'verify-success', 'verify-error', 'update:status'])

// 状态管理
const status = ref<VerificationStatus>('init')
const codeArray = ref<string[]>([])
const errorMessage = ref('')
const attemptCount = ref(0)
const isLocked = ref(false)
const unlockTimer = ref<number | undefined>()
const verificationInputRef = ref<InstanceType<typeof VerificationInput> | null>(null)

// 派生状态
const remainingAttempts = computed(() => Math.max(0, props.config.maxAttempts - attemptCount.value))
const mergedRules = computed<Rule[]>(() => [
  { required: true, message: '请输入验证码' },
  { validator: validateCaptcha }
])

// 生命周期管理
onUnmounted(() => {
  if (unlockTimer.value !== undefined) {
    clearTimeout(unlockTimer.value)
  }
})

// 值更新处理
const handleCodeUpdate = (value: string[]) => {
  codeArray.value = value
  const code = value.join('')
  emit('update:modelValue', code)
  
  // 自动触发基础校验
  if (code.length === props.config.codeLength) {
    status.value = 'init'
    errorMessage.value = ''
  }
}

// 输入完成处理
const handleComplete = async (code: string[]) => {
  if (!props.config.autoVerify) return
  await validateCaptcha()
}

// 核心校验逻辑
const validateCaptcha = async (): Promise<void> => {
  if (status.value === 'verifying') return
  
  const code = codeArray.value.join('')
  
  // 基础校验
  if (!validateBasic(code)) {
    status.value = 'error'
    return Promise.reject(errorMessage.value)
  }

  // API校验
  if (props.config.apiVerify) {
    return validateWithAPI(code)
  }
  
  return Promise.resolve()
}

// 基础校验
const validateBasic = (code: string): boolean => {
  // 空值校验
  if (!code) {
    errorMessage.value = '请输入验证码'
    return false
  }

  // 长度校验
  if (code.length !== props.config.codeLength) {
    errorMessage.value = `验证码必须为${props.config.codeLength}位`
    return false
  }

  // 数字格式校验
  if (props.config.numericOnly && !/^\d+$/.test(code)) {
    errorMessage.value = '验证码必须为纯数字'
    return false
  }

  return true
}

// API校验
const validateWithAPI = async (code: string): Promise<void> => {
  status.value = 'verifying'
  
  try {
    const response = await fetch(props.config.verifyUrl, {
      method: 'POST',
      body: JSON.stringify({ code })
    })

    const result = await response.json()
    
    if (result.success) {
      handleSuccess(result)
      return Promise.resolve()
    } else {
      handleError(result.message)
      return Promise.reject(result.message)
    }
  } catch (error) {
    handleError('验证服务不可用')
    return Promise.reject('验证服务不可用')
  }
}

// 校验成功处理
const handleSuccess = (result: any) => {
  status.value = 'success'
  errorMessage.value = ''
  attemptCount.value = 0
  emit('verify-success', result)
}

// 校验失败处理
const handleError = (message: string) => {
  status.value = 'error'
  errorMessage.value = message
  attemptCount.value++

  // 锁定处理
  if (remainingAttempts.value <= 0) {
    isLocked.value = true
    unlockTimer.value = window.setTimeout(() => {
      isLocked.value = false
      attemptCount.value = 0
    }, props.config.lockTime * 1000)
  }

  emit('verify-error', message)
}

// 暴露方法
defineExpose({
  triggerValidation: validateCaptcha,
  reset: () => {
    codeArray.value = []
    errorMessage.value = ''
    attemptCount.value = 0
    isLocked.value = false
    if (verificationInputRef.value) {
      verificationInputRef.value.reset()
    }
  }
})
</script>