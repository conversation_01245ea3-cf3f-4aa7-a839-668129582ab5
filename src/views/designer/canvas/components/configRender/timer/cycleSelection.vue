<template>
    <div class="flex rounded-lg overflow-hidden shadow-md bg-white w-[360px]">
        <div class="min-w-[120px] max-h-[240px] overflow-y-auto border-r border-gray-100 last:border-r-0">
            <div v-for="(item, idx) in periodTypes" 
                 :key="item" 
                 :class="[
                    'px-4 py-2.5 cursor-pointer select-none hover:bg-gray-50',
                    idx === periodTypeIdx ? 'bg-blue-50 text-blue-600' : ''
                 ]"
                 @click="selectPeriodType(idx)">
                {{ item }}
                <span class="float-right ml-2 text-gray-400">
                    <svg width="18" height="18" viewBox="0 0 1024 1024" fill="none">
                        <path d="M384 192l256 320-256 320" stroke="#97989C" stroke-width="64" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </span>
            </div>
        </div>
        <div class="min-w-[120px] max-h-[240px] overflow-y-auto border-r border-gray-100 last:border-r-0" 
             v-if="showSecondPanel">
            <div v-for="(item, idx) in currentPeriodOptions" 
                 :key="item"
                 :class="[
                    'px-4 py-2.5 cursor-pointer select-none hover:bg-gray-50',
                    idx === periodOptionIdx ? 'bg-blue-50 text-blue-600' : ''
                 ]" 
                 @click="selectPeriodOption(idx)">
                {{ item }}
                <span class="float-right ml-2 text-gray-400">
                    <svg width="18" height="18" viewBox="0 0 1024 1024" fill="none">
                        <path d="M384 192l256 320-256 320" stroke="#97989C" stroke-width="64" stroke-linecap="round"
                            stroke-linejoin="round" />
                    </svg>
                </span>
            </div>
        </div>
        <div class="min-w-[120px] max-h-[240px] overflow-y-auto border-r border-gray-100 last:border-r-0" 
             v-if="showThirdPanel">
            <div v-for="(item, idx) in timeOptions" 
                 :key="item" 
                 :class="[
                    'px-4 py-2.5 cursor-pointer select-none hover:bg-gray-50',
                    idx === timeIdx ? 'bg-blue-50 text-blue-600' : ''
                 ]"
                 @click="selectTime(idx)">
                {{ item }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const emit = defineEmits(['selected'])

const periodTypes = ['每日触发', '每周触发', '每月触发', '间隔触发']
const weekOptions = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
const monthOptions = Array.from({ length: 31 }, (_, i) => `${i + 1}日`)
const intervalOptions = ['2日', '3日', '4日', '5日', '6日']

// 定义频率类型映射
const frequencyTypeMap = {
    0: 'daily',    // 每日触发
    1: 'weekly',   // 每周触发
    2: 'monthly',  // 每月触发
    3: 'interval'  // 间隔触发
}

// 定义频率天数映射
const frequencyDayMap = {
    0: [1],        // 每日触发固定为1
    1: [0, 1, 2, 3, 4, 5, 6],  // 每周触发对应周日到周六
    2: Array.from({ length: 31 }, (_, i) => i + 1),  // 每月触发对应1-31日
    3: [2, 3, 4, 5, 6]  // 间隔触发对应2-6日
}

const periodTypeIdx = ref<number | null>(null)
const periodOptionIdx = ref<number | null>(null)
const timeIdx = ref<number | null>(null)

const timeOptions = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00:00`)

const currentPeriodOptions = computed(() => {
    if (periodTypeIdx.value === 1) return weekOptions
    if (periodTypeIdx.value === 2) return monthOptions
    if (periodTypeIdx.value === 3) return intervalOptions
    return []
})

const showSecondPanel = computed(() => periodTypeIdx.value !== null && currentPeriodOptions.value.length > 0)
const showThirdPanel = computed(() => {
    if (periodTypeIdx.value === 0) return periodTypeIdx.value !== null
    return showSecondPanel.value && periodOptionIdx.value !== null
})

const selectPeriodType = (idx: number) => {
    periodTypeIdx.value = idx
    periodOptionIdx.value = null
    timeIdx.value = null
}

const selectPeriodOption = (idx: number) => {
    if (periodOptionIdx.value === 0) {
        const timerConfig = {
            frequencyType: frequencyTypeMap[periodTypeIdx.value || 0],
            frequencyDay: frequencyDayMap[0],
            triggerTime: timeOptions[idx]
        }
        emit('selected', timerConfig)
    }
    periodOptionIdx.value = idx
    timeIdx.value = null
}

const selectTime = (idx: number) => {
    timeIdx.value = idx
    const timerConfig = {
        frequencyType: frequencyTypeMap[periodTypeIdx.value || 0],
        frequencyDay: frequencyDayMap[periodTypeIdx.value || 0][periodOptionIdx.value || 0],
        triggerTime: timeOptions[idx]
    }
    emit('selected', timerConfig)
}
</script>
