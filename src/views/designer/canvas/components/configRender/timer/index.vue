<template>
    <a-form layout="vertical" class="w-full">
        <a-form-item label="时区" class="w-full">
            <a-select :value="timezoneVal || null" @update:value="(val) => handleUpdateField(val)" placeholder="请选择时区"
                class="w-full flex-1" :options="timezoneOptions" showSearch />
        </a-form-item>
        <a-form-item label="触发时间" class="w-full">
            <div class="flex w-full">
                <a-select :value="triggerTimeTypeVal || null" @change="handleTypeChange" placeholder="请选择触发事件类型"
                    class="min-w-20" :class="triggerTimeTypeVal ? 'w-auto' : 'w-full'">
                    <a-select-option value="preset">选择预设时间</a-select-option>
                    <a-select-option value="cron">使用Cron表达式</a-select-option>
                </a-select>
                <template v-if="triggerTimeTypeVal === 'cron'">
                    <a-input :value="cronExpressionVal" @update:value="(val) => handleUpdateCron(val)"
                        placeholder="示例：0 0 18 * * * 表示每天 18 点执行。" class="flex-1 ml-2" />
                </template>
                <template v-else-if="triggerTimeTypeVal === 'preset'">
                    <a-input :value="triggerTimeDesc" placeholder="触发时间" readonly
                        class="flex-1 ml-2 cursor-not-allowed bg-gray-100" @click.stop="showSelection" />
                </template>
            </div>
            <div v-if="isShowSelect" @click.stop class="absolute top-full right-0 mt-2 z-10">
                <cycleSelection @selected="handleSelected" />
            </div>
        </a-form-item>
        <a-form-item label="绑定工作流" class="w-full">
            <div class="w-full">
                <a-input :value="workflowIdVal" placeholder="选择一个工作流，当触发器被激活时，将会执行该工作流。" readonly
                    class="cursor-not-allowed bg-gray-100" @click.stop="showWorkflowSelection" />
            </div>
            <a-modal :open="isShowWorkflowSelection" @cancel="handleCancel" title="选择工作流" width="1000px">
                <workflowSelection ref="workflowSelectionRef" />
                <template #footer>
                    <a-button type="primary" @click="handleOk">确认</a-button>
                </template>
            </a-modal>
            <div v-if="selectedWorkflowPath" class="mt-2 text-gray-500 text-sm break-all">
                选择的路径: {{ selectedWorkflowPath }}
            </div>
        </a-form-item>
    </a-form>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watchEffect } from 'vue';
import cycleSelection from './cycleSelection.vue'
import workflowSelection from './workflowSelection.vue'
import { useDesignerStore } from '@/store/designer'

const designerStore = useDesignerStore()

// 触发时间类型
interface TimerConfig {
    triggerTimeType: string
    cronExpression: string
    timezone: string
    triggerTime: string
    frequencyType: string
    frequencyDay: number
    workflowId: string
}
const props = defineProps({
    modelValue: {
        type: Object as PropType<TimerConfig>,
        default: () => ({})
    },
})

const emit = defineEmits(['update:modelValue'])

const updateField = (field, value) => {
    emit('update:modelValue', Object.assign(designerStore.currentNodeData?.properties, {
        [field]: value
    }))
}

// 时区
const timezoneVal = ref(props.modelValue?.timezone || '')

// 更新时区
const handleUpdateField = (val) => {
    timezoneVal.value = val
    updateField('timezone', val)
}

// cron表达式
const cronExpressionVal = ref(props.modelValue?.cronExpression || '')
// 更新cron表达式
const handleUpdateCron = (val) => {
    cronExpressionVal.value = val
    updateField('cronExpression', val)
}

// 触发时间
const triggerTimeVal = ref(props.modelValue?.triggerTime || '')
// 频率类型
const frequencyTypeVal = ref(props.modelValue?.frequencyType || '')
// 频率天数
const frequencyDayVal = ref(props.modelValue?.frequencyDay || '')
// 定义频率描述映射
const frequencyTypeDescMap = {
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    interval: '间隔'
}
const triggerTimeDesc = ref('')
// 选择触发时间的描述
watchEffect(() => {
    if (triggerTimeVal.value && frequencyTypeVal.value && frequencyDayVal.value) {
        const frequencyDesc = frequencyTypeDescMap[frequencyTypeVal.value]
        triggerTimeDesc.value = `${frequencyDesc} ${frequencyDayVal.value} ${triggerTimeVal.value}`
    } else {
        triggerTimeDesc.value = ''
    }
});

// 触发类型
const triggerTimeTypeVal = ref(props.modelValue?.triggerTimeType || '')
const handleTypeChange = (val) => {
    triggerTimeTypeVal.value = val
    updateField('triggerTimeType', val)
}

// 获取全部时区
const timezoneOptions = typeof (Intl as any).supportedValuesOf === 'function'
    ? (Intl as any).supportedValuesOf('timeZone').map((tz: string) => ({ label: tz, value: tz }))
    : []

// 是否显示选择器   
const isShowSelect = ref(false)
// 显示选择器
const showSelection = () => {
    isShowSelect.value = !isShowSelect.value
}
// 隐藏选择器
const hideSelection = () => {
    isShowSelect.value = false
}

// 选择完成后，关闭选择器
const handleSelected = (config) => {
    triggerTimeVal.value = config.triggerTime
    frequencyTypeVal.value = config.frequencyType
    frequencyDayVal.value = config.frequencyDay
    updateField('frequencyType', config.frequencyType)
    updateField('frequencyDay', config.frequencyDay)
    updateField('triggerTime', config.triggerTime)
    isShowSelect.value = false;
}

// 点击其他区域，隐藏选择器
onMounted(() => {
    const val = designerStore.currentNodeData?.properties
    if (val && Object.keys(val).length > 0) {
        timezoneVal.value = val.timezone || null
        triggerTimeTypeVal.value = val.triggerTimeType || null
        cronExpressionVal.value = val.cronExpression || null
        workflowIdVal.value = val.workflowId || null
        triggerTimeVal.value = val.triggerTime || null
        frequencyTypeVal.value = val.frequencyType || null
        frequencyDayVal.value = val.frequencyDay || null
    }
    document.addEventListener('click', hideSelection)
})
onBeforeUnmount(() => {
    document.removeEventListener('click', hideSelection)
})

// 是否展示选择工作流
const isShowWorkflowSelection = ref(false)
// 展示工作流
const showWorkflowSelection = () => {
    isShowWorkflowSelection.value = true
}
// 取消
const handleCancel = () => {
    isShowWorkflowSelection.value = false
}

// 工作流选择器
type WorkflowSelectionExpose = {
    selectedRowKeys: string[]
    selectedRow: any
}
const workflowSelectionRef = ref<WorkflowSelectionExpose | null>(null)
// 选择的工作流路径
const selectedWorkflowPath = ref('')
// 工作流id
const workflowIdVal = ref(props.modelValue?.workflowId || '')
const handleOk = () => {
    if ((workflowSelectionRef.value?.selectedRowKeys?.length ?? 0) > 0) {
        const selectedId = workflowSelectionRef.value!.selectedRowKeys![0]
        const selectedRow = workflowSelectionRef.value!.selectedRow
        selectedWorkflowPath.value = selectedRow?.path || ''
        workflowIdVal.value = selectedId
        updateField('workflowId', selectedId)
    }
    isShowWorkflowSelection.value = false;
}
</script>