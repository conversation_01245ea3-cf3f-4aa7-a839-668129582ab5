<template>
    <div class="h-full bg-global-background">
      <BaseTable
        :search-schema="searchSchema"
        :columns="TIMER_COLUMNS"
        :dataSource="designList"
        :loading="loading"
        :total="total"
        :current="currentPage"
        :pageSize="pageSize"
        :row-selection="rowSelection"
        :table-props="{
          rowKey: record => record.id,
          rowSelection: rowSelection
        }"
        @search="handleSearch"
        @reset="handleReset"
        @pageChange="handlePageChange"
      >
        <!-- 类型列 -->
        <template #type="{ record }">
          <a-tag :color="getTypeColor(record.type)">
            {{ record.type.toUpperCase() }}
          </a-tag>
        </template>
      </BaseTable>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue'
  import { message } from 'ant-design-vue'
  import {
    TYPE_COLORS,
    CONFIG_TYPES,
    PAGINATION,
    TIMER_COLUMNS
  } from '@/views/designer/files/constants'
  import { getEndpointPage } from '@/apis/designer'
  import { BaseTable } from '@/components'
  
  interface DesignItem {
    id: string
    project: string
    subsystem: string
    module: string
    description: string
    type: 'api' | 'agent' | 'cot'
    config: string
    pathUrl: string
    createTime: string
    updateTime: string
    templateId: string
    reqParam?: string
    path?: string
  }
  
  
  // 搜索配置
  const searchSchema = [
    {
      key: 'description',
      label: '描述',
      type: 'input',
      placeholder: '请输入描述关键词'
    },
  ]
  
  // 状态
  const loading = ref(false)
  const currentPage = ref(PAGINATION.DEFAULT_CURRENT_PAGE)
  const pageSize = ref(PAGINATION.DEFAULT_PAGE_SIZE)
  const total = ref(0)
  const designList = ref<DesignItem[]>([])
  
  // 表单
  const form = reactive({
    project: '',
    subsystem: '',
    module: '',
    description: '',
    type: '',
    configType: CONFIG_TYPES.INPUT,
    template: '',
    config: '',
    file: null as File | null
  })
  
  // 获取数据
  const fetchData = async (params = {}) => {
    loading.value = true
    try {
      const { current = 1, pageSize = 10, ...rest } = params
      const res = await getEndpointPage({ current, pageSize, ...rest })
      if (res.data) {
        designList.value = res.data.records || []
        total.value = res.data.total || 0
      }
    } catch (error) {
      message.error('获取数据失败：' + (error as Error).message)
    } finally {
      loading.value = false
    }
  }
  
  // 搜索处理
  const handleSearch = (params) => {
    console.log("params", params)
    currentPage.value = 1
    fetchData({ current: 1, pageSize: pageSize.value, ...params })
  }
  
  // 重置处理
  const handleReset = () => {
    currentPage.value = 1
    fetchData({ current: 1, pageSize: pageSize.value })
  }
  
  // 分页处理
  const handlePageChange = (pagination) => {
    currentPage.value = pagination.current
    pageSize.value = pagination.pageSize
    fetchData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...pagination.searchForm
    })
  }
  
  // 获取类型对应的颜色
  const getTypeColor = (type: string) => {
    return TYPE_COLORS[type as keyof typeof TYPE_COLORS] || 'default'
  }
  
  // 选择表单配置
  const selectedRowKeys = ref([]);
  // 选择行
  const selectedRow = ref<DesignItem | null>(null)
  const rowSelection = computed(() => ({
    type: 'radio',
    selectedRowKeys: selectedRowKeys.value,
    onChange: (keys) => {
      selectedRowKeys.value = keys;
    },
    onSelect: (record) => {
      selectedRow.value = record
    }
  }));
  
  defineExpose({
    selectedRowKeys,
    selectedRow
  });
  
  // 初始化
  onMounted(() => {
    fetchData()
  })
  </script>
  
  <style scoped>
  .w-full {
    width: 100%;
  }
  
  .config-content {
    max-height: 400px;
    overflow-y: auto;
  }
  
  .description {
    background-color: #fafafa;
  }
  
  .truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  </style>