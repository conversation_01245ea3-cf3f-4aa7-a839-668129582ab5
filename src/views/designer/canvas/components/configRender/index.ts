import type { Component } from 'vue'
import { defineAsyncComponent } from 'vue'
import * as Vue from 'vue' // 需要导入完整的 Vue API

// 明确指定模块加载器的类型
const modules: Record<string, () => Promise<{ default: Component }>> = import.meta.glob(
  './**/*.vue',
)

// 组件映射表
const customComponentMap: Record<string, Component> = {}

// 远程组件缓存
const remoteComponentCache = new Map<string, Component>()

// 处理组件注册
for (const path in modules) {
  const componentName = path.split('/').slice(-2)[0]
  if (componentName && path.endsWith('index.vue')) {
    customComponentMap[componentName] = defineAsyncComponent(modules[path])
  }
}

/**
 * 从远程URL加载Vue组件
 */
function getRemoteComponent(url: string): Component {
  console.log('开始加载远程组件，URL:', url)

  const asyncComponent = defineAsyncComponent({
    loader: async () => {
      try {
        // 从 URL 中获取组件名
        const fileName = url.split('/').pop()?.replace('.js', '') || ''
        const componentName = `VueComponent_${fileName}`

        // 如果已经加载过，先移除旧脚本
        const existingScript = document.querySelector(`script[src="${url}"]`)
        if (existingScript) {
          document.head.removeChild(existingScript)
        }

        await new Promise((resolve, reject) => {
          const script = document.createElement('script')
          script.type = 'text/javascript' // 确保脚本类型正确
          script.src = url

          script.onload = () => {
            console.log(`脚本加载成功，检查 ${componentName}:`, window[componentName])
            resolve()
          }

          script.onerror = (e) => {
            console.error('脚本加载失败:', e)
            reject(new Error(`脚本加载失败: ${url}`))
          }

          document.head.appendChild(script)
        })

        // 获取组件定义
        if (typeof window[componentName] !== 'function') {
          throw new Error(`远程组件 ${componentName} 未找到或不是函数`)
        }

        // 传递完整的Vue模块
        const component = window[componentName]({
          // 提供所有必要的Vue API
          createApp,
          defineComponent: Vue.defineComponent,
          ref: Vue.ref,
          reactive: Vue.reactive,
          computed: Vue.computed,
          watch: Vue.watch,
          onMounted: Vue.onMounted,
          h: Vue.h,
          // 其他可能需要的API...
          ...Vue, // 展开所有Vue API
        })

        return component
      } catch (error) {
        console.error('加载失败:', error)
        return {
          template: `<div class="error-component">远程组件加载失败: ${error instanceof Error ? error.message : String(error)}</div>`,
        }
      }
    },
    loadingComponent: {
      template: '<div class="loading-component">加载组件中...</div>',
    },
    timeout: 10000,
  })

  return asyncComponent
}

// 获取组件的方法
export const getConfigComponent = (type: string, componentConfig?: any): Component | string => {
  // 处理远程组件
  if (type === 'remoteNode' && componentConfig?.componentUrl) {
    return getRemoteComponent(componentConfig.componentUrl)
  }

  // 返回本地组件
  const component = customComponentMap[type] || type
  return component
}

// 导出所有自定义组件名称
export const customComponents = Object.keys(customComponentMap)
