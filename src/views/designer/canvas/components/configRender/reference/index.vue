<template>
  <div class="w-full">
    <div class="text-gray-500 flex mb-4">
      <span class="w-[150px] text-[12px]">参数名</span>
      <span class="text-[12px]">参数值</span>
    </div>

    <div
      v-for="(item, index) in parameters"
      :key="index"
      class="flex items-center gap-2 mb-4 relative"
    >
      <!-- 参数名称输入 -->
      <a-input
        v-model:value="item.name"
        placeholder="请输入"
        class="!w-[150px] !h-[32px] text-[12px]"
        :maxLength="20"
      >
        <template #suffix>
          <span class="text-[12px]">{{ item.name?.length || 0 }}/20</span>
        </template>
      </a-input>

      <!-- 使用新的参数值输入组件 -->
      <paramRef v-model="item.value" />

      <!-- 删除按钮 -->
      <a-button
        type="text"
        @click="removeParam(index)"
        class="!h-[32px] !w-[32px] !p-0 text-gray-400"
      >
        <MinusOutlined class="text-[16px]" />
      </a-button>
    </div>

    <!-- 添加按钮 -->
    <a-button
      type="link"
      @click="addParam"
      class="param-add-btn text-blue-600 flex items-center justify-center !h-[32px] !w-[32px] !p-0"
    >
      <PlusOutlined class="text-[16px]" />
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { PlusOutlined, MinusOutlined } from '@ant-design/icons-vue'
import type { PropType } from 'vue'
import paramRef from '../paramRef/index.vue'

interface ReferenceParam {
  name: string;
  value: string;
}

const props = defineProps({
  modelValue: {
    type: Array as PropType<ReferenceParam[]>,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const parameters = ref<ReferenceParam[]>(Array.isArray(props.modelValue) ? props.modelValue : [])

// 监听参数变化
watch(parameters, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== parameters.value) {
    parameters.value = Array.isArray(newVal) ? newVal : []
  }
}, { deep: true })

const addParam = () => {
  parameters.value = Array.isArray(parameters.value) ? parameters.value : []
  parameters.value.push({
    name: '',
    value: ''
  })
}

const removeParam = (index: number) => {
  if (!Array.isArray(parameters.value)) {
    parameters.value = []
    return
  }
  parameters.value.splice(index, 1)
}
</script>

<style lang="scss">
.param-add-btn {
  position: absolute !important;
  right: 28px;
  top: 15px;
}
</style>
