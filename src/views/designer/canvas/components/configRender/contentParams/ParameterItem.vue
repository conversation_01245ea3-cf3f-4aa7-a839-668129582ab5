<template>
  <div class="relative">
    <div class="flex items-center gap-2 mb-4">
      <!-- 展开/折叠按钮 - 只有 Object 类型才显示 -->
      <div v-if="isObject" class="cursor-pointer w-4" @click="toggleExpand">
        <RightOutlined v-if="!isExpanded" class="text-gray-400 text-[14px]" />
        <DownOutlined v-else class="text-gray-400 text-[14px]" />
      </div>

      <!-- 参数名称输入 -->
      <div class="w-[340px]">
        <a-input v-model:value="param.name" :spellcheck="false" placeholder="请输入" />
      </div>

      <!-- 参数类型改为输入框 -->
      <a-input
        v-model:value="param.type"
        class="!w-[120px]"
        placeholder="类型"
        @change="handleTypeChange"
      />

      <!-- 参数值输入 - 扩大显示区域 -->
      <div class="w-[400px]">
        <param-ref v-if="showValueInput" :param-type="param.type" v-model="param.value" />
      </div>

      <!-- 描述改为图标悬停提示 -->
      <a-tooltip placement="top">
        <template #title>
          <span>{{ param.description || '暂无描述' }}</span>
        </template>
        <QuestionCircleOutlined class="text-gray-400 text-[16px] cursor-pointer" />
      </a-tooltip>

      <!-- 描述编辑弹窗触发按钮 -->
      <a-button type="text" @click="showDescriptionModal = true" class="action-btn text-gray-400">
        <EditOutlined class="text-[14px]" />
      </a-button>

      <!-- 子参数添加按钮 - 只在展开的 Object 类型时显示 -->
      <a-button
        v-if="isObject && isExpanded"
        type="link"
        @click="addChild"
        class="action-btn text-blue-600"
      >
        <PlusOutlined class="text-[16px]" />
      </a-button>

      <!-- 删除按钮 -->
      <a-button type="text" @click="$emit('remove')" class="action-btn text-gray-400">
        <CloseOutlined class="text-[16px]" />
      </a-button>
    </div>

    <!-- 描述编辑弹窗 -->
    <a-modal
      v-model:visible="showDescriptionModal"
      title="编辑参数描述"
      @ok="showDescriptionModal = false"
      width="500px"
    >
      <a-textarea v-model:value="param.description" placeholder="请输入参数描述" :rows="4" />
    </a-modal>

    <!-- 子参数区域 -->
    <div v-if="isExpanded && isObject" class="pl-8 border-l border-l-gray-200 ml-2 mt-2">
      <parameter-item
        v-for="(child, index) in param.children"
        :key="index"
        :param="child"
        :typeOptions="typeOptions"
        @remove="removeChild(index)"
        @update:param="updateChild(index, $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { RightOutlined, DownOutlined, PlusOutlined, CloseOutlined, EditOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
import ParamRef from '../paramRef/index.vue'

const props = defineProps(['param', 'typeOptions'])
const emit = defineEmits(['remove', 'update:param'])

// 只有 Object 类型才能添加子项
const isObject = computed(() => props.param.type === 'Object')

// UI 状态，不影响实际数据
const isExpanded = ref(props.param.type === 'Object')
const showDescriptionModal = ref(false)

// 判断是否显示变量值输入框
const showValueInput = computed(() =>
  props.param.type !== 'Object' && props.param.type !== 'Array'
)

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

const handleTypeChange = (value: string) => {
  if (value === 'Object') {
    if (!props.param.children) {
      props.param.children = []
    }
    isExpanded.value = true  // 当类型改为 Object 时自动展开
  } else {
    props.param.children = undefined
  }
  emit('update:param', props.param)
}

const addChild = () => {
  if (!props.param.children) {
    props.param.children = []
  }
  props.param.children.push({
    name: '',
    type: 'String',
    value: '',
    description: '',
    expanded: false,
    children: []
  })
  emit('update:param', props.param)
}

const removeChild = (index: number) => {
  props.param.children?.splice(index, 1)
  emit('update:param', props.param)
}

const updateChild = (index: number, newChild: any) => {
  if (props.param.children) {
    props.param.children[index] = newChild
    emit('update:param', props.param)
  }
}
</script>

<style scoped>
:deep(.ant-select-selector) {
  height: 32px !important;
  line-height: 32px !important;
}

:deep(.ant-select-selection-item) {
  line-height: 30px !important;
  font-size: 14px !important;
}

.action-btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
}

:deep(.ant-input) {
  display: flex !important;
  align-items: center !important;
}

:deep(.ant-input-affix-wrapper) {
  padding: 0 11px !important;
  height: 32px !important;
}

:deep(.ant-select-selector) {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
}
</style>
