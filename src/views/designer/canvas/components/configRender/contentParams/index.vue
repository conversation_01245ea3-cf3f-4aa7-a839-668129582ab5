<template>
  <div class="w-full">
    <div class="flex-param-header text-gray-500 text-[12px] mb-4">
      <div class="flex-param-name dark:text-[var(--grey-auxiliary-color)]">参数名</div>
      <div class="flex-param-type dark:text-[var(--grey-auxiliary-color)]">参数类型</div>
      <div class="flex-param-value dark:text-[var(--grey-auxiliary-color)]">参数值</div>
      <div class="flex-param-default dark:text-[var(--grey-auxiliary-color)]">默认值</div>
      <div class="flex-param-desc dark:text-[var(--grey-auxiliary-color)]">描述</div>
    </div>

    <!-- 渲染从JSON配置中获取的参数 -->
    <div v-for="(param, index) in parameters" :key="index" class="flex-param-row">
      <!-- 参数名 -->
      <div class="flex-param-name">
        <a-input
          v-model:value="param.field"
          :spellcheck="false"
          placeholder="请输入"
          :disabled="true"
        />
      </div>

      <!-- 参数类型 -->
      <div class="flex-param-type">
        <a-input v-model:value="param.type" placeholder="类型" :disabled="true" />
      </div>

      <!-- 参数值 - 值引用选择 -->
      <div class="flex-param-value">
        <template v-if="param.type === 'a-select'">
          <a-select
            v-model:value="param.value"
            class="!w-full text-[12px]"
            :placeholder="param.description"
            @change="val => updateParamValue(index, val)"
            :getPopupContainer="triggerNode => triggerNode.parentNode"
            :allowClear="true"
          >
            <a-select-option
              v-for="option in getEnumOptions(param.enumKey)"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </template>
        <template v-else>
          <param-ref
            :key="`param-ref-${index}`"
            :param-type="param.type"
            :model-value="param.value"
            @update:model-value="val => updateParamValue(index, val)"
            :placeholder="param.description"
          />
        </template>
      </div>

      <!-- 默认值 -->
      <div class="flex-param-default">
        <template v-if="param.enumKey">
          <a-select
            v-model:value="param.default"
            class="!w-full text-[12px]"
            :placeholder="param.description"
            @change="val => updateDefaultValue(index, val)"
          >
            <a-select-option
              v-for="option in getEnumOptions(param.enumKey)"
              :key="option.value"
              :value="option.value"
              :allowClear="true"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </template>
        <a-input
          v-else
          v-model:value="param.default"
          :placeholder="param.description"
          class="text-[12px]"
          @change="val => updateDefaultValue(index, val)"
        >
            <template #suffix>
              <EditOutlined style="font-size: 14px" @click="openEditModal(param)" />
            </template>
        </a-input>
      </div>

      <!-- 参数描述改为图标悬停 -->
      <div class="flex-param-desc">
        <div class="flex justify-center items-center w-full">
          <a-tooltip placement="top">
            <template #title>
              <span>{{ param.description || '暂无描述' }}</span>
            </template>
            <QuestionCircleOutlined class="text-gray-400 text-[16px] cursor-pointer" />
          </a-tooltip>
        </div>
      </div>
    </div>
    <!-- 编辑模态框 -->
    <EditModal v-if="editModalVisible" :title="currentParam?.field || '编辑内容'" :value="currentInputValue"
      @update:value="currentInputValue = $event" @close="editModalVisible = false" @ok="handleModalOk" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted, computed, inject } from 'vue'
import type { PropType } from 'vue'
import ParamRef from '../paramRef/index.vue'
import { defaultTypeOptions } from './constants'
import { useAppStore } from '@/store/app'
import { QuestionCircleOutlined, EditOutlined } from '@ant-design/icons-vue'
import EditModal from '../tableEditor/EditModal.vue';
import { useDesignerStore } from '@/store/designer'

// Store
const designerStore = useDesignerStore()

const appStore = useAppStore()

// 检测是否在全屏模式下
const isFullscreen = computed(() => {
  // 通过检查最近的抽屉容器是否有全屏类名来判断
  if (typeof window !== 'undefined') {
    const drawerElement = document.querySelector('.fullscreen-drawer')
    return !!drawerElement
  }
  return false
})

const props = defineProps({
  modelValue: {
    type: [Array, Object] as PropType<any[] | Record<string, any>>,
    default: () => ({})
  },
  typeOptions: {
    type: Array as PropType<any[]>,
    default: () => defaultTypeOptions
  },
  // 接收从JSON配置传入的默认参数
  defaultParams: {
    type: Array as PropType<any[]>,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

// 编辑模态框相关
const editModalVisible = ref(false)
const currentParam = ref<any>(null)
const currentInputValue = ref<string>('');

const openEditModal = (param: any) => {
  currentParam.value = param
  currentInputValue.value = param.default || ''
  editModalVisible.value = true
}

const handleModalOk = () => {
  if (currentParam.value) {
    // 找到当前参数的索引
    const index = parameters.value.findIndex(p => p.field === currentParam.value.field)
    if (index !== -1) {
      // 更新参数的默认值
      parameters.value[index].default = currentInputValue.value
      // 发送更新
      sendParameterUpdate()
    }
  }
  editModalVisible.value = false
}

// 初始化参数
const parameters = ref<any[]>([])

// 初始化参数函数 - 必须在watch之前定义
const initializeParameters = () => {
  console.log("初始化参数 - 原始defaultParams:", props.defaultParams);
  console.log("当前modelValue:", props.modelValue);

  // 清空已有参数
  parameters.value = [];

  // 从defaultParams创建参数
  if (props.defaultParams && props.defaultParams.length > 0) {
    // 确保modelValue是有效对象
    const modelValues = typeof props.modelValue === 'object' && props.modelValue !== null
      ? props.modelValue
      : {};

    console.log("处理后的modelValues:", modelValues);

    // 映射参数并填充值
    parameters.value = props.defaultParams.map(param => {
      const field = param.field || '';

      // 获取保存的值
      const quoteField = `${field}_quote`;
      const defaultField = `${field}_default`;

      // 优先使用带后缀的特定值
      const savedQuote = modelValues[quoteField];
      const savedDefault = modelValues[defaultField];

      // 回退到原始值（如果特定值不存在）
      const savedValue = modelValues[field];

      console.log(`字段 ${field} - 保存的引用值:`, savedQuote);
      console.log(`字段 ${field} - 保存的默认值:`, savedDefault);
      console.log(`字段 ${field} - 保存的最终值:`, savedValue);
      console.log(`字段 ${field} - 配置的默认值:`, param.default);

      // 如果是a-select类型，使用原始值而不是引用值
      const value = param.type === 'a-select'
        ? (savedValue || '')
        : (savedQuote || '');

      return {
        field: field,
        type: param.type || 'String',
        value,
        default: savedDefault || param.default || '',
        description: param.description || '',
        enumKey: param.enumKey || '',
        expanded: false,
        children: param.children || []
      };
    });
  }

  console.log("初始化后的参数:", parameters.value);
}

// 更新现有参数而不重新初始化 - 必须在watch之前定义
const updateExistingParameters = (values: Record<string, any>) => {
  if (!values || typeof values !== 'object') return;

  // 从layout_properties中提取数据，如果存在的话
  const layoutProps = typeof values === 'object' && !Array.isArray(values) && 'layout_properties' in values
    ? values.layout_properties as Record<string, any>
    : {};

  parameters.value.forEach(param => {
    if (param.field) {
      const field = param.field;
      const quoteField = `${field}_quote`;
      const defaultField = `${field}_default`;

      // 对于 a-select 类型，它的值是直接的，不应被引用逻辑覆盖
      if (param.type === 'a-select') {
        if (values[field] !== undefined) {
          param.value = values[field];
        }
      } else {
        // 其他类型参数的引用值更新逻辑
        // 优先从layout_properties中获取带后缀的值
        if (layoutProps[quoteField] !== undefined) {
          param.value = layoutProps[quoteField];
        } else if (values[quoteField] !== undefined) {
          // 如果layout_properties中没有，尝试从主对象中获取
          param.value = values[quoteField];
        } else if (values[field] !== undefined && !values[defaultField]) {
          // 如果没有 quote 值，但有主字段值，且没有对应的 default 值（避免覆盖由默认值逻辑设定的情况）
          // 则认为这个主字段值是直接输入或通过某种方式设定的非引用值
          param.value = values[field];
        }
      }

      // 更新默认值 (所有类型参数共用)
      if (layoutProps[defaultField] !== undefined) {
        param.default = layoutProps[defaultField];
      } else if (values[defaultField] !== undefined) {
        // 如果layout_properties中没有，尝试从主对象中获取
        param.default = values[defaultField];
      } else if (param.default === undefined && values[field] !== undefined && param.type !== 'a-select' && !values[quoteField]){
        // 如果参数本身没有默认值定义，但 NodeConfig 传下来一个非引用、非 a-select 的值，可以考虑作为默认值
        // param.default = values[field]; // 这一行暂时注释，需要确认是否是期望行为
      }
    }
  });
}

// 更新参数值 - 当用户选择引用值时调用
const updateParamValue = (index: number, newValue: any) => {
  console.log(`更新参数 ${index} 值:`, newValue);

  if (parameters.value[index]) {
    const param = parameters.value[index];

    // 如果是a-select类型，直接使用选择的值，不需要引用格式
    if (param.type === 'a-select') {
      param.value = newValue;
    } else {
      // 其他类型保持原有的引用值处理
      param.value = newValue === null || newValue === undefined ? '' : newValue;
    }

    console.log(`更新参数 ${param.field} 为:`, param.value);

    // 发送更新
    sendParameterUpdate();
  }
}

// 更新默认值 - 当用户输入默认值时调用
const updateDefaultValue = (index: number, newValue: any) => {
  if (parameters.value[index]) {
    // 从事件对象中提取实际值或直接使用传入的值
    let actualValue = newValue;

    // 处理不同类型的事件/值
    if (newValue && typeof newValue === 'object') {
      if (newValue.target && newValue.target.value !== undefined) {
        actualValue = newValue.target.value;
      }
    }

    console.log(`更新默认值 - 索引:${index}, 新值:`, actualValue);

    // 更新默认值 - 只更新默认值，不影响参数值
    parameters.value[index].default = actualValue;

    // 发送更新
    sendParameterUpdate();
  }
}

// 发送参数更新 - 发送三个字段：原始值、默认值和引用值
const sendParameterUpdate = () => {
  // 创建一个新对象来保存所有值
  const result: Record<string, any> = {};

  // 添加所有值到结果中
  parameters.value.forEach(param => {
    if (param.field) {
      const field = param.field;
      const quoteField = `${field}_quote`;
      const defaultField = `${field}_default`;

      // 如果是a-select类型，不需要引用值格式
      if (param.type === 'a-select') {
        // 保存原始值
        result[field] = param.value;
        // 保存引用值（为空，因为下拉选择不使用引用）
        result[quoteField] = '';
        // 保存默认值
        result[defaultField] = param.default || '';
      } else {
        // 其他类型保持原有的处理逻辑
        // 1. 保存引用值 (quote字段)
        result[quoteField] = param.value || '';

        // 2. 保存默认值 (default字段)
        result[defaultField] = param.default || '';

        // 3. 保存原始值
        // 如果有引用值，使用引用值
        if (param.value && param.value !== '') {
          result[field] = param.value;
        }
        // 否则如果有默认值，使用默认值
        else if (param.default && param.default !== '') {
          result[field] = param.default;
        }
        // 两者都没有，设为空
        else {
          result[field] = '';
        }
      }

      console.log(`发送参数 ${field}:`);
      console.log(`  - 原始值: ${result[field]}`);
      console.log(`  - 引用值: ${result[quoteField]}`);
      console.log(`  - 默认值: ${result[defaultField]}`);
    }
  });

  console.log("发送参数更新:", result);
  emit('update:modelValue', result);
}

// 获取枚举选项
const getEnumOptions = (key: string) => {
  return appStore.getEnumOptions(key)
}
// 监听modelValue变化 - 应放在函数定义之后
watch([() => props.modelValue, () => designerStore.currentNode], ([newVal, newNode]) => {
  console.log("currentNode变化或modelValue变化");
  console.log("modelValue变化:", newVal);

  if (newNode) {
    // 如果节点变化，直接初始化
    initializeParameters();
  }
  else {
    // 如果已有参数，则只更新值，不重新初始化
    if (parameters.value.length > 0) {
      updateExistingParameters(newVal);
    } else {
      // 否则进行完整初始化
      initializeParameters();
    }
  }
}, { deep: true, immediate: true });

// 初始化
onMounted(() => {
  // 组件挂载后初始化参数
  if (parameters.value.length === 0) {
    initializeParameters();
  }
});
</script>

<style scoped>
/* Flex布局样式 */
.flex-param-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.flex-param-row {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  min-height: 48px;
}

.flex-param-name {
  flex: 2;
  min-width: 80px;
}

.flex-param-type {
  flex: 1;
  min-width: 70px;
}

.flex-param-value {
  flex: 2.5;
  min-width: 100px;
  position: relative;
  overflow: visible;
}

.flex-param-default {
  flex: 2;
  min-width: 80px;
}

.flex-param-desc {
  flex: 0.5;
  min-width: 40px;
}

/* 全屏模式下的flex比例调整 */
.fullscreen-drawer {
  .flex-param-header {
    gap: 16px;
    padding: 12px 0;
    font-size: 14px;
  }

  .flex-param-row {
    gap: 16px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 6px;
    margin-bottom: 8px;

    &:hover {
      background: rgba(0, 0, 0, 0.04);
    }
  }

  .flex-param-name {
    flex: 2.5;
    min-width: 200px;
  }

  .flex-param-type {
    flex: 1.2;
    min-width: 120px;
  }

  .flex-param-value {
    flex: 3;
    min-width: 250px;
    position: relative;
    overflow: visible;
  }

  .flex-param-default {
    flex: 2.5;
    min-width: 200px;
  }

  .flex-param-desc {
    flex: 0.8;
    min-width: 80px;
  }
}

/* 暗色主题适配 */
.dark .fullscreen-drawer .flex-param-row {
  background: rgba(255, 255, 255, 0.05);

  &:hover {
    background: rgba(255, 255, 255, 0.08);
  }
}

:deep(.ant-select-selector) {
  height: 32px !important;
  line-height: 32px !important;
}

:deep(.ant-select-selection-item) {
  line-height: 30px !important;
  font-size: 14px !important;
}

:deep(.ant-input) {
  display: flex !important;
  align-items: center !important;
}

:deep(.ant-input-affix-wrapper) {
  padding: 0 11px !important;
  height: 32px !important;
}

:deep(.ant-select-selector) {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
}
</style>
