<template>
  <div class="responsive-layout">
    <slot />
  </div>
</template>

<script setup lang="ts">
// 简化的响应式布局组件
// 全屏检测和样式应用现在通过全局CSS处理
interface Props {
  // 自定义类名前缀（保留接口兼容性）
  classPrefix?: string
}

const props = withDefaults(defineProps<Props>(), {
  classPrefix: 'responsive'
})
</script>

<style>
/* 全局样式，确保能够应用到所有子组件 */
.responsive-layout {
  width: 100%;
}

/* 普通模式样式 */
.responsive-normal {
  /* 可以在这里定义普通模式的默认样式 */
}

/* 全屏模式样式 */
.responsive-fullscreen {
  /* 可以在这里定义全屏模式的默认样式 */
}
</style>
