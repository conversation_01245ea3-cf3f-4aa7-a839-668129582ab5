<template>
  <div class="relative" @click.stop>
    <div
      class="flex items-center py-2 cursor-pointer rounded transition-colors group hover:bg-primary-hover hover:text-white"
      :style="{ paddingLeft: `${level * 8}px` }"
      @click="handleItemClick"
    >
      <div class="w-4 flex-shrink-0">
        <CaretRightOutlined
          v-if="node.type === 'Object'"
          class="text-[12px] text-gray-400 transition-transform duration-200 group-hover:text-white"
          :class="{ 'rotate-90': node.expanded }"
          @click.stop="toggleExpand"
        />
      </div>
      <span class="flex-1 text-[13px] cursor-pointer dark:!text-[var(--grey-auxiliary-color)]">
        {{ node.name }}
      </span>
      <span
        class="text-[12px] relative left-[-10px] text-gray-400 group-hover:text-white transition-colors"
      >
        {{ node.type }}
      </span>
    </div>

    <transition name="expand">
      <div v-if="node.expanded && node.type === 'Object'">
        <!-- 如果有子节点则显示子节点 -->
        <template v-if="node.children && node.children.length > 0">
          <tree-node
            v-for="child in node.children"
            :key="child.id"
            :node="child"
            :level="level + 1"
            @select="$emit('select', $event)"
          />
        </template>
        <!-- 如果没有子节点则显示空对象信息 -->
        <div
          v-else
          class="text-gray-400 text-[12px] py-1"
          :style="{ paddingLeft: `${(level + 1) * 8 + 16}px` }"
        >
          (空对象)
        </div>
      </div>
    </transition>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted } from 'vue'
import { CaretRightOutlined } from '@ant-design/icons-vue'

export default defineComponent({
  name: 'TreeNode',
  components: {
    CaretRightOutlined
  },
  props: {
    node: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 0
    }
  },
  emits: ['select'],
  setup(props, { emit }) {
    onMounted(() => {
      // 打印节点结构
      console.log(`TreeNode mounted: ${props.node.name}`, JSON.stringify(props.node, null, 2));
    });

    const toggleExpand = () => {
      console.log('toggleExpand called for:', JSON.stringify(props.node, null, 2));

      if (props.node.type === 'Object') {
        if (!props.node.children) {
          props.node.children = [];
        }
        props.node.expanded = !props.node.expanded;

        // 打印展开后的状态
        console.log(`Node ${props.node.name} expanded:`, props.node.expanded);
        if (props.node.expanded) {
          console.log(`Node ${props.node.name} children:`, JSON.stringify(props.node.children, null, 2));
        }
      }
    }

    const handleItemClick = () => {
      console.log('handleItemClick called for:', props.node.name);

      // 如果是对象类型，切换展开/收起
      if (props.node.type === 'Object') {
        toggleExpand();
      }
      // 非对象类型，直接选择
      else if (props.node.value) {
        console.log('Emitting select for:', props.node.value);
        emit('select', props.node.value);
      }
    }

    const handleClick = () => {
      console.log('handleClick called for:', JSON.stringify(props.node, null, 2));

      // 只处理非对象类型的点击选择
      if (props.node.type !== 'Object' && props.node.value) {
        emit('select', props.node.value);
      }
    }

    return {
      toggleExpand,
      handleClick,
      handleItemClick
    }
  }
})
</script>

<style scoped>
.rotate-90 {
  transform: rotate(90deg);
}

/* 添加过渡效果 */
.transition-transform {
  transition: transform 0.2s ease;
}

.transition-colors {
  transition: background-color 0.2s ease;
}

.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  height: 0;
  opacity: 0;
}
</style>
