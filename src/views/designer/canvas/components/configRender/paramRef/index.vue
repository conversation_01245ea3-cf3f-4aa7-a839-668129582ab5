<template>
  <div class="flex-1 relative w-full" ref="containerRef">
    <a-input
      @click="onClickInput"
      :readonly="paramType === 'File'"
      v-model:value="inputValue"
      :placeholder="paramType === 'File' ? '点击上传文件' : '输入或引用参数值'"
      class="w-full text-[12px]"
    >
      <template #suffix>
        <SettingOutlined
          class="text-[16px] cursor-pointer hover:text-blue-500"
          @click.stop="showReferenceSelect"
        />
        <EditOutlined style="font-size: 14px" @click="openEditModal(inputValue)" />
      </template>
    </a-input>

    <Teleport to="body">
      <div
        v-if="isDropdownVisible"
        class="fixed z-[9999] bg-white dark:!bg-area-fill border border-gray-200 dark:!border-border rounded-md shadow-lg overflow-y-auto max-h-[400px]"
        :style="dropdownStyle"
        @click.stop
      >
        <div v-if="params.length" class="divide-y divide-gray-100 ">
          <TreeNode
            v-for="param in params"
            :key="param.id"
            :node="param"
            :level="1"
            @select="selectReference"
          />
        </div>
        <div v-else class="py-8 text-[12px] px-4 text-center text-gray-400">暂无可用参数</div>
      </div>
    </Teleport>

     <!-- 编辑模态框 -->
     <EditModal v-if="editModalVisible" title="编辑内容" :value="currentInputValue"
      @update:value="currentInputValue = $event" @close="editModalVisible = false" @ok="handleModalOk" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, reactive } from 'vue'
import { SettingOutlined, RightOutlined, EditOutlined } from '@ant-design/icons-vue'
import { useDesignerStore } from '@/store/designer'
import TreeNode from './TreeNode.vue'
import { selectFile } from '@/utils/selectFile';
import { uploadFile } from '@/apis/designer';
import { message } from 'ant-design-vue';
import EditModal from '../tableEditor/EditModal.vue';

const props = defineProps<{
  modelValue: string
  paramType?: string
}>()

const emit = defineEmits(['update:modelValue'])

const onClickInput = () => {
  if (props.paramType === 'File') {
    onSelectFile()
  }
}

// 选择文件
const onSelectFile = async () => {
  if(props.paramType !== 'File') {
    return
  }
  const [file] = await selectFile()

  // 上传文件
  const formData = new FormData()
  formData.append('file', file)
  try {
    const { data } = await uploadFile(formData)
    emit('update:modelValue', data.url)
  } catch (error) {
    message.error('文件上传失败！')
  }
}

const designerStore = useDesignerStore()
const inputValue = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 编辑模态框相关
const editModalVisible = ref(false)
const currentInputValue = ref<string>('');

const openEditModal = (param: any) => {
  currentInputValue.value = param
  editModalVisible.value = true
}

const handleModalOk = () => {
  editModalVisible.value = false
  inputValue.value = currentInputValue.value
}

const isDropdownVisible = ref(false)
const containerRef = ref<HTMLElement | null>(null)

// 添加一个变量来存储滚动事件监听器
let scrollListener: (() => void) | null = null;

// 记录初始点击时的元素位置，而不仅仅是y坐标
const initialRect = ref<DOMRect | null>(null);

// 修改updateDropdownPosition函数，完全重新获取位置
const updateDropdownPosition = () => {
  // 如果下拉框不可见或没有初始位置记录，不更新
  if (!isDropdownVisible.value || !initialRect.value || !containerRef.value) return;

  // 获取当前元素的位置
  const currentRect = containerRef.value.getBoundingClientRect();

  // 计算元素位置的变化量
  const deltaY = currentRect.top - initialRect.value.top;

  // 更新clickY值，考虑滚动的影响
  clickY.value = clickY.value + deltaY;

  // 更新初始位置记录为当前位置
  initialRect.value = currentRect;

  // 强制重新计算样式
  forceUpdate.value = Date.now();

  console.log('Position updated after scroll, new clickY:', clickY.value);
}

// 添加一个响应式变量，用于强制更新计算属性
const forceUpdate = ref(0);

// 记录点击位置
const clickY = ref(0);

const dropdownStyle = computed(() => {
  // 使用 forceUpdate 来确保计算属性能够在页面滚动时重新计算
  // eslint-disable-next-line no-unused-vars
  const _ = forceUpdate.value;

  const rect = containerRef.value?.getBoundingClientRect()
  if (!rect) return {}

  const dropdownHeight = 400 // 下拉框最大高度
  const windowHeight = window.innerHeight
  const windowWidth = window.innerWidth

  // 获取点击位置，如未记录则使用输入框位置
  const yPosition = clickY.value || rect.bottom;

  // 计算下方剩余空间
  const spaceBelow = windowHeight - yPosition;

  // 只有当下方空间不足时才向上弹出
  const shouldShowAbove = spaceBelow < 200; // 200px是一个阈值

  // 计算左侧位置，与输入框保持一致
  let leftPosition = rect.left;
  if (leftPosition + rect.width > windowWidth) {
    leftPosition = windowWidth - rect.width - 10; // 10px 作为安全边距
  }

  // 根据空间情况确定垂直位置和变换
  let topPosition, transform = '';

  if (shouldShowAbove) {
    // 向上弹出，增加15px的间距（向上偏移）
    topPosition = yPosition - 15; // 减少15px使弹出框与输入框有间距
    transform = 'translateY(-100%)'; // 向上平移自身高度
  } else {
    // 向下弹出，增加15px的偏移，避免挡住输入框
    topPosition = yPosition + 15; // 向下增加15px的间距
    transform = ''; // 不需要平移
  }

  return {
    top: `${topPosition}px`,
    left: `${leftPosition}px`,
    width: `${rect.width}px`,
    maxHeight: `${dropdownHeight}px`,
    transform: transform
  }
})

// 类型定义
interface ParamItem {
  id: string
  name: string
  type: string
  expanded: boolean
  value?: string
  children?: ParamItem[]
}

// 添加处理嵌套结构的函数
const buildNestedParams = (parentObj: any, parentPath: string): ParamItem[] => {
  if (!parentObj || typeof parentObj !== 'object') return [];

  return Object.keys(parentObj).map(key => {
    const value = parentObj[key];
    const currentPath = parentPath ? `${parentPath}.${key}` : key;

    // 如果值是对象且不是数组，递归处理
    if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
      return {
        id: currentPath,
        name: key,
        type: 'Object',
        expanded: false,
        value: currentPath,
        children: buildNestedParams(value, currentPath)
      };
    } else {
      // 如果是基本类型值
      return {
        id: currentPath,
        name: key,
        type: Array.isArray(value) ? 'Array' : typeof value,
        expanded: false,
        value: currentPath
      };
    }
  });
};

// 修改params计算属性
const params = computed(() => {
  const graphData = designerStore.modGraph.toJSON()
  const currentNode = designerStore.currentNode
  if (!currentNode) return []

  // 找出前置节点
  const edges = graphData.cells.filter(cell => cell.shape === 'data-processing-curve')
  const previousNodes = new Set<string>()
  const findPreviousNodes = (nodeId: string) => {
    const incomingEdges = edges.filter(edge => edge.target.cell === nodeId)
    incomingEdges.forEach(edge => {
      const sourceId = edge.source.cell
      if (!previousNodes.has(sourceId)) {
        previousNodes.add(sourceId)
        findPreviousNodes(sourceId)
      }
    })
  }
  findPreviousNodes(currentNode.id)

  const nodes = graphData.cells.filter(cell =>
    cell.shape !== 'data-processing-curve' &&
    previousNodes.has(cell.id)
  )

  // 创建分组
  const groupedParams = {
    headers: [] as ParamItem[],
    commons: [] as ParamItem[],
    data: [] as ParamItem[]
  }
  const topLevelParams: ParamItem[] = []

  // 处理所有节点的输出
  nodes.forEach(node => {
    if (node.data?.outputs) {
      // 处理 startNode 的特殊结构
      if (node.data.type === 'startNode') {
        // 处理 headers 分组
        if (Array.isArray(node.data.outputs.headers)) {
          node.data.outputs.headers.forEach(item => {
            groupedParams.headers.push({
              id: `headers.${item.name}`,
              name: item.name,
              type: item.type,
              expanded: false,
              value: `headers.${item.name}`
            })
          })
        }

        // 处理 commons 分组
        if (Array.isArray(node.data.outputs.commons)) {
          node.data.outputs.commons.forEach(item => {
            groupedParams.commons.push({
              id: `commons.${item.name}`,
              name: item.name,
              type: item.type,
              expanded: false,
              value: `commons.${item.name}`
            })
          })
        }

        // 处理 data 分组，特别注意对象类型
        if (Array.isArray(node.data.outputs.data)) {
          node.data.outputs.data.forEach(item => {
            // 检查是否是对象类型
            if (item.type === 'Object') {
              // 递归处理对象及其子节点
              const processObjectItem = (obj: any, parentPath = ''): ParamItem => {
                const path = parentPath ? `${parentPath}.${obj.name}` : obj.name
                const id = `data.${path}`

                const result: ParamItem = {
                  id,
                  name: obj.name,
                  type: obj.type,
                  expanded: obj.expanded || false,
                  value: `data.${path}`
                }

                // 如果有子节点，递归处理
                if (obj.children && obj.children.length > 0) {
                  result.children = obj.children.map((child: any) =>
                    processObjectItem(child, path)
                  )
                }

                return result
              }

              // 添加处理后的对象
              groupedParams.data.push(processObjectItem(item))
            } else {
              // 非对象类型直接添加
              groupedParams.data.push({
                id: `data.${item.name}`,
                name: item.name,
                type: item.type,
                expanded: false,
                value: `data.${item.name}`
              })
            }
          })
        }
      }

      // 处理普通节点的 outputs
      if (Array.isArray(node.data.outputs)) {
        node.data.outputs.forEach(item => {
          const name = item.name
          if (typeof name === 'string') {
            // 处理带大括号的引用
            const match = name.match(/^\{(data|headers|commons)\.([^}]+)\}$/)
            if (match) {
              // 这里不需要做特殊处理，因为已经从startNode获取了完整的结构
            } else if (!name.startsWith('{')) {
              // 顶层项
              topLevelParams.push({
                id: name,
                name,
                type: 'String',
                expanded: false,
                value: name
              })
            }
          }
        })
      }
    }
  })

  // 构建最终结果
  const finalResult: ParamItem[] = []

  // 添加分组
  Object.keys(groupedParams).forEach(groupName => {
    if (groupedParams[groupName].length > 0) {
      finalResult.push({
        id: `group.${groupName}`,
        name: groupName,
        type: 'Object',
        expanded: true,
        children: groupedParams[groupName]
      })
    }
  })

  // 添加顶层项
  topLevelParams.forEach(param => {
    finalResult.push(param)
  })

  // 添加当前节点的处理结果字段
  const currentNodeComponents = currentNode.data?.components || []
  const resultParamsComponent = currentNodeComponents.find(
    (comp: any) => comp.type === 'resultParams'
  )

  if (resultParamsComponent?.props?.defaultParams?.data) {
    const currentParams: ParamItem[] = []

    resultParamsComponent.props.defaultParams.data.forEach((item: any) => {
      currentParams.push({
        id: `current.${item.name}`,
        name: item.name,
        type: item.type,
        expanded: false,
        value: `current.${item.name}`
      })
    })

    finalResult.push({
      id: 'group.current',
      name: 'current',
      type: 'Object',
      expanded: true,
      children: currentParams
    })
  }

  console.log('Final result:', JSON.stringify(finalResult, null, 2))
  return finalResult
})

const selectReference = (value: string) => {
  inputValue.value = `{${value}}`;
  isDropdownVisible.value = false;
}

const showReferenceSelect = (event: MouseEvent) => {
  const inputElement = (event.target as HTMLElement).closest('.flex-1') as HTMLElement
  if (!inputElement) {
    return
  }

  // 记录点击位置
  clickY.value = event.clientY;

  // 记录初始元素位置
  if (containerRef.value) {
    initialRect.value = containerRef.value.getBoundingClientRect();
  }

  isDropdownVisible.value = true

  // 使用防抖动的滚动处理函数
  let scrollTimeout: number | null = null;
  scrollListener = () => {
    if (scrollTimeout) {
      window.clearTimeout(scrollTimeout);
    }
    scrollTimeout = window.setTimeout(() => {
      updateDropdownPosition();
      scrollTimeout = null;
    }, 10); // 10ms的防抖动
  };

  // 添加到window上，捕获所有滚动
  window.addEventListener('scroll', scrollListener, true);

  // 添加到document.body，捕获可能的容器滚动
  document.body.addEventListener('scroll', scrollListener, true);
}

const closeDropdown = (e: MouseEvent) => {
  const target = e.target as HTMLElement
  if (!target.closest('.reference-select-dropdown') && !target.closest('.ant-input-suffix')) {
    isDropdownVisible.value = false

    // 移除所有滚动事件监听
    clearScrollListeners();
  }
}

// 添加一个函数专门用于清理所有滚动监听器
const clearScrollListeners = () => {
  if (scrollListener) {
    window.removeEventListener('scroll', scrollListener, true);

    // 同样移除在特定容器上添加的监听器
    const scrollContainers = document.querySelectorAll('.scrollable-container');
    scrollContainers.forEach(container => {
      container.removeEventListener('scroll', scrollListener!, true);
    });

    scrollListener = null;
  }
}

onMounted(() => {
  document.addEventListener('click', closeDropdown)
})

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown)
  clearScrollListeners();
})

// 在param-ref组件内部，确保正确处理选择事件
const handleClick = (param) => {
  console.log("handleClick called for:", param);
  // 直接更新值并发出事件
  inputValue.value = param.value || param.id;
  emit('update:modelValue', inputValue.value);

  // 关闭下拉菜单
  isDropdownVisible.value = false;
}
</script>

<style lang="scss">
.reference-select-dropdown {
  position: fixed;
  z-index: 9999;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  margin-top: 6px;
  display: flex;

  .anticon-right {
    font-size: 12px;
    color: #9CA3AF;
  }

  .params-menu {
    width: 300px;
    max-height: 400px;
    overflow-y: auto;
  }

  .param-group {
    border-bottom: 1px solid #e5e7eb;

    &:last-child {
      border-bottom: none;
    }
  }

  .group-title {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .param-item {
    width: 100%;
    cursor: pointer;
    &:hover {
      background-color: #f9fafb;
    }
  }

  .empty-data {
    width: 100%;
    padding: 40px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;

    .empty-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .empty-text {
      font-size: 14px;
    }
  }
}

:deep(.ant-input-suffix) {
  cursor: pointer;
}

.rotate-90 {
  transform: rotate(90deg);
}
</style>
