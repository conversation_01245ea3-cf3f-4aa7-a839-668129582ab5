/* 响应式布局样式 */

/* CSS变量定义 */
:root {
  /* 普通模式列宽 */
  --param-name-width-normal: 220px;
  --param-type-width-normal: 120px;
  --param-value-width-normal: 260px;
  --param-default-width-normal: 140px;
  --param-desc-width-normal: 50px;
  --param-length-width-normal: 100px;
  
  /* 全屏模式列宽 */
  --param-name-width-fullscreen: 300px;
  --param-type-width-fullscreen: 150px;
  --param-value-width-fullscreen: 400px;
  --param-default-width-fullscreen: 300px;
  --param-desc-width-fullscreen: 120px;
  --param-length-width-fullscreen: 120px;
  
  /* 间距 */
  --param-gap-normal: 8px;
  --param-gap-fullscreen: 16px;
  
  /* 行高 */
  --param-row-height-normal: auto;
  --param-row-height-fullscreen: 48px;
}

/* 响应式参数布局基础类 */
.responsive-params {
  width: 100%;
  
  .param-header {
    display: flex;
    margin-bottom: 16px;
    gap: var(--param-gap-normal);
    
    .param-name-col {
      width: var(--param-name-width-normal);
    }
    
    .param-type-col {
      width: var(--param-type-width-normal);
    }
    
    .param-value-col {
      width: var(--param-value-width-normal);
    }
    
    .param-default-col {
      width: var(--param-default-width-normal);
    }
    
    .param-desc-col {
      width: var(--param-desc-width-normal);
    }
    
    .param-length-col {
      width: var(--param-length-width-normal);
    }
  }
  
  .param-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: var(--param-gap-normal);
    min-height: var(--param-row-height-normal);
    
    .param-name-col {
      width: var(--param-name-width-normal);
    }
    
    .param-type-col {
      width: var(--param-type-width-normal);
    }
    
    .param-value-col {
      width: var(--param-value-width-normal);
    }
    
    .param-default-col {
      width: var(--param-default-width-normal);
    }
    
    .param-desc-col {
      width: var(--param-desc-width-normal);
    }
    
    .param-length-col {
      width: var(--param-length-width-normal);
    }
  }
}

/* 全屏模式样式 - 使用更具体的选择器和更高优先级 */
.fullscreen-drawer .commons-params-fullscreen,
.fullscreen-drawer .out-params-fullscreen,
.fullscreen-drawer .content-params-fullscreen,
.fullscreen-drawer .responsive-fullscreen,
.commons-params-fullscreen,
.out-params-fullscreen,
.content-params-fullscreen,
.responsive-fullscreen {
  .responsive-params {
    .param-header {
      gap: var(--param-gap-fullscreen) !important;
      margin-bottom: 20px !important;
      font-size: 14px !important;

      .param-name-col {
        width: var(--param-name-width-fullscreen) !important;
      }

      .param-type-col {
        width: var(--param-type-width-fullscreen) !important;
      }

      .param-value-col {
        width: var(--param-value-width-fullscreen) !important;
      }

      .param-default-col {
        width: var(--param-default-width-fullscreen) !important;
      }

      .param-desc-col {
        width: var(--param-desc-width-fullscreen) !important;
      }

      .param-length-col {
        width: var(--param-length-width-fullscreen) !important;
      }
    }

    .param-row {
      gap: var(--param-gap-fullscreen) !important;
      margin-bottom: 16px !important;
      min-height: var(--param-row-height-fullscreen) !important;
      padding: 12px !important;
      background: rgba(0, 0, 0, 0.02) !important;
      border-radius: 6px !important;
      transition: background-color 0.2s !important;

      &:hover {
        background: rgba(0, 0, 0, 0.04) !important;
      }

      .param-name-col {
        width: var(--param-name-width-fullscreen) !important;
      }

      .param-type-col {
        width: var(--param-type-width-fullscreen) !important;
      }

      .param-value-col {
        width: var(--param-value-width-fullscreen) !important;
      }

      .param-default-col {
        width: var(--param-default-width-fullscreen) !important;
      }

      .param-desc-col {
        width: var(--param-desc-width-fullscreen) !important;
      }

      .param-length-col {
        width: var(--param-length-width-fullscreen) !important;
      }
    }
  }
}

/* 暗色主题适配 */
.dark {
  .commons-params-fullscreen,
  .out-params-fullscreen,
  .content-params-fullscreen,
  .responsive-fullscreen {
    .responsive-params .param-row {
      background: rgba(255, 255, 255, 0.05) !important;

      &:hover {
        background: rgba(255, 255, 255, 0.08) !important;
      }
    }
  }
}

/* 通用工具类 */
.param-col-flex {
  display: flex;
  align-items: center;
}

.param-col-center {
  justify-content: center;
}

.param-col-end {
  justify-content: flex-end;
}

/* 全屏抽屉内的所有参数组件自动应用全屏样式 */
.fullscreen-drawer {
  .responsive-params {
    .param-header {
      gap: var(--param-gap-fullscreen) !important;
      margin-bottom: 20px !important;
      font-size: 14px !important;

      .param-name-col {
        width: var(--param-name-width-fullscreen) !important;
      }

      .param-type-col {
        width: var(--param-type-width-fullscreen) !important;
      }

      .param-value-col {
        width: var(--param-value-width-fullscreen) !important;
      }

      .param-default-col {
        width: var(--param-default-width-fullscreen) !important;
      }

      .param-desc-col {
        width: var(--param-desc-width-fullscreen) !important;
      }

      .param-length-col {
        width: var(--param-length-width-fullscreen) !important;
      }
    }

    .param-row {
      gap: var(--param-gap-fullscreen) !important;
      margin-bottom: 16px !important;
      min-height: var(--param-row-height-fullscreen) !important;
      padding: 12px !important;
      background: rgba(0, 0, 0, 0.02) !important;
      border-radius: 6px !important;
      transition: background-color 0.2s !important;

      &:hover {
        background: rgba(0, 0, 0, 0.04) !important;
      }

      .param-name-col {
        width: var(--param-name-width-fullscreen) !important;
      }

      .param-type-col {
        width: var(--param-type-width-fullscreen) !important;
      }

      .param-value-col {
        width: var(--param-value-width-fullscreen) !important;
      }

      .param-default-col {
        width: var(--param-default-width-fullscreen) !important;
      }

      .param-desc-col {
        width: var(--param-desc-width-fullscreen) !important;
      }

      .param-length-col {
        width: var(--param-length-width-fullscreen) !important;
      }
    }
  }
}

/* 暗色主题下的全屏抽屉样式 */
.dark .fullscreen-drawer {
  .responsive-params .param-row {
    background: rgba(255, 255, 255, 0.05) !important;

    &:hover {
      background: rgba(255, 255, 255, 0.08) !important;
    }
  }
}
