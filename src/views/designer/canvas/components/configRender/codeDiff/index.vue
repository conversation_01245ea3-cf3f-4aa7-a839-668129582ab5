<template>
  <div class="code-diff-container">
    <div v-if="props.diffHeader" class="code-diff-header">
      <div class="code-diff-title-section">
        <h3 class="code-diff-title">代码对比</h3>
      </div>
      <div class="code-diff-actions">
        <a-button-group>
          <a-button
            size="small"
            @click="handleSideBySide"
            :type="isSideBySide ? 'primary' : 'default'"
          >
            <template #icon><SplitCellsOutlined /></template>
            并排视图
          </a-button>
          <a-button
            size="small"
            @click="handleInline"
            :type="!isSideBySide ? 'primary' : 'default'"
          >
            <template #icon><MergeCellsOutlined /></template>
            内联视图
          </a-button>
        </a-button-group>
      </div>
    </div>
    <div class="code-diff-editor-container">
      <div class="editor-header" v-if="isSideBySide">
        <div class="file-header original">
          <span class="file-icon"><FileOutlined /></span>
          <span class="file-name">{{ props.originalFileName || '数据源' }}</span>
        </div>
        <div class="file-header modified">
          <span class="file-icon"><FileOutlined /></span>
          <span class="file-name">{{ props.modifiedFileName || '比对数据' }}</span>
        </div>
      </div>
      <div class="editor-header" v-else>
        <div class="file-header">
          <span class="file-icon"><FileOutlined /></span>
          <span class="file-name"
            >{{ props.originalFileName || '数据源' }} →
            {{ props.modifiedFileName || '比对数据' }}</span
          >
        </div>
      </div>
      <div class="code-diff-content" ref="editorContainer"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import * as monaco from 'monaco-editor'
import { SplitCellsOutlined, MergeCellsOutlined, FileOutlined } from '@ant-design/icons-vue'
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker'
import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker'
import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker'
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker'

// 配置 Monaco Editor 的 worker
window.MonacoEnvironment = {
  getWorker(_moduleId: string, label: string) {
    const workerMap = {
      json: jsonWorker,
      css: cssWorker,
      html: htmlWorker,
      typescript: tsWorker,
      javascript: tsWorker,
      default: editorWorker
    }

    return new (workerMap[label as keyof typeof workerMap] || workerMap.default)()
  }
}

// 初始化 Monaco Editor
monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
  target: monaco.languages.typescript.ScriptTarget.Latest,
  allowNonTsExtensions: true,
  moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
  module: monaco.languages.typescript.ModuleKind.CommonJS,
  noEmit: true,
  esModuleInterop: true,
  jsx: monaco.languages.typescript.JsxEmit.React,
  allowJs: true,
  typeRoots: ['node_modules/@types']
})

// 设置语言同步
monaco.languages.typescript.javascriptDefaults.setEagerModelSync(true)
monaco.languages.typescript.typescriptDefaults.setEagerModelSync(true)

interface DiffProps {
  modelValue: {
    originalCode: string
    modifiedCode: string
  }
  originalCode?: string
  modifiedCode?: string
  originalFileName?: string
  modifiedFileName?: string
  language?: string
  readOnly?: boolean
  enableEdit?: boolean
  onModifiedCodeChange?: (code: string) => void
  diffMode?: boolean
  diffHeader?: boolean
}

const props = withDefaults(defineProps<DiffProps>(), {
  modelValue: () => {
    return  {
      originalCode: '',
      modifiedCode: ''
    }
  },
  language: 'javascript',
  originalCode: '',
  modifiedCode: '',
  readOnly: true,
  originalFileName: '',
  modifiedFileName: '',
  enableEdit: false,
  diffMode: false,
  diffHeader: false
})

const isSideBySide = ref(true)
const editorContainer = ref<HTMLElement>()
let diffEditor: monaco.editor.IStandaloneDiffEditor | null = null

// 添加 emit 定义
const emit = defineEmits<{
  'update:modelValue': [{ originalCode: string, modifiedCode: string }]
  'update:modifiedCode': [code: string]
  'modified-code-change': [code: string]
}>()

// 初始化编辑器
const initEditor = async () => {
  if (!editorContainer.value) return
  await nextTick()
  diffEditor = monaco.editor.createDiffEditor(editorContainer.value, {
    automaticLayout: true,
    readOnly: !(props.diffMode || props.enableEdit),
    renderSideBySide: true,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    fontSize: 14,
    lineHeight: 21,
    renderOverviewRuler: true,
    folding: true,
    lineNumbers: 'on',
    theme: 'vs-dark',
    diffWordWrap: 'on',
    renderIndicators: true,
    renderMarginRevertIcon: true,
    glyphMargin: true,
    lineDecorationsWidth: 40,
    ignoreTrimWhitespace: false,
    enableSplitViewResizing: true,
    originalEditable: false,
    modifiedEditable: props.enableEdit,
  })

  updateDiffContent()

  // 延迟添加差异统计，确保编辑器完全加载
  setTimeout(() => {
    if (diffEditor) {
      updateDiffContent()
    }
  }, 100);

  // 添加修改后编辑器的内容变化监听
  const modifiedEditor = diffEditor.getModifiedEditor()
  modifiedEditor.onDidChangeModelContent(() => {
    const newModifiedCode = modifiedEditor.getValue()
    emit('update:modifiedCode', newModifiedCode)
    emit('modified-code-change', newModifiedCode)
    emit('update:modelValue', { originalCode: props.modelValue.originalCode, modifiedCode: newModifiedCode })
  })

  if (props.diffMode) {
    const originalEditor = diffEditor.getOriginalEditor()

    originalEditor.onDidChangeModelContent(() => {
      const newOriginalCode = originalEditor.getValue()
      modifiedEditor.setValue(newOriginalCode)
      emit('update:modifiedCode', newOriginalCode)
      emit('modified-code-change', newOriginalCode)
      emit('update:modelValue', { originalCode: newOriginalCode, modifiedCode: props.modelValue.modifiedCode })
    })
  }
}

// 更新对比内容
const updateDiffContent = () => {
  if (!diffEditor) return

  // 创建新的模型
  const originalModel = monaco.editor.createModel(props.originalCode, props.language)
  const modifiedModel = monaco.editor.createModel(props.modifiedCode, props.language)

  diffEditor.setModel({
    original: originalModel,
    modified: modifiedModel
  })
}

// 视图切换处理
const handleSideBySide = () => {
  isSideBySide.value = true
  diffEditor?.updateOptions({ renderSideBySide: true })
}

const handleInline = () => {
  isSideBySide.value = false
  diffEditor?.updateOptions({ renderSideBySide: false })
}

// 在 updateDiffContent 函数后添加
const handleLineChanges = () => {
  if (!diffEditor) return

  // 获取差异信息
  const lineChanges = diffEditor.getLineChanges()
  if (!lineChanges) return
  console.log('Line changes:', lineChanges)
}

// 监听代码变化
watch(() => [props.originalCode, props.modifiedCode], async () => {
  await nextTick()
  if (diffEditor) {
    updateDiffContent()
    handleLineChanges()
  }
}, { deep: true })

// 添加对 enableEdit 和 diffMode 的监听
watch(
  () => [props.enableEdit, props.diffMode],
  () => {
    if (diffEditor) {
      diffEditor.updateOptions({
        readOnly: !(props.diffMode || props.enableEdit),
        originalEditable: props.diffMode,
        modifiedEditable: props.enableEdit,
      })
    }
  }
)

const updateEditorLayout = async () => {
  await nextTick()
  setTimeout(() => {
    if (diffEditor) {
      diffEditor.layout()
    }
  }, 200)
}

// 生命周期钩子
onMounted(() => {
  initEditor()
  updateEditorLayout()
})

onBeforeUnmount(() => {
  // const model = diffEditor?.getModel()
  // if (model) {
  //   model.original?.dispose()
  //   model.modified?.dispose()
  //   diffEditor?.dispose()
  // }
})
</script>

<style scoped lang="scss">
.code-diff-container {
  @apply w-full h-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden flex min-h-[300px];
}

.code-diff-header {
  @apply px-4 py-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center;
}

.code-diff-title-section {
  @apply flex items-center gap-4;
}

.code-diff-title {
  @apply text-gray-700 dark:text-gray-200 font-medium text-base m-0;
}

.code-diff-actions {
  @apply flex items-center gap-2;
}

.code-diff-editor-container {
  @apply flex-1 flex flex-col min-h-0;
  position: relative;

  .monaco-diff-editor {
    .diffOverview {
      position: absolute;
      right: 0;
    }
  }
}

.editor-header {
  @apply flex border-b border-gray-200 dark:border-[#1E1E1E];
  height: 35px;
  position: relative;
  z-index: 1;

  .file-header {
    @apply flex items-center px-4;
    position: absolute;
    top: 0;
    height: 100%;

    &.original {
      @apply border-r border-gray-200 dark:border-[#1E1E1E] bg-[#f5f5f5] dark:bg-[#1E1E1E];
      left: 0;
      width: calc(50% - 1px);
    }

    &.modified {
      @apply bg-[#f5f5f5] dark:bg-[#1E1E1E];
      left: 50%;
      width: 50%;
    }

    .file-icon {
      @apply text-gray-400 dark:text-[#858585];
      flex-shrink: 0;
    }

    .file-name {
      @apply text-sm text-gray-600 dark:text-[#D4D4D4];
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 0;
      flex: 1;
    }
  }
}

.code-diff-content {
  @apply flex-1 min-h-0;
  position: relative;
  resize: vertical;
  overflow: auto;
}

/* 确保按钮容器不会被编辑器的其他元素遮挡 */
.monaco-editor .contentWidgets {
  z-index: 100;
}

/* 确保滚动条样式与 VSCode 一致 */
:deep(.monaco-editor .scrollbar) {
  .slider {
    background: var(--vscode-scrollbarSlider-background) !important;

    &:hover {
      background: var(--vscode-scrollbarSlider-hoverBackground) !important;
    }

    &.active {
      background: var(--vscode-scrollbarSlider-activeBackground) !important;
    }
  }
}

.editor-header {
  .file-header {
    &.original, &.modified {
      @apply bg-[#f5f5f5] dark:bg-[#1E1E1E];
      background-color: #f5f5f5 !important;
    }
  }
}

:deep(.dark) {
  .file-header {
    &.original, &.modified {
      background-color: #1E1E1E !important;
    }
  }
}
</style>
