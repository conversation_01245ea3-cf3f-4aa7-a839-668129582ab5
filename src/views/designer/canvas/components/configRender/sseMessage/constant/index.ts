export default {
    Message1001: { 
      data: { //md组件
        component: {
          type: 'Message1001',
          data:'你好啊',
          }
       }
    },
    Message1002 : { // 示例组件
      data: {
          component: {
          type: 'Message1002',
          data: '你好啊222',
          props: {
              examples:["2131","1231231", "asdasda"]  //示例
          }
          }
      },
  },
    Message1003 :{
      role: 'assistant',
      messageId: "ddsfdsfsdfsdd", // 保持相同的顶层 messageId
      data:{ component: {
        type: 'Message1003',
        data: '这是一个图文混合消息，下面是一些图片:',
        props: {
          images: [
            {
              url: 'https://example.com/image1.jpg',
              alt: '图片1描述'
            },
            {
              url: 'https://example.com/image2.jpg',
              alt: '图片2描述'
            }
          ]
        }
      }
    }
    },
    Message1004:{
      messageId: "ddsfdsfsdfsdd", 
      data: {
        component: {
            type: "Message1004",
            data: '这是文件组件',
            props: {
                files:[ 
                  {
                    name: '合同报告1',
                    size: "32000",
                    fileId: "sdasdsadasda",
                    filePath: "/user/data/docx",
                    type: "docx"
                },
                {
                  name: '合同报告1222',
                  size: "32000",
                  fileId: "sdasdsadasda",
                  filePath: "/user/data/docx",
                  type: "xlsx"
                },
              ]
            }
        }
    }
    },
    Message1005:{ // 引用组件
      messageId: "ddsfdsfsdfsdd", 
      data: {
        component: {
          type: 'Message1005',
          data: '这是引用后的回复内容',
          props: {
            quoteContent: '这是被引用的内容',
            quoteSource: '用户消息'
          }
        }
    }
    },
    Message2001:{ // 表格组件
      messageId: "ddsfdsfsdfsdd", 
      data: {
        component: {
          type: 'Message2001',
          content: '以下是用户列表：',
          data: {
            columns: [
              {
                key: 'name',
                title: '姓名'
              },
              {
                key: 'age',
                title: '年龄'
              },
              {
                key: 'email',
                title: '邮箱'
              }
            ],
            rows: [
              {
                name: '张三',
                age: 25,
                email: '<EMAIL>'
              },
              {
                name: '李四',
                age: 30,
                email: '<EMAIL>'
              }
            ]
          }
        }
    }
    },
    Message2002:{ // 图标组件
      data: {
        component: {
          type: 'Message2002',
          content: '以下是近一周股市走势图：',
          data: {
            chartType: 'line',
            chartOptions: {
              xAxis: {
                type: 'category',
                data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri']
              },
              yAxis: {
                type: 'value'
              },
              series: [{
                data: [150, 230, 224, 218, 135],
                type: 'line'
              }]
            }
          }
        }
    }
    },
    Message2003:{ // md组件，支持latext格式 // TODO：待修复 涉及Message4003组件
      data: {
        component: {
          type: 'Message2003',
          data: `
          # 标题
          这是一个Markdown消息。
          
          ## 代码示例
          \`\`\`javascript
          const hello = 'world';
          console.log(hello);
          \`\`\`
          
          ## 数学公式
          行内公式：$E = mc^2$
          
          块级公式：
          $$
          \\frac{n!}{k!(n-k)!} = \\binom{n}{k}
          $$
          
          ## 图表
          \`\`\`mermaid
          graph TD
              A[开始] --> B{判断}
              B -->|是| C[执行]
              B -->|否| D[结束]
              C --> D
          \`\`\`
              `
        }
        
    }
    },
    Message2004:{ // Markdown编辑器消息组件 // TODO：待修复
      data: {
        component: {
          type: 'Message2004',
          data: `
          # Markdown编辑器
          
          这是一个支持实时预览的Markdown编辑器。
          
          ## 工具栏功能
          - 撤销/重做
          - 标题、加粗、斜体
          - 列表、表格
          - 链接、图片、代码
          - 预览、目录、同步滚动、全屏
          
          ## 代码示例
          \`\`\`javascript
          const hello = 'world';
          console.log(hello);
          \`\`\`
          
          ## 数学公式
          行内公式：$E = mc^2$
          
          块级公式：
          $$
          \\frac{n!}{k!(n-k)!} = \\binom{n}{k}
          $$
          
          ## LaTeX环境
          \`\`\`latex
          \\begin{equation}
          f(x) = \\int_{a}^{b} x^2 dx
          \\end{equation}
          \`\`\`
              `
        }
        
    }
    },
    Message2005:{ // 合同问答表格组件
      data: {
        component: {
          type: 'Message2005',
          data: {
            title: '合同详情列表',
            tableData: [
              {
                id: 1,
                name: '合同A',
                date: '2024-01-01',
                status: '已签署',
                amount: '100,000'
              },
              {
                id: 2,
                name: '合同B',
                date: '2024-01-02',
                status: '待签署',
                amount: '200,000'
              }
            ],
            tableColumns: [
              { title: '合同名称', dataIndex: 'name', key: 'name' },
              { title: '签署日期', dataIndex: 'date', key: 'date' },
              { title: '状态', dataIndex: 'status', key: 'status' },
              { title: '金额', dataIndex: 'amount', key: 'amount' }
            ]
          }
        }
        
    }
    },
    Message2006:{ // 合同问答卡片组件 涉及Message2005组件
      data: {
        component: {
          type: 'Message2006',
          data: {
            title: '合同列表',
            tableData: [
              {
                id: 1,
                name: '合同A',
                date: '2024-01-01',
                status: '已签署',
                amount: '100,000'
              },
              {
                id: 2,
                name: '合同B',
                date: '2024-01-02',
                status: '待签署',
                amount: '200,000'
              }
            ],
            tableColumns: [
              { prop: 'name', label: '合同名称' },
              { prop: 'date', label: '签署日期' },
              { prop: 'status', label: '状态' },
              { prop: 'amount', label: '金额' }
            ],
            emptyText: '暂无数据'
          }
        }
      }
    },
    Message2007:{ // 表格组件   涉及Message2005组件
      data: {
        component: {
          type: 'Message2007',
          data: {
            title: '合同列表',
            tableData: [
              {
                id: 1,
                name: '合同A',
                date: '2024-01-01',
                status: '已签署',
                amount: '100,000'
              },
              {
                id: 2,
                name: '合同B',
                date: '2024-01-02',
                status: '待签署',
                amount: '200,000'
              }
            ],
            tableColumns: [
              { prop: 'name', label: '合同名称' },
              { prop: 'date', label: '签署日期' },
              { prop: 'status', label: '状态' },
              { prop: 'amount', label: '金额' }
            ]
          }
        }
        
    }
    },
    Message3001:{ // 语音消息组件
      data: {
        component: {
          type: 'Message3001',
          data: {
            audioUrl: 'https://example.com/audio/message.mp3'  // 示例音频URL
          }
        }
      }
    },
    Message3002: { // 图片消息组件
      data: {
        component: {
          type: 'Message3002',
          data: {
            content: '旅行照片分享',
            images: [
              {
                url: 'https://picsum.photos/800/600?random=1',
                alt: '风景照片1'
              },
              {
                url: 'https://picsum.photos/800/600?random=2',
                alt: '风景照片2'
              },
              {
                url: 'https://picsum.photos/800/600?random=3',
                alt: '风景照片3'
              },
              {
                url: 'https://picsum.photos/800/600?random=4',
                alt: '风景照片4'
              }
            ]
          }
        }
      }
    },
    Message3003:{  //终端组件 
      data: {
        component: {
          type: 'Message3003',
          data: 'ls\n pwd'
        }
        
    }
    },
    Message4001: { // 按钮组消息组件
      data: {
        component: {
          type: 'Message4001',
          data: {
            content: `## 欢迎使用 AI 服务
  
  我可以帮助你完成以下操作：
  
  - 提问并获取解答  
  - 查询知识库内容  
  - 执行预设指令
  
  点击下方任意按钮开始体验。`,
            buttons: [
              { label: '开始提问', value: 'start_question', type: 'primary' },
              { label: '查询知识库', value: 'query_kb', type: 'secondary' },
              { label: '了解更多', value: 'learn_more' } // 默认为 default 类型
            ]
          }
        }
      }
    },
    Message4002: { // 表单消息组件
      data: {
        component: {
          type: 'Message4002',
          data: {
            content: `## 用户信息收集
  
  请填写以下信息，帮助我们更好地为您服务。`,
            fields: [
              {
                name: 'name',
                label: '姓名',
                type: 'text',
                placeholder: '请输入您的姓名'
              },
              {
                name: 'email',
                label: '邮箱',
                type: 'text',
                placeholder: '请输入您的邮箱地址',
                props: {
                  type: 'email'
                }
              },
              {
                name: 'description',
                label: '个人简介',
                type: 'textarea',
                placeholder: '请简单介绍一下您自己'
              },
              {
                name: 'profession',
                label: '职业',
                type: 'select',
                placeholder: '请选择您的职业',
                props: {
                  options: [
                    { value: 'student', label: '学生' },
                    { value: 'engineer', label: '工程师' },
                    { value: 'teacher', label: '教师' },
                    { value: 'other', label: '其他' }
                  ]
                }
              }
            ],
            submitText: '提交信息'
          }
        }
      }
    },
    Message4003:{  //数据编辑表格组件   //TODO：待修复部分样式，例如默认展开收起， 应用文献等
      data: {
        component: {
          type: 'Message4003',
          defaultThinkingMessage: false,
          data: `<think>
          这是AI的思考过程内容，描述了AI如何分析问题并得出结论。
          1. 首先分析用户问题
          2. 查询相关信息
          3. 整合并推理
          </think>
          这是AI给出的最终回答内容，更加简洁和直接。`,
              flowResponses: [
                {
                  quoteList: [
                    { id: 1, sourceName: '文档A' },
                    { id: 2, sourceName: '文档B' }
                  ],
                  runningTime: 2.5,
                  historyPreview: [
                    { id: 1, content: '上下文信息1' },
                    { id: 2, content: '上下文信息2' }
                  ]
                }
              ]
        }
    }
    },
    Message4004:{  //多步骤流程时间轴组件   
      data: {
        component: {
          type: 'Message4004',
          data: [
            {
              stepName: '步骤一：需求分析',
              stepDesc: '分析用户需求并确定目标',
              think: '分析用户提供的信息，确定关键需求点...',
              step: 1,
              stepStatus: 'success', // 可选值: 'unStart', 'loading', 'success', 'error'
              timeConsuming: 1200,
              completionTime: '2023-06-01 10:30:45',
              flowList: [
                {
                  flowName: '需求类型',
                  flowValue: '功能需求',
                  flowComponent: 'a-select',
                  flowDict: ['功能需求', '性能需求', '安全需求', '用户体验需求']
                },
                {
                  flowName: '优先级',
                  flowValue: '高',
                  flowComponent: 'a-select',
                  flowDict: ['高', '中', '低']
                }
              ]
            },
            {
              stepName: '步骤二：设计方案',
              stepDesc: '根据需求设计解决方案',
              think: '考虑多种设计方案，从性能、可维护性等方面对比...',
              step: 2,
              stepStatus: 'loading',
              flowList: [
                {
                  flowName: '设计模式',
                  flowValue: '',
                  flowComponent: 'a-input',
                },
                {
                  flowName: '完成日期',
                  flowValue: '',
                  flowComponent: 'a-date-picker',
                }
              ]
            },
            {
              stepName: '步骤三：实施计划',
              stepDesc: '制定实施计划和时间表',
              think: '',
              step: 3,
              stepStatus: 'unStart',
              flowList: []
            }
          ]
        }
    }
    },
    Message4005:{  //同4003 具体区别待验证
      data: {
        component: {
          type: 'Message4005',
          defaultThinkingMessage: false,
          data: `<think>
          这是AI的思考过程内容，描述了AI如何分析问题并得出结论。
          1. 首先分析用户问题
          2. 查询相关信息
          3. 整合并推理
          </think>
          这是AI给出的最终回答内容，更加简洁和直接。`,
              flowResponses: [
                {
                  quoteList: [
                    { id: 1, sourceName: '文档A' },
                    { id: 2, sourceName: '文档B' }
                  ],
                  runningTime: 2.5,
                  historyPreview: [
                    { id: 1, content: '上下文信息1' },
                    { id: 2, content: '上下文信息2' }
                  ]
                }
              ]
        }
    }
    },
    Message4006:{  //动态表单组件 待修复
        content: '请填写以下用户信息：',
        data: {
          component: {
            type: 'Message4006',
            data: {
              title: '用户信息表单',
              submitText: '提交用户信息',
              fields: [
                {
                  paramsName: '用户名',
                  variableType: 'String',
                  value: '',
                  description: '请输入您的用户名',
                  required: true,
                  isData: true
                },
                {
                  paramsName: '电子邮箱',
                  variableType: 'String',
                  value: '',
                  description: '请输入有效的电子邮箱地址',
                  required: true,
                  isData: true
                },
                {
                  paramsName: '年龄',
                  variableType: 'Number',
                  value: null,
                  description: '请输入您的年龄',
                  isData: true
                },
                {
                  paramsName: '是否激活',
                  variableType: 'Boolean',
                  value: false,
                  description: '选择用户状态',
                  isData: true
                }
              ]
            },
            mockApi: async () => {
              return {
                用户名: '张三',
                电子邮箱: '<EMAIL>',
                年龄: 28,
                是否激活: true
              };
            },
            submitApi: async (data) => {
              console.log('Mock 表单提交数据:', data);
              return Promise.resolve(data);
            }
          }
        }
    },
    Message6001:{  //思考等待组件
        data: {
          component: {
            type: 'Message6001',
            data: "正在处理中"
          }
        }
    },
    Message6002:{  //思考等待组件
        data: {
          component: {
            type: 'Message6002',
            data: "大模型处理失败"
          }
        }
    },
    Message6003 : { //弃用
      messageId: "mock-message-6003",
      data: {
        component: {
          type: "Message6003",
          data: null,
          props: {
            title: "多步骤执行示例",
            steps: [
              {
                stepId: "step-1",
                title: "第一步：准备数据",
                timestamp: 1718000000000,
                data: {
                  component: {
                    type: "StepComponent",
                    data: "准备数据的详细内容",
                    props: {
                      extraInfo: "可选扩展信息"
                    }
                  }
                }
              },
              {
                stepId: "step-2",
                title: "第二步：处理数据",
                timestamp: 1718000001000,
                data: {
                  component: {
                    type: "StepComponent",
                    data: "处理数据的详细内容"
                  }
                }
              },
              {
                stepId: "step-3",
                title: "第三步：输出结果",
                timestamp: 1718000002000,
                data: {
                  component: {
                    type: "StepComponent",
                    data: "输出结果的详细内容"
                  }
                }
              }
            ],
            currentStepIdx: 2, // 当前进行到第二步
            status: "running", // 可选值: running | done | error
            messageId: "mock-message-6003",
            targetMessageId: "",
            progress: 66 // 进度百分比
          }
        }
      }
    },
    Message7001 : { //列表组件  content支持markdown
      data: {
        component:{
          type: 'Message7001',
          data: {
            title: "系统通知",
            description: "重要系统消息和通知",
            messages: [
              {
                agentName: "系统",
                content: "🔔 **系统维护通知**\n\n系统将于今晚22:00-24:00进行例行维护，期间可能影响部分功能使用。\n\n维护内容：\n- 数据库优化\n- 安全补丁更新\n- 性能提升\n\n感谢您的理解与支持！",
                timestamp: Date.now() - 120000
              },
              {
                agentName: "安全中心",
                content: "🛡️ **安全提醒**\n\n检测到您的账户在新设备上登录，如非本人操作请及时修改密码。\n\n登录信息：\n- 时间：2024-01-15 14:30\n- 地点：上海市\n- 设备：iPhone 15",
                timestamp: Date.now() - 60000
              }
            ],
          
          }
      }
      }
    },
    message4007: { //masus组件
      data: {
        component: {
          type: "Message4007", //渲染消息编码
          props: {
            categories: [ //  子任务列表
              {
                title: "编写分析报告", //任务名称
                isCompleted: false, //分类完成状态
                items: [  //子任务拆解完成度
                  {
                    title: "创建分析报告文件并添加目录", //分解任务名称
                    content: "正在创建文件 tesla_analysis/report.md", //当前步骤的内容
                    status: "正在创建文件", //分解任务当前的状态
                  },
                  {
                    title: "添加摘要和市场概况部分",
                    content: "正在向文件添加内容 tesla_analysis/report.md",
                    status: "正在向文件添加内容",
                  },
                  {
                    title: "添加数据与方法部分和股价走势分析部分",
                    content: "正在向文件添加内容 tesla_analysis/report.md",
                    status: "正在向文件添加内容",
                  },
                  {
                    title: "添加波动性分析和关键事件影响分析部分",
                    content: "正在向文件添加内容 tesla_analysis/report.md",
                    status: "正在向文件添加内容",
                  },
                  {
                    title: "添加对比分析、结论与展望以及参考资料部分",
                    content: "正在向文件添加内容 tesla_analysis/report.md",
                    status: "正在向文件添加内容",
                  },
                ],
                activeItemIndex: 0, //当前活跃任务索引 -1表示未开始
              },
              {
                title: "生成最终报表并交付给用户",
                isCompleted: false,
                isExpanded: true,
                items: [
                  {
                    title: "生成PDF报告",
                    content: "正在执行命令 manus-md-to-pdf /home/<USER>/tesla_analysis/report.md",
                    status: "正在执行命令",
                  },
                  {
                    title: "创建包含所有分析内容的压缩文件",
                    content: "cd /home/<USER>/",
                    status: "正在执行命令",
                  },
                ],
                activeItemIndex: -1,
              },
            ],
            // 最终结论
            analysisResult:
             `我已完成特斯拉与苹果股价对比分析报告。以下是主要发现：
  
  **差异化表现**：在2025年3月至6月期间，特斯拉股价总体上涨8.07%，而苹果股价下跌14.75%，相对表现差距达22.81个百分点。
  
  **波动性特征**：特斯拉股价波动性（5.79%）显著高于苹果（3.26%），是后者的1.78倍。
  
  **低相关性**：两只股票的价格相关系数为-0.05，接近于零，表明它们受到不同因素的影响。
  
  **关键事件影响**：特斯拉股价受马斯克个人言行影响显著，尤其是6月5日与特朗普关系破裂导致股价暴跌14%；苹果则主要受监管环境变化和供应链调整影响。
  
  **投资风格差异**：特斯拉代表高增长、高波动的投资风格；苹果则代表相对稳定、波动较小的投资风格。`,
          },
        },
      },
    }
  }