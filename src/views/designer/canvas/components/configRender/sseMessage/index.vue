<template>
    <a-form layout="vertical" class="w-full">
        <a-form-item label="消息类型" class="w-full">
            <a-select :value="messageType || null" @update:value="(val) => handleUpdateField(val)" placeholder="请选择消息类型"
                class="w-full flex-1" :options="messageTypeOptions" showSearch />
        </a-form-item>
        <a-form-item label="消息内容" class="w-full">
            <div class="flex items-center mb-2">
                <a-radio-group :value="currentMode" @update:value="val => currentMode = val" button-style="solid">
                    <a-radio-button value="json">JSON编辑</a-radio-button>
                    <a-radio-button value="preview">预览</a-radio-button>
                </a-radio-group>
            </div>
            
            <div v-show="currentMode === 'json'" class="w-full h-[300px]">
                <json-editor
                    :modelValue="messageContent"
                    class="h-full w-full"
                    @update:modelValue="handleContentChange"
                />
            </div>
            <div v-show="currentMode === 'preview'" class="w-full min-h-[300px] border rounded p-4">
                <component 
                    v-if="messageType && currentComponent"
                    :is="currentComponent"
                    v-bind="messageContentObj"
                    :message="messageContentObj"
                    :data="messageContentObj.data"
                    class="w-full"
                />
                <div v-else class="text-gray-400">请先选择消息类型</div>
            </div>
        </a-form-item>
    </a-form>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useAppStore } from '@/store/app'
import { useDesignerStore } from '@/store/designer'
import JsonEditor from '@/views/designer/canvas/components/jsonEditor/index.vue'
import constant from './constant/index'
import * as MessageComponents from '@yss/ai-chat-components'

const appStore = useAppStore()
const designerStore = useDesignerStore()

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({})
    },
})

const emit = defineEmits(['update:modelValue'])

const updateField = (field, value) => {
    emit('update:modelValue', Object.assign(designerStore.currentNodeData?.properties, {
        [field]: value
    }))
}

const messageType = ref(props.modelValue?.messageType || '')
const messageContent = ref(props.modelValue?.content ? JSON.stringify(props.modelValue.content, null, 2) : '')
const currentMode = ref('json')

const messageTypeOptions = computed(() => {
    const options = appStore.getEnumOptions('MessageType')
    return options.map(item => ({
        label: item.label,
        value: item.value.split('-')?.[0] || item.value
    }))
})

// 计算当前应该显示的组件
const currentComponent = computed(() => {
    if (!messageType.value) return null
    return MessageComponents[messageType.value] || null
})

// 解析消息内容为对象
const messageContentObj = computed(() => {
    try {
        console.log('messageContent.value', messageContent.value)
        return messageContent.value ? JSON.parse(messageContent.value) : {}
    } catch (e) {
        console.warn('Invalid JSON:', e)
        return {}
    }
})

// 更新消息类型
const handleUpdateField = (val) => {
    messageType.value = val
    updateField('messageType', val)
}

// 更新消息内容
const handleContentChange = (val) => {
    messageContent.value = val
    try {
        const jsonContent = val ? JSON.parse(val) : {}
        updateField('content', jsonContent)
    } catch (e) {
        console.warn('Invalid JSON:', e)
    }
}

watch(messageType, (newVal) => {
    if (newVal) {
        const component = constant[newVal]
        messageContent.value = JSON.stringify(component, null, 2)
    }
})
</script>

<style scoped>
:deep(.monaco-editor) {
    height: 100% !important;
    min-height: 300px !important;
}
</style>
