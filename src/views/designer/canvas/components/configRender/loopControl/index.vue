<template>
  <div class="control-wrapper" :class="{ 'is-fullscreen': isFullscreen }">
    <div class="control-header">
      <div class="title">循环控制</div>
      <div class="flex-spacer"></div>
      <a-button type="text" class="fullscreen-btn" @click="toggleFullscreen">
        <template #icon>
          <FullscreenExitOutlined v-if="isFullscreen" />
          <FullscreenOutlined v-else />
        </template>
      </a-button>
    </div>
    <div class="control-content">
      <a-form layout="vertical">
        <a-form-item label="循环类型">
          <a-select 
            v-model:value="loopType" 
            :options="loopOptions"
            @change="handleLoopTypeChange"
          />
        </a-form-item>
        <a-form-item 
          v-if="loopType === 'SpecifiedTimes'" 
          label="循环次数"
        >
          <a-input-number 
            v-model:value="loopCount" 
            :min="1" 
            @change="handleParamsChange"
          />
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watchEffect, onMounted } from 'vue'
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue'
import { useDesignerStore } from '@/store/designer'
import type { SelectProps } from 'ant-design-vue'

interface Props {
  modelValue: Record<string, any>
  entryParams?: any[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'execute'])

const designerStore = useDesignerStore()
const isFullscreen = ref(false)
const loopType = ref<LoopType>('Array')
const loopCount = ref<number>(1)

type LoopType = 'Array' | 'Infinity' | 'SpecifiedTimes'

const loopOptions: SelectProps['options'] = [
  { value: 'Array', label: '数组循环' },
  { value: 'SpecifiedTimes', label: '指定次数' },
  { value: 'Infinity', label: '无限循环' }
]

// 计算实际循环次数
const actualLoopCount = computed(() => {
  switch (loopType.value) {
    case 'Array':
      return props.entryParams?.length || 0
    case 'Infinity':
      return Infinity
    default:
      return loopCount.value
  }
})

// 参数变化处理
const handleParamsChange = () => {
  emit('update:modelValue', {
    loopType: loopType.value,
    loopCount: actualLoopCount.value
  })
}

// 循环类型变化时重置参数
const handleLoopTypeChange = () => {
  if (loopType.value !== 'SpecifiedTimes') {
    loopCount.value = 1
  }
  handleParamsChange()
}

// 执行循环逻辑
watchEffect(() => {
  const loopBodyNodes = designerStore.modGraph?.getNodes()
    .filter(n => n.getData().type === 'loopBodyNode') || []
  
  if (actualLoopCount.value === Infinity) {
    // 处理无限循环逻辑
    let count = 0
    const executeLoop = () => {
      if (count < 1000) { // 安全限制
        loopBodyNodes.forEach(node => {
          node.setData({ ...node.getData(), loopIndex: count++ })
          designerStore.executeNode(node.id)
        })
        requestAnimationFrame(executeLoop)
      }
    }
    executeLoop()
  } else {
    // 有限次数循环
    for (let i = 0; i < actualLoopCount.value; i++) {
      loopBodyNodes.forEach(node => {
        node.setData({ ...node.getData(), loopIndex: i })
        designerStore.executeNode(node.id)
      })
    }
  }
})

// 全屏切换逻辑
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// 初始化参数
onMounted(() => {
  if (props.modelValue) {
    loopType.value = props.modelValue.loopType || 'Array'
    loopCount.value = props.modelValue.loopCount || 1
  }
})
</script>

<style scoped>
.control-wrapper {
  border: 1px solid #ccc;
  border-radius: 8px;
  background: white;
}

.control-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f0f2f5;
  border-bottom: 1px solid #ccc;
  border-radius: 8px 8px 0 0;
}

.control-content {
  padding: 16px;
  min-height: 200px;
}

.is-fullscreen {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80vw;
  height: 80vh;
  z-index: 1000;
  box-shadow: 0 0 20px rgba(0,0,0,0.2);
}
</style>