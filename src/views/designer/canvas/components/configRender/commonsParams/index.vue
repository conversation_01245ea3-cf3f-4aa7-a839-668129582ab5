<template>
  <ResponsiveLayout class-prefix="commons-params">
    <div class="w-full relative responsive-params">
      <!-- 根级别参数组 -->
      <div v-for="(group, groupName) in rootGroups" :key="groupName" class="mb-8">
        <div class="flex items-center justify-between mb-4">
          <span class="text-[16px] font-medium dark:!text-[var(--text-secondary)]">{{ groupName }}</span>
          <a-button
            type="link"
            @click="addParam(groupName)"
            class="flex items-center !h-[24px] !p-0 text-blue-600"
          >
            <PlusOutlined class="text-[14px]" />
          </a-button>
        </div>

        <!-- 参数列表 -->
        <div v-if="parameters[groupName]?.length" class="pl-4">
          <div class="flex-param-header text-gray-500 text-[12px] mb-3">
            <div class="flex-param-name dark:!text-[var(--grey-auxiliary-color)]">参数名</div>
            <div class="flex-param-type dark:!text-[var(--grey-auxiliary-color)]">参数类型</div>
            <div class="flex-param-length dark:!text-[var(--grey-auxiliary-color)]">长度</div>
            <div class="flex-param-value dark:!text-[var(--grey-auxiliary-color)]">参数值</div>
            <div class="flex-param-desc dark:!text-[var(--grey-auxiliary-color)]">描述</div>
            <div class="flex-param-actions">操作</div>
          </div>

          <commons-parameter-item
            v-for="(param, index) in parameters[groupName]"
            :key="index"
            :param="param"
            :typeOptions="typeOptions"
            :allow-children="true"
            @remove="removeParam(groupName, index)"
            @update:param="updateParam(groupName, index, $event)"
          />
        </div>
      </div>
    </div>
  </ResponsiveLayout>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { PropType } from 'vue'
import CommonsParameterItem from './CommonsParameterItem.vue'
import ResponsiveLayout from '../ResponsiveLayout.vue'

interface Parameter {
  name: string
  type: string
  value?: any
  description?: string
  expanded?: boolean
  children?: Parameter[]
  length?: number
}

interface ParameterGroups {
  [key: string]: Parameter[]
}

const rootGroups = {
  headers: 'headers',
  commons: 'commons',
  data: 'data'
} as const

const props = defineProps({
  modelValue: {
    type: Object as PropType<ParameterGroups>,
    default: () => ({
      headers: [],
      commons: [],
      data: []
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const parameters = ref<ParameterGroups>(props.modelValue)

// 初始化时规范化数据
const normalizeParameters = () => {
  if (!parameters.value) {
    parameters.value = {
      headers: [],
      commons: [],
      data: []
    }
  }

  // 确保所有需要的组都存在且是数组
  Object.keys(rootGroups).forEach(groupName => {
    if (!parameters.value[groupName]) {
      parameters.value[groupName] = []
    } else if (typeof parameters.value[groupName] === 'string') {
      console.warn(`组 ${groupName} 的值是字符串而不是数组, 正在自动转换为数组`);
      parameters.value[groupName] = []
    } else if (!Array.isArray(parameters.value[groupName])) {
      console.warn(`组 ${groupName} 的值不是数组, 类型是: ${typeof parameters.value[groupName]}, 正在自动转换为数组`);
      parameters.value[groupName] = []
    }
  })
}

// 初始化时调用
normalizeParameters()

// 当modelValue变化时也要规范化
watch(() => props.modelValue, (newVal) => {
  parameters.value = newVal
  // 确保数据结构正确
  normalizeParameters()
}, { deep: true })

watch(parameters, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

const addParam = (group: string) => {
  // 确保组存在且是数组
  if (!parameters.value[group]) {
    parameters.value[group] = []
  } else if (typeof parameters.value[group] === 'string') {
    console.warn(`组 ${group} 的值是字符串而不是数组, 正在自动转换为数组`);
    parameters.value[group] = []
  } else if (!Array.isArray(parameters.value[group])) {
    console.warn(`组 ${group} 的值不是数组, 类型是: ${typeof parameters.value[group]}, 正在自动转换为数组`);
    parameters.value[group] = []
  }

  // 添加一个新参数
  parameters.value[group].push({
    name: '',
    type: 'String',
    value: '',
    description: '',
    expanded: false,
    children: [],
    length: 255 // 默认字符串长度
  })
}

const removeParam = (group: string, index: number) => {
  const newParameters = JSON.parse(JSON.stringify(parameters.value))
  newParameters[group].splice(index, 1)
  parameters.value = newParameters
  emit('update:modelValue', newParameters)
}

const updateParam = (group: string, index: number, newParam: Parameter) => {
  parameters.value[group][index] = newParam
}

const typeOptions = [
  { value: 'String', label: 'String' },
  { value: 'Integer', label: 'Integer' },
  { value: 'Boolean', label: 'Boolean' },
  { value: 'Number', label: 'Number' },
  { value: 'Object', label: 'Object' },
  { value: 'File', label: 'File' },
  { value: 'Array', label: 'Array' },
]
</script>

<style lang="scss">
@import '../responsive-layout.scss';
</style>

<style scoped>
.ant-btn-link {
  color: rgb(37 99 235) !important;
}

/* Flex布局样式 */
.flex-param-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.flex-param-name {
  flex: 2;
  min-width: 80px;
}

.flex-param-type {
  flex: 1;
  min-width: 70px;
}

.flex-param-length {
  flex: 0.8;
  min-width: 60px;
}

.flex-param-value {
  flex: 2.5;
  min-width: 100px;
}

.flex-param-desc {
  flex: 1.5;
  min-width: 80px;
}

.flex-param-actions {
  flex: 0.5;
  min-width: 60px;
  text-align: center;
}

/* 全屏模式下的flex比例调整 */
.fullscreen-drawer {
  .flex-param-name {
    flex: 2.5;
    min-width: 200px;
  }

  .flex-param-type {
    flex: 1.2;
    min-width: 120px;
  }

  .flex-param-length {
    flex: 1;
    min-width: 100px;
  }

  .flex-param-value {
    flex: 3;
    min-width: 250px;
  }

  .flex-param-desc {
    flex: 2;
    min-width: 150px;
  }

  .flex-param-actions {
    flex: 0.8;
    min-width: 100px;
  }
}
</style>
