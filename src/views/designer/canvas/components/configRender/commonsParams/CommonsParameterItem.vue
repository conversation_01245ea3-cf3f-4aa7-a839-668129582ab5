<template>
  <div class="relative">
    <div class="flex-param-row">
      <!-- 参数名称输入 -->
      <div class="flex-param-name">
        <div class="flex items-center gap-2">
          <!-- 展开/折叠按钮 -->
          <div v-if="isObjectOrArray" class="cursor-pointer w-4" @click="toggleExpand">
            <RightOutlined v-if="!localParam.expanded" class="text-gray-400 text-[12px]" />
            <DownOutlined v-else class="text-gray-400 text-[12px]" />
          </div>
          <a-input
            v-model:value="localParam.name"
            placeholder="请输入"
            class="!h-[32px] text-[12px] flex-1"
            :maxLength="20"
          >
            <template #suffix>{{ localParam.name?.length || 0 }}/20</template>
          </a-input>
        </div>
      </div>

      <!-- 参数类型选择 -->
      <div class="flex-param-type">
        <a-select
          v-model:value="localParam.type"
          class="!w-full"
          @change="handleTypeChange"
          :dropdown-match-select-width="false"
          :popup-container="(triggerNode) => triggerNode.parentNode"
        >
          <a-select-option v-for="type in typeOptions" :key="type.value" :value="type.value">
            {{ type.label }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 参数长度输入 -->
      <div class="flex-param-length">
        <a-input-number
          v-if="showLengthField"
          v-model:value="localParam.length"
          placeholder="长度"
          class="!w-full !h-[32px] text-[12px]"
          :min="1"
          :max="9999"
        />
        <div v-else class="w-full"></div>
      </div>

      <!-- 变量值输入 -->
      <div class="flex-param-value">
        <a-input
          v-if="!isObjectOrArray"
          v-model:value="localParam.value"
          placeholder="参数值"
          class="!w-full !h-[32px] text-[12px]"
        />
        <div v-else class="w-full"></div>
      </div>

      <!-- 描述和操作 -->
      <div class="flex-param-desc">
        <a-input
          v-model:value="localParam.description"
          placeholder="描述"
          class="!h-[32px] text-[12px]"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="flex-param-actions">
        <div class="flex items-center justify-center gap-1">
          <!-- 子参数添加按钮 -->
          <a-button
            v-if="isObjectOrArray && localParam.expanded"
            type="link"
            @click="addChild"
            class="action-btn text-blue-600"
          >
            <PlusOutlined class="text-[12px]" />
          </a-button>

          <!-- 删除按钮 -->
          <a-button type="text" @click="$emit('remove')" class="action-btn text-gray-400">
            <CloseOutlined class="text-[12px]" />
          </a-button>
        </div>
      </div>
    </div>

    <!-- 子参数区域 -->
    <div
      v-if="isObjectOrArray && localParam.expanded"
      class="pl-8 border-l border-l-gray-200 ml-2 mt-2"
    >
      <commons-parameter-item
        v-for="(child, index) in localParam.children"
        :key="index"
        :param="child"
        :typeOptions="typeOptions"
        :allow-children="true"
        @remove="removeChild(index)"
        @update:param="updateChild(index, $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { RightOutlined, DownOutlined, PlusOutlined, CloseOutlined } from '@ant-design/icons-vue'
import type { PropType } from 'vue'

interface Parameter {
  name: string
  type: string
  value?: any
  description?: string
  expanded?: boolean
  children?: Parameter[]
  isFile?: boolean
  length?: number
}

const props = defineProps({
  param: {
    type: Object as PropType<Parameter>,
    required: true
  },
  typeOptions: {
    type: Array as PropType<{ value: string; label: string }[]>,
    required: true
  },
  allowChildren: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:param', 'remove'])

const localParam = computed({
  get: () => props.param,
  set: (newValue) => {
    emit('update:param', newValue)
  }
})

const isObjectOrArray = computed(() => {
  return localParam.value.type === 'Object' || localParam.value.type.startsWith('Array')
})

// 显示长度字段的条件：String、Integer、Number类型需要显示长度
const showLengthField = computed(() => {
  return ['String', 'Integer', 'Number'].includes(localParam.value.type)
})

const handleTypeChange = (value: string) => {
  const newParam = { ...localParam.value }
  if (value === 'Object' || value.startsWith('Array')) {
    if (!newParam.children) {
      newParam.children = []
    }
    newParam.expanded = true
  } else if (value === 'File') {
    newParam.children = undefined
    newParam.isFile = true
  } else {
    newParam.children = undefined
    newParam.isFile = false
  }

  // 当类型改变时，设置默认长度
  if (['String', 'Integer', 'Number'].includes(value)) {
    newParam.length = value === 'String' ? 255 : 11
  } else {
    newParam.length = undefined
  }

  emit('update:param', newParam)
}

const updateParam = () => {
  emit('update:param', { ...localParam.value })
}

const toggleExpand = () => {
  const newParam = { ...localParam.value }
  newParam.expanded = !newParam.expanded
  emit('update:param', newParam)
}

const addChild = () => {
  const newParam = { ...localParam.value }
  if (!newParam.children) {
    newParam.children = []
  }
  newParam.children.push({
    name: '',
    type: 'String',
    value: '',
    description: '',
    expanded: false,
    children: [],
    isFile: false,
    length: 255 // 默认字符串长度
  })
  emit('update:param', newParam)
}

const removeChild = (index: number) => {
  const newParam = { ...localParam.value }
  newParam.children?.splice(index, 1)
  emit('update:param', newParam)
}

const updateChild = (index: number, newChild: Parameter) => {
  const newParam = { ...localParam.value }
  if (newParam.children) {
    newParam.children[index] = newChild
    emit('update:param', newParam)
  }
}
</script>

<style lang="scss">
@import '../responsive-layout.scss';
</style>

<style scoped>
/* Flex布局样式 */
.flex-param-row {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
  min-height: 48px;
}

.flex-param-name {
  flex: 2;
  min-width: 80px;
}

.flex-param-type {
  flex: 1;
  min-width: 70px;
}

.flex-param-length {
  flex: 0.8;
  min-width: 60px;
}

.flex-param-value {
  flex: 2.5;
  min-width: 100px;
}

.flex-param-desc {
  flex: 1.5;
  min-width: 80px;
}

.flex-param-actions {
  flex: 0.5;
  min-width: 60px;
}

/* 全屏模式下的flex比例调整 */
:global(.fullscreen-drawer) .flex-param-row {
  gap: 16px;
  padding: 12px 0;

  .flex-param-name {
    flex: 2.5;
    min-width: 200px;
  }

  .flex-param-type {
    flex: 1.2;
    min-width: 120px;
  }

  .flex-param-length {
    flex: 1;
    min-width: 100px;
  }

  .flex-param-value {
    flex: 3;
    min-width: 250px;
  }

  .flex-param-desc {
    flex: 2;
    min-width: 150px;
  }

  .flex-param-actions {
    flex: 0.8;
    min-width: 100px;
  }
}

:deep(.ant-select-selector) {
  height: 32px !important;
  line-height: 32px !important;
}

:deep(.ant-select-selection-item) {
  line-height: 30px !important;
  font-size: 12px !important;
}

.action-btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
  flex-shrink: 0;
}

:deep(.ant-input) {
  display: flex !important;
  align-items: center !important;
}

:deep(.ant-input-affix-wrapper) {
  padding: 0 11px !important;
  height: 32px !important;
}

:deep(.ant-select-selector) {
  display: flex !important;
  align-items: center !important;
  height: 32px !important;
}

:deep(.ant-select-dropdown) {
  min-width: 200px !important;
}

:deep(.ant-input-number) {
  height: 32px !important;
  line-height: 32px !important;
}

:deep(.ant-input-number-input) {
  height: 30px !important;
  font-size: 12px !important;
}
</style>
