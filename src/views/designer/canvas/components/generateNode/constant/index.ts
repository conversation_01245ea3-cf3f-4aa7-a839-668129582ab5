export const promptText = `
请根据上面输入参数生成一个节点配置JSON：
输入参数配置：
name-名称：<请在此处填写节点名称>
input-输入参数：<请在此处填写JSON格式的输入参数>
output-输出参数：<请在此处填写JSON格式的输出参数>
leftPort-左侧端口数：<请在此处填写左侧端口数>
rightPort-右侧端口数：<请在此处填写右侧端口数>

请按照以下规则生成配置：基础结构：{
  "id": "节点ID",
  "type": "节点类型",
  "title": "节点名称",
  "description": "功能描述",
  "ports": [],
  "components": []
}

1. id和type使用节点名称的英文翻译或拼音，采用驼峰命名法，首字母大写，使用大小写字母组合，不要使用下划线
2. title使用输入的节点名称
3. description对节点功能进行详细说明
4. ports根据输入的左右端口数生成，id从1开始自增
5. components根据输入参数和输出参数自动生成，包含原生组件和自定义组件：

**原生组件（antd-vue）**：
- 字符串类型：a-input
- 数字类型：a-input-number
- 布尔类型：a-switch
- 下拉选择：a-select（多选用a-select-v2）
- 多行文本：a-textarea
- 日期时间：a-date-picker
- 其他antd-vue组件：a-button、a-checkbox、a-radio等

**自定义组件**：

1. **conditional** - 条件分支组件，用于设置多个条件判断及分支逻辑
   {
     "type": "conditional",
     "field": "conditions",
     "label": "条件分支",
     "props": {}
   }

2. **paramRef** - 参数引用组件，允许用户输入值或引用参数，支持树形参数选择
   {
     "type": "paramRef",
     "field": "field_name",
     "label": "参数引用",
     "props": {
       "placeholder": "提示文本",
       "paramType": "参数类型"
     }
   }

3. **codeEditor** - 代码编辑器组件，支持多种语言的代码编辑和全屏模式
   {
     "type": "codeEditor",
     "field": "code_field",
     "label": "代码编辑",
     "props": {
       "language": "编程语言",
       "placeholder": "请输入代码"
     }
   }

4. **resultParams** - 结果参数显示组件，展示变量名、类型和描述，每个节点都必须添加
   {
     "type": "resultParams",
     "field": "results",
     "label": "结果参数",
     "props": {
       "defaultParams": {
         "data": [
           {
             "name": "参数名",
             "type": "参数类型",
             "description": "参数说明"
           }
         ]
       }
     }
   }

5. **skillSelector** - 技能选择器，用于添加和管理各类技能（知识库、记忆、时间等）
   {
     "type": "skillSelector",
     "field": "skills",
     "label": "技能选择",
     "props": {}
   }

6. **tableEditor** - 表格编辑器，用于编辑表格数据
   {
     "type": "tableEditor",
     "field": "table_data",
     "label": "表格数据",
     "props": {
       "columns": [
         {
           "title": "列标题",
           "key": "列键名",
           "type": "列类型"
         }
       ]
     }
   }

7. **contentParams** - 内容参数配置组件，用于设置节点内容参数
   {
     "type": "contentParams",
     "field": "contents",
     "label": "内容参数",
     "props": {
       "defaultParams": [
         {
           "field": "参数名",
           "type": "参数类型",
           "value": "默认值",
           "description": "参数说明"
         }
       ]
     }
   }

8. **timer** - 定时器配置组件，用于设置定时任务参数
   {
     "type": "timer",
     "field": "timer_config",
     "label": "定时器配置",
     "props": {}
   }

9. **upload** - 文件上传组件，支持文件选择和上传
   {
     "type": "upload",
     "field": "upload_file",
     "label": "文件上传",
     "props": {}
   }

10. **loopControl** - 循环控制组件，用于设置循环条件和操作
    {
      "type": "loopControl",
      "field": "loop_config",
      "label": "循环控制",
      "props": {}
    }

11. **radioConfig** - 单选配置组件，用于设置单选参数
    {
      "type": "radioConfig",
      "field": "radio_value",
      "label": "单选配置",
      "props": {
        "options": [
          {"label": "选项1", "value": "value1"},
          {"label": "选项2", "value": "value2"}
        ]
      }
    }

12. **reference** - 引用组件，用于引用其他节点或参数
    {
      "type": "reference",
      "field": "reference_path",
      "label": "引用配置",
      "props": {}
    }

13. **dynamicInputList** - 动态输入列表，支持添加/删除多个输入项
    {
      "type": "dynamicInputList",
      "field": "input_list",
      "label": "动态输入列表",
      "props": {}
    }

14. **fileUpload** - 文件上传组件，专门处理文件上传功能
    {
      "type": "fileUpload",
      "field": "file_info",
      "label": "文件上传",
      "props": {}
    }

15. **httpQueryBody** - HTTP请求参数配置，用于设置请求体和查询参数
    {
      "type": "httpQueryBody",
      "field": "http_config",
      "label": "HTTP请求配置",
      "props": {}
    }

16. **captchaVerifier** - 验证码验证组件，用于验证码输入和验证
    {
      "type": "captchaVerifier",
      "field": "captcha_value",
      "label": "验证码验证",
      "props": {}
    }

17. **codeDiff** - 代码差异对比组件，展示两段代码的差异
    {
      "type": "codeDiff",
      "field": "code_diff",
      "label": "代码对比",
      "props": {
        "originalCode": "原始代码",
        "newCode": "新代码"
      }
    }

18. **commonsParams** - 通用参数配置组件，处理常见参数设置
    {
      "type": "commonsParams",
      "field": "common_params",
      "label": "通用参数",
      "props": {}
    }

19. **captchaSender** - 验证码发送组件，用于发送验证码
    {
      "type": "captchaSender",
      "field": "captcha_sender",
      "label": "验证码发送",
      "props": {
        "phone": "手机号"
      }
    }
20. **outParams** - 标准输入输出配置，**每个节点都必须添加输入和输出模块**，放在components的第一个位置和最后一个位置
   数据结构:
   {
     "type": "outParams",
     "field": "inputs",
     "label": "输入参数"
   }
   {
     "type": "outParams",
     "field": "outputs",
     "label": "输出参数"
   }

**重要提醒：每个生成的节点配置都必须包含输入参数和输出参数组件，即使用户没有明确指定输入输出参数，也要添加默认的outParams组件。**
**重要提醒：所有的参数名不能包含中文，只能使用英文，如filed字段，如resultParams的data中的name不能包含中文**

请直接返回JSON格式，不要返回其他内容。

 /nothink

`