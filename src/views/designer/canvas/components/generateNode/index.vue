<template>
    <a-modal
      :visible="visible"
      title="生成节点配置"
      @cancel="handleCancel"
      @ok="handleSubmit"
      @update:visible="(val) => emit('update:visible', val)"
      width="90%"
      class="generate-node-modal"
      :bodyStyle="{ padding: '0' }"
      :maskClosable="false"
      okText="生成后端"
    >
      <div class="flex h-[700px]">
        <!-- 左侧节点配置 -->
        <div :class="[
          'flex flex-col transition-all duration-300',
          leftPanelCollapsed ? 'w-12' : 'w-1/3'
        ]">
          <div class="px-6 py-4 flex justify-between items-center">
            <h3 v-if="!leftPanelCollapsed" class="text-base font-medium">节点配置</h3>
            <a-button
              type="text"
              size="small"
              @click="leftPanelCollapsed = !leftPanelCollapsed"
              class="flex-shrink-0"
            >
              <template #icon>
                <MenuUnfoldOutlined v-if="leftPanelCollapsed" />
                <MenuFoldOutlined v-else />
              </template>
            </a-button>
          </div>
          <div v-if="!leftPanelCollapsed" class="flex-1 p-6 overflow-hidden">
            <a-form
              ref="formRef"
              :model="form"
              :rules="rules"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
              class="h-full flex flex-col"
            >
              <a-form-item label="名称" name="name" class="mb-4">
                <a-input
                  v-model:value="form.name"
                  placeholder="请输入节点名称"
                  class="h-9"
                />
              </a-form-item>

              <a-form-item label="提示词" name="prompt" class="flex-1 mb-4">
                <a-textarea
                  v-model:value="form.prompt"
                  placeholder="请输入提示词描述节点功能"
                  :rows="15"
                  class="resize-none"
                />
              </a-form-item>

              <div class="grid grid-cols-2 gap-3 mb-4">
                <a-form-item label="左侧端口" name="leftPort" class="mb-0">
                  <a-input
                    v-model:value="form.leftPort"
                    placeholder="左侧端口"
                    class="h-9"
                  />
                </a-form-item>
                <a-form-item label="右侧端口" name="rightPort" class="mb-0">
                  <a-input
                    v-model:value="form.rightPort"
                    placeholder="右侧端口"
                    class="h-9"
                  />
                </a-form-item>
              </div>

              <a-button
                type="primary"
                :loading="loading"
                @click="getConfig"
                class="w-full h-10 mt-auto"
              >
                生成配置
              </a-button>
            </a-form>
          </div>
        </div>

        <!-- 中间节点JSON -->
        <div class="flex-1 flex flex-col">
          <div class="px-6 py-4">
            <h3 class="text-base font-medium">生成配置</h3>
          </div>
          <div class="flex-1 p-6 overflow-hidden">
            <div class="h-full">
              <!-- @vue-ignore -->
              <JsonEditorVue
                v-model="form.content"
                :main-menu-bar="false"
                :navigation-bar="false"
                :status-bar="false"
                :expandedOnStart="true"
                mode="text"
                class="json-editor h-full"
                @change="handleJsonChange"
              />
            </div>
          </div>
        </div>

        <!-- 右侧预览 -->
        <div class="flex-1 flex flex-col">
          <div class="px-6 py-4">
            <h3 class="text-base font-medium">预览效果</h3>
          </div>
          <div class="flex-1 p-6 overflow-y-auto">
            <div class="h-full">
              <NodeConfigPreview v-if="previewConfig" :node-data="previewConfig" class="h-full" />
              <div v-else class="flex items-center justify-center h-full text-gray-400">
                <div class="text-center">
                  <p class="text-sm">请输入有效的节点配置进行预览</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </template>
  
  <script lang="ts" setup>
  import { ref, watch } from 'vue'
  import { message } from 'ant-design-vue'
  import type { FormInstance } from 'ant-design-vue'
  import JsonEditorVue from 'json-editor-vue'
  import NodeConfigPreview from '@/views/designer/nodes/components/NodeConfigPreview.vue'
  import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue'
  import { useDesignerStore } from '@/store/designer'
  import { generateNodeConfig, generateNodeCode } from '@/apis/designer'
  import { promptText } from './constant/index'
  import jaison from 'jaison'

  const designerStore = useDesignerStore()
  
  const props = defineProps<{
    visible: boolean
  }>()
  
  const emit = defineEmits(['update:visible', 'success'])
  
  const formRef = ref<FormInstance>()
  const form = ref({
    name: '',
    input: '{}',
    output: '{}',
    leftPort: '',
    rightPort: '',
    prompt: '',
    content: '{}',
    type: 'generate'
  })
  
  const loading = ref(false)

  // 添加预览配置
  const previewConfig = ref<any>(null)

  // 左侧面板折叠状态
  const leftPanelCollapsed = ref(false)
  
  const rules = {
    name: [{ required: true, message: '请输入名称' }],
    leftPort: [{ required: true, message: '请输入左侧端口' }],
    rightPort: [{ required: true, message: '请输入右侧端口' }],
    prompt: [{ required: true, message: '请输入提示词' }],
  }
  
  // 处理 JSON 变化
  const handleJsonChange = () => {
    try {
      if (!form.value.content) {
        previewConfig.value = null
        return
      }
  
      const config = typeof form.value.content === 'string'
        ? JSON.parse(form.value.content)
        : form.value.content
  
      previewConfig.value = config ? {
        getData: () => ({
          type: form.value.type,
          ...config,
          // 确保formValues是一个新对象而不是引用
          formValues: { ...config.formValues }
        })
      } : null
    } catch (error) {
      previewConfig.value = null
    }
  }

  // 监听表单内容变化
  watch(() => form.value.content, () => {
    handleJsonChange()
  }, { immediate: true })

  // 监听弹窗显示状态
  watch(() => props.visible, (newVisible) => {
    if (newVisible) {
      // 弹窗显示时初始化表单
      form.value = {
        name: '',
        input: '{}',
        output: '{}',
        leftPort: '',
        rightPort: '',
        prompt: '',
        content: '{}',
        type: 'generate'
      }
    }
  })

  const getConfig = async () => {
    try {
      loading.value = true
      // 整合用户填写的内容到模板提示词中
      const finalPrompt = promptText
        .replace('<请在此处填写节点名称>', form.value.name || '未命名节点')
        .replace('<请在此处填写JSON格式的输入参数>', form.value.input || '{}')
        .replace('<请在此处填写JSON格式的输出参数>', form.value.output || '{}')
        .replace('<请在此处填写左侧端口数>', form.value.leftPort || '1')
        .replace('<请在此处填写右侧端口数>', form.value.rightPort || '1') +
        `\n\n用户描述：${form.value.prompt || '无特殊要求'}`

      const data = {
        "requestId": "req-001",
        "modelName": "Qwen3-8B",
        "systemPrompt": "你是一个节点生成器，请根据需求生成节点配置。",
        "chat": finalPrompt,
        "temperature": 0.7,
        "maxTokens": 3000
      }
      const res = await generateNodeConfig(data)
      if (res.code === 200) {
        const result = jaison(res.data.result)
        if (result && result?.fixedJson) {
          form.value.content = JSON.parse(JSON.stringify(result.fixedJson))
        } else {
          form.value.content = JSON.parse(JSON.stringify(res.data.result))
        }
      } else {
        message.error('生成配置失败，请检查后重试')
      }
    } catch (error) {
      message.error('生成配置失败')
    } finally {
      loading.value = false
    }
  }
  
  // 取消
  const handleCancel = () => {
    designerStore.setGenerateNodePanelVisiable(false)
  }
  
  // 提交
  const handleSubmit = async () => {
    try {
      const content = typeof form.value.content === 'string' ? JSON.parse(form.value.content) : form.value.content

      // 处理output
      let output = {}
      if (Array.isArray(content.components)) {
        const resultParamsComp = content.components.find(
          comp => comp.type === 'resultParams' && comp.props && comp.props.defaultParams && Array.isArray(comp.props.defaultParams.data)
        )
        if (resultParamsComp) {
          resultParamsComp.props.defaultParams.data.forEach(item => {
            if (item.name && item.type) {
              output[item.name] = item.type
            }
          })
        } else {
          output = {}
        }
      } else {
        output = {}
      }

      // 处理properties
      let properties = {}
    if (Array.isArray(content.components)) {
      content.components.forEach(comp => {
        if (comp.type === 'resultParams' || comp.type === 'outParams') return

        if (comp.type === 'contentParams' && Array.isArray(comp.props?.defaultParams)) {
          comp.props.defaultParams.forEach(item => {
            if (item.field && item.description) {
              properties[item.field] = item.description
            }
          })
        } else if (comp.type === 'tableEditor' && Array.isArray(comp.columns)) {
          comp.columns.forEach(col => {
            if (col.key && col.title) {
              properties[col.key] = col.title
            }
          })
        } else if (comp.field && comp.label) {
          properties[comp.field] = comp.label
        }
      })
    }
      // 构建符合接口要求的参数格式，从生成的配置中获取所有信息
      const data = {
        "id": content.id || content.type || "",
        "desc": content.description || content.desc || "",
        "input": content.input || { "param": "输入参数" },  // 输入参数拿不到暂时先传固定的
        "properties": content.properties || properties,
        "output": content.output || output,
      }

      console.log('调用生成后端接口，参数：', data)
      const res = await generateNodeCode(data)

      if (res.code === 200) {
        message.success('生成后端代码成功')
        designerStore.setGenerateNodePanelVisiable(false)
      } else {
        message.error('生成后端代码失败')
      }
    } catch (error) {
      console.error('生成后端代码出错：', error)
      message.error('生成后端代码失败，请检查配置格式')
    }
  }


  </script>
  
  <style lang="scss" scoped>
  // 全局样式，强制禁用模态框期间的页面滚动
  body.ant-scrolling-effect {
    overflow: hidden !important;
    width: 100% !important;
    padding-right: 0 !important;
  }

  .ant-modal-mask {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
  }

  .ant-modal-wrap {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .ant-modal {
    top: auto !important;
    margin: 0 !important;
    position: relative !important;
  }
  </style>

  <style scoped lang="scss">
  .generate-node-modal {
    :deep(.ant-modal-body) {
      padding: 0 !important;
    }
  }

  .json-editor {
    height: 100% !important;
    min-height: 600px;

    :deep(.ace-jsoneditor) {
      background: var(--global-background-color) !important;
    }
    :deep(.ace_gutter),
    :deep(.ace_scroller),
    :deep(.ace_content),
    :deep(.ace_layer) {
      background: var(--global-background-color) !important;
    }

    // 选中内容高亮
    :deep(.ace_marker-layer .ace_selection) {
      background: var(--primary-color) !important;
      opacity: 0.3;
    }
  }
  
  .input-output-editor {
    height: 150px;
    :deep(*) {
      background-color: var(--global-background-color) !important;
      color: var(--text-color) !important;
    }

    :deep(.ace-jsoneditor) {
      background: var(--global-background-color) !important;
      color: var(--text-color) !important;
      
      .ace_gutter,
      .ace_scroller,
      .ace_content,
      .ace_layer {
        background: var(--global-background-color) !important;
        color: var(--text-color) !important;
      }
    }
  }
  
  /* 自定义滚动条样式 */
  .overflow-y-auto {
    scrollbar-width: thin;
    scrollbar-color: var(--guide-text-color) var(--global-background-color);
  }
  
  .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
  }
  
  .overflow-y-auto::-webkit-scrollbar-track {
    background: var(--global-background-color);
    border-radius: 0.375rem;
  }

  .overflow-y-auto::-webkit-scrollbar-thumb {
    background-color: var(--guide-text-color);
    border-radius: 3px;
  }

  .preview-content {
    min-height: 300px;
  }

  :deep(.ant-form-item-label > label) {
    color: #6b7280;
    font-weight: 500;
  }

  :deep(.ant-input), :deep(.ant-input-textarea) {
    border-radius: 0.5rem;
    border-color: #d1d5db;
  }

  :deep(.ant-input:hover), :deep(.ant-input-textarea:hover) {
    border-color: #60a5fa;
  }

  :deep(.ant-input:focus), :deep(.ant-input-textarea:focus) {
    border-color: #3b82f6;
    box-shadow: none;
  }

  :deep(.ant-btn-primary) {
    background-color: #3b82f6;
    border-color: #3b82f6;
  }

  :deep(.ant-btn-primary:hover) {
    background-color: #2563eb;
    border-color: #2563eb;
  }
  </style>
  