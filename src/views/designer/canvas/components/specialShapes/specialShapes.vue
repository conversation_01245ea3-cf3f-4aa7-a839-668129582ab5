<template>
  <div class="relative w-full h-full" @dblclick="focusEditableDiv">
    <!-- SVG 图形 -->
    <svg class="w-full h-full text-primary dark:!text-primary-hover" preserveAspectRatio="none">
      <use :xlink:href="`${shapesUrl}#${node.data.shapeId}`" fill="currentColor" stroke="none" />
    </svg>
    <!-- 居中可编辑的文字 -->
    <div class="absolute inset-0 flex items-center justify-center pointer-events-none select-none">
      <div
        class="w-4/5 min-h-[16px] max-h-[80%] bg-transparent text-[#fff] text-[14px] dark:!text-white text-center outline-none overflow-hidden cursor-text pointer-events-auto select-text inline-block"
        contenteditable="true"
        :innerHTML="labelHtml"
        @input="onInput"
        @blur="onBlur"
        @focus="onFocus"
        ref="editableDiv"
        spellcheck="false"
        style="height:auto;"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import shapesUrl from '@/assets/shapes.svg?url'

const props = defineProps<{
  node: any
}>()

const editableDiv = ref<HTMLDivElement | null>(null)
const containerHeight = ref(0)

// 保证换行和空格正常显示
const labelHtml = ref(props.node.data.label?.replace(/\n/g, '<br>') || '&ZeroWidthSpace;')

// 监听 node.data.label 外部变化，更新 contenteditable 内容
watch(
  () => props.node.data.label,
  (val) => {
    if (editableDiv.value && editableDiv.value.innerText !== val) {
      labelHtml.value = (val || '').replace(/\n/g, '<br>') || '&ZeroWidthSpace;'
      editableDiv.value.innerHTML = labelHtml.value
    }
  }
)


// 监听输入事件，更新 node.data.label
function onInput(e: Event) {
  const text = (e.target as HTMLDivElement).innerText
  props.node.data.label = text
  // 如果内容为空，插入零宽空格
  if (editableDiv.value && !text) {
    editableDiv.value.innerHTML = '&ZeroWidthSpace;'
  }
}

// 失焦时修正内容（防止粘贴带格式）
function onBlur(e: Event) {
  if (editableDiv.value) {
    editableDiv.value.innerText = props.node.data.label
    if (!props.node.data.label) {
      editableDiv.value.innerHTML = '&ZeroWidthSpace;'
    }
  }
}

function onFocus(e: Event) {
  if (editableDiv.value && !editableDiv.value.innerText) {
    editableDiv.value.innerHTML = '&ZeroWidthSpace;'
  }
}

// 计算容器高度
onMounted(() => {
  if (editableDiv.value) {
    containerHeight.value = editableDiv.value.parentElement?.clientHeight || 0
  }
})

function focusEditableDiv() {
  editableDiv.value?.focus()
}
</script>

