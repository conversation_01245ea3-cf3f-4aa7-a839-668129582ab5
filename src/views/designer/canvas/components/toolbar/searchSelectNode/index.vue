<template>
  <div
    v-if="visible"
    class="node-search-dropdown bg-bg dark:!bg-global-background dark:!border-border dark:!border"
    :style="{
      position: 'fixed',
      left: `${position.x}px`,
      top: `${position.y}px`,
      transform: 'translate(-50%, -50%)'
    }"
  >
    <a-input-search
      v-model:value="searchKeyword"
      placeholder="搜索节点"
      class="mb-2"
      autofocus
      @blur="handleSearchBlur"
      size="middle"
    >
          <template #prefix>
            <search-outlined class="text-gray-400" />
          </template>
    </a-input-search>
    <div class="search-results">
      <div v-if="filteredNodes.length === 0" class="p-2 text-center text-gray-500">
        未找到匹配的节点
      </div>
      <div
        v-for="node in filteredNodes"
        :key="node.id"
        class="search-result-item"
        @mousedown="startDrag($event, node)"
      >
        <svg class="icon" width="16" height="16">
          <use :xlink:href="`${iconsUrl}#${validateType(node.type)}`"></use>
        </svg>
        <span class="ml-1.5">{{ node.title }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useDesignerStore } from '@/store/designer'
import { cloneDeep } from 'lodash-es'
import { message } from 'ant-design-vue'
import iconsUrl from '@/assets/icons.svg?url'
import { validateIconType } from '@/utils/utils'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  position: {
    type: Object as () => { x: number; y: number },
    required: true
  }
})

const emit = defineEmits(['update:visible'])

const validateType = (type) => {
  return validateIconType(type, iconsUrl)
}

const designerStore = useDesignerStore()
const searchKeyword = ref('')

// 过滤节点
const filteredNodes = computed(() => {
  const keyword = searchKeyword.value.toLowerCase()
  const nodes = {...designerStore.nodeGroups, ...designerStore.shapeGroups}

  // 从nodeGroups中获取所有节点
  const allNodes = Object.values(nodes).flatMap(group =>
    Object.values(group.nodes || {})  // 添加空对象检查
  )

  return keyword
    ? allNodes.filter(node =>
      node.title?.toLowerCase().includes(keyword) ||
      node.description?.toLowerCase().includes(keyword)
    )
    : allNodes
})

// 处理搜索框失焦
const handleSearchBlur = () => {
  setTimeout(() => {
    emit('update:visible', false)
    searchKeyword.value = ''
  }, 200)
}

// 开始拖拽
const startDrag = (event: MouseEvent, node: any) => {
  if (node.disabled) {
    message.warning('该节点当前不可用')
    return
  }

  const nodeData = cloneDeep(node)
  const nodeOptions = {
    shape: nodeData.type,
    data: nodeData,
    width: nodeData.options?.width,
    height: nodeData.options?.height
  }

  const newNode = designerStore.modGraph.createNode(nodeOptions)
  designerStore.dndInstance?.start(newNode, event)
}
</script>

<style lang="scss" scoped>
.node-search-dropdown {
  position: absolute;
  z-index: 1000;
//   background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 300px;
  padding: 8px;

  .search-results {
    max-height: 400px;
    overflow-y: auto;
  }

  .search-result-item {
    display: flex;
    align-items: center;
    padding: 8px;
    cursor: move;
    transition: background-color 0.2s;
    @apply dark:!text-primary;

    &:hover {
      background-color: #f5f5f5;
      @apply dark:!bg-primary-hover;
    }

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }
  }
}
:deep(.ant-input-search-button) {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
