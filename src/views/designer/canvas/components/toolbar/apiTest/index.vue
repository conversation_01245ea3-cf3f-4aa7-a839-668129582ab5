<template>
  <div class="api-test-panel">
    <!-- 添加地址显示区域 -->
    <div v-if="endpointUrl" class="mb-4">
      <div class="flex justify-between items-center mb-2">
        <div class="text-sm font-medium">请求地址</div>
      </div>
      <div class="flex items-center">
        <span class="flex-1 bg-gray-100 p-2 rounded break-all dark:!bg-global-background">{{ endpointUrl }}</span>
        <a-button type="primary" size="small" class="ml-2" @click="copyToClipboard(endpointUrl)">
          复制
        </a-button>
      </div>
    </div>

    <a-spin :spinning="mockLoading" tip="生成模拟数据中...">
      <div class="flex justify-between items-center mb-2">
        <div class="text-sm font-medium">请求参数</div>
        <!-- <a-button size="small" @click="handleMock">模拟数据</a-button> -->
      </div>

      <div class="params-container">
        <ParamItem
          v-for="(item, index) in paramsData"
          :key="index"
          :item="item"
          @file-change="handleFileChange"
        />

        <div v-if="paramsData.length === 0" class="text-center text-gray-500 py-4">
          没有可配置的参数
        </div>
      </div>
    </a-spin>

    <div class="flex justify-end mt-4">
      <a-button type="primary" :loading="btnLoading" @click="handleRun">测试运行</a-button>
    </div>

    <div v-if="testResult" class="mt-4">
      <div class="mb-2 font-medium">测试结果：</div>
      <pre
        class="p-3 bg-gray-100 rounded border border-gray-300 overflow-auto text-left max-h-[200px]"
        >{{ testResult }}</pre
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { generateMockFields } from '@/apis/designer'
import ParamItem from '../debug/ParamItem.vue'
import axios, { AxiosError } from 'axios'
import { useSSERequest } from '@yss-design/utils/hooks'

const props = defineProps({
  endpointUrl: {
    type: String,
    default: ''
  },
  configData: {
    type: String,
    default: ''
  },
  reqParamData: {
    type: String,
    default: ''
  },
  templateId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['debug-result'])

// 状态变量
const btnLoading = ref(false)
const mockLoading = ref(false)
const testResult = ref('')

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  if (!text) {
    message.error('无可复制内容')
    return
  }

  if (navigator.clipboard) {
    navigator.clipboard.writeText(text)
      .then(() => {
        message.success('已复制到剪贴板')
      })
      .catch(() => {
        message.error('复制失败')
      })
  } else {
    // 兼容性处理
    const textarea = document.createElement('textarea')
    textarea.value = text
    document.body.appendChild(textarea)
    textarea.select()
    try {
      document.execCommand('copy')
      message.success('已复制到剪贴板')
    } catch (err) {
      message.error('复制失败')
    }
    document.body.removeChild(textarea)
  }
}

// 参数数据接口
interface ParamData {
  paramsName: string
  variableType: string
  value: string | File | File[] | any
  description?: string
  required?: boolean
  isHeader?: boolean
  isFile?: boolean
  accept?: string[]
  maxSize?: number
  multiple?: boolean
  children?: ParamData[]
  level?: number
  isCommon?: boolean
  isData?: boolean
  isEnumList?: boolean
  options?: Array<{label: string, value: string}>
}

// 参数数据
const paramsData = ref<ParamData[]>([])

// 从reqParam解析参数
const parseReqParams = (reqParamStr: string) => {
  try {
    if (!reqParamStr) return []

    const reqParam = JSON.parse(reqParamStr)
    const params: ParamData[] = []

    // 处理data参数
    if (reqParam.data) {
      Object.entries(reqParam.data).forEach(([key, value]) => {
        const type =  value?.type ? value?.type : (typeof value === 'object' ? 'Object' : typeof value)
        params.push({
          paramsName: key,
          // variableType: typeof value === 'object' ? 'Object' : typeof value,
          //TODO: 这里需要根据value的类型来确定variableType
          variableType: type|| 'String',
          value: value || '',
          description: '数据参数',
          required: true,
          //TODO: 需要确认isData的业务逻辑是什么，暂时隐藏
          // isData: true,

          level: 0
        })
      })
    }

    // 处理common参数
    if (reqParam.common) {
      Object.entries(reqParam.common).forEach(([key, value]) => {
        params.push({
          paramsName: key,
          variableType: typeof value === 'object' ? 'Object' : typeof value,
          value: value || '',
          description: '通用参数',
          required: true,
          isCommon: true,
          level: 0
        })
      })
    }

    // 处理header参数
    if (reqParam.header) {
      Object.entries(reqParam.header).forEach(([key, value]) => {
        params.push({
          paramsName: key,
          variableType: typeof value === 'object' ? 'Object' : typeof value,
          value: value || '',
          description: '请求头参数',
          required: true,
          isHeader: true,
          level: 0
        })
      })
    }

    return params
  } catch (error) {
    console.error('解析reqParam失败:', error)
    return []
  }
}

// 初始化参数 - 定义先于使用
const initParams = (configJson: string, reqParamStr: string) => {
  try {
    // 首先尝试从reqParam初始化
    const reqParams = parseReqParams(reqParamStr)
    if (reqParams.length > 0) {
      paramsData.value = reqParams
      return
    }
    // 如果reqParam没有参数，尝试从configData初始化
    if (!configJson) return

    const config = JSON.parse(configJson)
    const cells = config?.cells || []

    // 定义输出变量类型
    interface OutputsType {
      headers?: any[];
      commons?: any[];
      data?: any[];
      [key: string]: any;
    }

    // 查找startNode并获取outputs
    let outputs: OutputsType = {};

    // 尝试从config.nodes中查找
    if (Array.isArray(config.nodes)) {
      const startNode = config.nodes.find((node: any) => node.data?.type === 'startNode');
      if (startNode?.data?.outputs) {
        outputs = startNode.data.outputs;
      }
    }

    // 如果没找到，尝试从其他地方获取
    if (!outputs.headers && !outputs.commons && !outputs.data) {
      // 尝试直接使用config.outputs
      if (config.outputs) {
        outputs = config.outputs;
      }
    }

    // 生成参数列表
    const processParams = (params: any[], type: 'header' | 'common' | 'data') => {
       
      if (!Array.isArray(params)) return []
      return params.map((param: any) => {
        const baseParam: ParamData = {
          paramsName: param.name || '',
          variableType: param.type || 'String',
          value: param.defaultValue || '',
          description: param.description || '',
          required: param.required ?? true,
          level: 0,
          isFile: param.isFile || false,
          accept: param.accept || [],
          maxSize: param.maxSize,
          multiple: param.multiple || false,
          ...(type === 'header' && { isHeader: true }),
          ...(type === 'common' && { isCommon: true }),
          ...(type === 'data' && { isData: true })
        }

        if (param.type === 'Object' && param.children?.length) {
          baseParam.children = param.children.map((child: any) => ({
            paramsName: child.name || '',
            variableType: child.type || 'String',
            value: child.defaultValue || '',
            description: child.description || '',
            required: child.required ?? true,
            level: 1,
            isFile: child.isFile || false,
            accept: child.accept || [],
            maxSize: child.maxSize,
            multiple: child.multiple || false
          }))
        }

        return baseParam
      })
    }

    const { headers = [], commons = [], data = [] } = outputs
    let allParams: ParamData[] = []

    if (Array.isArray(headers) && headers.length) {
      allParams.push(...processParams(headers, 'header'))
    }

    if (Array.isArray(commons) && commons.length) {
      allParams.push(...processParams(commons, 'common'))
    }

    if (Array.isArray(data) && data.length) {
      allParams.push(...processParams(data, 'data'))
    }

    paramsData.value = allParams
  } catch (error) {
    console.error('初始化参数失败:', error)
    paramsData.value = [] // 出错时清空参数
  } finally {
    console.log('paramsData.value', paramsData.value)
  }
}

// 监听配置数据变化
watch([() => props.configData, () => props.reqParamData], ([newConfigData, newReqParamData]) => {
  initParams(newConfigData, newReqParamData)
}, { immediate: true })

// 文件变更处理
const handleFileChange = (file: File | File[]) => {
  console.log('文件已变更:', file)
}

// 生成模拟数据
const handleMock = async () => {
  if (paramsData.value.length === 0) {
    message.info('没有可用的参数')
    return
  }

  try {
    mockLoading.value = true

    // 准备请求参数
    const fields = paramsData.value.map(param => ({
      name: param.paramsName,
      type: param.variableType,
      description: param.description || '',
      children: param.children?.map(child => ({
        name: child.paramsName,
        type: child.variableType,
        description: child.description || ''
      }))
    }))

    // 调用模拟数据生成接口
    const res = await generateMockFields({ fields })

    if (res.code === 200 && res.data?.mockData) {
      // 更新参数值
      const mockData = res.data.mockData

      paramsData.value = paramsData.value.map(param => {
        const clonedParam = { ...param }

        // 检查 mockData 中是否有对应的字段值
        if (mockData && mockData[param.paramsName] !== undefined) {
          clonedParam.value = mockData[param.paramsName]
        }

        if (clonedParam.children?.length) {
          clonedParam.children = clonedParam.children.map(child => {
            const clonedChild = { ...child }
            // 检查 mockData 中是否有对应的子字段值
            if (mockData && mockData[child.paramsName] !== undefined) {
              clonedChild.value = mockData[child.paramsName]
            }
            return clonedChild
          })
        }

        return clonedParam
      })

      message.success('模拟数据已生成')
    } else {
      message.error('生成模拟数据失败')
    }
  } catch (error) {
    console.error('生成模拟数据失败:', error)
    message.error('生成模拟数据失败')
  } finally {
    mockLoading.value = false
  }
}

// 运行测试
const handleRun = async () => {
  if (!props.endpointUrl) {
    message.error('请求地址不能为空')
    return
  }

  try {
    btnLoading.value = true
    testResult.value = ''

    // 构建请求数据
    const data: { [key: string]: any } = {}
    const commons = {}
    const headers = {}

    // 处理文件参数
    const fileParams: ParamData[] = []

    // 找出所有文件类型参数
    paramsData.value.forEach(param => {
      const paramValue = param.value
      const isFile = (param.isFile || param.variableType === 'File') &&
                     (paramValue instanceof File ||
                     (Array.isArray(paramValue) && paramValue[0] instanceof File))

      if (isFile) {
        fileParams.push(param)
      }
    })

    // 处理常规参数
    paramsData.value.forEach(param => {
      // 跳过文件类型参数
      if (fileParams.some(fp => fp.paramsName === param.paramsName)) {
        return
      }

      if (param.isHeader) {
        headers[param.paramsName] = param.value
      } else if (param.isCommon) {
        commons[param.paramsName] = param.value
      } else {
        data[param.paramsName] = param.value
      }
    })

    // 添加templateId到data对象
    if (props.templateId) {
      data.templateId = props.templateId
    }

    // 构建最终请求体
    const requestBody = {
      data,
      common: Object.keys(commons).length > 0 ? commons : undefined,
      header: Object.keys(headers).length > 0 ? headers : undefined
    }

    const {
      streamData,      // 响应式的流式数据
      finalResult,     // 响应式的最终结果
      loading,         // 响应式的加载状态
      connected,       // 响应式的连接状态
      error,           // 响应式的错误信息
      reconnectCount,  // 响应式的重连次数
      fetch,           // 执行请求的方法
      close,           // 关闭连接的方法
      reconnect,       // 手动重连的方法
      clear            // 清空数据的方法
    } = useSSERequest()

    let response

    // 如果有文件参数，使用FormData
    if (fileParams.length > 0) {
      const formData = new FormData()

      // 添加JSON数据
      formData.append('data', JSON.stringify(requestBody))

      // 添加文件参数
      fileParams.forEach(param => {
        const paramValue = param.value

        if (paramValue instanceof File) {
          formData.append(param.paramsName, paramValue)
        } else if (Array.isArray(paramValue)) {
          paramValue.forEach((file: File) => {
            formData.append(param.paramsName, file)
          })
        }
      })

      // 发送请求
      response = await fetch({
        url: `${props.endpointUrl}?stream=true`,
        data: formData,
        headers,
      })
    } else {
      // 没有文件参数，直接发送JSON
      response = await fetch({
        url: `${props.endpointUrl}?stream=true`,
        data: requestBody,
        headers,
      })
    }

    // 设置测试结果
    testResult.value = JSON.stringify(response.data, null, 2)

    // 触发debug结果事件
    emit('debug-result', response.data)

    message.info('测试完成')
  } catch (error) {
    console.error('测试失败:', error)
    message.error('测试失败')

    const axiosError = error as AxiosError
    if (axiosError.response) {
      testResult.value = JSON.stringify({
        status: axiosError.response.status,
        statusText: axiosError.response.statusText,
        data: axiosError.response.data
      }, null, 2)
    } else {
      testResult.value = JSON.stringify({ error: '请求失败' }, null, 2)
    }
  } finally {
    btnLoading.value = false
  }
}

// 重置组件状态
const reset = () => {
  paramsData.value = []
  testResult.value = ''
}

// 组件挂载时清空测试结果
onMounted(() => {
  testResult.value = ''
})

// 监听visible属性变化
watch(() => props.endpointUrl, () => {
  // 当API地址改变时，清空测试结果
  testResult.value = ''
}, { immediate: true })

// 导出方法
defineExpose({
  reset,
  handleMock,
  handleRun
})
</script>

<style scoped>
.api-test-panel {
  width: 100%;
}

.params-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 16px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  text-align: left;
}
</style>
