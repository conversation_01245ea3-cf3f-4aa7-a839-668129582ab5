<template>
  <a-modal
    v-model:visible="visible"
    title="系统变量管理"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="system-variables">
      <!-- 顶部操作栏 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="flex items-center gap-4">
          <h3 class="text-lg font-medium">变量列表</h3>
          <a-tag color="blue">共 {{ variables.length }} 个变量</a-tag>
        </div>
        <div class="flex gap-2">
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon>
              <PlusOutlined />
            </template>
            添加变量
          </a-button>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
        <div class="text-sm text-blue-800">
          <strong>使用说明:</strong> 在节点配置的输入框中使用 <code>${{变量名}}</code> 格式引用变量，如 <code>${{API_BASE_URL}}</code>
        </div>
      </div>

      <!-- 变量列表 -->
      <div class="variable-list">
        <a-spin :spinning="loading">
          <div v-if="variables.length === 0" class="text-center py-8 text-gray-500">
            暂无系统变量，点击"添加变量"创建第一个变量
          </div>
          
          <div v-else class="space-y-3 max-h-96 overflow-y-auto">
            <div
              v-for="variable in variables"
              :key="variable.id"
              class="variable-card border rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="font-semibold text-lg">{{ variable.name }}</span>
                    <a-tag :color="getTypeColor(variable.type)">
                      {{ getTypeText(variable.type) }}
                    </a-tag>
                    <a-tag v-if="variable.isGlobal" color="green">全局</a-tag>
                  </div>
                  
                  <div class="text-gray-600 mb-2">
                    <span class="font-medium">值:</span> 
                    <span class="font-mono bg-gray-100 px-2 py-1 rounded">{{ formatValue(variable.value) }}</span>
                  </div>
                  
                  <div class="text-sm text-gray-500 mb-2">
                    {{ variable.description || '无描述' }}
                  </div>
                  
                  <div class="text-xs text-gray-400">
                    引用方式: <code class="bg-gray-100 px-1 rounded">${{variable.name}}</code>
                  </div>
                </div>
                
                <div class="flex gap-2 ml-4">
                  <a-button size="small" @click="handleEditVariable(variable)">
                    <template #icon>
                      <EditOutlined />
                    </template>
                  </a-button>
                  <a-button size="small" @click="handleCopyReference(variable)">
                    <template #icon>
                      <CopyOutlined />
                    </template>
                  </a-button>
                  <a-button size="small" danger @click="handleDeleteVariable(variable)">
                    <template #icon>
                      <DeleteOutlined />
                    </template>
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>
    </div>

    <!-- 创建/编辑变量弹窗 -->
    <a-modal
      v-model:visible="showCreateModal"
      :title="editingVariable ? '编辑变量' : '创建变量'"
      width="600px"
      @ok="handleSaveVariable"
      @cancel="handleCancelEdit"
    >
      <a-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        layout="vertical"
      >
        <a-form-item label="变量名" name="name">
          <a-input 
            v-model:value="createForm.name" 
            placeholder="请输入变量名，如：API_BASE_URL"
            :disabled="!!editingVariable"
          />
          <div class="text-gray-500 text-sm mt-1">
            变量名只能包含字母、数字和下划线，建议使用大写字母
          </div>
        </a-form-item>
        
        <a-form-item label="变量类型" name="type">
          <a-select v-model:value="createForm.type" placeholder="选择变量类型">
            <a-select-option value="string">字符串</a-select-option>
            <a-select-option value="number">数字</a-select-option>
            <a-select-option value="boolean">布尔值</a-select-option>
            <a-select-option value="json">JSON对象</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="变量值" name="value">
          <a-textarea 
            v-if="createForm.type === 'json'"
            v-model:value="createForm.value" 
            placeholder="请输入JSON格式的值"
            :rows="4"
          />
          <a-switch 
            v-else-if="createForm.type === 'boolean'"
            v-model:checked="createForm.booleanValue"
            checked-children="true"
            un-checked-children="false"
          />
          <a-input-number 
            v-else-if="createForm.type === 'number'"
            v-model:value="createForm.numberValue"
            placeholder="请输入数字值"
            class="w-full"
          />
          <a-input 
            v-else
            v-model:value="createForm.value" 
            placeholder="请输入变量值"
          />
        </a-form-item>
        
        <a-form-item label="描述" name="description">
          <a-textarea 
            v-model:value="createForm.description" 
            placeholder="请输入变量描述"
            :rows="2"
          />
        </a-form-item>
        
        <a-form-item label="作用域" name="isGlobal">
          <a-radio-group v-model:value="createForm.isGlobal">
            <a-radio :value="true">全局变量 - 在所有画布中可用</a-radio>
            <a-radio :value="false">局部变量 - 仅在当前画布中可用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { 
  ReloadOutlined, 
  PlusOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { useSystemVariablesStore } from '@/store/systemVariables'

interface SystemVariable {
  id: string
  name: string
  type: 'string' | 'number' | 'boolean' | 'json'
  value: any
  description: string
  isGlobal: boolean
  createTime: string
  updateTime: string
}

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// Store
const systemVariablesStore = useSystemVariablesStore()

// 数据状态
const loading = ref(false)
const variables = ref<SystemVariable[]>([])
const showCreateModal = ref(false)
const editingVariable = ref<SystemVariable | null>(null)
const createFormRef = ref()

// 创建表单
const createForm = reactive({
  name: '',
  type: 'string' as SystemVariable['type'],
  value: '',
  numberValue: 0,
  booleanValue: false,
  description: '',
  isGlobal: true
})

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入变量名', trigger: 'blur' },
    { pattern: /^[A-Z_][A-Z0-9_]*$/, message: '变量名只能包含大写字母、数字和下划线，且不能以数字开头', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择变量类型', trigger: 'change' }
  ],
  value: [
    { required: true, message: '请输入变量值', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 加载变量列表
const loadVariables = async () => {
  loading.value = true
  try {
    variables.value = systemVariablesStore.getAllVariables()
  } catch (error) {
    message.error('加载系统变量失败')
  } finally {
    loading.value = false
  }
}

// 刷新变量列表
const handleRefresh = () => {
  loadVariables()
}

// 编辑变量
const handleEditVariable = (variable: SystemVariable) => {
  editingVariable.value = variable
  createForm.name = variable.name
  createForm.type = variable.type
  createForm.description = variable.description
  createForm.isGlobal = variable.isGlobal
  
  // 根据类型设置值
  if (variable.type === 'boolean') {
    createForm.booleanValue = variable.value
  } else if (variable.type === 'number') {
    createForm.numberValue = variable.value
  } else {
    createForm.value = typeof variable.value === 'object' ? JSON.stringify(variable.value, null, 2) : variable.value
  }
  
  showCreateModal.value = true
}

// 复制引用
const handleCopyReference = async (variable: SystemVariable) => {
  const reference = `\${${variable.name}}`
  try {
    await navigator.clipboard.writeText(reference)
    message.success('引用格式已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

// 删除变量
const handleDeleteVariable = (variable: SystemVariable) => {
  Modal.confirm({
    title: '确认删除变量',
    content: `确定要删除变量 "${variable.name}" 吗？此操作不可恢复。`,
    type: 'warning',
    onOk() {
      systemVariablesStore.deleteVariable(variable.id)
      loadVariables()
      message.success('变量删除成功')
    }
  })
}

// 保存变量
const handleSaveVariable = async () => {
  try {
    await createFormRef.value.validate()
    
    // 根据类型获取值
    let value = createForm.value
    if (createForm.type === 'boolean') {
      value = createForm.booleanValue
    } else if (createForm.type === 'number') {
      value = createForm.numberValue
    } else if (createForm.type === 'json') {
      try {
        value = JSON.parse(createForm.value)
      } catch (error) {
        message.error('JSON格式不正确')
        return
      }
    }
    
    const variableData = {
      name: createForm.name,
      type: createForm.type,
      value,
      description: createForm.description,
      isGlobal: createForm.isGlobal
    }
    
    if (editingVariable.value) {
      // 更新变量
      systemVariablesStore.updateVariable(editingVariable.value.id, variableData)
      message.success('变量更新成功')
    } else {
      // 创建变量
      systemVariablesStore.createVariable(variableData)
      message.success('变量创建成功')
    }
    
    showCreateModal.value = false
    handleCancelEdit()
    loadVariables()
  } catch (error) {
    // 表单验证失败
  }
}

// 取消编辑
const handleCancelEdit = () => {
  editingVariable.value = null
  createForm.name = ''
  createForm.type = 'string'
  createForm.value = ''
  createForm.numberValue = 0
  createForm.booleanValue = false
  createForm.description = ''
  createForm.isGlobal = true
}

// 关闭弹窗
const handleCancel = () => {
  visible.value = false
}

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colorMap = {
    string: 'blue',
    number: 'green',
    boolean: 'orange',
    json: 'purple'
  }
  return colorMap[type] || 'default'
}

// 获取类型文本
const getTypeText = (type: string) => {
  const textMap = {
    string: '字符串',
    number: '数字',
    boolean: '布尔值',
    json: 'JSON'
  }
  return textMap[type] || type
}

// 格式化值显示
const formatValue = (value: any) => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadVariables()
  }
})
</script>

<style scoped>
.system-variables {
  min-height: 400px;
}

.variable-card {
  transition: all 0.2s ease;
}

.variable-card:hover {
  transform: translateY(-1px);
}

code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>
