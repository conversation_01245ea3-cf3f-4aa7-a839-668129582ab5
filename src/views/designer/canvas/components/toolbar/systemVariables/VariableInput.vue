<template>
  <div class="variable-input">
    <div class="input-container">
      <!-- 主输入框 -->
      <a-input
        v-if="inputType === 'input'"
        v-model:value="inputValue"
        v-bind="$attrs"
        @change="handleChange"
        @blur="handleBlur"
        class="variable-enhanced-input"
        :class="{ 'has-variables': hasVariables }"
      />
      
      <a-textarea
        v-else-if="inputType === 'textarea'"
        v-model:value="inputValue"
        v-bind="$attrs"
        @change="handleChange"
        @blur="handleBlur"
        class="variable-enhanced-input"
        :class="{ 'has-variables': hasVariables }"
      />
      
      <!-- 变量选择器 -->
      <div class="variable-selector-container">
        <VariableSelector 
          @variable-select="handleVariableSelect"
          @manage-variables="$emit('manage-variables')"
        />
      </div>
    </div>
    
    <!-- 变量预览 -->
    <div v-if="hasVariables" class="variable-preview">
      <div class="preview-label">预览值:</div>
      <div class="preview-value">{{ resolvedValue }}</div>
    </div>
    
    <!-- 变量引用提示 -->
    <div v-if="variableRefs.length > 0" class="variable-refs">
      <div class="refs-label">引用的变量:</div>
      <div class="refs-list">
        <a-tag 
          v-for="ref in variableRefs" 
          :key="ref"
          :color="getVariableStatus(ref).color"
          size="small"
          class="ref-tag"
        >
          {{ ref }}
          <span v-if="!getVariableStatus(ref).exists" class="ml-1">❌</span>
        </a-tag>
      </div>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="invalidRefs.length > 0" class="error-message">
      <ExclamationCircleOutlined class="error-icon" />
      <span>未定义的变量: {{ invalidRefs.join(', ') }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { useSystemVariablesStore } from '@/store/systemVariables'
import VariableSelector from './VariableSelector.vue'

interface Props {
  modelValue: string
  inputType?: 'input' | 'textarea'
}

const props = withDefaults(defineProps<Props>(), {
  inputType: 'input'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'manage-variables': []
}>()

// Store
const systemVariablesStore = useSystemVariablesStore()

// 状态
const inputValue = ref(props.modelValue || '')

// 计算属性
const hasVariables = computed(() => 
  systemVariablesStore.hasVariableReferences(inputValue.value)
)

const resolvedValue = computed(() => 
  systemVariablesStore.resolveVariables(inputValue.value)
)

const variableRefs = computed(() => 
  systemVariablesStore.getVariableReferences(inputValue.value)
)

const validationResult = computed(() => 
  systemVariablesStore.validateVariableReferences(inputValue.value)
)

const invalidRefs = computed(() => validationResult.value.invalidRefs)

// 获取变量状态
const getVariableStatus = (varName: string) => {
  const variable = systemVariablesStore.getVariable(varName)
  return {
    exists: !!variable,
    color: variable ? 'green' : 'red'
  }
}

// 处理输入变化
const handleChange = (e: Event) => {
  const value = (e.target as HTMLInputElement).value
  inputValue.value = value
  emit('update:modelValue', value)
}

// 处理失焦
const handleBlur = () => {
  emit('update:modelValue', inputValue.value)
}

// 处理变量选择
const handleVariableSelect = (variableName: string) => {
  const cursorPos = getCursorPosition()
  const before = inputValue.value.substring(0, cursorPos)
  const after = inputValue.value.substring(cursorPos)
  const variableRef = `\${${variableName}}`
  
  inputValue.value = before + variableRef + after
  emit('update:modelValue', inputValue.value)
  
  // 设置光标位置到变量引用之后
  setTimeout(() => {
    setCursorPosition(cursorPos + variableRef.length)
  }, 0)
}

// 获取光标位置
const getCursorPosition = (): number => {
  const input = document.activeElement as HTMLInputElement | HTMLTextAreaElement
  if (input && (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA')) {
    return input.selectionStart || 0
  }
  return inputValue.value.length
}

// 设置光标位置
const setCursorPosition = (pos: number) => {
  const input = document.activeElement as HTMLInputElement | HTMLTextAreaElement
  if (input && (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA')) {
    input.setSelectionRange(pos, pos)
  }
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== inputValue.value) {
    inputValue.value = newValue || ''
  }
})
</script>

<style scoped>
.variable-input {
  width: 100%;
}

.input-container {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.variable-enhanced-input {
  flex: 1;
}

.variable-enhanced-input.has-variables {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.variable-selector-container {
  flex-shrink: 0;
}

.variable-preview {
  margin-top: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.preview-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.preview-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #333;
  word-break: break-all;
}

.variable-refs {
  margin-top: 8px;
}

.refs-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.refs-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.ref-tag {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.error-message {
  margin-top: 8px;
  padding: 8px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #ff4d4f;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-icon {
  color: #ff4d4f;
}

/* 深色主题适配 */
.dark .variable-preview {
  background: #1f1f1f;
  border-left-color: #1890ff;
}

.dark .preview-value {
  color: #fff;
}

.dark .error-message {
  background: #2a1215;
  border-color: #a8071a;
  color: #ff7875;
}
</style>
