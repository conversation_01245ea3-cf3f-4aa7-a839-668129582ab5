<template>
  <div class="variable-selector">
    <!-- 变量引用按钮 -->
    <a-dropdown :trigger="['click']" placement="bottomLeft">
      <a-button size="small" type="text" class="variable-btn">
        <template #icon>
          <AppstoreOutlined />
        </template>
        变量
      </a-button>
      
      <template #overlay>
        <a-menu @click="handleVariableSelect">
          <a-menu-item-group title="全局变量">
            <a-menu-item 
              v-for="variable in globalVariables" 
              :key="variable.name"
              :data-variable="variable.name"
            >
              <div class="flex items-center justify-between w-full">
                <div class="flex items-center gap-2">
                  <a-tag :color="getTypeColor(variable.type)" size="small">
                    {{ getTypeText(variable.type) }}
                  </a-tag>
                  <span class="font-mono">{{ variable.name }}</span>
                </div>
                <span class="text-xs text-gray-500 ml-2">{{ formatValue(variable.value) }}</span>
              </div>
            </a-menu-item>
          </a-menu-item-group>
          
          <a-menu-divider v-if="globalVariables.length > 0 && localVariables.length > 0" />
          
          <a-menu-item-group v-if="localVariables.length > 0" title="局部变量">
            <a-menu-item 
              v-for="variable in localVariables" 
              :key="variable.name"
              :data-variable="variable.name"
            >
              <div class="flex items-center justify-between w-full">
                <div class="flex items-center gap-2">
                  <a-tag :color="getTypeColor(variable.type)" size="small">
                    {{ getTypeText(variable.type) }}
                  </a-tag>
                  <span class="font-mono">{{ variable.name }}</span>
                </div>
                <span class="text-xs text-gray-500 ml-2">{{ formatValue(variable.value) }}</span>
              </div>
            </a-menu-item>
          </a-menu-item-group>
          
          <a-menu-divider />
          
          <a-menu-item key="manage" @click="$emit('manage-variables')">
            <div class="flex items-center gap-2 text-blue-600">
              <SettingOutlined />
              <span>管理变量</span>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { AppstoreOutlined, SettingOutlined } from '@ant-design/icons-vue'
import { useSystemVariablesStore } from '@/store/systemVariables'

const emit = defineEmits<{
  'variable-select': [variableName: string]
  'manage-variables': []
}>()

// Store
const systemVariablesStore = useSystemVariablesStore()

// 计算属性
const globalVariables = computed(() => systemVariablesStore.globalVariables)
const localVariables = computed(() => systemVariablesStore.localVariables)

// 处理变量选择
const handleVariableSelect = ({ key, domEvent }) => {
  const variableName = domEvent.target.closest('[data-variable]')?.getAttribute('data-variable')
  if (variableName && key !== 'manage') {
    emit('variable-select', variableName)
  }
}

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colorMap = {
    string: 'blue',
    number: 'green',
    boolean: 'orange',
    json: 'purple'
  }
  return colorMap[type] || 'default'
}

// 获取类型文本
const getTypeText = (type: string) => {
  const textMap = {
    string: 'Str',
    number: 'Num',
    boolean: 'Bool',
    json: 'JSON'
  }
  return textMap[type] || type
}

// 格式化值显示
const formatValue = (value: any) => {
  if (typeof value === 'object') {
    return '{...}'
  }
  const str = String(value)
  return str.length > 20 ? str.substring(0, 20) + '...' : str
}
</script>

<style scoped>
.variable-btn {
  color: #1890ff;
  border: 1px dashed #1890ff;
  background: rgba(24, 144, 255, 0.05);
}

.variable-btn:hover {
  background: rgba(24, 144, 255, 0.1);
  border-color: #40a9ff;
}

:deep(.ant-dropdown-menu) {
  max-width: 300px;
}

:deep(.ant-dropdown-menu-item) {
  padding: 8px 12px;
}

:deep(.ant-dropdown-menu-item-group-title) {
  padding: 8px 12px 4px;
  font-weight: 600;
  color: #666;
}
</style>
