<template>
  <div class="version-history">
    <!-- 版本列表 -->
    <div class="mb-4">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-lg font-medium">版本历史</h3>
        <a-button type="primary" size="small" @click="refreshVersions">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
      </div>
      
      <!-- 当前版本标识 -->
      <a-alert 
        v-if="currentVersion"
        :message="`当前版本: ${currentVersion.version}`"
        :description="currentVersion.description"
        type="info"
        show-icon
        class="mb-4"
      />
    </div>

    <!-- 版本列表 -->
    <div class="version-list">
      <a-spin :spinning="loading">
        <div v-if="versions.length === 0" class="text-center py-8 text-gray-500">
          暂无版本历史
        </div>
        
        <div v-else class="space-y-3">
          <div 
            v-for="version in versions" 
            :key="version.id"
            class="version-item border rounded-lg p-4 hover:bg-gray-50 transition-colors"
            :class="{ 'border-blue-500 bg-blue-50': version.isCurrent }"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                  <span class="font-medium text-lg">{{ version.version }}</span>
                  <a-tag v-if="version.isCurrent" color="blue">当前版本</a-tag>
                  <a-tag v-if="version.isLatest" color="green">最新版本</a-tag>
                  <a-tag :color="getReleaseTypeColor(version.releaseType)">
                    {{ getReleaseTypeText(version.releaseType) }}
                  </a-tag>
                </div>
                
                <div class="text-gray-600 mb-2">
                  {{ version.description || '无描述' }}
                </div>
                
                <div class="text-sm text-gray-500">
                  发布时间: {{ formatDate(version.publishTime) }} | 
                  发布者: {{ version.publisher }}
                </div>
              </div>
              
              <div class="flex gap-2 ml-4">
                <a-button size="small" @click="handleViewVersion(version)">
                  查看
                </a-button>
                <a-button 
                  v-if="!version.isCurrent" 
                  size="small" 
                  type="primary" 
                  @click="handleRollback(version)"
                >
                  回退
                </a-button>
                <a-dropdown>
                  <a-button size="small">
                    <template #icon>
                      <MoreOutlined />
                    </template>
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="handleCompareVersion(version)">
                        对比版本
                      </a-menu-item>
                      <a-menu-item @click="handleDownloadVersion(version)">
                        下载配置
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item 
                        v-if="!version.isCurrent && !version.isLatest"
                        danger
                        @click="handleDeleteVersion(version)"
                      >
                        删除版本
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 分页 -->
    <div v-if="total > pageSize" class="mt-4 text-center">
      <a-pagination
        v-model:current="current"
        v-model:page-size="pageSize"
        :total="total"
        :show-size-changer="false"
        :show-quick-jumper="true"
        @change="loadVersions"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { ReloadOutlined, MoreOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

interface Version {
  id: string
  version: string
  description: string
  releaseType: 'major' | 'minor' | 'patch'
  publishTime: string
  publisher: string
  isCurrent: boolean
  isLatest: boolean
  config: any
}

const props = defineProps<{
  endpointId: string
}>()

const emit = defineEmits<{
  rollback: [version: Version]
  viewVersion: [version: Version]
}>()

// 数据状态
const loading = ref(false)
const versions = ref<Version[]>([])
const currentVersion = ref<Version | null>(null)
const current = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取版本列表
const loadVersions = async () => {
  if (!props.endpointId) return
  
  loading.value = true
  try {
    // 模拟API调用 - 实际应该调用真实的API
    const mockVersions: Version[] = [
      {
        id: '1',
        version: 'v1.2.0',
        description: '新增用户权限管理功能',
        releaseType: 'minor',
        publishTime: '2024-01-15 14:30:00',
        publisher: '张三',
        isCurrent: true,
        isLatest: true,
        config: {}
      },
      {
        id: '2',
        version: 'v1.1.1',
        description: '修复登录验证bug',
        releaseType: 'patch',
        publishTime: '2024-01-10 09:15:00',
        publisher: '李四',
        isCurrent: false,
        isLatest: false,
        config: {}
      },
      {
        id: '3',
        version: 'v1.1.0',
        description: '新增数据导出功能',
        releaseType: 'minor',
        publishTime: '2024-01-05 16:45:00',
        publisher: '王五',
        isCurrent: false,
        isLatest: false,
        config: {}
      }
    ]
    
    versions.value = mockVersions
    currentVersion.value = mockVersions.find(v => v.isCurrent) || null
    total.value = mockVersions.length
  } catch (error) {
    message.error('获取版本历史失败')
  } finally {
    loading.value = false
  }
}

// 刷新版本列表
const refreshVersions = () => {
  loadVersions()
}

// 处理版本回退
const handleRollback = (version: Version) => {
  Modal.confirm({
    title: '确认回退版本',
    content: `确定要回退到版本 ${version.version} 吗？此操作将会创建一个新的版本。`,
    onOk() {
      emit('rollback', version)
    }
  })
}

// 查看版本详情
const handleViewVersion = (version: Version) => {
  emit('viewVersion', version)
}

// 对比版本
const handleCompareVersion = (version: Version) => {
  message.info('版本对比功能开发中...')
}

// 下载版本配置
const handleDownloadVersion = (version: Version) => {
  const dataStr = JSON.stringify(version.config, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${version.version}-config.json`
  link.click()
  URL.revokeObjectURL(url)
}

// 删除版本
const handleDeleteVersion = (version: Version) => {
  Modal.confirm({
    title: '确认删除版本',
    content: `确定要删除版本 ${version.version} 吗？此操作不可恢复。`,
    type: 'warning',
    onOk() {
      message.success('版本删除成功')
      loadVersions()
    }
  })
}

// 获取发布类型颜色
const getReleaseTypeColor = (type: string) => {
  const colorMap = {
    major: 'red',
    minor: 'blue',
    patch: 'green'
  }
  return colorMap[type] || 'default'
}

// 获取发布类型文本
const getReleaseTypeText = (type: string) => {
  const textMap = {
    major: '主版本',
    minor: '次版本',
    patch: '补丁'
  }
  return textMap[type] || type
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

// 监听endpointId变化
watch(() => props.endpointId, (newId) => {
  if (newId) {
    loadVersions()
  }
}, { immediate: true })

onMounted(() => {
  if (props.endpointId) {
    loadVersions()
  }
})
</script>

<style scoped>
.version-history {
  max-height: 600px;
  overflow-y: auto;
}

.version-item {
  transition: all 0.2s ease;
}

.version-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
