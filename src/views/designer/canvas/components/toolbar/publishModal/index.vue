<template>
  <a-modal v-model:visible="visible" title="发布配置" width="900px" :footer="null">
    <!-- 版本管理标签页 -->
    <a-tabs v-model:activeKey="activeTab" class="mb-4">
      <a-tab-pane key="publish" tab="发布配置">
        <!-- 发布表单 -->
        <a-form
          v-if="!showTestArea"
          :model="form"
          :rules="rules"
          ref="formRef"
          layout="horizontal"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
        >
      <a-form-item label="项目" name="project">
        <a-input v-model:value="form.project" placeholder="请输入项目/产品名称" />
      </a-form-item>

      <a-form-item label="子系统" name="subsystem">
        <a-input v-model:value="form.subsystem" placeholder="请输入子系统名称" />
      </a-form-item>

      <a-form-item label="模块" name="module">
        <a-input v-model:value="form.module" placeholder="请输入模块名称" />
      </a-form-item>

      <a-form-item label="路径" name="path">
        <a-input v-model:value="form.path" placeholder="请输入路径配置" />
      </a-form-item>

      <a-form-item label="描述" name="description">
        <a-input v-model:value="form.description" placeholder="请输入描述" />
      </a-form-item>

      <a-form-item label="类型" name="type">
        <a-select v-model:value="form.type" placeholder="请选择类型" class="w-full">
          <a-select-option v-for="option in typeOptions" :key="option.value" :value="option.value">
            <div class="flex items-center gap-2">
              <span>{{ option.label }}</span>
            </div>
          </a-select-option>
        </a-select>
      </a-form-item>

      <!-- 版本配置 -->
      <a-divider>版本配置</a-divider>
      <a-form-item label="版本号" name="version">
        <a-input v-model:value="form.version" placeholder="请输入版本号，如：v1.0.0" />
        <div class="text-gray-500 text-sm mt-1">留空将自动生成版本号</div>
      </a-form-item>
      <a-form-item label="版本描述" name="versionDescription">
        <a-textarea v-model:value="form.versionDescription" placeholder="请输入版本更新说明" :rows="2" />
      </a-form-item>
      <a-form-item label="发布类型" name="releaseType">
        <a-radio-group v-model:value="form.releaseType">
          <a-radio value="major">主版本 (1.0.0 → 2.0.0)</a-radio>
          <a-radio value="minor">次版本 (1.0.0 → 1.1.0)</a-radio>
          <a-radio value="patch">补丁版本 (1.0.0 → 1.0.1)</a-radio>
        </a-radio-group>
      </a-form-item>

      <div class="flex justify-end mt-4">
        <a-button class="mr-2" @click="visible = false">取消</a-button>
        <a-button type="primary" :loading="submitting" @click="handleSubmit">发布</a-button>
      </div>
    </a-form>

        <!-- 测试区域 -->
        <div v-if="showTestArea" class="test-area">
          <div class="mb-4">
            <div class="flex items-center justify-between">
              <div class="text-lg font-medium">API测试</div>
            </div>
          </div>

          <!-- 嵌入Debug组件 -->
          <ApiTestPanel
            ref="apiTestRef"
            :endpoint-url="endpointInfo.url"
            :config-data="form.config"
            :req-param-data="form.reqParam"
            :template-id="currentFileId"
            class="api-test-wrapper"
          />
        </div>
      </a-tab-pane>

      <a-tab-pane key="versions" tab="版本历史">
        <VersionHistory
          :endpoint-id="endpointInfo.id"
          @rollback="handleRollback"
          @view-version="handleViewVersion"
        />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { createEndpoint, updateEndpoint } from '@/apis/designer'
import { useAppStore } from '@/store/app'
import ApiTestPanel from '../apiTest/index.vue'
import VersionHistory from './VersionHistory.vue'

const appStore = useAppStore()
const visible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const apiTestRef = ref(null)
const currentFileId = ref('')
const activeTab = ref('publish')

const endpointInfo = ref({
  id: '',
  url: '',
  data: null
})

// 表单
const form = reactive({
  project: '',
  subsystem: '',
  module: '',
  path: '',
  description: '',
  type: 'api',
  config: '',
  reqParam: '',
  // 版本相关字段
  version: '',
  versionDescription: '',
  releaseType: 'patch'
})

// 表单校验规则
const rules = {
  project: [{ required: true, message: '请输入项目/产品名称', trigger: 'blur' }],
  subsystem: [{ required: true, message: '请输入子系统名称', trigger: 'blur' }],
  module: [{ required: true, message: '请输入模块名称', trigger: 'blur' }],
  path: [{ required: true, message: '请输入路径配置', trigger: 'blur' }],
  description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }]
}

// 测试相关
const showTestArea = ref(false)

// 类型选项
const typeOptions = computed(() => {
  return appStore.getEnumOptions('publishFlow') || []
})

// 组件挂载时获取枚举数据
onMounted(async () => {
  if (!appStore.enumLoaded) {
    await appStore.fetchEnumList()
  }
})

// 打开弹窗
const open = (data: { currentFile: any, graphData: string, endpoint?: any }) => {
  currentFileId.value = data.currentFile.id

  // 初始化表单
  form.project = ''
  form.subsystem = ''
  form.module = ''
  form.path = ''
  form.description = ''
  form.type = 'api'
  form.reqParam = ''
  form.version = ''
  form.versionDescription = ''
  form.releaseType = 'patch'

  // 格式化JSON以便更好地显示
  try {
    if (data.graphData) {
      const jsonObj = JSON.parse(data.graphData)
      form.config = JSON.stringify(jsonObj, null, 2)
    } else {
      form.config = '';
    }
  } catch (e) {
    form.config = data.graphData || '';
  }

  // 重置访问信息
  endpointInfo.value = {
    id: '',
    url: '',
    data: null
  }

  // 如果是编辑模式，填充现有数据
  if (data.endpoint) {
    form.project = data.endpoint.project || ''
    form.subsystem = data.endpoint.subsystem || ''
    form.module = data.endpoint.module || ''
    form.path = data.endpoint.path || ''
    form.description = data.endpoint.description || ''
    form.type = data.endpoint.type || 'api'
    form.reqParam = data.endpoint.reqParam || ''

    // 设置当前端点ID
    endpointInfo.value.id = data.endpoint.id
    endpointInfo.value.url = data.endpoint.path
    endpointInfo.value.data = data.endpoint
  }

  // 隐藏测试区域
  showTestArea.value = false
  // 重置到发布配置标签页
  activeTab.value = 'publish'

  visible.value = true
}

// 版本回退处理
const handleRollback = async (version: any) => {
  try {
    // 这里调用版本回退API
    message.success(`已回退到版本 ${version.version}`)
    // 刷新版本历史
  } catch (error) {
    message.error('版本回退失败')
  }
}

// 查看版本详情
const handleViewVersion = (version: any) => {
  // 切换到发布配置标签页并填充版本数据
  activeTab.value = 'publish'
  form.version = version.version
  form.versionDescription = version.description
  // 填充其他字段...
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const data = {
      project: form.project,
      subsystem: form.subsystem,
      module: form.module,
      path: form.path,
      description: form.description,
      type: form.type,
      templateId: currentFileId.value,
      // 版本相关数据
      version: form.version,
      versionDescription: form.versionDescription,
      releaseType: form.releaseType
    }

    let res
    if (!endpointInfo.value.id) {
      res = await createEndpoint(data)
    } else {
      res = await updateEndpoint({
        id: endpointInfo.value.id,
        ...data
      })
    }

    // 检查响应状态
    if (res.code !== 200) {
      return // 错误由全局拦截器处理
    }

    // 只有成功才显示成功消息并继续
    if (!endpointInfo.value.id) {
      message.success('发布成功')
    } else {
      message.success('更新成功')
    }

    if (res.data) {
      await nextTick()
      
      // 更新reqParam
      form.reqParam = res.data.reqParam || ''

      endpointInfo.value = {
        id: res.data.id,
        url: res.data.path,
        data: res.data
      }

      await nextTick()
      showTestArea.value = true
    }
  } catch (error) {
    if (error instanceof Error) {
      message.error(error.message)
    } else {
      message.error('提交失败')
    }
  } finally {
    submitting.value = false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
.w-full {
  width: 100%;
}

.test-area {
  padding: 0 16px;
}

.api-test-wrapper {
  margin-top: 16px;
  min-height: 300px;
}
</style>
