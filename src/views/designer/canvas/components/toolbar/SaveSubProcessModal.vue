<template>
  <Modal v-model:open="visible" title="子流程配置" @ok="onOkHandler" @cancel="visible = false">
    <Form :model="formState" :label-col="{ flex: '100px' }" ref="formRef" autocomplete="off">
      <Form.Item
        label="父节点"
        name="directoryId"
        :rules="[{ required: true, message: '请选择父节点' }]"
      >
        <TreeSelect
          disabled
          v-model:value="formState.directoryId"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="Please select"
          allow-clear
          tree-default-expand-all
          :tree-data="treeData"
          tree-node-filter-prop="label"
          :field-names="{
            label: 'name',
            value: 'id',
            children: 'children'
          }"
        >
        </TreeSelect>
      </Form.Item>
      <Form.Item
        label="子流程名称"
        name="name"
        :rules="[{ required: true, message: '请输入子流程名称' }]"
      >
        <Input v-model:value="formState.name" :spellcheck="false" />
      </Form.Item>
    </Form>
  </Modal>
</template>

<script setup lang="ts">
import { useDesignerStore } from '@/store/designer';
import { Modal, Form, TreeSelect, Input, message ,type FormInstance} from 'ant-design-vue'
import { createFileApi, getFileTree, saveFileApi } from '@/apis/designer'
import { v4 as uuid } from 'uuid'
import { cloneDeep } from 'lodash-es'

const designerStore = useDesignerStore()
const visible = defineModel<boolean>('open', { default: false })
const formState = reactive({ directoryId: '', name: '', content: '' })

const treeData = ref<any[]>([])

// 只在模态框打开时获取数据
watch(visible, (isVisible) => {
  if (isVisible && treeData.value.length === 0) {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    getFileTree({ userId: userInfo.id, type: 'canvas' }).then(res => {
      if (res.code === 200) {
        treeData.value = res.data || []
        formState.directoryId = '1897160557543936002'
      }
    })
  }
})

const refreshDirectoryTree = inject<() => void>('refreshDirectoryTree')
const formRef= ref<FormInstance>()
watch(visible, (val) => {
  if(!val) {
    formRef.value?.resetFields()
  }
})
const onOkHandler = async () => {
  await formRef.value?.validate()
  // 排除游离节点（未连线节点）
  const cells = designerStore.selectionCells

  const edges = cells.filter(cell => cell.isEdge())
  const nodes = cells.filter(cell => cell.isNode())


  // 找出框选区域的开始节点
  let selectionStartNode = nodes.find(node => node.data.type === 'startNode')
  if(!selectionStartNode){
    const startNodes = nodes.filter(node => {
      const incomingEdges = edges.filter(edge => edge.getTargetCellId() === node.id)

      return incomingEdges.length === 0 || incomingEdges.some(edge => {
        const sourceId = edge.getSourceCellId()
        return !nodes.some(n => n.id === sourceId)
      })
    })
    selectionStartNode = startNodes?.[0] ?? null
  }


  // 找出框选区域的结束节点
  let selectionEndNode = nodes.find(node => node.data.type === 'endNode')
  if(!selectionEndNode){
    const endNodes = nodes.filter(node => {
      const outgoingEdges = edges.filter(edge => edge.getSourceCellId() === node.id)

      return outgoingEdges.length === 0 || outgoingEdges.some(edge => {
        const targetId = edge.getTargetCellId()
        return !nodes.some(n => n.id === targetId)
      })
    })
    selectionEndNode = endNodes?.[0] ?? null
  }

  const selectedCellsData = cells.map(cell => cell.toJSON({ diff: true }))
  let { startNode, endNode } = toRaw(designerStore.nodeGroups.flow.nodes)
  startNode = cloneDeep(startNode)
  endNode = cloneDeep(endNode)
  // 如果选择的开始节点不是标准的startNode类型，则使用系统的startNode
  if (selectionStartNode?.data.type !== 'startNode') {
    // 获取选择的开始节点的位置，并将startNode放在其左侧
    const position = selectionStartNode.getPosition()
    position.x -= 358 + 100
    startNode.components.unshift({
      type: 'outParams',
      label: '输入',
      field: 'inputs'
    })
    const startNodeJson = {
      id: uuid(),
      position,
      shape:"startNode",
      zIndex: 1,
      data: {
        ...startNode,
        outputs: {
          data: selectionStartNode.data.inputs
        },
        inputs: selectionStartNode.data.inputs
      }
    }

    // 找出连接到selectionStartNode左侧的所有边
    const incomingEdges = edges.filter(edge => {
      return edge.getTargetCellId() === selectionStartNode.id &&
             !nodes.some(n => n.id === edge.getSourceCellId())
    })

    const rightPort = startNode.ports.find(port => port.group === 'right')

    // 将这些边重新连接到startNode
    incomingEdges.forEach(edge => {
      const edgeData = edge.toJSON({ diff: true })
      // 修改目标为新创建的startNode
      edgeData.source = {
        cell: startNodeJson.id,
        port: rightPort.id
      }

      // 在selectedCellsData中找到并替换这条边
      const edgeIndex = selectedCellsData.findIndex(cell => cell.id === edge.id)
      if (edgeIndex !== -1) {
        selectedCellsData[edgeIndex] = edgeData
      }
    })
    if(incomingEdges.length === 0) {
      const leftPort = selectionStartNode.getPortsByGroup('left')?.[0]
      // 创建一条从startNode到selectionStartNode的边
      const edgeJson = {
        shape: 'data-processing-curve',
        source: {
          cell: startNodeJson.id,
          port: rightPort.id
        },
        target: {
          cell: selectionStartNode.id,
          port: leftPort?.id ?? 1
        },
        connector: {
          name: 'curveConnector'
        },
        id: uuid()
      }
      selectedCellsData.push(edgeJson)
    }

    // 将新节点和边添加到graphData
    selectedCellsData.push(startNodeJson)
  }

  // 同样处理结束节点
  if (selectionEndNode?.data.type !== 'endNode') {
    // 克隆endNode并添加到graphData
    const position = selectionEndNode.getPosition()
    position.x += 358 + 100
    const endNodeJson = {
      id: uuid(),
      position,
      shape:"endNode",
      zIndex: 1,
      data: {
       ...endNode,
        outputs: selectionEndNode.data.outputs,
        inputs: selectionEndNode.data.outputs.data
      }
    }

    const leftPort = endNode.ports.find(port => port.group === 'left')
    // 找出连接到selectionEndNode右侧的所有边
    const outgoingEdges = edges.filter(edge => {
      return edge.getSourceCellId() === selectionEndNode.id && !nodes.some(n => n.id === edge.getTargetCellId())
    })

    // 将这些边重新连接到endNode
    outgoingEdges.forEach(edge => {
      const edgeData = edge.toJSON({ diff: true })
      // 修改源为新创建的endNode
      edgeData.target = {
        cell: endNodeJson.id,
        port: leftPort.id
      }

      // 在selectedCellsData中找到并替换这条边
      const edgeIndex = selectedCellsData.findIndex(cell => cell.id === edge.id)
      if (edgeIndex !== -1) {
        selectedCellsData[edgeIndex] = edgeData
      }
    })
    if(outgoingEdges.length === 0) {
      const rightPort = selectionEndNode.getPortsByGroup('right')?.[0]
      // 创建一条从selectionEndNode到endNode的边
      const edgeJson = {
        shape: 'data-processing-curve',
        source: {
          cell: selectionEndNode.id,
          port: rightPort?.id??1
        },
        target: {
          cell: endNodeJson.id,
          port: leftPort.id
        },
        connector: {
          name: 'curveConnector'
        },
        id: uuid()
      }
      selectedCellsData.push(edgeJson)
    }

    // 将新节点和边添加到graphData
    selectedCellsData.push(endNodeJson)
  }

  try {
    const res = await createFileApi(formState)
    if (res.code !== 200) throw new Error()
    const graphData = {
      cells: selectedCellsData,
      model: designerStore.mode,
      id: uuid(),
    }
    const content = JSON.stringify(graphData)
    const {updateTime,createTime,...reset} = res.data
    // 获取userid
    const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))
    const saveRes = await saveFileApi({ ...reset, content, userId: userInfo.value.id })
    if (saveRes.code !== 200) throw new Error()

    message.success('子流程保存成功!')
    designerStore.modGraph.cleanSelection()
    refreshDirectoryTree?.()
    visible.value = false
  } catch (e) {
    message.error('子流程保存失败！')
  }
}
</script>
