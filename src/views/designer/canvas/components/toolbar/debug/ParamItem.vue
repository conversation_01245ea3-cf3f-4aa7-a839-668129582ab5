<template>
  <div
    class="param-item"
    :class="{ 'border-b border-gray-200': !item.level }"
    :style="{ paddingLeft: `${(item.level || 0) * 20 + 16}px` }"
  >
    <div class="flex items-center gap-2 mb-1">
      <div v-if="item.level && item.level > 0" class="w-4 h-px bg-gray-300"></div>

      <span class="text-sm text-gray-900 dark:!text-white">{{ item.paramsName }}</span>
      <span v-if="item.required" class="text-red-500">*</span>
      <span class="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded">
        {{ item.variableType }}
      </span>
      <span v-if="item.isHeader" class="px-2 py-0.5 text-xs bg-blue-50 text-blue-600 rounded">
        Header
      </span>
      <span v-if="item.isCommon" class="px-2 py-0.5 text-xs bg-green-50 text-green-600 rounded">
        Common
      </span>
      <span v-if="item.isData" class="px-2 py-0.5 text-xs bg-purple-50 text-purple-600 rounded">
        Data
      </span>
    </div>

    <div class="text-xs text-gray-500 mb-2" :class="{ 'ml-6': item.level && item.level > 0 }">
      {{ item.description }}
    </div>
    <!-- 枚举列表 -->
    <a-select
      v-if="item.isEnumList"
      v-model="paramValue"
      :options="item.options"
      class="w-full"
    ></a-select>
    <!-- 只有非 Object 类型才显示输入框 -->
    <template v-else-if="item.variableType !== 'Object'">
      <!-- 文件上传 -->
      <template v-if="item.variableType === 'File' || item.isFile">
        <a-button
          class="file-upload-btn w-full flex items-center justify-center"
          :class="{ 'ml-6': item.level && item.level > 0 }"
          @click="handleFileSelect"
        >
          <upload-outlined />
          <span class="ml-1">{{ getFileName ? '更换文件' : '点击选择文件' }}</span>
        </a-button>
        <div
          v-if="getFileName"
          class="text-xs text-gray-600 mt-1 truncate"
          :class="{ 'ml-6': item.level && item.level > 0 }"
        >
          已选择: {{ getFileName }}
        </div>
      </template>
      <!-- 输入动态列表 -->
      <DynamicInputList v-else-if="item.variableType === 'Array<String>'" v-model="paramValue" />
      <!-- 普通输入框 -->
      <template v-else>
        <a-input
          v-if="item.variableType !== 'Array'"
          :value="paramValue"
          :placeholder="'请输入' + item.paramsName"
          class="!text-sm"
          :class="{ 'ml-6': item.level && item.level > 0 }"
          inputmode="text"
          :spellcheck="false"
          @input="handleInput"
        />
        <div v-else class="array-container" :class="{ 'ml-6': item.level && item.level > 0 }">
          <div class="flex items-center justify-between mb-2">
            <span class="text-xs text-gray-500">数组项列表</span>
            <a-button type="link" size="small" @click="addArrayItem">
              <plus-outlined /> 添加
            </a-button>
          </div>
          <div v-if="!isArrayWithItems" class="text-xs text-gray-400 mb-2">
            暂无数据，点击添加按钮新增
          </div>
          <div v-else class="array-items">
            <div v-for="(arrayItem, index) in paramValue" :key="index" class="array-item">
              <div class="flex items-center gap-2">
                <div class="flex-1">
                  <template v-if="item.children && item.children[0]">
                    <!-- 如果子项是对象类型，递归渲染 ParamItem -->
                    <template v-if="item.children[0].variableType === 'Object'">
                      <div class="recursive-input-container">
                        <ParamItem
                          :item="{
                            ...item.children[0],
                            children: cloneChildrenStructure(item.children[0].children || []),
                            value: arrayItem,
                            level: (item.level || 0) + 1,
                            allowChinese: true,
                            isRecursive: true
                          }"
                          v-model="paramValue[index]"
                        />
                      </div>
                    </template>
                    <!-- 如果子项是基本类型，直接渲染输入框 -->
                    <template v-else>
                      <!-- 递归渲染的字符串输入框 -->
                      <a-input
                        v-if="item.children[0].variableType === 'String' && item.isRecursive"
                        :value="paramValue[index]"
                        :placeholder="'请输入字符串'"
                        class="!text-sm recursive-string-input"
                        inputmode="text"
                        :spellcheck="false"
                        @input="(e) => handleArrayInput(e, index)"
                      />
                      <!-- 普通的字符串输入框 -->
                      <a-input
                        v-else-if="item.children[0].variableType === 'String'"
                        :value="paramValue[index]"
                        :placeholder="'请输入字符串'"
                        class="!text-sm"
                        inputmode="text"
                        :spellcheck="false"
                        @input="(e) => handleArrayInput(e, index)"
                      />
                      <a-input-number
                        v-else-if="item.children[0].variableType === 'Number'"
                        v-model="paramValue[index]"
                        :placeholder="'请输入数字'"
                        class="!w-full !text-sm"
                      />
                      <a-switch
                        v-else-if="item.children[0].variableType === 'Boolean'"
                        v-model="paramValue[index]"
                        class="!text-sm"
                      />
                      <!-- 如果子项也是数组，递归渲染 ParamItem -->
                      <template v-else-if="item.children[0].variableType === 'Array'">
                        <ParamItem
                          :item="{
                            ...item.children[0],
                            children: cloneChildrenStructure(item.children[0].children || []),
                            value: arrayItem,
                            level: (item.level || 0) + 1,
                            allowChinese: true
                          }"
                          v-model="paramValue[index]"
                        />
                      </template>
                    </template>
                  </template>
                  <!-- 如果没有子项定义，默认使用字符串输入框 -->
                  <template v-else>
                    <a-input
                      v-model="paramValue[index]"
                      :placeholder="'请输入值'"
                      class="!text-sm"
                      inputmode="text"
                      :spellcheck="false"
                    />
                  </template>
                </div>
                <a-button type="link" danger size="small" @click="removeArrayItem(index)">
                  <delete-outlined />
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </template>
    </template>

    <!-- 递归渲染子参数 -->
    <template v-if="item.variableType == 'Object' && item.children && item.children.length > 0">
      <ParamItem
        v-for="(child, index) in item.children"
        :key="index"
        :item="{ ...child, allowChinese: true }"
        v-model="paramValue[child.paramsName]"
        @file-change="(file) => $emit('fileChange', file)"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { UploadOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { selectFile } from '@/utils/selectFile'
import DynamicInputList from '@/views/designer/canvas/components/configRender/dynamicInputList/index.vue'

interface ParamData {
  paramsName: string
  variableType: string
  value: string | number | boolean | any[] | Record<string, any> | File | File[]
  description?: string
  required?: boolean
  isHeader?: boolean
  isCommon?: boolean
  isData?: boolean
  isFile?: boolean
  accept?: string[]
  maxSize?: number
  multiple?: boolean
  children?: ParamData[]
  level?: number
  isEnumList?: boolean
  options?: Array<{label: string, value: string}>
  allowChinese?: boolean
  isRecursive?: boolean
}

const props = defineProps<{
  item: ParamData,
  modelValue?: any
}>()

const emit = defineEmits(['update:modelValue', 'fileChange'])

// 参数值的计算属性
const paramValue = computed({
  get() {
    return props.modelValue !== undefined ? props.modelValue : props.item.value
  },
  set(newValue) {
    // 如果是File类型或File数组，直接使用原值，否则进行深拷贝
    const value = newValue instanceof File || (Array.isArray(newValue) && newValue[0] instanceof File)
      ? newValue
      : typeof newValue === 'object' ? JSON.parse(JSON.stringify(newValue)) : newValue
    emit('update:modelValue', value)
    // 同时更新 item.value，确保内部状态同步
    if (props.item) {
      props.item.value = value
    }
  }
})

// 获取文件名
const getFileName = computed(() => {
  const value = paramValue.value;
  if (!value) return '';

  if (value instanceof File) {
    return value.name;
  } else if (Array.isArray(value) && value.length > 0 && value[0] instanceof File) {
    return `${value.length}个文件`;
  }

  return typeof value === 'string' ? value : '';
});

// 检查是否为有内容的数组
const isArrayWithItems = computed(() => {
  const value = paramValue.value
  return Array.isArray(value) && value.length > 0
})

// 处理文件选择
const handleFileSelect = async () => {
  try {
    const options = {
      multiple: props.item.multiple || false,
      accept: props.item.accept && props.item.accept.length > 0 ? props.item.accept.join(',') : undefined
    };

    const files = await selectFile(options);

    if (!files || files.length === 0) return;

    paramValue.value = options.multiple ? files : files[0];
    emit('fileChange', paramValue.value);
  } catch (error) {
    console.error('选择文件失败:', error);
  }
}

// 添加数组项
const addArrayItem = () => {
  if (!Array.isArray(paramValue.value)) {
    paramValue.value = []
  }
  const childType = props.item.children?.[0]?.variableType || 'String'
  let defaultValue: any
  switch (childType) {
    case 'Number':
      defaultValue = 0
      break
    case 'Boolean':
      defaultValue = false
      break
    case 'Array':
      defaultValue = []
      break
    case 'Object':
      defaultValue = createObjectDefaultValue(props.item.children?.[0])
      break
    default:
      defaultValue = ''
  }
  paramValue.value.push(defaultValue)
}

// 创建Object类型的默认值
const createObjectDefaultValue = (childItem: ParamData | undefined): Record<string, any> => {
  const defaultValue: Record<string, any> = {}
  
  if (childItem?.children && childItem.children.length > 0) {
    childItem.children.forEach(child => {
      switch (child.variableType) {
        case 'Number':
          defaultValue[child.paramsName] = 0
          break
        case 'Boolean':
          defaultValue[child.paramsName] = false
          break
        case 'Array':
          defaultValue[child.paramsName] = []
          break
        case 'Object':
          defaultValue[child.paramsName] = createObjectDefaultValue(child)
          break
        default:
          defaultValue[child.paramsName] = ''
      }
    })
  }
  
  return defaultValue
}

// 深拷贝子对象结构
const cloneChildrenStructure = (children: ParamData[]): ParamData[] => {
  return children.map(child => ({
    ...child,
    value: child.variableType === 'Object' ? createObjectDefaultValue(child) : 
           child.variableType === 'Array' ? [] : 
           child.variableType === 'Number' ? 0 : 
           child.variableType === 'Boolean' ? false : '',
    children: child.children ? cloneChildrenStructure(child.children) : undefined,
    allowChinese: true
  }))
}

// 删除数组项
const removeArrayItem = (index: number) => {
  if (Array.isArray(paramValue.value)) {
    paramValue.value.splice(index, 1)
  }
}

// 添加输入处理函数
const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement
  const value = target.value
  paramValue.value = value
}

// 处理数组输入
const handleArrayInput = (e: Event, index: number) => {
  const target = e.target as HTMLInputElement
  const value = target.value
  if (Array.isArray(paramValue.value)) {
    paramValue.value[index] = value
  }
}

</script>

<style lang="scss" scoped>
.param-item {
  padding: 12px 16px;

  // 只给顶层参数添加下边框
  &:last-child {
    border-bottom: none;
  }
}

:deep(.ant-upload) {
  display: block;
  width: 100%;
}

:deep(.ant-upload-select) {
  display: block;
  width: 100%;
}

.file-upload-btn {
  height: 32px;
  font-size: 14px;
  border-radius: 4px;
}

.array-container {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
}

.array-items {
  .array-item {
    padding: 8px;
    margin-bottom: 8px;
    border: 1px dashed #f0f0f0;
    border-radius: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.recursive-input-container {
  :deep(.ant-input) {
    font-family: inherit;
    text-transform: none;
    unicode-bidi: normal;
  }
}

.recursive-string-input {
  :deep(.ant-input) {
    font-family: inherit !important;
    text-transform: none !important;
    unicode-bidi: normal !important;
  }
}
</style>
