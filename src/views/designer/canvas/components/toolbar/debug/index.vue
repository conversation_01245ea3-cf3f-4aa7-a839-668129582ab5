<template>
  <EmbeddedDrawer
    :model-value="drawerVisible"
    @update:model-value="(val) => drawerVisible = val"
    title="debug"
    :width="400"
    @close="handleClose"
    :maskClosable="true"
  >
    <template #header-actions>
      <a-space>
        <span class="font-normal">是否保留debug数据</span>
        <a-switch v-model:checked="isKeepDebugData" checked-children="保留" un-checked-children="不保留" />
      </a-space>
      <a-button size="small" @click="handleMock"> 模拟数据 </a-button>
    </template>

    <!-- 内容区 -->
    <a-spin :spinning="mockLoading" tip="生成模拟数据中...">
      <div class="flex items-center gap-2 px-4 py-3 border-b border-gray-200">
        <MonitorOutlined class="text-blue-500 dark:!text-primary" />
        <span class="text-sm dark:!text-[var(--text-secondary)]">{{ getNodeName }}</span>
      </div>

      <ParamItem 
        v-for="(item, index) in paramsData" 
        :key="index" 
        :item="item"
        @update:value="(val) => handleParamValueUpdate(index, val)"
      />
    </a-spin>

    <template #footer>
      <a-button
        type="primary"
        class="w-full h-10 rounded-full text-sm bg-primary dark:!bg-[var(--btn-color)] text-white hover:bg-primary-hover hover:border-primary-hover"
        :loading="btnLoading"
        @click="handleRun"
      >
        debug
      </a-button>
    </template>
  </EmbeddedDrawer>
</template>

<script setup lang="ts">
import { tryRunApi, generateMockFields, getDesignerEnumList, tryRunApi_SSE } from '@/apis/designer'
import { useDesignerStore } from '@/store/designer'
import { useTryRunStore } from '@/store/tryRun'
import {
  MonitorOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, ref } from 'vue'
import ParamItem from './ParamItem.vue'
import { EmbeddedDrawer } from '@/components'

const props = defineProps({
  modelValue: Boolean,
  isDirectDebug: {
    type: Boolean,
    default: false
  },
  initialParams: {
    type: [String, Object],
    default: () => ({})
  },
  currentNode: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'debug'])

const drawerVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const designerStore = useDesignerStore()
const tryRunStore = useTryRunStore()
const btnLoading = ref(false)
const currentNodeId = ref(null)

// 是否保留debug数据
const isKeepDebugData = ref(true)

// 添加一个新的 ref 用于模拟数据加载状态
const mockLoading = ref(false)

// 添加需要的接口定义
interface GraphNode {
  getData(): any;
  id: string;
}

interface MockDataResponse extends IResType<any> {
  mockData?: Record<string, any>;
}

// 修改 ParamData 接口，添加更多类型支持
interface ParamData {
  paramsName: string
  variableType: string
  value: string | File | File[] | any
  description?: string
  required?: boolean
  isHeader?: boolean
  isFile?: boolean
  accept?: string[]
  maxSize?: number
  multiple?: boolean
  children?: ParamData[]
  level?: number
  isBody?: boolean
  readOnly?: boolean
  isCommon?: boolean
  isData?: boolean
  isEnumList?: boolean
  options?: Array<{label: string, value: string}>
}

// 修改 ref 的类型声明
const paramsData = ref<ParamData[]>([])

const getTargetNode = () => {
  const graph = designerStore.modGraph
  if (!graph) return null

  if (props.isDirectDebug) {
    const startNode = graph.getNodes().find(node => node.getData()?.type === 'startNode')
    return startNode
  }
  return designerStore.currentNode
}

// 初始化参数
const initParams = async () => {
  const graph = designerStore.modGraph
  if (!graph) return

  if (isKeepDebugData.value) {
    const targetNode = getTargetNode()
    if (targetNode) {
      const savedData = designerStore.getNodeDebugData(targetNode.id)
      if (savedData) {
        paramsData.value = JSON.parse(JSON.stringify(savedData))
        return
      }
    }
  }
  let targetNode: GraphNode | null = null
  let startNode: GraphNode | null = null
  const nodes = graph.getNodes() as GraphNode[]

  // 检查当前选中的节点是否是开始节点或结束节点
  const currentNodeType = designerStore.currentNode?.getData()?.type
  const isSpecialNode = currentNodeType === 'startNode' || currentNodeType === 'endNode'

  // 如果是直接调试模式，始终使用开始节点
  if (props.isDirectDebug) {
    targetNode = nodes.find(node => node.getData()?.type === 'startNode') || null
  }
  // 如果当前选中的是特殊节点
  else if (isSpecialNode) {
    if (currentNodeType === 'endNode') {
      // 如果是结束节点，直接使用当前节点
      targetNode = designerStore.currentNode as GraphNode
    } else {
      // 如果是开始节点，直接使用当前节点
      targetNode = designerStore.currentNode as GraphNode
    }
  } else {
    // 普通节点的处理
    startNode = nodes.find(node => node.getData()?.type === 'startNode') || null
    targetNode = designerStore.currentNode as GraphNode
  }

  if (!targetNode && !startNode) return

  let nodeData = targetNode?.getData() || {} as any
  let startNodeData = null as any

  // 如果是直接调试模式或当前选中的是特殊节点
  if (props.isDirectDebug || isSpecialNode) {
    // 直接使用目标节点的数据
  } else {
    startNodeData = startNode?.getData() || {} as any
    nodeData = targetNode?.getData() || {} as any
  }

  let allParams: ParamData[] = []

  // 修改 initParams 函数中的 processParams 方法
  const processParams = (params: any[], type: 'header' | 'common' | 'data') => {
    return params.map((param: any) => {
      const baseParam: ParamData = {
        paramsName: param.name || '',
        variableType: param.type || 'String',
        value: param.defaultValue || (nodeData.outputs?.data?.[param.name] || ''),
        description: param.description || '',
        required: param.required ?? true,
        level: 0,
        isFile: param.isFile || false,
        accept: param.accept || [],
        maxSize: param.maxSize,
        multiple: param.multiple || false,
        ...(type === 'header' && { isHeader: true }),
        ...(type === 'common' && { isCommon: true }),
        ...(type === 'data' && { isData: true })
      }

      // 处理 Object 类型
      if (param.type === 'Object' && param.children?.length) {
        baseParam.value = param.defaultValue || {}
        baseParam.children = param.children.map((child: any) => ({
          paramsName: child.name || '',
          variableType: child.type || 'String',
          value: child.defaultValue || '',
          description: child.description || '',
          required: child.required ?? true,
          level: 1,
          isFile: child.isFile || false,
          accept: child.accept || [],
          maxSize: child.maxSize,
          multiple: child.multiple || false
        }))
      }

      // 处理 Array 类型
      if (param.type === 'Array') {
        baseParam.multiple = true
        baseParam.value = param.defaultValue || []
        // 如果数组有子项定义
        if (param.children) {
          baseParam.children = param.children.map((child: any) => ({
            paramsName: child.name || '',
            variableType: child.type || 'String',
            value: '',
            description: child.description || '',
            required: child.required ?? true,
            level: 1,
            isFile: child.isFile || false,
            accept: child.accept || [],
            maxSize: child.maxSize,
            multiple: false,
            children: child.children?.map((item: any) => ({
              paramsName: item.name || '',
              variableType: item.type || 'String',
              value: '',
              description: item.description || '',
              required: item.required ?? true,
            }))
          }))
        }
      }

      return baseParam
    })
  }

  // 处理所有类型的参数，添加空值检查
  const outputs = (props.isDirectDebug || isSpecialNode) ?
    (nodeData.outputs || {}) :
    (startNodeData?.outputs || {})

  const { headers = [], commons = [], data = [] } = outputs

  if (headers?.length) allParams.push(...processParams(headers, 'header'))
  if (commons?.length) allParams.push(...processParams(commons, 'common'))
  if (data?.length) allParams.push(...processParams(data, 'data'))

  // 如果是直接调试模式或当前选中的是开始节点，不需要进一步处理
  if (props.isDirectDebug || isSpecialNode) {
    paramsData.value = allParams
    return
  }

  const componentsData = await getComponentsData()
  const allParamsData = JSON.parse(JSON.stringify(allParams))
  allParams = filterCurrentParamsData(allParamsData, componentsData)
  
  paramsData.value = allParams
}

function extractFieldName(str: string): string | null {
  const regex = /^\{data\.(\w+)\}$/
  const match = str.match(regex)
  return match ? match[1] : null
}

// 获取options枚举值
const fetchEnumOptions = async (enumKey: string) => {
  try {
    const response = await getDesignerEnumList(enumKey)
    if (response?.data?.[0]?.content) {
      // 将 content 数组转换为 label-value 格式
      return response.data[0].content.map((item: string) => ({
        label: item,
        value: item
      })) || []
    }
  } catch (error) {
    console.error('获取枚举值失败:', error)
    return []
  }
}

// 获取组件数据
const getComponentsData = async() => {
  const currentNode = designerStore.currentNode
  if (!currentNode) return []
  const nodeData = currentNode.getData()
  // select options 枚举数据
  for (const component of nodeData.components || []) {
    if (component.props?.enumKey) {
      const enumOptions = await fetchEnumOptions(component.props.enumKey)
      component.props = {
        ...component.props,
        options: enumOptions
      }
    }
  }
  return nodeData?.components || []
}

// 处理组件参数
const getContentParamsValues = (components: any) => {
  let result: any[] = [];
  // 使用默认参数获取属性
  components.forEach((component: any) => {
    if(component.type === 'contentParams' && component.props?.defaultParams) {
      result = result.concat(component.props.defaultParams)
    }
  })
  return result
}

// 过滤当前参数数据
const filterCurrentParamsData = (allParams: ParamData[], componentsData: any[]): ParamData[] => {
  const currentParamsData = getCurrentParamsData()
  const contentParamsData = getContentParamsValues(componentsData)
  const mergedParamsData = currentParamsData.map(item => {
    // 查找 allParams 中是否存在相同 paramsName 的对象
    const existingParamIndex = allParams.findIndex(param => param.paramsName === item.paramsName);
    if (existingParamIndex !== -1) {
      // 合并对象
      return {
        ...allParams[existingParamIndex],
        ...item
      }
    }
    const referenceIndex = allParams.findIndex(param => extractFieldName(item.value as string) === param.paramsName);
    if (referenceIndex !== -1) {
      return {
        ...allParams[referenceIndex],
        ...item,
        paramsName: item.paramsName
      }
    }
    return item
  })

  // 处理枚举数据
  const newParamsData = mergedParamsData.map(item => {
    const enumItem = componentsData.find(emumItem => emumItem?.props?.enumKey)
    if(enumItem && item.paramsName === enumItem.field) {
      return {
        ...item,
        variableType: 'Array',
        enumKey: enumItem.props?.enumKey,
        isEnumList: true,
        options: enumItem.props?.options,
        level: 0
      }
    }
    return item;
  }).filter((item): item is ParamData => !!item)
  const filterParamsData:any[] = []
  const newComponentsData = JSON.parse(JSON.stringify([...componentsData, ...contentParamsData]))
  newComponentsData.forEach((item) => {
    const curItem = newParamsData.find(param => param.paramsName === item.field)
    if(curItem && !!extractFieldName(curItem?.value)) {
      curItem.value = ''
    }
    if(curItem) {
      filterParamsData.push(curItem)
    } else if (item.props?.enumKey) {
      const enumItem = {
        paramsName: item.field,
        variableType: 'Array',
        value: '',
        description: item.description || '',
        required: item?.required ?? true,
        level: 0,
        isFile: item.isFile || false,
        accept: item.accept || [],
        maxSize: item.maxSize,
        multiple: item.multiple || false,
        enumKey: item.props?.enumKey,
        options: item.props?.options,
        isEnumList: true
      }
      filterParamsData.push(enumItem)
    }
  })
  return filterParamsData
}

// 获取当前参数数据
const getCurrentParamsData = () => {
  const currentNode = designerStore.currentNode
  if (!currentNode) return []
  const nodeData = currentNode.getData()
  if (!nodeData?.properties) return []
  return Object.entries(nodeData.properties).map(([key, value]) => ({
    paramsName: key,
    value: value || ''
  }))
}
const streamData = ref(null)
// 修改 handleRun 函数中处理文件上传的逻辑
const handleRun = async () => {
  try {
    btnLoading.value = true

    // 构建请求数据
    const commons = {
      canvas: designerStore.modGraph.toJSON(),
      currentNode: currentNodeId.value
    }

    // 如果不是直接调试模式，添加 currentNode 字段
    if (!props.isDirectDebug) {
      commons.currentNode = designerStore.currentNode?.id
    }

    const data = {}
    const headers = {}

    // 先找出所有文件类型参数
    const fileParams: ParamData[] = []

    // 找出所有文件类型参数
    paramsData.value.forEach(param => {
      const paramValue = param.value
      const isFile = (param.isFile || param.variableType === 'File') &&
                     (paramValue instanceof File ||
                     (Array.isArray(paramValue) && paramValue[0] instanceof File))

      if (isFile) {
        fileParams.push(param)
      }
    })

    // 只处理非文件参数，文件字段完全不放入JSON对象中
    paramsData.value.forEach(param => {
      // 跳过文件类型参数
      if (fileParams.some(fp => fp.paramsName === param.paramsName)) {
        return
      }

      // 处理数组类型参数，转换为对象数组格式
      // 不需要转换，直接传入
      let paramValue = param.value
      // if (Array.isArray(paramValue) && param.variableType === 'Array') {
      //   // 将数组转换为 {id: index+1, value: item} 格式
      //   paramValue = paramValue.map((item, index) => ({
      //     id: index + 1,
      //     value: item
      //   }))
      // }

      // 只处理非文件类型
      if (param.isCommon) {
        commons[param.paramsName] = paramValue
      } else if (param.isData) {
        data[param.paramsName] = paramValue
      } else if (param.isHeader) {
        headers[param.paramsName] = paramValue
      }
    })

    // 创建FormData
    const formData = new FormData()

    // 添加普通数据
    formData.append('commons', JSON.stringify(commons))
    formData.append('data', JSON.stringify(data))

    // 添加文件 - 以原始字段名添加
    fileParams.forEach(param => {
      const paramValue = param.value

      // 单个文件
      if (paramValue instanceof File) {
        formData.append(param.paramsName, paramValue)
      }
      // 多个文件
      else if (Array.isArray(paramValue)) {
        paramValue.forEach((file: File) => {
          formData.append(param.paramsName, file)
        })
      }
    })

    // 调用试运行接口
    const {
      streamData: innerStreamData,        // 响应式的流式数据
      finalResult,       // 响应式的最终结果
      loading,           // 响应式的加载状态
      connected,         // 响应式的连接状态
      error,             // 响应式的错误信息
      reconnectCount,    // 响应式的重连次数
      fetch: debugFetch, // 执行请求的方法
      close,             // 关闭连接的方法
      reconnect,         // 手动重连的方法
      clear              // 清空数据的方法
    } = tryRunApi_SSE;

    watch(
  () => innerStreamData.value,
  (val) => {
    streamData.value = val
  },
  { immediate: true, deep: true }
)

    const res = await debugFetch({
      data: formData,
      headers: headers
    })
    // const res = await tryRunApi(formData, headers)
    if (res.code === 200) {
      tryRunStore.$reset()

      const nodes = res.data.nodes || []
      nodes.forEach(nodeData => {
        const nodeId = nodeData.nodeId || nodeData.id
        if (nodeId) {
          tryRunStore.setNodeDebugResult(nodeId, {
            inputs: nodeData.inputs || {},
            outputs: nodeData.outputs || {},
            status: nodeData.status || 'completed',
            startTime: nodeData.startTime || Date.now(),
            endTime: nodeData.endTime || Date.now(),
            errorMessage: nodeData.errorMessage,
            nodeType: nodeData.nodeType || nodeData.type,
            properties: nodeData.properties
          })
        }
      })

      message.success('运行成功')
      // 保存debug数据
      if (isKeepDebugData.value) {
        const targetNode = getTargetNode()
        if (targetNode) {
          const dataToSave = JSON.parse(JSON.stringify(paramsData.value))
          designerStore.setNodeDebugData(targetNode.id, dataToSave)
        }
      }
      emit('debug', res)
      drawerVisible.value = false
    } else {
      message.error(res.msg || '运行失败')
    }
  } catch (error) {
    console.error('Debug error:', error)
    message.error('运行失败')
  } finally {
    btnLoading.value = false
  }
}

watch(
  () => streamData.value,
  (newVal, oldVal) => {
    if (!oldVal && newVal) {
      tryRunStore.$reset()
    }
    if (newVal) {
      const nodeData = JSON.parse(newVal)
      if (nodeData) {
        const nodeId = nodeData?.data?.nodeId || nodeData?.data?.id
        if (nodeId) {
          tryRunStore.setNodeDebugResult(nodeId, {
            inputs: nodeData?.data?.inputs || {},
            outputs: nodeData?.data?.outputs || {},
            status: nodeData?.data?.status || 'completed',
            startTime: nodeData?.data?.startTime || Date.now(),
            endTime: nodeData?.data?.endTime || Date.now(),
            errorMessage: nodeData?.data?.errorMessage,
            nodeType: nodeData?.data?.nodeType || nodeData?.data?.type,
            properties: nodeData?.data?.properties
          })
        }
      }
    }
  },
  { immediate: true, deep: true }
)

const handleClose = () => {
  if (isKeepDebugData.value) {
    const targetNode = getTargetNode()
    if (targetNode) {
      const dataToSave = JSON.parse(JSON.stringify(paramsData.value))
      designerStore.setNodeDebugData(targetNode.id, dataToSave)
    }
  }
  drawerVisible.value = false
  paramsData.value = []
  designerStore.modGraph.cleanSelection()
}

// 监听抽屉显示状态
watch(() => drawerVisible.value, (val) => {
  if (val) {
  // 只在抽屉打开时初始化参数
    initParams()
  } else {
  // 抽屉关闭时清空参数
    paramsData.value = []
  }
})

// 监听图数据变化，但只在抽屉显示时更新
watch(() => designerStore.modGraph, () => {
  if (drawerVisible.value) {
    initParams()
  }
}, { deep: true })

// 修改开关状态变化的监听
watch(() => isKeepDebugData.value, (newVal) => {
  if (!newVal) {
    const targetNode = getTargetNode()
    if (targetNode) {
      designerStore.clearNodeDebugData(targetNode.id)
    }
  }
})

// 修改 Mock 数据生成逻辑
const generateMockValue = (param: ParamData): any => {
  switch (param.variableType) {
  case 'String':
    return `mock_${param.paramsName}_${Math.random().toString(36).substring(7)}`
  case 'Number':
    return Math.floor(Math.random() * 1000)
  case 'Boolean':
    return Math.random() > 0.5
  default:
    return `mock_${param.paramsName}`
  }
}

// Mock数据处理函数
const mockParams = (params: ParamData[]): ParamData[] => {
  return params.map(param => {
    const clonedParam = { ...param }
    clonedParam.value = generateMockValue(clonedParam)

    if (clonedParam.children?.length) {
      clonedParam.children = mockParams(clonedParam.children)
    }

    return clonedParam
  })
}

// 修改 handleMock 函数
const handleMock = async () => {
  try {
    mockLoading.value = true // 开始加载

    // 准备请求参数
    const fields = paramsData.value.map(param => ({
      name: param.paramsName,
      type: param.variableType,
      description: param.description || '',
      children: param.children?.map(child => ({
        name: child.paramsName,
        type: child.variableType,
        description: child.description || ''
      }))
    }))

    // 调用模拟数据生成接口
    const res = await generateMockFields({ fields }) as MockDataResponse

    if (res.code === 200 && res.data?.mockData) {
      // 更新参数值
      const mockData = res.data.mockData

      paramsData.value = paramsData.value.map(param => {
        const clonedParam = { ...param }
        // 检查 mockData 中是否有对应的字段值
        if (mockData && mockData[param.paramsName] !== undefined) {
          clonedParam.value = mockData[param.paramsName]
        }

        if (clonedParam.children?.length) {
          clonedParam.children = clonedParam.children.map(child => {
            const clonedChild = { ...child }
            // 检查 mockData 中是否有对应的子字段值
            if (mockData && mockData[child.paramsName] !== undefined) {
              clonedChild.value = mockData[child.paramsName]
            }
            return clonedChild
          })
        }

        return clonedParam
      })

      message.success('模拟数据已生成')
    } else {
      console.log('模拟数据响应:', res)
      message.error('生成模拟数据失败')
    }
  } catch (error) {
    console.error('生成模拟数据失败:', error)
    message.error('生成模拟数据失败')
  } finally {
    mockLoading.value = false // 结束加载
  }
}

// 修改获取节点名称的部分
const getNodeName = computed(() => {
  if (props.isDirectDebug) {
  // 如果是直接调试模式，获取开始节点
    return '开始节点'
  } else {
  // 否则获取当前选中节点
    return designerStore.currentNode?.getData()?.title || ''
  }
})

// 添加参数值更新处理函数
const handleParamValueUpdate = (index: number, value: any) => {
  if (paramsData.value[index]) {
    paramsData.value[index].value = value
  }
}
</script>

<style>
.debug-drawer-root {
.ant-drawer-content-wrapper {
  border-radius: 12px !important;
}

.ant-drawer-content {
  border-radius: 12px !important;
}

.ant-drawer-header {
  padding: 16px 24px;
  background: linear-gradient(180deg, #f9f2fe 0%, #fcfcff 100%);
}
}

.header-gradient {
position: relative;
min-height: 40px;
}

.close-icon {
font-size: 16px;
color: rgba(0, 0, 0, 0.45);
cursor: pointer;

&:hover {
  color: rgba(0, 0, 0, 0.75);
}
}
</style>
