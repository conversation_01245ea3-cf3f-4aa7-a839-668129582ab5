<template>
  <div class="flex items-center gap-2">
    <!-- 左侧工具组 -->
    <div
      class="bg-area-fill dark:!bg-[var(--black-auxiliary-color)] rounded-md px-2 py-1 shadow-md flex items-center gap-2"
    >
      <!-- 缩放比例选择 -->
      <a-select
        v-model:value="scale"
        class="w-24"
        size="small"
        :options="[
          { value: '50%', label: '50%' },
          { value: '75%', label: '75%' },
          { value: '100%', label: '100%' },
          { value: '150%', label: '150%' },
          { value: '200%', label: '200%' },
        ]"
        @change="handleScaleChange"
      />
      <!-- 比例恢复100%按钮 -->
      <a-tooltip
        title="恢复到100%"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn"
          @click="resetZoom"
        >
          <template #icon>
            <reload-outlined class="text-custom" />
          </template>
        </a-button>
      </a-tooltip>
      <!-- 缩小按钮 -->
      <a-tooltip
        title="缩小"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn"
          @click="handleZoomOut"
        >
          <template #icon>
            <zoom-out-outlined class="text-custom" />
          </template>
        </a-button>
      </a-tooltip>

      <!-- 放大按钮 -->
      <a-tooltip
        title="放大"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn"
          @click="handleZoomIn"
        >
          <template #icon>
            <zoom-in-outlined class="text-custom" />
          </template>
        </a-button>
      </a-tooltip>

      <!-- 分隔线 -->
      <div class="h-5 w-px bg-gray-200"></div>
      <!-- 缩略图按钮和弹出层 -->
      <a-popover
        trigger="click"
        placement="bottomRight"
        :open="showMinimap"
        overlayClassName="minimap-popover"
        @open-change="handleMinimapVisibleChange"
      >
        <template #content>
          <Minimap :graph="props.graph" />
        </template>
        <a-button
          type="text"
          class="toolbar-btn"
          :class="{ 'toolbar-btn-active': showMinimap }"
        >
          <template #icon>
            <eye-outlined class="text-custom" />
          </template>
        </a-button>
      </a-popover>

      <!-- 分隔线 -->
      <div class="h-5 w-px bg-gray-200"></div>

      <!-- 添加标签按钮 -->
      <a-tooltip
        title="添加标签"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn"
          @click="handleNodeClick('label')"
        >
          <template #icon>
            <font-size-outlined class="text-custom" />
          </template>
        </a-button>
      </a-tooltip>

      <!-- 系统变量按钮 -->
      <a-tooltip
        title="系统变量"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn"
          @click="showSystemVariables = true"
        >
          <template #icon>
            <appstore-outlined class="text-custom" />
          </template>
        </a-button>
      </a-tooltip>

      <!-- 添加占位节点按钮 -->
      <a-tooltip
        title="添加占位节点"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn"
          @click="handleNodeClick('placeholder')"
        >
          <template #icon>
            <img
              src="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/box.svg"
              alt=""
              class="filter dark:invert"
            />
          </template>
        </a-button>
      </a-tooltip>

      <!-- 添加节点按钮 -->
      <a-button
        type="text"
        class="toolbar-btn"
        @click="nodeSelector?.openNodeSelector()"
      >
        <template #icon>
          <plus-circle-outlined class="text-custom" />
        </template>
      </a-button>
    </div>

    <!-- 蓝色按钮组 -->
    <div
      class="bg-area-fill dark:!bg-[var(--black-auxiliary-color)] rounded-md px-2 py-1 shadow-md flex items-center gap-2"
    >
      <!-- AI助手按钮 -->
      <a-tooltip
        v-if="mode != 'readonly'"
        title="CHAT"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn-blue"
          @click="$emit('toggleAIChat')"
        >
          <template #icon>
            <message-filled class="text-primary" />
          </template>
        </a-button>
      </a-tooltip>

      <!-- 试运行按钮 -->
      <a-tooltip
        title="DEBUG"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn-blue"
          @click="handleRun"
        >
          <template #icon>
            <bug-filled class="text-primary" />
          </template>
        </a-button>
      </a-tooltip>

      <!-- 保存按钮 -->
      <a-tooltip
        title="SAVE"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn-blue"
          @click="handleSave"
        >
          <template #icon>
            <save-filled class="text-primary" />
          </template>
        </a-button>
      </a-tooltip>

      <!-- 发布按钮 -->
      <a-tooltip
        v-if="mode != 'readonly'"
        title="PUBLISH"
        placement="top"
      >
        <a-button
          type="text"
          class="toolbar-btn-blue"
          @click="handlePublish"
        >
          <template #icon>
            <cloud-filled class="text-primary" />
          </template>
        </a-button>
      </a-tooltip>

      <!-- 模块化按钮 -->
      <a-tooltip
        v-if="mode != 'readonly'"
        title="模块化"
        placement="top"
      >
        <a-button
          v-if="saveSubProcessButtonVisible"
          type="text"
          class="toolbar-btn-blue"
          @click="handleSaveAsSubProcess"
        >
          <template #icon>
            <appstore-filled class="text-primary" />
          </template>
        </a-button>
      </a-tooltip>
    </div>
  </div>

  <!-- 节点选择器 -->
  <NodeSelector ref="nodeSelector" />

  <!-- 发布弹窗 -->
  <PublishModal ref="publishModalRef" />

  <!-- 保存为子流程弹窗 -->
  <SaveSubProcessModal v-model:open="saveSubProcessModalVisible" />

  <!-- 系统变量管理 -->
  <SystemVariables v-model:visible="showSystemVariables" />
</template>

<script setup lang="ts">
import {
  BugOutlined,
  BugFilled,
  MessageOutlined,
  MessageFilled,
  PlusCircleOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FontSizeOutlined,
  EyeOutlined,
  SaveOutlined,
  SaveFilled,
  CloudUploadOutlined,
  ReloadOutlined,
  AppstoreOutlined,
  CloudFilled,
  AppstoreFilled
} from '@ant-design/icons-vue'
import { Graph } from '@antv/x6'
import { message } from 'ant-design-vue'
import { ref, computed, onUnmounted, onMounted } from 'vue'
import { useDesignerStore } from '@/store/designer'
import { useAppStore } from '@/store/app'
import { useTryRunStore } from '@/store/tryRun'
import { saveFileApi } from '@/apis/designer'
import NodeSelector from './nodeSelector/index.vue'
import { v4 as uuid } from 'uuid'
import Minimap from './minimap/index.vue'
import PublishModal from './publishModal/index.vue'
import SaveSubProcessModal from './SaveSubProcessModal.vue'
import SystemVariables from './systemVariables/index.vue'

const props = withDefaults(defineProps<{
  graph?: Graph,
  toolPanelRef?: any
}>(), {})

const emit = defineEmits(['toggleAIChat', 'toggleFullscreen', 'toggleMinimap', 'debug'])

const scale = ref<string>('100%')
const nodeSelector = ref<InstanceType<typeof NodeSelector>>()
const designerStore = useDesignerStore()
const appStore = useAppStore()
const tryRunStore = useTryRunStore()
const showMinimap = ref(false)
const publishModalRef = ref<InstanceType<typeof PublishModal>>()
const showSystemVariables = ref(false)

// 获取mode状态
const mode = computed(() => appStore.mode)

// 添加新的状态
const currentNode = ref(null as any)
const isFollowing = ref(false)

const handleScaleChange = (value: string) => {
  const scaleValue = parseInt(value)
  props.graph?.zoomTo(scaleValue / 100)
  scale.value = value
}

const handleRun = () => {
  tryRunStore.isDirectDebug = true // 设置为直接调试模式
  tryRunStore.setVisible(true)
}

// 监听图形缩放
props.graph?.on('scale', ({ sx }) => {
  scale.value = `${Math.round(sx * 100)}%`
})

// 保存文件
const handleSave = async () => {
  const graphData = designerStore.modGraph.toJSON({ diff: true })
  graphData['mode'] = designerStore.mode
  if (!graphData['id']) {
    graphData['id'] = uuid()
  }
  const content = JSON.stringify(graphData)
  // 获取userid
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))
  const res = await saveFileApi({
    id: designerStore.currentFile.id,
    directoryId: designerStore.currentFile.directoryId,
    name: designerStore.currentFile.name,
    content,
    userId: userInfo.value.id
  })
  if (res.code === 200) {
    message.success('保存成功')
  }
}

// 缩小
const handleZoomOut = () => {
  const currentScale = parseInt(scale.value)
  const newScale = Math.max(currentScale - 25, 50)
  scale.value = `${newScale}%`
  props.graph?.zoomTo(newScale / 100)
}

// 放大
const handleZoomIn = () => {
  const currentScale = parseInt(scale.value)
  const newScale = Math.min(currentScale + 25, 200)
  scale.value = `${newScale}%`
  props.graph?.zoomTo(newScale / 100)
}

// 将比例恢复到100%
const resetZoom = () => {
  const defaultScale = 100
  scale.value = `${defaultScale}%`
  props.graph?.zoomTo(defaultScale / 100)
  props.graph?.centerContent()
}

// 处理节点点击
const handleNodeClick = (type: 'label' | 'placeholder') => {
  // 如果已经在跟随状态，直接返回，不做任何操作
  if (isFollowing.value) return

  // 创建节点
  const node = type === 'label' ? createLabelNode() : createPlaceholderNode()
  if (!node) return

  currentNode.value = node
  isFollowing.value = true

  // 添加到画布
  props.graph?.addNode(node)

  // 添加鼠标移动监听
  document.addEventListener('mousemove', handleMouseMove)
  // 添加点击监听，使用 mousedown 而不是 click
  document.addEventListener('mousedown', handleDocumentClick, true)
}

// 创建标签节点
const createLabelNode = () => {
  return props.graph?.createNode({
    shape: 'label-node',
    data: {
      type: 'label-node',
      properties: {
        fontSize: 14,
        color: '#333'
      }
    },
    width: 250,
    height: 160,
    ports: {
      groups: {},
      items: []
    }
  })
}

// 创建占位节点
const createPlaceholderNode = () => {
  return props.graph?.createNode({
    shape: 'placeholder-node',
    data: {
      type: 'placeholder-node',
      properties: {
        fontSize: 14,
        color: '#333'
      }
    },
    width: 250,
    height: 160,
    ports: {
      groups: {
        right: {
          position: 'right',
          attrs: {
            circle: {
              r: 3,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff'
            }
          }
        },
        left: {
          position: 'left',
          attrs: {
            circle: {
              r: 3,
              magnet: true,
              stroke: '#5F95FF',
              strokeWidth: 1,
              fill: '#fff'
            }
          }
        }
      },
      items: [
        { group: 'right', id: 'right' },
        { group: 'left', id: 'left' }
      ]
    }
  })
}

// 处理鼠标移动
const handleMouseMove = (e: MouseEvent) => {
  if (!isFollowing.value || !currentNode.value || !props.graph) return

  const position = props.graph.clientToLocal(e.clientX, e.clientY)
  currentNode.value.setPosition(position.x - 125, position.y - 80)
}

// 处理文档点击
const handleDocumentClick = (e: MouseEvent) => {
  // 如果点击的是工具栏按钮，不处理
  if ((e.target as HTMLElement).closest('.toolbar-btn')) {
    return
  }

  if (isFollowing.value && currentNode.value) {
    // 阻止事件冒泡，防止触发其他点击事件
    e.stopPropagation()
    e.preventDefault()

    // 放置节点
    isFollowing.value = false
    currentNode.value = null

    // 移除事件监听
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mousedown', handleDocumentClick, true)
  }
}

// 获取数据
const handleGetData = () => {
  console.log('当前图表数据:', designerStore.modGraph.toJSON({ diff: true }))
}

// 处理快捷键
const handleKeyDown = (e: KeyboardEvent) => {
  // 检测 Ctrl+K 或 Command+K
  if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'k') {
    e.preventDefault() // 阻止默认行为
    handleGetData()
  }
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

// 组件卸载时清理所有事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mousedown', handleDocumentClick, true)
  document.removeEventListener('keydown', handleKeyDown)
})

// 处理缩略图显示状态变化
const handleMinimapVisibleChange = (visible: boolean) => {
  showMinimap.value = visible
}

// 处理发布
const handlePublish = () => {
  const graphData = designerStore.modGraph.toJSON({ diff: true })
  graphData['mode'] = designerStore.mode
  if (!graphData['id']) {
    graphData['id'] = uuid()
  }

  publishModalRef.value?.open({
    currentFile: designerStore.currentFile,
    graphData: JSON.stringify(graphData)
  })
}

const saveSubProcessButtonVisible = computed(() => designerStore.selectionCells.length > 0)
const saveSubProcessModalVisible = ref(false)
const handleSaveAsSubProcess = () => {
  const nodes = designerStore.selectionCells.filter(cell => cell.isNode())
  if(nodes.length===0) {
    message.warning('至少框选一个节点！')
    return
  }else if(nodes.length===1) {
    const node = nodes[0]
    if(node.data.type === 'startNode' || node.data.type === 'endNode') {
      message.warning('不能仅保存开始节点或结束节点！')
      return
    }
  }
  saveSubProcessModalVisible.value = true
}
</script>

<style lang="scss" scoped>
.toolbar-btn {
  @apply flex items-center justify-center h-8 dark:hover:bg-[var(--input-fill-color)];

  &-active {
    @apply bg-primary text-white dark:!bg-[var(--select-color)];

    &:hover {
      @apply bg-primary text-white dark:!bg-primary-hover;
    }
  }
}

.toolbar-btn-blue {
  @apply flex items-center justify-center h-8 w-8
         relative overflow-hidden
         transition-all duration-200
         active:scale-95;

  .anticon {
    @apply relative z-10 text-primary;
  }
  &:hover .anticon {
    @apply text-primary;
  }
}

// 覆盖 ant-design-vue tooltip 样式，移除默认内边距
:deep(.ant-tooltip-inner) {
  padding: 0;
  background: transparent;
}

// Ensure minimap popover has no padding
:deep(.minimap-popover) {
  .ant-popover-inner-content {
    padding: 0;
  }

  .ant-popover-arrow {
    display: none;
  }
}

.toolbar-btn-special {
  @apply h-8 w-8 flex items-center justify-center
         relative overflow-hidden
         transition-all duration-200
         hover:before:bg-white/10
         active:scale-95;

  &::before {
    @apply content-[''] absolute inset-0
           bg-white/0 rounded-[6px]
           transition-colors duration-200;
  }

  .anticon {
    @apply relative z-10;
  }
}
</style>
