<template>
  <div class="minimap-container">
    <div ref="minimapContainer" class="minimap-content"></div>
  </div>
</template>

<script setup lang="ts">
import { Graph } from '@antv/x6'
import { MiniMap } from '@antv/x6-plugin-minimap'
import { onMounted, ref, onUnmounted } from 'vue'

const props = defineProps<{
  graph?: Graph
}>()

const minimapContainer = ref<HTMLElement>()
let minimapPlugin: MiniMap | null = null

onMounted(() => {
  if (props.graph && minimapContainer.value) {
    minimapPlugin = new MiniMap({
      container: minimapContainer.value,
      width: 200,
      height: 150,
      padding: 10,
      graphOptions: {
        async: true,
        getCellStyle: () => {
          return {
            'stroke': '#3377ff',
            'stroke-width': 1,
          }
        },
        background: {
          color: '#F2F7FA',
        },
      },
      viewport: {
        stroke: '#3377ff',
        strokeWidth: 2,
        fill: '#F2F7FA',
        fillOpacity: 0.6,
      },
    })

    props.graph.use(minimapPlugin)
  }
})

// Clean up when component is unmounted
onUnmounted(() => {
  if (props.graph && minimapPlugin) {
    props.graph.unuse(minimapPlugin)
  }
})
</script>

<style scoped>
.minimap-container {
  width: 220px;
  height: 170px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);
  padding: 10px;
  box-sizing: border-box;
}

.minimap-content {
  width: 100%;
  height: 100%;
  background-color: #F2F7FA;
  border-radius: 4px;
}
</style>
