<template>
  <div class="relative inline-block">
    <div
      v-show="nodeSelectorVisible"
      class="absolute bg-area-fill rounded-lg shadow-xl w-[440px] z-50 node-selector border border-gray-100 dark:!border dark:!border-border"
      style="top: -585px; left: 100%;"
      @click.stop
    >
      <!-- 搜索框 -->
      <div class="p-4 pr-4 border-b border-gray-100 dark:!border dark:!border-border">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索Agent资源:COT节点/工作流/数据表/知识库/MCP能力"
          class="!rounded-md search-input-custom"
          allow-clear
          size="middle"
        >
          <template #prefix>
            <search-outlined class="text-gray-400" />
          </template>
        </a-input-search>
      </div>

      <!-- Tabs 组件 -->
      <a-tabs v-model:activeKey="activeTab" class="w-full !mb-0">
        <!-- 节点 Tab -->
        <a-tab-pane key="nodes" tab="节点">
          <div
            class="h-[460px] overflow-y-auto px-4 py-2 scrollbar-thin dark:!border dark:!border-border"
          >
            <template v-if="filteredGroups.length">
              <template v-for="(group, groupKey) in filteredGroups" :key="groupKey">
                <div
                  class="text-sm font-medium text-gray-700 mb-2 mt-3 dark:!text-white"
                  >{{ group.title }}</div
                >
                <div class="grid grid-cols-2 gap-2">
                  <a-card
                    v-for="node in group.nodes"
                    :key="node.id"
                    :class="[
                      'cursor-move transition-all hover:border-primary hover:shadow-md group min-h-[40px] dark:bg-input-fill dark:!border dark:!border-border',
                      { 'opacity-50 cursor-not-allowed': node.disabled },
                      { 'hidden': node.visible ===false }
                    ]"
                    :bordered="true"
                    size="small"
                    @mousedown="startDrag($event, node)"
                  >
                    <div class="flex items-center min-h-[24px]">
                      <div
                        class="w-5 h-5 flex items-center justify-center mr-1.5 rounded bg-gray-50 group-hover:bg-primary/5 dark:!bg-area-fill dark:!text-white"
                      >
                        <svg class="icon" width="14" height="14">
                          <use :xlink:href="`${iconsUrl}#${validateType(node.type)}`"></use>
                        </svg>
                      </div>
                      <span
                        class="truncate flex-1 text-[13px] dark:!text-white text-gray-700 group-hover:text-primary"
                        >{{ node.title }}</span
                      >
                    </div>
                  </a-card>
                </div>
              </template>
            </template>
            <div v-else class="flex items-center justify-center h-full text-gray-400">
              <EyeOutlined class="text-xl mr-2" />
              <span>未找到匹配的节点</span>
            </div>
          </div>
        </a-tab-pane>

        <!-- 工作流 Tab -->
        <a-tab-pane key="workflows" tab="工作流">
          <div
            class="h-[460px] overflow-y-auto px-4 py-2 scrollbar-thin dark:!border dark:!border-border"
          >
            <template v-if="filteredWorkflows.length">
              <div class="grid grid-cols-2 gap-3">
                <a-card
                  v-for="workflow in filteredWorkflows"
                  :key="workflow.id"
                  :class="[
                      'cursor-move transition-all hover:border-primary hover:shadow-md group min-h-[40px]',
                      { 'opacity-50 cursor-not-allowed': workflow.disabled }
                    ]"
                  :bordered="true"
                  size="small"
                  @mousedown="startWorkflowDrag($event, workflow)"
                >
                  <div class="flex items-center min-h-[24px]">
                    <div
                      class="w-5 h-5 flex items-center justify-center mr-1.5 rounded bg-gray-50 group-hover:bg-primary/5 dark:!bg-area-fill dark:!text-white"
                    >
                      <apartment-outlined
                        class="text-[14px] text-blue-400 group-hover:text-primary"
                      />
                    </div>
                    <span
                      class="truncate flex-1 text-[13px] dark:!text-white text-gray-700 group-hover:text-primary"
                      >{{ workflow.name }}</span
                    >
                  </div>
                </a-card>
              </div>
            </template>
            <div v-else class="flex items-center justify-center h-full text-gray-400">
              <EyeOutlined class="text-xl mr-2" />
              <span>未找到匹配的工作流</span>
            </div>
          </div>
        </a-tab-pane>

        <!-- 特殊形状 Tab -->
        <a-tab-pane key="specialShape" tab="形状">
          <div class="h-[460px] overflow-y-auto px-4 py-2 scrollbar-thin">
            <div v-if="filteredShapeGroups.length">
              <template v-for="(group, groupKey) in filteredShapeGroups" :key="groupKey">
                <div class="text-sm font-medium text-gray-700 mb-2 mt-3">{{ group.title }}</div>
                <div class="grid grid-cols-10 gap-2">
                  <a-card
                    v-for="node in group.nodes"
                    :key="node.id"
                    :class="[
                      'cursor-move transition-all hover:border-primary hover:shadow-md group min-h-[40px]',
                      { 'opacity-50 cursor-not-allowed': node.disabled },
                      { 'hidden': node.visible ===false }
                    ]"
                    :bordered="true"
                    size="small"
                    @mousedown="startDrag($event, node)"
                  >
                    <div class="flex items-center min-h-[24px] w-full" :title="node.title">
                      <div
                        class="w-full h-5 flex items-center justify-center mr-1.5 rounded bg-gray-50 dark:!bg-primary-hover dark:!text-white group-hover:bg-primary/5"
                      >
                        <svg class="icon text-gray-700 dark:!text-white" width="14" height="14">
                          <use :xlink:href="`${shapesUrl}#${node.shapeId}`" fill="none" stroke="currentColor"></use>
                        </svg>
                      </div>
                    </div>
                  </a-card>
                </div>
              </template>
            </div>
            <div v-else class="flex items-center justify-center h-full text-gray-400">
              <EyeOutlined class="text-xl mr-2" />
              <span>未找到匹配的形状</span>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useDesignerStore } from '@/store/designer'
import { Dnd } from '@antv/x6-plugin-dnd'
import { cloneDeep } from 'lodash-es'
import { message } from 'ant-design-vue'
import { ApartmentOutlined, SearchOutlined, EyeOutlined } from '@ant-design/icons-vue'
import iconsUrl from '@/assets/icons.svg?url'
import shapesUrl from '@/assets/shapes.svg?url'
import { validateIconType } from '@/utils/utils'

const designerStore = useDesignerStore()
const nodeSelectorVisible = ref(false)
const searchKeyword = ref('')
const dndInstance = ref<Dnd>()
const nodeGroups = ref<any>({})
const activeTab = ref('nodes')

const validateType = (type) => {
  return validateIconType(type, iconsUrl)
}

// 过滤节点和分组
const filteredGroups = computed<any[]>(() => {
  if (!nodeGroups.value) return []

  const keyword = searchKeyword.value.toLowerCase()
  if (!keyword) return Object.values(nodeGroups.value)

  return Object.values(nodeGroups.value)
    .map(group => ({
      // @ts-ignore
      ...group,
      // @ts-ignore
      nodes: Object.values<any>(group.nodes)
        .filter(node =>
          node.title.toLowerCase().includes(keyword) ||
          node.description?.toLowerCase().includes(keyword)
        )
    }))
    .filter(group => group.nodes.length > 0)
})

// 过滤形状和分组
const shapeNodeGroups = ref<any>({})
const filteredShapeGroups = computed<any[]>(() => {
  if (!shapeNodeGroups.value) return []

  const keyword = searchKeyword.value.toLowerCase()
  if (!keyword) return Object.values(shapeNodeGroups.value)

  return Object.values(shapeNodeGroups.value)
    .map(group => ({
      // @ts-ignore
      ...group,
      // @ts-ignore
      nodes: Object.values<any>(group.nodes)
        .filter(node =>
          node.title.toLowerCase().includes(keyword) ||
          node.description?.toLowerCase().includes(keyword)
        )
    }))
    .filter(group => group.nodes.length > 0)
})

const treeData = inject<Ref<any[]>>('treeData',ref([]))
// 模拟工作流数据
const workflows = ref<any[]>([])
const findSubProcessContents = (nodes: any[]): any[] => {
  if (!nodes) return []
  
  for (const node of nodes) {
    if (node.label === "子流程(禁止删除)") {
      return node.contents ?? []
    }
    if (node.children) {
      const result = findSubProcessContents(node.children)
      if (result.length) return result
    }
  }
  return []
}

watch(treeData, () => {
  workflows.value = findSubProcessContents(treeData.value ?? [])
}, { immediate: true })
// 过滤工作流
const filteredWorkflows = computed(() => {
  const keyword = searchKeyword.value.toLowerCase()
  if (!keyword) return workflows.value

  return workflows.value.filter(workflow =>
    workflow.name.toLowerCase().includes(keyword) ||
    workflow.description?.toLowerCase().includes(keyword)
  )
})

// 处理工作流点击
const startWorkflowDrag = (event: MouseEvent, workflow: any) => {
  const {workflowNode} = toRaw(designerStore.nodeGroups.flow.nodes)
  const componentIndex = workflowNode.components.findIndex(item => item.field === "processId")
  workflowNode.components[componentIndex].props.value = workflow.id
  workflowNode.components[componentIndex].props.options = [{label:workflow.name,value:workflow.id}]
  workflowNode.properties = {
    processId: workflow.id
  }

  const nodeOptions = {
    shape: workflowNode.type,
    data: workflowNode,
    width: workflowNode.options?.width,
    height: workflowNode.options?.height,
    ports: workflowNode.ports
  }

  const newNode = designerStore.modGraph.createNode(nodeOptions)

  dndInstance.value?.start(newNode, event)
}

// Add handleClickOutside function
const handleClickOutside = (event: MouseEvent) => {
  const triggerButton = document.querySelector('.add-node-btn')
  if (triggerButton && triggerButton.contains(event.target as Node)) {
    return
  }

  const selectorElement = document.querySelector('.node-selector')
  if (selectorElement && !selectorElement.contains(event.target as Node)) {
    console.log('closeNodeSelector');

    closeNodeSelector()
  }
}

// Modify openNodeSelector
const openNodeSelector = () => {
  console.log('opening selector')
  // 重置搜索关键词
  searchKeyword.value = ''
  // 重置为默认 tab
  activeTab.value = 'nodes'

  // 先移除之前可能存在的事件监听器
  document.removeEventListener('click', handleClickOutside)

  nodeSelectorVisible.value = true
  setTimeout(() => {
    document.addEventListener('click', handleClickOutside)
  }, 0)
}

// Modify closeNodeSelector
const closeNodeSelector = () => {
  nodeSelectorVisible.value = false
  // 确保移除事件监听器
  document.removeEventListener('click', handleClickOutside)
}

// 初始化 DND 和节点组
const initDnd = () => {
  nextTick(() => {
    dndInstance.value = new Dnd({
      target: designerStore.modGraph,
      getDragNode: (node) => node.clone({ keepId: true }),
      getDropNode: (node) => node.clone({ keepId: true })
    })
    designerStore.setDndInstance(dndInstance.value)
  })
}

// 添加拖拽开始方法
const startDrag = (event: MouseEvent, node: any) => {
  if (node.disabled) {
    message.warning('该节点当前不可用')
    return
  }

  console.log('Original node:', node)
  const nodeData = cloneDeep(node)
  console.log('Cloned nodeData:', nodeData)

  const nodeOptions = {
    shape: nodeData.type,
    data: nodeData,
    width: nodeData.options?.width,
    height: nodeData.options?.height,
    ports: nodeData.ports,
    attrs: {
      body: {
        class: nodeData.type === 'special-shape' ? 'special-shape' : '',
      }
    }
  }
  console.log('Node options:', nodeOptions)

  const newNode = designerStore.modGraph.createNode(nodeOptions)
  console.log('Created node:', newNode)

  dndInstance.value?.start(newNode, event)
}

// 监听 store 中节点组的变化
watch(() => designerStore.nodeGroups, (newGroups) => {
  nodeGroups.value = newGroups || {}
}, { immediate: true })

// 监听store 中形状组的变化
watch(() => designerStore.shapeGroups, (newGroups) => {
  shapeNodeGroups.value = newGroups || {}
}, { immediate: true })

// 暴露方法给父组件使用
defineExpose({
  openNodeSelector,
  closeNodeSelector
})

onMounted(() => {
  initDnd()
})

onUnmounted(() => {
  // 确保组件卸载时移除事件监听器
  document.removeEventListener('click', handleClickOutside)

  // 关闭选择器，确保状态清理
  nodeSelectorVisible.value = false
})
</script>

<style lang="scss">
// 自定义滚动条样式
.scrollbar-thin {
  @apply mb-0 px-4 border-b border-gray-100;
  scrollbar-width: thin;
}

// Tabs 样式覆盖
.ant-tabs {
  .ant-tabs-nav {
    @apply mb-0 px-4 border-b border-gray-100;

    .ant-tabs-tab {
      @apply px-0 py-3 mr-6;

      &:hover .ant-tabs-tab-btn {
        @apply text-primary;
      }
    }

    .ant-tabs-tab-active .ant-tabs-tab-btn {
      @apply text-primary;
    }

    .ant-tabs-ink-bar {
      @apply bg-primary;
    }
  }
}

// 卡片样式优化
.ant-card-small {
  .ant-card-body {
    @apply p-2;
  }
}

// 搜索框自定义样式
.search-input-custom {
  width: 100%;

  .ant-input-affix-wrapper {
    height: 32px !important;
    padding: 0 11px !important;
    line-height: 32px !important;
  }

  .ant-input {
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 !important;
    font-size: 12px;
    text-align: center;
  }

  .ant-input-search-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .ant-input-prefix {
    margin-right: 8px !important;
    height: 32px !important;
    line-height: 32px !important;
    display: flex;
    align-items: center;
  }

  .ant-input-suffix {
    height: 32px !important;
    line-height: 32px !important;
    display: flex;
    align-items: center;

    .anticon {
      font-size: 12px;
    }
  }
}

// 网格布局调整
.grid-cols-2 {
  @apply gap-2;
}

.x6-node rect.special-shape,
.x6-cell rect.special-shape,
svg rect.special-shape {
  fill: none !important;
}
</style>
