<template>
  <div class="debug-panel" :class="{ 'is-minimized': isMinimized }">
    <div class="panel-header">
      <div class="left">
        <span>调试</span>
        <el-tag size="small" type="success" v-if="processDetails.length">运行完成</el-tag>
      </div>
      <div class="actions">
        <el-icon class="action-icon" @click="toggleMinimize">
          <component :is="isMinimized ? 'ArrowUp' : 'ArrowDown'" />
        </el-icon>
        <el-icon class="action-icon" @click="$emit('update:modelValue', false)">
          <Close />
        </el-icon>
      </div>
    </div>

    <div class="panel-content w-full" v-show="!isMinimized">
      <div class="flex w-full">
        <!-- 左侧节点列表 -->
        <div class="w-[280px] border-r border-[#f0f0f0] flex flex-col">
          <div class="list-content flex-1 overflow-y-auto py-2">
            <div
              v-for="node in processDetails"
              :key="node.nodeId"
              class="px-4 py-3 cursor-pointer hover:bg-[#f5f7fa] transition-colors"
              :class="{ 
                'bg-[#e6f4ff]': selectedNode?.nodeId === node.nodeId,
                'text-[#52c41a]': node.status === 'success',
                'text-[#ff4d4f]': node.status === 'error'
              }"
              @click="selectNode(node)"
            >
              <div class="flex items-center gap-2">
                <CheckCircleOutlined v-if="node.status === 'success'" />
                <WarningOutlined v-else-if="node.status === 'error'" />
                <LoadingOutlined v-else />
                <span class="flex-1 text-sm">{{ node.nodeName }}</span>
                <span class="text-xs text-[#8c8c8c]">{{ node.executionTime }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧日志区域 -->
        <div class="flex-1 w-full flex items-center justify-center">
          <template v-if="selectedNode">
            <LogViewer :logs="selectedNode.logs || []" class="h-full w-full" />
          </template>
          <div v-else class="text-xs text-[#8c8c8c]"> 请选择节点查看执行日志 </div>
        </div>
      </div>
    </div>

    <!-- 节点详情抽屉 -->
    <NodeDetail :node="selectedNode" v-model:visible="showNodeDetail" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CheckCircleOutlined, WarningOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import NodeDetail from './NodeDetail.vue'
import LogViewer from './LogViewer.vue'
import type { NodeDetail as NodeDetailType } from './types'
const emit = defineEmits(['update:modelValue'])

defineProps<{
  modelValue: boolean,
  processDetails: NodeDetailType[]
}>()

const activeTab = ref<'detail' | 'logs'>('detail')
const selectedNode = ref<NodeDetailType | null>(null)
const showNodeDetail = ref(false)
const isMinimized = ref(false)

const selectNode = (node: NodeDetailType) => {
  selectedNode.value = node
  showNodeDetail.value = true
}

const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
}
</script>

<style lang="scss" scoped>
.debug-panel {
  width: 100%;
  height: 350px;
  background: #fff;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #EBEEF5;

  &.is-minimized {
    height: 48px;
  }

  .panel-header {
    height: 48px;
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #EBEEF5;
    background: #fff;

    .left {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
    }

    .actions {
      display: flex;
      gap: 16px;

      .action-icon {
        cursor: pointer;
        font-size: 16px;
        color: #909399;

        &:hover {
          color: #409EFF;
        }
      }
    }
  }

  .panel-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    .node-list {
      width: 280px;
      border-right: 1px solid #EBEEF5;
      display: flex;
      flex-direction: column;

      .list-header {
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 500;
        border-bottom: 1px solid #EBEEF5;
      }

      .list-content {
        flex: 1;
        overflow-y: auto;
        padding: 8px 0;

        .node-item {
          padding: 12px 16px;
          cursor: pointer;

          &:hover {
            background: #F5F7FA;
          }

          &.active {
            background: #ECF5FF;
          }

          &.status-success .node-info {
            .success-icon {
              color: #67C23A;
            }
          }

          &.status-error .node-info {
            .error-icon {
              color: #F56C6C;
            }
          }

          .node-info {
            display: flex;
            align-items: center;
            gap: 8px;

            .node-name {
              flex: 1;
              font-size: 14px;
            }

            .execution-time {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }

    .detail-container {
      flex: 1;
      overflow: hidden;
      position: relative;

      .detail-header {
        height: 48px;
        padding: 0 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #EBEEF5;

        .left {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .actions {
          .action-icon {
            cursor: pointer;
            font-size: 16px;
            color: #909399;

            &:hover {
              color: #409EFF;
            }
          }
        }
      }

      .empty-detail {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .close-detail-btn {
        position: absolute;
        top: 12px;
        right: 12px;
        font-size: 16px;
        cursor: pointer;
        color: #909399;
        z-index: 1;

        &:hover {
          color: #409EFF;
        }
      }
    }
  }
}
</style>
