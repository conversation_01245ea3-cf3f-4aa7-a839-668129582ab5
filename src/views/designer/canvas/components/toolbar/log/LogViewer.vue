<template>
  <div class="border-b border-[#f0f0f0]">
    <div class="flex items-center justify-between px-4 py-4">
      <span class="text-sm font-medium">执行日志</span>
      <a-tooltip title="复制日志">
        <CopyOutlined
          class="text-[#595959] hover:text-[#1890ff] cursor-pointer transition-colors"
          @click="copyLogs"
        />
      </a-tooltip>
    </div>

    <div class="mx-4 mb-4">
      <div class="bg-[#141414] rounded p-3 max-h-[300px] overflow-y-auto font-mono text-sm">
        <template v-if="logs?.length">
          <div
            v-for="(log, index) in logs"
            :key="index"
            class="py-1"
            :class="{
              'text-[#d9d9d9]': log.level === 'info',
              'text-[#ff4d4f]': log.level === 'error',
              'text-[#faad14]': log.level === 'warning'
            }"
          >
            <span class="text-[#595959] mr-2">{{ formatTime(log.timestamp) }}</span>
            <span class="mr-2 uppercase">{{ log.level }}</span>
            <span>{{ log.message }}</span>
          </div>
        </template>
        <div v-else class="text-center py-5 text-[#595959]"> 暂无日志 </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CopyOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

interface Log {
  timestamp: string
  level: 'info' | 'error' | 'warning'
  message: string
}

const props = defineProps<{
  logs: Log[]
}>()

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

const copyLogs = () => {
  const logText = props.logs
    .map(log => `[${formatTime(log.timestamp)}] [${log.level}] ${log.message}`)
    .join('\n')
  navigator.clipboard.writeText(logText)
  message.success('日志已复制到剪贴板')
}
</script>
