<template>
  <Teleport to="body">
    <a-drawer
      v-model:open="visible"
      placement="right"
      :width="400"
      @close="emit('update:visible', false)"
      :closable="false"
    >
      <template #title>
        <div class="flex justify-between items-center">
          <span class="text-base font-medium">运行结果</span>
          <close-outlined
            class="text-gray-400 hover:text-blue-500 cursor-pointer text-base"
            @click="emit('update:visible', false)"
          />
        </div>
      </template>

      <div class="flex flex-col">
        <!-- 执行信息 -->
        <div class="p-4 border-b border-gray-200">
          <div class="flex items-center gap-2 mb-3">
            <span class="text-sm font-medium">{{ node?.nodeName }}</span>
            <a-tag color="success" v-if="node?.status === 'success'">成功</a-tag>
          </div>
          <div class="text-sm text-gray-600">
            <div>耗时: {{ node?.executionTime }}</div>
            <div class="flex items-center gap-1">
              NodeID: {{ node?.nodeId }}
              <copy-outlined class="cursor-pointer text-gray-400 hover:text-blue-500" />
            </div>
          </div>
        </div>

        <!-- 输入参数 -->
        <div class="p-4 border-b border-gray-200">
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium">输入</span>
            <copy-outlined class="cursor-pointer text-gray-400 hover:text-blue-500" />
          </div>
          <div class="bg-gray-50 rounded p-3">
            <pre
              class="m-0 font-mono text-sm break-all whitespace-pre-wrap"
              >{{ formatInputs(node?.inputs) }}</pre
            >
          </div>
        </div>

        <!-- 输出参数 -->
        <div class="p-4 border-b border-gray-200">
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium">输出</span>
            <copy-outlined class="cursor-pointer text-gray-400 hover:text-blue-500" />
          </div>
          <div class="bg-gray-50 rounded p-3">
            <pre
              class="m-0 font-mono text-sm break-all whitespace-pre-wrap"
              >{{ formatOutputs(node?.outputs) }}</pre
            >
          </div>
        </div>
      </div>
    </a-drawer>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CopyOutlined, CloseOutlined } from '@ant-design/icons-vue'
import type { NodeDetail } from './types'

const props = defineProps<{
  visible: boolean
  node: NodeDetail | null
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 创建计算属性来处理双向绑定
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 格式化输入参数
const formatInputs = (inputs: any) => {
  if (!inputs) return ''
  return JSON.stringify(inputs, null, 2)
}

// 格式化输出参数
const formatOutputs = (outputs: any) => {
  if (!outputs) return ''

  // 处理条件判断节点的特殊输出结构
  if (outputs.conditions) {
    return JSON.stringify(outputs, null, 2)
  }

  // 处理普通节点的输出数组
  if (Array.isArray(outputs)) {
    const formattedOutputs = outputs.reduce((acc: any, curr: any) => {
      acc[curr.name] = curr.value
      return acc
    }, {})
    return JSON.stringify(formattedOutputs, null, 2)
  }

  // 如果是其他情况，直接格式化
  return JSON.stringify(outputs, null, 2)
}
</script>
