<template>
  <div class="json-editor-wrapper">
    <!-- 工具栏 -->
    <div class="flex items-center justify-between px-4 py-2 bg-gray-50 border-b border-gray-200">
      <div class="text-sm text-gray-600">JSON编辑器</div>
      <div class="flex items-center space-x-2">
        <a-tooltip title="格式化">
          <a-button type="text" size="small" @click="formatJson">
            <template #icon><CodeOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="复制">
          <a-button type="text" size="small" @click="copyJson">
            <template #icon><CopyOutlined /></template>
          </a-button>
        </a-tooltip>
        <a-tooltip title="全屏">
          <a-button type="text" size="small" @click="toggleFullscreen">
            <template #icon>
              <FullscreenOutlined v-if="!isFullscreen" />
              <FullscreenExitOutlined v-else />
            </template>
          </a-button>
        </a-tooltip>
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div :class="['relative', isFullscreen ? 'h-screen' : 'h-full']">
      <a-textarea
        v-model:value="innerValue"
        :autoSize="false"
        class="w-full h-full font-mono text-sm p-3 border-0 resize-none focus:outline-none focus:shadow-none"
        style="height: calc(100% - 1px)"
        @input="handleChange"
      />

      <!-- 错误提示 -->
      <div
        v-if="error"
        class="absolute bottom-0 left-0 right-0 p-2 bg-red-50 text-red-500 text-sm border-t border-red-200"
      >
        {{ error }}
      </div>
    </div>

    <!-- 全屏模式遮罩 -->
    <div v-if="isFullscreen" class="fixed inset-0 bg-white z-50">
      <div class="h-full flex flex-col">
        <div
          class="flex items-center justify-between px-4 py-3 bg-gray-50 border-b border-gray-200"
        >
          <div class="text-base font-medium text-gray-700">JSON编辑器</div>
          <a-button type="text" @click="toggleFullscreen">
            <template #icon><FullscreenExitOutlined /></template>
          </a-button>
        </div>
        <div class="flex-1 overflow-hidden">
          <a-textarea
            v-model:value="innerValue"
            :autoSize="false"
            class="w-full h-full font-mono text-sm p-3 border-0 resize-none focus:outline-none focus:shadow-none"
            style="height: 100%"
            @input="handleChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  CodeOutlined,
  CopyOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons-vue'

const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits(['update:modelValue', 'change', 'error'])

const innerValue = ref(props.modelValue)
const isFullscreen = ref(false)
const error = ref('')

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== innerValue.value) {
    innerValue.value = newVal
  }
})

// 处理值变化
const handleChange = () => {
  emit('update:modelValue', innerValue.value)
  emit('change', innerValue.value)
  validateJson()
}

// 验证JSON
const validateJson = () => {
  try {
    if (innerValue.value.trim()) {
      JSON.parse(innerValue.value)
      error.value = ''
    }
  } catch (err) {
    error.value = '无效的JSON格式'
    emit('error', err)
  }
}

// 格式化JSON
const formatJson = () => {
  try {
    const parsed = JSON.parse(innerValue.value)
    innerValue.value = JSON.stringify(parsed, null, 2)
    error.value = ''
    handleChange()
    message.success('格式化成功')
  } catch (err) {
    error.value = '无效的JSON格式'
  }
}

// 复制JSON
const copyJson = async () => {
  try {
    await navigator.clipboard.writeText(innerValue.value)
    message.success('复制成功')
  } catch (err) {
    message.error('复制失败')
  }
}

// 切换全屏
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}
</script>

<style scoped>
.json-editor-wrapper {
  @apply relative bg-white rounded-md shadow-sm overflow-hidden;
  height: 100%;
}
</style>
