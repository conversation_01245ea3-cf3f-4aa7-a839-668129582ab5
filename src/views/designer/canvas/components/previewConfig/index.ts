// 使用import.meta.glob同步导入所有组件
const modules = import.meta.glob('./**/index.vue', { eager: true })

import SubProcess from './subProcess/index.vue'

// 创建自动更新的组件映射表
const componentMap: Record<string, Component> = {
  subprocess: SubProcess
}

// 遍历所有模块，提取组件类型并添加到映射表中
Object.keys(modules).forEach(path => {
  // 从路径中提取组件类型，例如 './conditional/index.vue' -> 'conditional'
  const type = path.split('/')[1]
  if (type) {
    // 将组件添加到映射表中
    componentMap[type] = (modules[path] as any).default
  }
})

/**
 * 获取预览组件
 * @param type 组件类型
 * @returns 组件
 */
export const getPreviewComponent = (type: string) => {
  // 直接从映射表中返回组件
  return componentMap[type] || null
}
