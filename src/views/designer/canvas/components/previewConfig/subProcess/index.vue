<template>
  <div class="subprocess-container">
    <div ref="container" class="subprocess-graph"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { Graph } from '@antv/x6'
import { useDesignerStore } from '@/store/designer'
import { getFileApi } from '@/apis/designer'

defineOptions({
  name: 'PreviewConfigSubProcess'
})

interface Props {
  nodeConfig: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  nodeConfig: () => ({})
})

// 获取设计器状态
const designerStore = useDesignerStore()
const container = ref<HTMLElement | null>(null)

const initGraph = () => {
  if (!container.value) return

  // 创建子流程画布
  const graph = new Graph({
    container: container.value,
    width: container.value.clientWidth || 800,
    height: 350,
    background: {
      color: '#F8F9FA'
    },
    grid: {
      visible: true,
      type: 'doubleMesh',
      args: [
        {
          color: '#E7E8EA',
          thickness: 1,
        },
        {
          color: '#E7E8EA',
          thickness: 1,
          factor: 4,
        },
      ],
    },
    interacting: false,
    autoResize: true,
  })

  // 使用nextTick等待画布初始化完成
  nextTick(async () => {
    await startWorkflowDrag(designerStore.currentNodeData, graph)
    
    // 确保节点在视图中居中显示
    graph.centerContent()
    
    // 调整画布缩放以适应内容
    graph.zoomToFit({ padding: 20 })
  })

  // 监听容器大小变化
  const resizeObserver = new ResizeObserver(() => {
    if (container.value) {
      graph.resize(container.value.clientWidth, 350)
      graph.centerContent()
      graph.zoomToFit({ padding: 20 })
    }
  })

  if (container.value) {
    resizeObserver.observe(container.value)
  }
}

const findSubProcessContents = (nodes: any[]): any[] => {
  if (!nodes) return []
  
  for (const node of nodes) {
    if (node.label === "子流程(禁止删除)") {
      return node.contents ?? []
    }
    if (node.children) {
      const result = findSubProcessContents(node.children)
      if (result.length) return result
    }
  }
  return []
}

// 处理工作流点击
const startWorkflowDrag = async (workflow: any, graph: any) => {
  const workflowId = workflow?.properties?.processId;
  const res = await getFileApi({id:workflowId})
  if (res.code == 200) {
    // 解析工作流内容
  const workflowContent = JSON.parse(res?.data?.content)
  const cells = workflowContent.cells || []

  // 准备所有节点
  const nodeData = cells
    .filter(cell => cell.shape && cell.shape !== 'data-processing-curve')
    .map(cell => {
      const position = {
        x: cell.position?.x || 0,
        y: cell.position?.y || 0
      }
      return {
        id: cell.id,
        shape: cell.shape,
        position,
        size: cell.size || { width: 358, height: 142.6 },
        attrs: {
          body: {
            fill: '#fff',
            stroke: 'none'
          }
        },
        data: cell.data || {},
        ports: {
          groups: {
            left: {
              position: {
                name: 'left',
                args: {
                  dx: -4
                }
              },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  fill: '#fff'
                }
              }
            },
            right: {
              position: {
                name: 'right',
                args: {
                  dx: 4
                }
              },
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  fill: '#fff'
                }
              }
            },
            top: {
              position: 'top',
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  fill: '#fff',
                  visibility: 'visible'
                }
              }
            },
            bottom: {
              position: 'bottom',
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  fill: '#fff',
                  visibility: 'visible'
                }
              }
            }
          },
          items: cell.data?.ports || [
            {
              id: '1',
              group: 'left'
            },
            {
              id: '2',
              group: 'right'
            }
          ]
        },
        zIndex: cell.zIndex || 0
      }
    })
  // 一次性添加所有节点
  graph.addNodes(nodeData)

  // 创建节点映射
  const nodeMap = new Map()
  nodeData.forEach(node => {
    const addedNode = graph.getCellById(node.id)
    if (addedNode) {
      nodeMap.set(node.id, addedNode)
    }
  })

  // 创建所有边
  cells.forEach(cell => {
    if (cell.shape === 'data-processing-curve') { // 边
      const sourceNode = nodeMap.get(cell.source?.cell)
      const targetNode = nodeMap.get(cell.target?.cell)
      
      if (sourceNode && targetNode) {
        // 创建边
        const edgeOptions = {
          id: cell.id,
          shape: 'data-processing-curve', // 使用原始形状
          source: {
            cell: sourceNode.id,
            port: cell.source?.port || '1'
          },
          target: {
            cell: targetNode.id,
            port: cell.target?.port || '1'
          },
          attrs: {
            line: {
              targetMarker: {
                width: 12,
                height: 8
              },
              strokeDasharray: ''
            }
          },
          zIndex: cell.zIndex || -1,
          data: cell.data || { showButton: false }
        }
        try {
          const edge = graph.createEdge(edgeOptions)
          graph.addEdge(edge)
          // 立即将边移到最上层
          edge.toFront()
        } catch (error) {
          console.error('创建边失败:', error)
        }
      }
    }
  })

  // 确保所有节点和边都可见
  nodeMap.forEach(node => {
    node.toFront()
  })
  // 确保所有边都在最上层
  graph.getEdges().forEach(edge => {
    edge.toFront()
  })

  // 重新调整视图以确保所有内容可见
  nextTick(() => {
    graph.centerContent()
    graph.zoomToFit({ padding: 20 })
  })
  }
}

// 生命周期钩子
onMounted(() => {
  nextTick(() => initGraph())
})
</script>

<style scoped>
.subprocess-container {
  width: 100%;
  height: 350px;
  border: 1px solid #E7E8EA;
  border-radius: 4px;
  overflow: hidden;
  margin: 8px 0;
}

.subprocess-graph {
  width: 100%;
  height: 100%;
}
</style>
