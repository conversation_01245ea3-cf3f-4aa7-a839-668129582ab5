<template>
  <div class="for-loop-container p-4">
    <!-- 循环配置显示区域 -->
    <div class="mb-4 grid gap-[8px] grid-cols-[auto_minmax(0,1fr)]">
      <!-- 初始化表达式 -->
      <div class="self-center text-right text-[12px] font-medium leading-[16px] mr-0.5 whitespace-nowrap text-gray-700 dark:text-[var(--grey-auxiliary-color)]">初始化表达式</div>
      <div class="text-[12px] relative">
        <span class="overflow-hidden truncate whitespace-nowrap select-none leading-[16px] px-[4px] py-[1px] text-[12px] bg-[#EFF0F8] dark:bg-input-fill dark:text-white rounded-[4px]">
          {{ nodeData?.properties?.initialization || '未定义' }}
        </span>
      </div>
      
      <!-- 条件表达式 -->
      <div class="self-center text-right text-[12px] font-medium leading-[16px] mr-0.5 whitespace-nowrap text-gray-700 dark:text-[var(--grey-auxiliary-color)]">条件表达式</div>
      <div class="text-[12px] relative">
        <span class="overflow-hidden truncate whitespace-nowrap select-none leading-[16px] px-[4px] py-[1px] text-[12px] bg-[#EFF0F8] dark:bg-input-fill dark:text-white rounded-[4px]">
          {{ nodeData?.properties?.condition || '未定义' }}
        </span>
      </div>
      
      <!-- 更新表达式 -->
      <div class="self-center text-right text-[12px] font-medium leading-[16px] mr-0.5 whitespace-nowrap text-gray-700 dark:text-[var(--grey-auxiliary-color)]">更新表达式</div>
      <div class="text-[12px] relative">
        <span class="overflow-hidden truncate whitespace-nowrap select-none leading-[16px] px-[4px] py-[1px] text-[12px] bg-[#EFF0F8] dark:bg-input-fill dark:text-white rounded-[4px]">
          {{ nodeData?.properties?.update || '未定义' }}
        </span>
      </div>
    </div>
    
    <!-- 循环变量说明 -->
    <!-- <div class="loop-variables px-3 py-2 rounded bg-blue-50 dark:bg-blue-900 mb-3">
      <div class="text-xs font-medium mb-2 text-blue-700 dark:text-blue-300">循环过程中可用变量:</div>
      <div class="grid grid-cols-2 gap-2 text-xs">
        <div class="flex items-center">
          <code class="mr-1 bg-blue-50 dark:bg-blue-900 px-1.5 py-0.5 rounded text-blue-700 dark:text-blue-300">loop.index</code>
          <span class="text-gray-600 dark:text-gray-400">当前索引</span>
        </div>
        <div class="flex items-center">
          <code class="mr-1 bg-blue-50 dark:bg-blue-900 px-1.5 py-0.5 rounded text-blue-700 dark:text-blue-300">loop.item</code>
          <span class="text-gray-600 dark:text-gray-400">当前元素</span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

defineOptions({
  name: 'PreviewConfigForLoop'
})

interface Props {
  nodeConfig: Record<string, any>
}

const props = defineProps<Props>()

// 获取节点数据
const nodeData = computed(() => props.nodeConfig || {})

// 格式化数组显示
const formatArrayDisplay = (array: any): string => {
  if (!array) return '[]'
  
  if (typeof array === 'string') {
    // 处理引用型数组表达式
    if (array.includes('${')) {
      return array
    }
    // 尝试解析JSON
    try {
      const parsed = JSON.parse(array)
      if (Array.isArray(parsed)) {
        if (parsed.length === 0) return '[]'
        if (parsed.length <= 3) return JSON.stringify(parsed)
        return `[${parsed.slice(0, 3).join(', ')}... 共${parsed.length}项]`
      }
      return array
    } catch (e) {
      return array
    }
  }
  
  if (Array.isArray(array)) {
    if (array.length === 0) return '[]'
    if (array.length <= 3) return JSON.stringify(array)
    return `[${array.slice(0, 3).join(', ')}... 共${array.length}项]`
  }
  
  return String(array)
}
</script>

<style scoped>
.for-loop-container {
  min-height: 180px;
}

code {
  font-family: monospace;
  font-size: 0.8rem;
}
</style> 