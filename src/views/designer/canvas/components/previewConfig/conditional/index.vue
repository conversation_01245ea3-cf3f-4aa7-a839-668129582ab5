<template>
  <div class="selector-container" ref="selectorContainerRef">
    <div class="flex gap-3 mb-3" v-for="(branch, index) in branches" :key="index">
      <div class="shrink-0 w-16 flex items-center justify-end text-gray-500 dark:!text-[var(--text-secondary)]">
        {{ index === 0 ? '如果' : index === branches.length - 1 ? '否则' : '否则如果'        }}
      </div>
      <div
        class="flex-1 border border-dashed border-gray-300 rounded-md p-2"
        :style="{display: index === branches.length - 1 ? 'none':undefined}"
      >
        <template v-for="(condition, i) in branch.conditions" :key="i">
          <Divider v-if="i>0" style="margin: 8px 0;font-size: 12px;">
            {{logicOperatorsMap[condition.logic]}}
          </Divider>
          <div class="flex gap-2 items-center">
            <div class="flex-1 bg-gray-100 rounded-sm p-1 dark:!bg-input-fill dark:!text-white">{{ condition.leftValue }}</div>
            <div class="dark:!text-[var(--text-secondary)]">{{ condition.operator }}</div>
            <div class="flex-1 bg-gray-100 rounded-sm p-1 dark:!bg-input-fill dark:!text-white">{{ condition.rightValue }}</div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Divider } from 'ant-design-vue'
import { LOGIC_OPERATORS } from '@/views/designer/canvas/components/configRender/conditional/constant'
import { type Node } from '@antv/x6'
import { useDesignerStore } from '@/store/designer'

const logicOperatorsMap = Object.fromEntries(LOGIC_OPERATORS.map(item => [item.value.toUpperCase(), item.label]))

const designerStore = useDesignerStore()
const props = defineProps<{ nodeConfig: any }>()
const nodeData = computed(() =>{
  return designerStore.modGraph.getCellById(props.nodeConfig.id)?.getData() ?? props.nodeConfig
})
const getNode = inject('getNode') as Function
const node = computed(() => getNode?.() as Node)
const branches = computed(() =>nodeData.value?.properties?.branches ?? [])
const selectorContainerRef = ref<HTMLDivElement>()
// 计算新的高度 - 根据分支数量动态计算
const updateNodeHeight = () => {
  const newHeight = 44 + selectorContainerRef.value!.clientHeight
  const {width, height} = node.value!.size()
  if(height !== newHeight){
    node.value?.resize(width, newHeight)
  }
}
const resizeObserver = ref<ResizeObserver>()
onMounted(() => {
  updateNodeHeight()
  resizeObserver.value = new ResizeObserver(() => updateNodeHeight())
  resizeObserver.value.observe(selectorContainerRef.value!)
})
onBeforeUnmount(() => {
  resizeObserver.value?.disconnect()
})
</script>

<style scoped>
.selector-container {
    padding: 8px 16px;
    font-size: 12px;
}
</style>
