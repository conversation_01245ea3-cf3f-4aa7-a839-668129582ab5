<script setup lang="ts">
    import { onMounted } from 'vue'
    import { Graph } from '@antv/x6'
    import { useDesignerStore } from '@/store/designer'

    defineOptions({
        name: 'PreviewConfigLoop'
    })

    // 获取设计器状态
    const designerStore = useDesignerStore()

    const initStartNode = () => {
        const modGraph = designerStore.modGraph as Graph
        const loopControlNodes = modGraph.getNodes().filter(item => item.getData().type === "loopControlNode")!
        if (!loopControlNodes.length) return

        const loopControlNode = loopControlNodes.find(item => !item.getChildren()?.length)!
        if (!loopControlNode) return
        loopControlNode.setSize(800, 400)
    }
    // 生命周期钩子
    onMounted(() => {
        nextTick(() => initStartNode())
    })

</script>
