<template>
  <div
    class="label-node bg-yellow-50 rounded-md p-2 h-[160px]"
    :class="{ 
      'label-node-selected': isSelected || isEditing,
      'cursor-move': !isEditing,
      'cursor-text': isEditing 
    }"
    @click.stop="handleClick"
    @dblclick.stop="startEdit"
    @keydown.backspace.stop="handleDelete"
    tabindex="0"
  >
    <div
      v-if="!isEditing"
      class="text-gray-600 text-sm whitespace-pre-wrap break-all min-w-[250px] w-full"
    >
      {{ node.getData().properties.text || '输入要添加的注释' }}
    </div>
    <textarea
      v-else
      ref="textareaRef"
      v-model="editText"
      class="text-sm bg-transparent w-full h-full resize-none border-none focus:outline-none text-gray-600 whitespace-pre-wrap break-all"
      :rows="6"
      placeholder="输入要添加的注释"
      @blur="handleBlur"
      @keydown.enter.stop
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Node } from '@antv/x6'

const props = defineProps<{
  node: Node
}>()

const isEditing = ref(false)
const isSelected = ref(false)
const editText = ref('')
const textareaRef = ref<HTMLTextAreaElement | null>(null)

const handleDelete = (event: KeyboardEvent) => {
  if (isEditing.value) return

  if (isSelected.value) {
    props.node.remove()
  }
}

const handleClick = (event: MouseEvent) => {
  event.stopPropagation()
  isSelected.value = true

  // 获取当前元素的引用
  const currentElement = event.currentTarget as HTMLElement
  currentElement.focus()

  // 创建一个唯一标识，用于跟踪这个特定的点击事件
  const clickId = Symbol('click-id')

  // 文档点击处理函数
  const handleDocumentClick = (e: MouseEvent) => {
    // 安全检查：确保目标元素存在且有效
    try {
      // 如果点击的是元素外部
      if (currentElement && e.target && !currentElement.contains(e.target as Node)) {
        isSelected.value = false
        document.removeEventListener('click', handleDocumentClick)
      }
    } catch (error) {
      // 如果出现任何错误，清理监听器并重置状态
      isSelected.value = false
      document.removeEventListener('click', handleDocumentClick)
    }
  }

  // 使用 requestAnimationFrame 确保在下一帧渲染前添加监听器
  // 这样可以避免当前事件冒泡时触发我们的监听器
  requestAnimationFrame(() => {
    // 再次检查组件是否仍然挂载
    if (document.body.contains(currentElement)) {
      document.addEventListener('click', handleDocumentClick, { once: true })
    }
  })
}

const startEdit = async (event: MouseEvent) => {
  if (isEditing.value) return
  event.stopPropagation()
  isEditing.value = true
  isSelected.value = false
  editText.value = props.node.getData().properties.text || ''

  await nextTick()
  textareaRef.value?.focus()
}

const handleBlur = (event: FocusEvent) => {
  const clickedElement = document.activeElement
  if (event.currentTarget.contains(clickedElement)) {
    return
  }
  finishEdit()
}

const finishEdit = () => {
  if (!isEditing.value) return

  const nodeData = props.node.getData()
  nodeData.properties.text = editText.value
  props.node.setData(nodeData)

  isEditing.value = false
}
</script>

<style lang="scss" scoped>
.label-node {
  border: 1px solid rgb(242, 182, 0);
  outline: none;

  textarea, div {
    word-break: break-all;
    white-space: pre-wrap;
    line-height: 1.5;
  }

  &:hover {
    cursor: move;
  }

  &-selected {
    background-color: #fff6f0 !important;
    border: 1px solid rgb(255, 129, 26) !important;
  }
}
</style>
