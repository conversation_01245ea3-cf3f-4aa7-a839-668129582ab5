<template>
  <div
    class="placeholder-node dark:!bg-area-fill dark:!text-white dark:!border-border dark:!border rounded-md p-4 shadow-md relative"
    :class="{
      'placeholder-node-selected': isSelected,
      'cursor-move': !isEditing,
      'cursor-text': isEditing
    }"
    @click.stop="handleClick"
    @dblclick.stop="startEdit"
    @mousedown="handleDragStart"
    @mouseup="handleDragEnd"
    @mouseenter="showReplaceIcon = true"
    @mouseleave="showReplaceIcon = false"
    tabindex="0"
  >
    <!-- 替换图标按钮 -->
    <div
      v-show="showReplaceIcon && !isEditing"
      class="replace-icon-btn absolute -top-2 -right-2 w-6 h-6 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center cursor-pointer shadow-lg transition-all duration-200 z-10"
      @click.stop="handleReplaceClick"
      title="替换节点"
    >
      <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
      </svg>
    </div>

    <!-- 编辑区域 -->
    <div
      v-show="!isEditing"
      class="text-gray-600 dark:!text-white text-sm whitespace-pre-wrap break-all w-full h-full overflow-auto"
    >
      {{ node.getData().properties.description || '请输入占位说明' }}
    </div>
    <textarea
      v-show="isEditing"
      ref="textareaRef"
      v-model="editText"
      class="text-sm bg-transparent w-full h-full resize-none border-none focus:outline-none text-gray-600 dark:!text-white whitespace-pre-wrap break-all"
      :rows="4"
      placeholder="请输入占位说明"
      @blur="handleBlur"
      @keydown.enter.stop
      @click.stop="textAreaClick"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Node } from '@antv/x6'
import { useDesignerStore } from '@/store/designer'

const props = defineProps<{
  node: Node
}>()

const designerStore = useDesignerStore()
const isEditing = ref(false)
const isSelected = ref(false)
const editText = ref('')
const textareaRef = ref<HTMLTextAreaElement | null>(null)
const isDragging = ref(false)
const mouseStartPos = ref({ x: 0, y: 0 })
const showReplaceIcon = ref(false)

const textAreaClick = async (event: MouseEvent) => {
  startEdit(event)
  designerStore.setRightPanelVisiable(false)
}

const handleReplaceClick = (event: MouseEvent) => {
  event.stopPropagation()
  // 显示生成节点面板
  designerStore.setGenerateNodePanelVisiable(true)
  // 阻止右侧面板显示
  designerStore.setRightPanelVisiable(false)
  // 设置选中状态
  isSelected.value = true
}

const handleClick = (event: MouseEvent) => {
  event.stopPropagation()
  isSelected.value = true

  // 阻止右侧面板显示 - Prevent right panel from showing
  designerStore.setRightPanelVisiable(false)

  const currentElement = event.currentTarget as HTMLElement
  currentElement?.focus()

  const handleDocumentClick = (e: MouseEvent) => {
    try {
      if (currentElement && e.target && !currentElement.contains(e.target as HTMLElement)) {
        isSelected.value = false
        document.removeEventListener('click', handleDocumentClick)
      }
    } catch (error) {
      isSelected.value = false
      document.removeEventListener('click', handleDocumentClick)
    }
  }

  requestAnimationFrame(() => {
    if (document.body.contains(currentElement)) {
      document.addEventListener('click', handleDocumentClick, { once: true })
    }
  })

  event.preventDefault()
}

const startEdit = async (event: MouseEvent) => {
  if (isEditing.value) return
  event.stopPropagation()
  isEditing.value = true
  isSelected.value = false
  editText.value = props.node.getData().properties.description || ''

  await nextTick()
  textareaRef.value?.focus()
}

const handleBlur = (event: FocusEvent) => {
  const clickedElement = document.activeElement
  if ((event.currentTarget as HTMLElement)?.contains(clickedElement)) {
    return
  }
  finishEdit()
}

const finishEdit = () => {
  if (!isEditing.value) return

  const nodeData = props.node.getData()
  if (!nodeData.properties) {
    nodeData.properties = {}
  }
  nodeData.properties.description = editText.value
  props.node.setData(nodeData)

  isEditing.value = false
}

const handleDragStart = (event: MouseEvent) => {
  isDragging.value = true
  mouseStartPos.value = {
    x: event.clientX,
    y: event.clientY
  }
}

const handleDragEnd = () => {
  isDragging.value = false
}
</script>

<style lang="scss" scoped>
.placeholder-node {
  outline: none;
  height: 100%;
  background-color: rgb(242, 242, 255);

  textarea, div {
    word-break: break-all;
    white-space: pre-wrap;
    line-height: 1.5;
  }

  textarea {
    &::placeholder {
      color: #999;
    }
  }

  &:hover {
    cursor: move;
  }
&.cursor-text {
  background-color: #fff;
}
  &-selected {
    border: 1px solid #60a5fa !important;
  }

  :global(.dark) &-selected {
    border-color: var(--border-color) !important;
  }
}
</style>
