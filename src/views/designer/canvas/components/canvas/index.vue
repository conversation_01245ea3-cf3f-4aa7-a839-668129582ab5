<template>
  <div
    class="data-model-mapping-container overflow-hidden"
    :style="{ width: width, height: height }"
  >
    <!-- 右上方模式切换按钮 -->
    <div class="absolute top-4 right-4 z-20">
      <a-radio-group
        v-model:value="currentMode"
        @change="handleModeChange"
        size="middle"
        button-style="solid"
      >
        <a-radio-button value="all">
          <DeploymentUnitOutlined style="font-size: 15px;" />
        </a-radio-button>
        <a-radio-button value="json">
          <EditOutlined style="font-size: 15px;" />
        </a-radio-button>
        <a-radio-button value="qrcode">
          <QrcodeOutlined style="font-size: 15px;" />
        </a-radio-button>
      </a-radio-group>
    </div>

    <!-- 视图模式 - 画布 -->
    <div
      id="mod-canvas"
      ref="modCanvasRef"
      class="mod-canvas bg-[var(--canvas-fill-color)]"
      v-show="currentMode === 'all'"
    >
      <!-- 试运行抽屉组件 -->
    </div>

    <!-- JSON模式 - 编辑器 - 与画布完全重合 -->
    <div v-show="currentMode === 'json'" class="w-full h-full absolute inset-0">
      <a-textarea
        v-model:value="jsonContent"
        class="w-full h-full font-mono text-sm p-3 border-0 resize-none bg-area-fill text-custom"
        :autoSize="false"
        @change="onJsonChange"
        placeholder="JSON内容"
      />
      <div
        v-if="jsonError"
        class="absolute bottom-16 left-0 right-0 p-2 bg-red-50 text-red-500 text-sm"
      >
        {{ jsonError }}
      </div>
    </div>

    <!-- 二维码模式 - 显示二维码 -->
    <div v-if="currentMode === 'qrcode'" class="w-full h-full absolute inset-0 flex items-center justify-center">
      <div class="flex flex-col items-center">
        <!-- 生成状态信息 -->
        <div class="text-center mb-4">
          <div class="text-sm text-gray-600">{{ transferMessage }}</div>
          <div v-if="isGeneratingQR && totalChunks > 0" class="text-sm text-blue-600 mt-1">
            生成进度: {{ currentChunk }}/{{ totalChunks }}
          </div>
          <div class="text-xs text-gray-500 mt-2">
            提示：二维码会循环显示所有数据块，请耐心等待扫描
          </div>
        </div>
        
        <!-- 二维码容器 -->
        <div ref="qrCodeContainer" class="bg-white p-4 rounded-lg shadow-lg">
          <div id="qrcode" class="w-[500px] h-[500px]"></div>
        </div>
      </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="absolute bottom-[15px] left-1/2 -translate-x-1/2 z-10">
      <!-- 视图模式工具栏 -->
      <Toolbar
        v-if="currentMode === 'all'"
        :graph="graph"
        :tool-panel-ref="toolPanelRef"
        @debug="showDebugPanel = true"
        @toggleAIChat="showAIChat = !showAIChat"
        @toggleFullscreen="handleFullscreen"
      />

      <!-- JSON模式工具栏 - 只有格式化和发布按钮 -->
      <div v-else-if="currentMode === 'json'" class="flex items-center gap-2">
        <!-- 格式化按钮 (与保存按钮样式一致) -->
        <a-button
          type="primary"
          class="shadow-md h-[40px] w-[40px] flex items-center justify-center"
          @click="formatJson"
        >
          <FormatPainterOutlined style="font-size: 16px;" />
        </a-button>

        <!-- 保存按钮 -->
        <a-button
          type="primary"
          class="shadow-md h-[40px] w-[40px] flex items-center justify-center"
          @click="()=>saveContent(false)"
        >
          <SaveOutlined style="font-size: 16px;" />
        </a-button>

        <!-- 发布按钮 -->
        <a-button
          v-if="mode !== 'readonly'"
          type="primary"
          class="shadow-md h-[40px] w-[40px] flex items-center justify-center"
          @click="publishContent"
        >
          <CloudUploadOutlined style="font-size: 16px;" />
        </a-button>
      </div>

      <!-- 二维码模式工具栏 - 只有开始/停止生成按钮 -->
      <div v-else-if="currentMode === 'qrcode'" class="flex items-center gap-2">
        <a-button
          type="primary"
          class="shadow-md h-[40px] px-4 flex items-center justify-center mt-2"
          @click="(isGeneratingQR || isLooping) ? stopQRGeneration() : startQRGeneration()"
          :loading="isGeneratingQR"
        >
          {{ (isGeneratingQR || isLooping) ? '停止生成' : '开始生成' }}
        </a-button>
      </div>
    </div>

    <!-- 调试面板 -->
    <div v-if="showDebugPanel" class="debug-panel-container">
      <DebugPanel v-model="showDebugPanel" :process-details="debugDetails" />
    </div>

    <!-- AI 聊天容器 -->
    <div v-if="showAIChat" class="ai-chat-container">
      <AIChatContainer
        :key="chatKey"
        :sseUrl="PREFIX_SSE + `?tabId=${TAB_ID}&aip-session=${AI_TOKEN}`"
        :sendSSEMessage="handleSendSSEMessage"
        class="dark:!bg-area-fill rounded-lg"
        style="height: 100%;"
        @closeChat="showAIChat = false"
        :menuList="[]"
        :selectInfo="{
          name: '',
          data: []
        }"
      />
    </div>

    <!-- 添加 NodeConfig 到画布容器内 -->
    <NodeConfig
      :visible="designerStore.rightPanelVisiable"
      :title="designerStore.currentNodeData?.title || ''"
      :description="designerStore.currentNodeData?.description"
      :components="designerStore.currentNodeData?.components || []"
      @update:visible="handleConfigVisibleChange"
      class="absolute right-0 top-0 h-full"
    />

    <DebugDrawer
      v-model="tryRunStore.visible"
      :is-direct-debug="tryRunStore.isDirectDebug"
      @debug="handleDebug"
    />

    <SearchSelectNode
      v-model:visible="showNodeSearch"
      :position="searchPosition"
    ></SearchSelectNode>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, computed } from 'vue'
import { Graph } from '@antv/x6'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Selection } from '@antv/x6-plugin-selection'
import { Transform } from '@antv/x6-plugin-transform'
import { v4 as uuidv4 } from 'uuid'
import { message } from 'ant-design-vue'
import { PREFIX_SSE, getSse } from '@/apis/designer'
import { useDesignerStore } from '@/store/designer'
import { useTryRunStore } from '@/store/tryRun'
import { useAppStore } from '@/store/app'
import bus from '@/utils/bus'
import SearchSelectNode from "../toolbar/searchSelectNode/index.vue"
import DebugDrawer from '../toolbar/debug/index.vue'
import Toolbar from '../toolbar/index.vue'
import DebugPanel from '../toolbar/log/DebugPanel.vue'
import NodeConfig from '../../components/node/NodeConfig/index.vue'
import { AIChatContainer } from '@yss/ai-chat-components'
import {
  EditOutlined,
  SaveOutlined,
  CloudUploadOutlined,
  FormatPainterOutlined,
  DeploymentUnitOutlined,
  QrcodeOutlined
} from '@ant-design/icons-vue'
import QRCode from 'qrcode'
import pako from 'pako'

// Props
const props = defineProps({
  width: { type: String, default: '100%' },
  height: { type: String, default: '100%' },
  refreshData: { type: Function, default: () => {} },
  treeData: { type: Array, default: () => [] },
  toolPanelRef: { type: Object, default: null }
})

// Emits
const emit = defineEmits(['debug', 'toggleAIChat', 'toggleFullscreen'])

// Refs
const modCanvasRef = ref(null)
const graph = ref(null)
const showDebugPanel = ref(false)
const debugDetails = ref([])
const showNodeSearch = ref(false)
const searchPosition = ref({ x: 0, y: 0 })
const showAIChat = ref(false)
const chatKey = ref(0)
const TAB_ID = ref(uuidv4())
const AI_TOKEN = ref(uuidv4())

// Store
const designerStore = useDesignerStore()
const tryRunStore = useTryRunStore()
const appStore = useAppStore()

// 获取mode状态
const mode = computed(() => appStore.mode)

// JSON相关状态
const currentMode = ref('all')
const jsonContent = ref('')
const jsonError = ref('')
const isUpdatingJson = ref(false)

// 二维码相关状态
const isGeneratingQR = ref(false)
const isLooping = ref(false)
const qrCodeUrl = ref('')
const qrCodeContainer = ref(null)
const qrCodeObject = ref<any>(null)
const transferMessage = ref('')
const chunkSize = 250
const currentChunk = ref(0)
const totalChunks = ref(0)
const transferInterval = ref<number | null>(null)

// 监听模式变化
watch(currentMode, (newMode) => {
  designerStore.setMode(newMode)
})

// 监听jsonViewContent的变化
watch(() => designerStore.jsonViewContent, (newContent) => {
  if (currentMode.value === 'json' && newContent) {
    isUpdatingJson.value = true
    jsonContent.value = newContent
    jsonError.value = ''
    isUpdatingJson.value = false
  }
})

// 处理模式切换
const handleModeChange = (e) => {
  const newMode = e.target.value
  const oldMode = currentMode.value

  if (newMode === 'json') {
    // 切换到JSON模式时，获取最新画布内容
    updateJsonFromGraph()
  } else if (newMode === 'qrcode') {
    // 切换到二维码模式时，停止生成并初始化二维码
    stopQRGeneration()
    nextTick(async () => {
      await initQRCode()
    })
  } else if (oldMode === 'json' && !jsonError.value) {
    // 从JSON模式切换到视图模式时，自动应用更改并保存
    applyJsonChanges()
    // 静默保存 - 不显示消息提示
    saveContent(true)
  }
}

// 更新二维码内容
const updateQRCode = async (text: string) => {
  const qrcodeElement = document.getElementById('qrcode')
  if (qrcodeElement) {
    try {
      const qrDataURL = await QRCode.toDataURL(text, {
        width: 600, 
        height: 600,
        margin: 4, 
        color: {
          dark: '#000000', 
          light: '#FFFFFF' 
        },
        errorCorrectionLevel: 'H',
        type: 'image/png',
        quality: 1.0
      })
      qrcodeElement.innerHTML = `<img src="${qrDataURL}" alt="QR Code" style="width: 500px; height: 500px; image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges;" />`
      qrCodeObject.value = { dataURL: qrDataURL }
    } catch (error) {
      console.error('生成二维码失败:', error)
      message.error('生成二维码失败，请重试')
    }
  }
}

// 初始化二维码对象
const initQRCode = async () => {
  const qrcodeElement = document.getElementById('qrcode')
  if (qrcodeElement && !qrCodeObject.value) {
    qrcodeElement.innerHTML = ''
    await updateQRCode('点击开始生成')
  }
}

const encodeData = (index, inputBytes) => {
  try {
    const uint8Array = new Uint8Array(inputBytes)
    const base64String = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)))
    return `${index},${base64String}`
  } catch (error) {
    console.error('数据编码失败:', error)
    const encodedString = String.fromCharCode.apply(null, Array.from(inputBytes))
    const utf8Encoder = new TextEncoder()
    const utf8Bytes = utf8Encoder.encode(encodedString)
    const encodedData = btoa(String.fromCharCode.apply(null, Array.from(utf8Bytes)))
    return index + ',' + encodedData
  }
}

// 停止生成二维码
const stopQRGeneration = () => {
  isGeneratingQR.value = false
  isLooping.value = false
  transferMessage.value = '点击开始生成'
  currentChunk.value = 0
  totalChunks.value = 0
  
  if (transferInterval.value) {
    clearInterval(transferInterval.value)
    transferInterval.value = null
  }
  
  if (qrCodeObject.value) {
    updateQRCode('点击开始生成')
  }
}

// 开始生成二维码
const startQRGeneration = async () => {
  try {
    isGeneratingQR.value = true
    isLooping.value = false
    transferMessage.value = '正在准备数据...'
    
    let contentToEncode = ''
    if (currentMode.value === 'json' && jsonContent.value) {
      contentToEncode = jsonContent.value
    } else if (graph.value) {
      const graphContent = graph.value.toJSON()
      contentToEncode = JSON.stringify(graphContent, null, 2)
    }
    
    if (!contentToEncode) {
      message.warning('没有可用的内容来生成二维码')
      stopQRGeneration()
      return
    }
    
    // 压缩数据
    const textEncoder = new TextEncoder()
    const jsonBytes = textEncoder.encode(contentToEncode)
    const compressedData = pako.gzip(jsonBytes, { level: 9 })
    
    // 计算分块
    const optimizedChunkSize = 200
    totalChunks.value = Math.ceil(compressedData.length / optimizedChunkSize)
    currentChunk.value = 0
    
    // 创建文件元数据
    const fileMetadata = { 
      name: 'canvas-data.json', 
      chunks: totalChunks.value,
      originalSize: jsonBytes.length,
      compressedSize: compressedData.length,
      chunkSize: optimizedChunkSize
    }
    
    // 显示元数据二维码
    await updateQRCode(JSON.stringify(fileMetadata))
    
    transferMessage.value = '开始生成，请稍等...'
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 开始分块生成
    let chunkIndex = 0
    
    // 预生成所有数据块
    const allChunks: string[] = []
    for (let i = 0; i < totalChunks.value; i++) {
      const start = i * optimizedChunkSize
      const chunk = compressedData.subarray(start, start + optimizedChunkSize)
      const encodedData = encodeData(i, chunk)
      allChunks.push(encodedData)
    }
    
    transferInterval.value = setInterval(async () => {
      if (!isGeneratingQR.value && !isLooping.value) {
        if (transferInterval.value) {
          clearInterval(transferInterval.value)
        }
        return
      }
      
      // 显示当前数据块
      const encodedData = allChunks[chunkIndex]
      await updateQRCode(encodedData)
      currentChunk.value = chunkIndex + 1
      transferMessage.value = `正在生成数据块 ${chunkIndex + 1}/${totalChunks.value}...`
      
      chunkIndex++
      
      if (chunkIndex >= totalChunks.value) {
        chunkIndex = 0
        currentChunk.value = 1
        transferMessage.value = `正在生成数据块 ${chunkIndex + 1}/${totalChunks.value}... (循环中)`
        isGeneratingQR.value = false
        isLooping.value = true
        return
      }
    }, 800)
    
  } catch (error) {
    console.error('生成二维码失败:', error)
    message.error('生成二维码失败')
    isGeneratingQR.value = false
    isLooping.value = false
    stopQRGeneration()
  }
}



// 从画布更新JSON
const updateJsonFromGraph = () => {
  if (!graph.value) return
  try {
    isUpdatingJson.value = true
    const content = graph.value.toJSON()
    jsonContent.value = JSON.stringify(content, null, 2)
    jsonError.value = ''
  } catch (error) {
    console.error('画布内容转换JSON失败:', error)
    jsonError.value = '画布内容转换JSON失败'
  } finally {
    isUpdatingJson.value = false
  }
}

// JSON格式化
const formatJson = () => {
  try {
    const parsed = JSON.parse(jsonContent.value)
    jsonContent.value = JSON.stringify(parsed, null, 2)
    jsonError.value = ''
    message.success('格式化成功')
  } catch (err) {
    jsonError.value = '无效的JSON格式'
    message.error('格式化失败: 无效的JSON格式')
  }
}

// JSON输入变化
const onJsonChange = () => {
  if (isUpdatingJson.value) return
  try {
    JSON.parse(jsonContent.value)
    jsonError.value = ''
    // 更新画布
    applyJsonChanges()
  } catch (error) {
    jsonError.value = '无效的JSON格式'
  }
}

// 应用JSON更改
const applyJsonChanges = () => {
  if (jsonError.value || !graph.value) return

  try {
    const jsonData = JSON.parse(jsonContent.value)

    // 更新画布
    graph.value.clearCells()
    graph.value.fromJSON(jsonData)

    // 更新文件内容
    if (designerStore.currentFile?.id) {
      const currentFile = { ...designerStore.currentFile }
      currentFile.content = jsonData
      designerStore.setCurrentFile(currentFile)

      // 更新标签数据
      const currentTab = designerStore.tabData.get(currentFile.id)
      if (currentTab) {
        currentTab.content = jsonData
        designerStore.tabData.set(currentFile.id, currentTab)
      }
    }
    return true
  } catch (error) {
    console.error('应用JSON更改失败:', error)
    jsonError.value = '应用JSON更改失败: ' + error.message
    message.error('应用更改失败')
    return false
  }
}

// 保存内容
const saveContent = async (silent = false) => {
  try {
    // JSON模式下先应用更改
    if (currentMode.value === 'json') {
      if(!jsonError.value){
        if (!applyJsonChanges()) {
          !silent && message.error('保存失败')
          return
        }
      }else{
        !silent && message.error('保存失败')
        return
      }

    }

    // 调用原有的保存逻辑
    const result = await designerStore.saveCurrentContent()
    if (result && !silent) {
      message.success('保存成功')
    }
  } catch (error) {
    console.error('保存失败:', error)
    if (!silent) {
      message.error('保存失败')
    }
  }
}

// 发布内容
const publishContent = () => {
  try {
    // 先保存，然后发布
    saveContent(true)
    message.success('发布成功')
  } catch (error) {
    console.error('发布失败:', error)
    message.error('发布失败')
  }
}

// 监听画布变化
const setupGraphChangeListeners = () => {
  if (!graph.value) return

  graph.value.on('cell:added cell:removed cell:change:*', () => {
    if (currentMode.value === 'json' && !isUpdatingJson.value) {
      updateJsonFromGraph()
    }
  })
}

// 初始化画布
const initModCanvas = () => {
  graph.value = new Graph({
    container: document.getElementById('mod-canvas'),
    autoResize: true,
    grid: {
      visible: true,
      type: 'dot',
      size: 20, // 增大网格点间距，从默认的10改为20
      args: { color: '#d0d0d0', thickness: 2 } // 调整颜色为更浅的灰色，增加点的大小
    },
    background: {
      color: appStore.theme === 'dark' ? '#000000' : '#fafafa' // 设置画布背景色为浅灰色
    },
    panning: { enabled: true },
    mousewheel: { enabled: true, modifiers: ['ctrl', 'meta'] },
    translating: {
      // 子节点不可移出父节点范围
      restrict(view) {
        if (view) {
          const cell = view.cell
          if (cell.isNode()) {
            const parent = cell.getParent()
            if (parent) {
              const bbox = parent.getBBox()
              return {
                ...bbox,
                y: bbox.y + 52,
                height: bbox.height - 52
              }
            }
          }
        }
        return null
      }
    },
    connecting: {
      snap: true,
      allowBlank: false,
      allowLoop: false,
      allowNode: false,
      allowEdge: false,
      highlight: true,
      connector: 'curveConnector',
      connectionPoint: 'anchor',
      anchor: 'center',
      // 自定义边的样式，水平三阶贝塞尔曲线
      createEdge() {
        console.log('createEdge called')
        return (graph.value as Graph).createEdge({
          shape: 'data-processing-curve',
          attrs: {
            line: {
              targetMarker: {
                name: 'classic',
                width: 12,
                height: 8
              },
              strokeDasharray: '5 5'
            }
          },
          zIndex: -1
        })
      },
      // 在移动边的时候判断连接是否有效，如果返回 false ，当鼠标放开的时候，不会连接到当前元素，否则会连接到当前元素。
      validateConnection({ sourceCell, targetCell, sourceMagnet, targetMagnet }) {
        console.log('validateConnection:', {
          sourceCell: sourceCell?.id,
          targetCell: targetCell?.id,
          sourceMagnet: sourceMagnet?.getAttribute('port'),
          targetMagnet: targetMagnet?.getAttribute('port'),
          hasMagnet: !!sourceMagnet && !!targetMagnet
        })

        // 暂时返回 true 允许所有连接，看看是否能连上
        return true
      }
    },
    highlighting: {
      // 连接桩可以被连接时在连接桩外围围渲染一个包围框
      magnetAvailable: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#fff',
            stroke: '#A4DEB1',
            strokeWidth: 4
          }
        }
      },
      // 连接桩吸附连线时在连接桩外围围渲染一个包围框
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#fff',
            stroke: '#31d0c6',
            strokeWidth: 4
          }
        }
      }
    },
    embedding:{
      enabled: true,
      validate: ({parent}) => {
        const nodeData = parent.getData()
        return nodeData.embedding
      }
    },
    interacting: {
      nodeMovable: true
    }
  })
  graph.value.use(
    new Selection({
      enabled: true,
      // multiple: false,
      rubberband: true,
      movable: true,
      rubberEdge: true,
      showNodeSelectionBox: true,
      className: 'selection-box'
    })
  )
  graph.value.use(
    new Transform({
      resizing: {
        enabled: (node) => {
          const nodeData = node.getData()
          return nodeData.resizing === true
        },
        orthogonal: false,
        minWidth: 40,
        minHeight: 40
      }
    })
  )
  graph.value.use(
    new History({
      enabled: true
    })
  )
  graph.value.use(
    new Keyboard({
      enabled: true,
      global: true
    })
  )
  graph.value.use(
    new Clipboard({
      enabled: true
    })
  )
  graph.value.bindKey(['ctrl+z', 'command+z'], () => {
    graph.value?.undo()
  })

  // 添加删除键盘事件处理
  graph.value.bindKey(['BackSpace', 'Delete'], () => {
    const selectedCells = graph.value?.getSelectedCells()
    if (selectedCells?.length) {
      graph.value?.removeCells(selectedCells)
      designerStore.setRightPanelVisiable(false)
    }
    return false
  })

  graph.value.bindKey(['ctrl+c', 'command+c'], () => {
    const cells = graph?.value?.getSelectedCells()
    if (cells?.length) {
      graph.value?.copy(cells)
    }
    return false
  })

  graph.value.bindKey(['ctrl+v', 'command+v'], () => {
    if (!graph.value?.isClipboardEmpty()) {
      const cells = graph.value?.paste({ offset: 32 })
      graph.value?.cleanSelection()
      graph.value?.select(cells as unknown as Cell)
    }
    return false
  })
  graph.value.setRubberbandModifiers(['ctrl', 'alt'])
  designerStore.setModGraph(graph.value)
}

const deleteNodeNextAndPrevId = (id, targetId)=>{
  // targetId要删除的id
  const graphValue = graph.value as Graph
  const node = graphValue.getCellById(id)

  const data = node.getData()
  const prevIndex = data.prevNodeIds?.indexOf(targetId)
  const nextIndex = data.nextNodeIds?.indexOf(targetId)
  if (Number.isInteger(prevIndex) && prevIndex > -1) {
    data.prevNodeIds.splice(prevIndex, 1)
  }
  if(Number.isInteger(nextIndex) && nextIndex > -1){
    data.nextNodeIds.splice(nextIndex, 1)
  }
  node.setData(data)
}

const graphListener = () => {
  const graphValue = graph.value as Graph
  // 监听画布变化-added、removed、changed

  graphValue.on('node:added', ({ cell }) => {
    // 添加一个节点后把连接桩信息添加到store中
    (cell as Node).getPorts().forEach((port) => {
      designerStore.setPorts(port.id)
    })
    //添加节点后把节点类型添加到store中
    const nodeData = cell.getData()
    const nodeId = nodeData.id
    designerStore.setNode(nodeId)
    // only节点只能存在一个，添加一个后把菜单设置为disabled:true
    if(nodeData.only) {
      bus.emit('setPanelData', { id: nodeId, disabled: true })
    }

    // 新增：处理节点初始默认值
    if (nodeData.type) {
      // 查找节点配置
      let nodeConfig = null;
      for (const group of Object.values(designerStore.nodeGroups || {})) {
        const config = group.nodes[nodeData.type];
        if (config) {
          nodeConfig = config;
          break;
        }
      }

      // 如果找到了节点配置，设置默认值
      if (nodeConfig && nodeConfig.components) {
        // 确保properties对象存在
        if (!nodeData.properties) {
          nodeData.properties = {}
        }

        // 处理所有组件
        for (const component of nodeConfig.components) {
          // 特别处理 contentParams 类型
          if (component.type === 'contentParams' && component.props?.defaultParams) {
            // 遍历 defaultParams 设置默认值
            component.props.defaultParams.forEach((param) => {
              if (param.field && param.default !== undefined && !nodeData.properties[param.field]) {
                nodeData.properties[param.field] = param.default
              }
            })
          }
        }

        // 更新节点数据
        cell.setData(nodeData)
      }
    }
  })

  graphValue.on('node:removed', ({ cell }) => {
    //在store中 删除节点 同时删除节点上的连接桩
    const nodeId = cell.getData().id
    designerStore.deleteNode(nodeId)
    //  同时删除节点上的连接桩
    ;(cell as Node).getPorts().forEach((port) => {
      designerStore.deletePort(port.id)
    })
    // only只能存在一个，删除后后把菜单设置为disabled:false
    bus.emit('setPanelData', { id: nodeId, disabled: false })
  })

  graphValue.on('edge:removed', ({ cell }) => {
    // 删除边时， 在store中删除已连接的连接桩
    const sourcePortId = (cell as Edge).getSourcePortId()
    const targetPortId = (cell as Edge).getTargetPortId()
    if (sourcePortId && targetPortId) {
      designerStore.deleteConnectedNodePort(sourcePortId)
      designerStore.deleteConnectedNodePort(targetPortId)
    }
    // cot节点删除已设置的连接节点信息
    const sourceNodeId = (cell as Edge).getSourceCellId()
    const targetNodeId = (cell as Edge).getTargetCellId()
    if (!sourceNodeId || !targetNodeId) {
      return
    }
    const sourceNode = graph.value?.getCellById(sourceNodeId)
    const targetNode = graph.value?.getCellById(targetNodeId)
    if(sourceNode) {
      const sourceNodeData = graph.value?.getCellById(sourceNodeId).getData()
      if(sourceNodeData?.parentType === 'CotNode'){
      // 删除节点的nextNodeIds和prevNodeIds
        deleteNodeNextAndPrevId(sourceNodeId, targetNodeId)
      }
      // 更新条件分支节点的数据
      updateIfElseNodeEdge('delete', sourcePortId, sourceNodeData, sourceNode, targetNode)
    }
    if(targetNode) {
      const targetNodeData = graph.value?.getCellById(targetNodeId).getData()
      if(targetNodeData?.parentType === 'CotNode'){
      // 删除节点的nextNodeIds和prevNodeIds
        deleteNodeNextAndPrevId(targetNodeId, sourceNodeId)
      }
    }
  })


  graphValue.on('edge:connected', ({ edge }) => {
    // 两个连接桩连接后，把两个连接桩的id存到store中
    const sourcePortId = edge.getSourcePortId() as string
    const targetPortId = edge.getTargetPortId() as string
    designerStore.setConnectedNodePort(sourcePortId)
    designerStore.setConnectedNodePort(targetPortId)
    edge.attr({
      line: {
        strokeDasharray: ''
      }
    })
    /**
     * modify by ksw 2025 04-18 解决选择器节点无法传递下个节点的问题
     */
    // 获取连接线源节点和目标节点
    const sourceNode = edge.getSourceCell()
    const targetNode = edge.getTargetCell()
    const sourceNodeData = sourceNode?.getData()

    // 从端口ID提取索引
    if (sourcePortId) {
      // 更新条件分支节点的数据
      updateIfElseNodeEdge('add', sourcePortId, sourceNodeData, sourceNode, targetNode)
    }
  })
  graphValue.on('cell:click', ({ cell }) => {
    const allCells = graphValue.getCells()
    allCells.forEach((item) => {
      item.removeTool('button-remove')
      item.setData({ showButton: false })
    })
    // 点击边添加删除按钮
    if (cell.isEdge() && graphValue.isSelected(cell)) {
      cell.addTools([
        {
          name: 'button-remove',
          args: { distance: 20 }
        }
      ])
    }
    // 点击节点添加操作按钮
    if (cell.isNode() && graphValue.isSelected(cell)) {
    const nodeData = cell.getData();

    // 直接检查是否为特殊形状节点
    if (nodeData.type === 'special-shape' || nodeData.notSetRightPanel) {
      // 不设置任何状态，不打开面板
      designerStore.setRightPanelVisiable(false);
      console.log('特殊形状节点，不打开面板');
      return;
    }

    // 普通节点正常处理
    cell.setData({ showButton: true });
    const nodeDataCopy = JSON.stringify(cell.getData());
    designerStore.setCurrentNodeData({ ...JSON.parse(nodeDataCopy), id: cell.id });
    designerStore.setCurrentNode(cell);
    designerStore.setRightPanelVisiable(true);
  }
  })
  // 点击空白处删除按钮
  graphValue.on('blank:click', () => {
    const allCells = graphValue.getCells()
    allCells.forEach((item) => {
      item.removeTool('button-remove')
      // 隐藏节点上的图标按钮
      item.setData({ showButton: false })
      // 隐藏菜单
    })
    // 关闭右侧面板
    designerStore.setCurrentNodeData(null)
    designerStore.setCurrentNode(null)
    designerStore.setRightPanelVisiable(false)
    showNodeSearch.value = false
  })
  // 双击空白处弹出追加节点弹框
  graphValue.on('blank:dblclick', (data) => {
    // 将画布坐标转换为视口坐标
    const localPoint = { x: data.x, y: data.y }
    const viewportPoint = graphValue.localToClient(localPoint)

    searchPosition.value = {
      x: viewportPoint.x,
      y: viewportPoint.y
    }
    showNodeSearch.value = true
  })
  graphValue.on('cell:selected', () => {
    designerStore.setCurrentSelectedNode(graphValue.getSelectedCells())
  })
  graphValue.on('cell:unselected', () => {
    designerStore.setCurrentSelectedNode(graphValue.getSelectedCells())
  })
  // 添加选框右键菜单监听
  graphValue.on('selection:changed', ({selected,...props}) => {
    // 框选的节点、连线
    designerStore.setSelectionCells(selected)
  })
}


const initDevTool = (graph: Graph) => {
  window.__x6_instances__ = []
  window.__x6_instances__.push(graph)
}

// 处理调试事件
const handleDebug = (res: any) => {
  console.log('Received debug details:', res)
  debugDetails.value = res.processDetails
}

// 直接调用 API
const handleSendSSEMessage = async ({ question }) => {
  try {
    const res = await getSse({question},AI_TOKEN.value)
    if (res.data.code === 200) {
      return res.data
    } else {
      throw new Error(res.data.message || '请求失败')
    }
  } catch (error) {
  }
}

// 添加处理配置面板显示状态变化的方法
const handleConfigVisibleChange = (visible: boolean) => {
  designerStore.setRightPanelVisiable(visible)
  if(!visible) {
    graph.value?.cleanSelection()
  }
}

// 处理全屏切换
const handleFullscreen = () => {
  appStore.toggleFullscreen()
}

const updateIfElseNodeEdge = (
  type,
  sourcePortId,
  sourceNodeData,
  sourceNode,
  targetNode
) => {
  let extractedIndex = -1;
  if (sourcePortId.includes("right-")) {
    const portIndexMatch = sourcePortId.match(/right-(\d+)$/);
    extractedIndex = portIndexMatch ? parseInt(portIndexMatch[1]) : -1;
  } else {
    extractedIndex = parseInt(sourcePortId);
    if (extractedIndex >= 2) {
      extractedIndex = extractedIndex - 2;
    }
  }
  if (sourceNodeData?.type === "ifElseNode") {
    const branches = sourceNodeData?.properties?.branches ?? [];
    const branchIndex = extractedIndex;
    const targetNodeId = targetNode?.id;

    // 确保索引有效
    if (branchIndex >= 0 && branchIndex < branches.length) {
      // 更新分支的nextNode为目标节点ID
      branches[branchIndex].nextNode = type === "delete" ? "" : targetNodeId;
      sourceNode?.setData({
        ...sourceNodeData,
        properties: { ...sourceNodeData.properties, branches },
      });
    } else if (branchIndex === branches.length) {
      branches.push({ nextNode: targetNodeId });
      sourceNode?.setData({
        ...sourceNodeData,
        properties: { ...sourceNodeData.properties, branches },
      });
    }
  }
}

onMounted(() => {
  initModCanvas()
  graphListener()
  initDevTool(graph.value as Graph)

  // 设置初始模式
  currentMode.value = designerStore.mode || 'all'

  // 设置画布变化监听
  setupGraphChangeListeners()

      // 初始化二维码对象
    nextTick(async () => {
      await initQRCode()
    })

  // 确保当前文件内容可以渲染
  nextTick(() => {
    try {
      if (designerStore.currentFile?.id && designerStore.currentFile?.content) {
        if (designerStore.modGraph?.fromJSON) {
          // 确保画布清空
          designerStore.modGraph.clearCells();
          // 渲染内容
          designerStore.modGraph.fromJSON(designerStore.currentFile.content);

          if (currentMode.value === 'json') {
            updateJsonFromGraph()
          }
        } else {
        }
      } else {
      }
    } catch (error) {
    }
  });
})
</script>

<style lang="scss" scoped>
.data-model-mapping-container {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;

  .mod-canvas {
    width: 100%;
    height: 100%;
    flex: 1;
    :deep(.x6-cell.x6-node > rect) {
      rx: .5em;
      fill: var(--area-fill-color);
    }
    :deep(.x6-html-shape-node) {
      pointer-events: none !important;
    }
  }

  .debug-panel-container {
    width: 100%;
    position: absolute;  /* 固定在底部 */
    bottom: 0;
    z-index:999;
    background: #fff;
    @apply dark:!bg-area-fill;
    box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .ai-chat-container {
    position: absolute;
    right: 20px;
    width: 400px;
    height: calc(100% - 80px); // 减去底部工具栏的高度
    border-radius: 8px;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 9999999;
    animation: slideIn 0.3s ease-out;
  }

  @keyframes slideIn {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }

  // 添加 NodeConfig 相关样式
  :deep(.ant-drawer) {
    position: absolute;
    height: 100%;

    .ant-drawer-content-wrapper {
      height: 100%;
    }
  }

  // 确保textarea占满容器
  :deep(.ant-input) {
    height: 100% !important;
  }
}
</style>

<style>
/* 全局样式，修改选框颜色 */
.x6-widget-selection-box {
  border: 2px solid var(--canvas-border-select) !important; /* 蓝色边框 */
}
.x6-widget-selection-inner {
  border: 1px dashed var(--canvas-border-select) !important; /* 内部虚线边框也改为蓝色 */
  background-color: rgba(24, 144, 255, 0.05) !important; /* 淡蓝色背景 */
}
.x6-widget-selection-box.x6-widget-selection-box-node {
  pointer-events: none !important;
}
</style>
