<template>
  <div class="space-y-6">
    <!-- 头部信息 -->
    <div class="pb-4 border-b border-gray-200 dark:border-gray-700">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white">{{ modelName }} · 参数设置</h2>
      <p class="text-xs text-gray-500 mt-1">深度思考 · 128K</p>
    </div>

    <!-- 生成多样性 -->
    <div class="space-y-4">
      <div class="flex items-center space-x-2">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white">生成多样性</h3>
        <a-tooltip title="控制生成内容的多样性程度">
          <QuestionCircleOutlined class="text-gray-400 text-xs" />
        </a-tooltip>
      </div>
      
      <!-- 模式选择 -->
      <div class="grid grid-cols-4 gap-2">
        <button
          v-for="mode in diversityModes"
          :key="mode.value"
          @click="modelParams.diversityMode = mode.value"
          class="px-3 py-2 text-xs rounded-md border transition-colors"
          :class="modelParams.diversityMode === mode.value 
            ? 'bg-blue-50 border-blue-500 text-blue-600' 
            : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'"
        >
          {{ mode.label }}
        </button>
      </div>
    </div>

    <!-- 生成随机性 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="text-sm font-medium text-gray-900 dark:text-white">生成随机性</span>
          <a-tooltip title="控制生成内容的随机程度，值越高越随机">
            <QuestionCircleOutlined class="text-gray-400 text-xs" />
          </a-tooltip>
        </div>
        <div class="flex items-center space-x-2">
          <a-button size="small" @click="adjustValue('temperature', -0.1)">
            <MinusOutlined />
          </a-button>
          <div class="w-16 text-center">
            <a-input-number
              v-model:value="modelParams.temperature"
              :min="0"
              :max="2"
              :step="0.1"
              :precision="1"
              size="small"
              class="w-full"
            />
          </div>
          <a-button size="small" @click="adjustValue('temperature', 0.1)">
            <PlusOutlined />
          </a-button>
        </div>
      </div>
      <a-slider
        v-model:value="modelParams.temperature"
        :min="0"
        :max="2"
        :step="0.1"
        :tooltip-formatter="(value) => value.toFixed(1)"
      />
    </div>

    <!-- Top P -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="text-sm font-medium text-gray-900 dark:text-white">Top P</span>
          <a-tooltip title="控制生成时考虑的词汇范围，值越小越聚焦">
            <QuestionCircleOutlined class="text-gray-400 text-xs" />
          </a-tooltip>
        </div>
        <div class="flex items-center space-x-2">
          <a-button size="small" @click="adjustValue('topP', -0.01)">
            <MinusOutlined />
          </a-button>
          <div class="w-16 text-center">
            <a-input-number
              v-model:value="modelParams.topP"
              :min="0"
              :max="1"
              :step="0.01"
              :precision="2"
              size="small"
              class="w-full"
            />
          </div>
          <a-button size="small" @click="adjustValue('topP', 0.01)">
            <PlusOutlined />
          </a-button>
        </div>
      </div>
      <a-slider
        v-model:value="modelParams.topP"
        :min="0"
        :max="1"
        :step="0.01"
        :tooltip-formatter="(value) => value.toFixed(2)"
      />
    </div>

    <!-- 重复语句惩罚 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="text-sm font-medium text-gray-900 dark:text-white">重复语句惩罚</span>
          <a-tooltip title="防止生成重复内容，值越高惩罚越严重">
            <QuestionCircleOutlined class="text-gray-400 text-xs" />
          </a-tooltip>
        </div>
        <div class="flex items-center space-x-2">
          <a-button size="small" @click="adjustValue('repetitionPenalty', -0.01)">
            <MinusOutlined />
          </a-button>
          <div class="w-16 text-center">
            <a-input-number
              v-model:value="modelParams.repetitionPenalty"
              :min="0"
              :max="2"
              :step="0.01"
              :precision="2"
              size="small"
              class="w-full"
            />
          </div>
          <a-button size="small" @click="adjustValue('repetitionPenalty', 0.01)">
            <PlusOutlined />
          </a-button>
        </div>
      </div>
      <a-slider
        v-model:value="modelParams.repetitionPenalty"
        :min="0"
        :max="2"
        :step="0.01"
        :tooltip-formatter="(value) => value.toFixed(2)"
      />
    </div>

    <!-- 输入及输出设置 -->
    <div class="space-y-4">
      <h3 class="text-sm font-medium text-gray-900 dark:text-white border-t pt-4">输入及输出设置</h3>
      
      <!-- 携带上下文轮数 -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium text-gray-900 dark:text-white">携带上下文轮数</span>
            <a-tooltip title="对话时携带的历史轮数">
              <QuestionCircleOutlined class="text-gray-400 text-xs" />
            </a-tooltip>
          </div>
          <div class="flex items-center space-x-2">
            <a-button size="small" @click="adjustValue('contextRounds', -1)">
              <MinusOutlined />
            </a-button>
            <div class="w-16 text-center">
              <a-input-number
                v-model:value="modelParams.contextRounds"
                :min="1"
                :max="100"
                :step="1"
                size="small"
                class="w-full"
              />
            </div>
            <a-button size="small" @click="adjustValue('contextRounds', 1)">
              <PlusOutlined />
            </a-button>
          </div>
        </div>
        <a-slider
          v-model:value="modelParams.contextRounds"
          :min="1"
          :max="100"
          :step="1"
        />
      </div>

      <!-- 最大回复长度 -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium text-gray-900 dark:text-white">最大回复长度</span>
            <a-tooltip title="单次回复的最大字符数">
              <QuestionCircleOutlined class="text-gray-400 text-xs" />
            </a-tooltip>
          </div>
          <div class="flex items-center space-x-2">
            <a-button size="small" @click="adjustValue('maxLength', -100)">
              <MinusOutlined />
            </a-button>
            <div class="w-20 text-center">
              <a-input-number
                v-model:value="modelParams.maxLength"
                :min="100"
                :max="32000"
                :step="100"
                size="small"
                class="w-full"
              />
            </div>
            <a-button size="small" @click="adjustValue('maxLength', 100)">
              <PlusOutlined />
            </a-button>
          </div>
        </div>
        <a-slider
          v-model:value="modelParams.maxLength"
          :min="100"
          :max="32000"
          :step="100"
        />
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
      <a-button @click="resetToDefault">
        重置默认
      </a-button>
      <div class="flex items-center space-x-2">
        <a-button @click="handleCancel">
          取消
        </a-button>
        <a-button type="primary" @click="handleSave">
          保存设置
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  QuestionCircleOutlined,
  MinusOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

interface ModelParams {
  diversityMode: string
  temperature: number
  topP: number
  repetitionPenalty: number
  contextRounds: number
  maxLength: number
}

interface Props {
  modelName: string
  initialParams?: Partial<ModelParams>
}

interface Emits {
  (e: 'save', params: ModelParams): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelName: 'LLM模式',
  initialParams: () => ({})
})

const emit = defineEmits<Emits>()

// 多样性模式选项
const diversityModes = [
  { label: '精确模式', value: 'precise' },
  { label: '平衡模式', value: 'balanced' },
  { label: '创意模式', value: 'creative' },
  { label: '自定义', value: 'custom' }
]

// 默认参数
const defaultParams: ModelParams = {
  diversityMode: 'balanced',
  temperature: 0.8,
  topP: 0.79,
  repetitionPenalty: 1.02,
  contextRounds: 57,
  maxLength: 9687
}

// 模型参数
const modelParams = ref<ModelParams>({ ...defaultParams, ...props.initialParams })

// 调整数值
const adjustValue = (key: keyof ModelParams, delta: number) => {
  const currentValue = modelParams.value[key] as number
  const newValue = currentValue + delta
  
  // 根据不同参数设置边界
  switch (key) {
    case 'temperature':
      modelParams.value[key] = Math.max(0, Math.min(2, newValue))
      break
    case 'topP':
      modelParams.value[key] = Math.max(0, Math.min(1, newValue))
      break
    case 'repetitionPenalty':
      modelParams.value[key] = Math.max(0, Math.min(2, newValue))
      break
    case 'contextRounds':
      modelParams.value[key] = Math.max(1, Math.min(100, newValue))
      break
    case 'maxLength':
      modelParams.value[key] = Math.max(100, Math.min(32000, newValue))
      break
  }
}

// 重置为默认值
const resetToDefault = () => {
  modelParams.value = { ...defaultParams }
  message.success('已重置为默认参数')
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 保存设置
const handleSave = () => {
  emit('save', { ...modelParams.value })
  message.success('参数设置已保存')
}

// 监听初始参数变化
watch(() => props.initialParams, (newParams) => {
  if (newParams) {
    modelParams.value = { ...defaultParams, ...newParams }
  }
}, { deep: true, immediate: true })
</script>

<style scoped>
/* 自定义滑块样式 */
:deep(.ant-slider-track) {
  background-color: #3b82f6;
}

:deep(.ant-slider-handle) {
  border-color: #3b82f6;
}

:deep(.ant-slider-handle:hover) {
  border-color: #2563eb;
}

:deep(.ant-slider-handle:focus) {
  border-color: #2563eb;
  box-shadow: 0 0 0 5px rgba(59, 130, 246, 0.12);
}

/* 输入框样式 */
:deep(.ant-input-number) {
  border-radius: 4px;
}

:deep(.ant-input-number-input) {
  text-align: center;
}

/* 按钮样式 */
:deep(.ant-btn) {
  border-radius: 4px;
}
</style> 