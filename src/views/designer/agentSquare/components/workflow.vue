<template>
  <a-modal
    v-model:visible="visible"
    title="添加工作流"
    :footer="null"
    width="800px"
    class="workflow-modal"
    @cancel="handleCancel"
  >
    <div class="flex h-[500px]">
      <!-- 左侧区域 -->
      <!-- <div class="w-[240px] border-r p-4">
        <a-input-search
          v-model:value="name"
          placeholder="搜索"
          class="mb-4"
          @search="handleSearch"
        >
          <template #prefix>
            <SearchOutlined class="text-gray-400" />
          </template>
        </a-input-search>

        <!-- 创建工作流按钮 -->
      <!-- <a-button
          type="primary"
          class="w-full mb-4 !bg-indigo-500 hover:!bg-indigo-600"
          @click="handleCreateWorkflow"
        >
          创建工作流
          <template #icon>
            <DownOutlined />
          </template>
        </a-button> -->

      <!-- 左侧菜单列表 -->
      <!-- <div class="space-y-1">
          <div
            class="flex items-center px-3 py-2 rounded cursor-pointer hover:bg-gray-100"
            :class="{ 'bg-gray-100': selectedMenu === 'resource' }"
            @click="selectedMenu = 'resource'"
          >
            <AppstoreOutlined class="mr-2" />
            资源库工作流
          </div>
          <div
            class="flex items-center px-3 py-2 rounded cursor-pointer hover:bg-gray-100"
            :class="{ 'bg-gray-100': selectedMenu === 'official' }"
            @click="selectedMenu = 'official'"
          >
            <AppstoreOutlined class="mr-2" />
            官方示例
          </div>
        </div> -->
      <!-- </div> -->

      <!-- 右侧内容区域 -->
      <div class="flex-1 p-4">
        <!-- 筛选区域 -->
        <a-input-search
          v-model:value="name"
          placeholder="搜索"
          class="mb-4"
          @search="handleSearch"
        >
          <template #prefix>
            <SearchOutlined class="text-gray-400" />
          </template>
        </a-input-search>
        <!-- <div class="flex items-center gap-4 mb-4">
          <div class="flex items-center gap-2">
            <span class="text-gray-600">类型:</span>
            <a-select
              v-model:value="selectedType"
              style="width: 120px"
              size="middle"
            >
              <a-select-option value="">
                全部
              </a-select-option>
              <a-select-option value="1">
                工作流
              </a-select-option>
              <a-select-option value="2">
                任务流
              </a-select-option>
            </a-select>
          </div>
          <div class="flex items-center gap-2">
            <span class="text-gray-600">状态:</span>
            <a-select
              v-model:value="selectedStatus"
              style="width: 120px"
              size="middle"
            >
              <a-select-option value="published">
                已发布
              </a-select-option>
              <a-select-option value="unPublished">
                未发布
              </a-select-option>
            </a-select>
          </div>
        </div> -->

        <!-- 工作流列表 -->
        <div class="min-h-[300px] relative">
          <div
            v-if="loading"
            class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75"
          >
            <a-spin />
          </div>
          <div class="overflow-y-auto h-[370px]">
            <div
              v-for="item in filteredWorkflows"
              :key="item.id"
              class="workflow-item"
            >
              <div class="flex items-center p-4 hover:bg-gray-50 dark:hover:!bg-primary-hover rounded cursor-pointer">
                <div class="w-10 h-10 bg-green-50 rounded flex items-center justify-center mr-4">
                  <ShareAltOutlined class="text-green-500 text-lg" />
                </div>
                <div class="flex-1">
                  <div class="flex items-center justify-between">
                    <div>
                      <div class="flex items-center gap-2">
                        <h3 class="text-base font-medium">
                          {{ item.name }}
                        </h3>
                        <a-tag
                          color="success"
                          class="flex items-center"
                        >
                          <CheckCircleOutlined class="mr-1" />
                          已发布
                        </a-tag>
                      </div>
                      <div class="text-gray-500 text-sm mt-1">
                        {{ item.description }}
                      </div>
                    </div>
                    <a-button
                      :type="item.added ? 'default' : 'primary'"
                      size="middle"
                      class="min-w-[88px] workflow-btn"
                      :class="{ 'added': item.added }"
                      @click="handleAdd(item)"
                    >
                      <span>{{ getButtonText(item) }}</span>
                    </a-button>
                  </div>
                  <div class="text-gray-400 text-sm mt-2">
                    发布于 {{ item.publishDate }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-4 flex justify-end">
            <a-pagination
              v-model:current="current"
              v-model:pageSize="pageSize"
              :total="total"
              :show-total="total => `共 ${total} 条`"
              :show-size-changer="true"
              @change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>
  
<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  SearchOutlined,
  ShareAltOutlined,
  CheckCircleOutlined,
  AppstoreOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { getCanvasPage } from '@/apis/designer'
interface Workflow {
  id: string
  name: string
  description: string
  type: string
  status: string
  publishDate: string
  added: boolean
}
interface CanvasPageParams {
  current?: number
  pageSize?: number
  name?: string
  type?: string
  status?: string
}

const props = defineProps<{
  addedWorkflowIds: string[]
}>()

const visible = ref(false)
const name = ref('')
const selectedType = ref('')
const selectedStatus = ref('published')
const selectedMenu = ref('resource')

// 更新示例数据
const workflows = ref<Workflow[]>([])

// 监听 addedWorkflowIds 变化，更新工作流状态
watch(
  () => props.addedWorkflowIds,
  (newIds) => {
    workflows.value.forEach((workflow) => {
      workflow.added = newIds.includes(workflow.id)
    })
  },
  { immediate: true }
)

// 更新工作流状态的方法
const updateWorkflowStatus = (workflowId: string, added: boolean) => {
  const workflow = workflows.value.find((w) => w.id === workflowId)
  if (workflow) {
    workflow.added = added
  }
}
// 添加分页相关的响应式变量
const current = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 获取工作流列表数据
const fetchWorkflows = async () => {
  try {
    loading.value = true
    const params: CanvasPageParams = {
      current: current.value,
      pageSize: pageSize.value,
      name: name.value,
      type: selectedType.value,
      status: selectedStatus.value
    }

    const response = await getCanvasPage(params)
    if (response.code === 200 && response.data) {
      // 转换接口数据为组件所需格式
      workflows.value = response.data.records.map((record) => ({
        id: record.id,
        name: record.name || '未命名工作流',
        description: record.description || '暂无描述',
        type: record.type || '',
        status: record.status || 'unPublished',
        publishDate: record.updateTime || new Date().toISOString(),
        added: props.addedWorkflowIds.includes(record.id)
      }))
      total.value = response.data.total
    }
  } catch (error) {
    console.error('获取工作流列表失败:', error)
  } finally {
    loading.value = false
  }
}
// 过滤工作流列表
const filteredWorkflows = computed(() => workflows.value)
const handleSearch = async (value: string) => {
  name.value = value
  current.value = 1 // 重置页码
  await fetchWorkflows()
}

// 监听筛选条件变化
watch([selectedType, selectedStatus], async () => {
  current.value = 1 // 重置页码
  await fetchWorkflows()
})

// 添加分页处理
const handlePageChange = async (page: number, size: number) => {
  current.value = page
  pageSize.value = size
  await fetchWorkflows()
}

// 在组件显示时获取数据
const show = async () => {
  visible.value = true
  await fetchWorkflows()
}
// 处理添加工作流
const handleAdd = (workflow: Workflow) => {
  if (workflow.added) {
    // 如果已添加，则移除
    workflow.added = false
    emit('remove', workflow)
  } else {
    // 如果未添加，则添加
    workflow.added = true
    emit('add', workflow)
  }
}

// 处理取消
const handleCancel = () => {
  visible.value = false
}

const emit = defineEmits(['add', 'remove'])

// 处理创建工作流
const handleCreateWorkflow = () => {
  console.log('创建工作流')
}

// 获取按钮文字
const getButtonText = (workflow: Workflow) => {
  return workflow.added ? '已添加' : '添加'
}

// 修改 defineExpose
defineExpose({
  show,
  updateWorkflowStatus
})
</script>
  
<style scoped>
.workflow-modal :deep(.ant-modal-content) {
  padding: 0;
}

.workflow-modal :deep(.ant-modal-header) {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0;
}

.workflow-modal :deep(.ant-modal-body) {
  padding: 0;
}

.workflow-item {
  border-bottom: 1px solid #f0f0f0;
}

.workflow-item:hover {
  background-color: #f5f5f5;
}

.workflow-item .w-10 {
  background-color: #ecfdf5 !important; /* 更浅的绿色背景 */
}

:deep(.ant-select-selector) {
  background-color: transparent !important;
}

:deep(.ant-tag) {
  margin-right: 0;
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
}

.text-gray-500 {
  font-size: 13px;
}

.text-gray-400 {
  font-size: 12px;
}

/* 添加按钮样式 */
.workflow-btn {
  transition: all 0.3s;
  position: relative;
}

/* 已添加状态的按钮样式 */
.workflow-btn.added {
  background-color: white;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.88);
  @apply dark:!bg-global-background dark:!border-border;
}

/* 已添加状态下的悬浮样式 */
.workflow-btn.added:hover {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: white !important;
}

/* 已添加状态下的文字样式 */
.workflow-btn.added span {
  display: inline-block;
  transition: opacity 0.3s;
}

/* 已添加状态下悬浮时隐藏文字 */
.workflow-btn.added:hover span {
  opacity: 0;
}

/* 已添加状态下悬浮时显示新文字 */
.workflow-btn.added:hover::before {
  content: '从智能体移除';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: white;
}
/* 添加加载状态样式 */
.workflow-item {
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
</style>