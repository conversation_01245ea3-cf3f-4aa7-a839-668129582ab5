<template>
  <div class="flex flex-col bg-gray-50 h-full dark:bg-gray-900 overflow-hidden">
    <!-- 主要内容区域 -->
    <div class="flex flex-1 overflow-hidden h-full">
      <!-- 左侧配置区 -->
      <div 
        class="bg-white dark:bg-gray-800 overflow-y-auto h-full transition-all duration-300"
        :class="showDebugPanel ? 'w-1/3' : 'w-1/2'"
      >
        <div class="p-4 space-y-6">
          
          <!-- 编排和模型选择 -->
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">编排</h3>
            <div class="flex items-center space-x-2">
              <a-select
                v-model:value="agentMode"
                class="w-40"
                size="middle"
                placeholder="选择模型"
              >
                <a-select-option value="llm">
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>o1</span>
                  </div>
                </a-select-option>
                <a-select-option value="gpt4">
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>GPT-4</span>
                  </div>
                </a-select-option>
                <a-select-option value="claude">
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>Claude</span>
                  </div>
                </a-select-option>
              </a-select>
              <a-tooltip title="模型参数设置">
                <a-button type="text" size="small" @click="showModelSettings">
                  <SettingOutlined />
                </a-button>
              </a-tooltip>
            </div>
          </div>

          <!-- 人设与回复逻辑 -->
          <div class="space-y-3">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white">
              人设与回复逻辑
            </h3>
            <a-textarea
              v-model:value="systemPrompt"
              placeholder="请输入Agent的角色定义..."
              :auto-size="{ minRows: 3, maxRows: 5 }"
              @change="handlePromptChange"
              class="no-background"
            />
          </div>

          <!-- 工作流选择 -->
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white flex items-center">
                <ShareAltOutlined class="mr-2 text-green-500" />
                工作流
              </h3>
              <a-button type="text" size="small" @click="showWorkflowModal">
                <PlusOutlined />
              </a-button>
            </div>
            <div class="space-y-2">
              <div
                v-for="workflow in addedWorkflows"
                :key="workflow.id"
                class="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
              >
                <span>{{ workflow.name }}</span>
                <a-button type="text" size="small" @click="removeWorkflow(workflow)">
                  <DeleteOutlined class="text-red-400" />
                </a-button>
              </div>
              <div v-if="addedWorkflows.length === 0" class="text-gray-400 text-xs text-center py-2">
                暂无工作流
              </div>
            </div>
          </div>

          <!-- 内容组件组 -->
          <div class="space-y-3">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white">知识库</h3>
            
            <!-- 文本 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <FileTextOutlined class="mr-2" />
                文本
              </span>
              <a-button type="text" size="small" @click="handleAddComponent('text')">
                <PlusOutlined />
              </a-button>
            </div>

            <!-- 表格 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <TableOutlined class="mr-2" />
                表格
              </span>
              <a-button type="text" size="small" @click="handleAddComponent('table')">
                <PlusOutlined />
              </a-button>
            </div>

            <!-- 图片 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <PictureOutlined class="mr-2" />
                图片
              </span>
              <a-button type="text" size="small" @click="handleAddComponent('image')">
                <PlusOutlined />
              </a-button>
            </div>
          </div>

          <!-- 记忆组 -->
          <div class="space-y-3">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white">记忆</h3>
            
            <!-- 变量 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <CodeOutlined class="mr-2" />
                变量
              </span>
              <a-button type="text" size="small" @click="handleAddComponent('variable')">
                <PlusOutlined />
              </a-button>
            </div>

            <!-- 数据库 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <DatabaseOutlined class="mr-2" />
                数据库
              </span>
              <a-button type="text" size="small" @click="handleAddComponent('database')">
                <PlusOutlined />
              </a-button>
            </div>

            <!-- 长期记忆 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <BranchesOutlined class="mr-2" />
                长期记忆
              </span>
              <a-switch v-model:checked="memorySettings.longTerm" size="small" />
            </div>

            <!-- 文件盒子 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <FolderOutlined class="mr-2" />
                文件盒子
              </span>
              <a-switch v-model:checked="memorySettings.fileBox" size="small" />
            </div>
          </div>

          <!-- 时间工具组 -->
          <div class="space-y-3">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white flex items-center">
              <ClockCircleOutlined class="mr-2 text-orange-500" />
              时间
            </h3>
            
            <!-- 获取当前时间 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <FieldTimeOutlined class="mr-2" />
                获取当前时间
              </span>
              <a-switch v-model:checked="timeSettings.getCurrentTime" size="small" />
            </div>

            <!-- 获取时间戳 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <NumberOutlined class="mr-2" />
                获取时间戳
              </span>
              <a-switch v-model:checked="timeSettings.getTimestamp" size="small" />
            </div>

            <!-- 时间戳转换 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <SwapOutlined class="mr-2" />
                时间戳转换
              </span>
              <a-switch v-model:checked="timeSettings.timestampConvert" size="small" />
            </div>

            <!-- 时区转换 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <GlobalOutlined class="mr-2" />
                时区转换
              </span>
              <a-switch v-model:checked="timeSettings.timezoneConvert" size="small" />
            </div>

            <!-- 星期几计算 -->
            <div class="flex items-center justify-between">
              <span class="text-xs text-gray-600 flex items-center">
                <CalendarOutlined class="mr-2" />
                星期几计算
              </span>
              <a-switch v-model:checked="timeSettings.weekdayCalculate" size="small" />
            </div>
          </div>

        </div>
      </div>

      <!-- 中间预览区 -->
      <div 
        class="bg-white dark:bg-gray-800 flex flex-col h-full transition-all duration-300"
        :class="showDebugPanel ? 'w-1/3' : 'w-1/2'"
      >
        <!-- 预览区头部 -->
        <div class="flex items-center justify-between p-4 flex-shrink-0">
          <div class="flex items-center space-x-4">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">预览与调试</h2>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <span class="text-sm text-gray-500">Agent 就绪</span>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <a-tooltip title="设置">
              <a-button type="text" size="small">
                <SettingOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="全屏预览">
              <a-button type="text" size="small">
                <ExpandOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="刷新">
              <a-button type="text" size="small" @click="chatKey++">
                <ReloadOutlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="调试">
              <a-button type="text" @click="toggleDebugPanel">
                <BugOutlined />
              </a-button>
            </a-tooltip>
          </div>
        </div>

        <!-- 预览内容区 -->
        <div class="flex-1 p-4 overflow-hidden">
          <div class="bg-gray-50 rounded-lg dark:bg-gray-900 h-full">
            <AIChatContainer
              :key="chatKey"
              :width="showDebugPanel ? 400 : 600"
              :systemShowChatHeader="true"
              class="w-full"
              :sseUrl="`${PREFIX_DEBUG}?stream=true&tabId=${TAB_ID}&aip-session=${AI_TOKEN}`"
              :sendSSEMessage="handleSendSSEMessage"
              :menuList="[]"
              :selectInfo="{
                name: '',
                data: []
              }"
            />
          </div>
        </div>
      </div>

      <!-- 右侧调试详情区 -->
      <div 
        v-if="showDebugPanel"
        class="w-1/3 bg-white h-full border-l border-gray-200 dark:bg-gray-800 dark:border-gray-700 flex flex-col transition-all duration-300"
      >
        <!-- 调试区头部 -->
        <div class="flex items-center justify-between p-4 flex-shrink-0">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">调试详情</h3>
          <a-button type="text" size="small" @click="showDebugPanel = false">
            <CloseOutlined />
          </a-button>
        </div>

        <!-- 调试内容 -->
        <div class="flex-1 p-4 overflow-y-auto space-y-4">
          <!-- 请求信息 -->
          <div class="space-y-2">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">请求信息</h4>
            <div class="bg-gray-50 rounded p-3 text-xs dark:bg-gray-900">
              <div class="space-y-1">
                <div><span class="text-gray-500">Agent ID:</span> {{ agentId }}</div>
                <div><span class="text-gray-500">会话 ID:</span> {{ AI_TOKEN }}</div>
                <div><span class="text-gray-500">工作流数量:</span> {{ addedWorkflows.length }}</div>
              </div>
            </div>
          </div>

          <!-- 系统状态 -->
          <div class="space-y-2">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">系统状态</h4>
            <div class="space-y-2">
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-500">角色定义</span>
                <a-tag :color="systemPrompt ? 'green' : 'red'" size="small">
                  {{ systemPrompt ? '已配置' : '未配置' }}
                </a-tag>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-500">工作流</span>
                <a-tag :color="addedWorkflows.length > 0 ? 'green' : 'orange'" size="small">
                  {{ addedWorkflows.length }}个
                </a-tag>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-500">记忆模块</span>
                <a-tag :color="hasMemoryEnabled ? 'green' : 'gray'" size="small">
                  {{ hasMemoryEnabled ? '已启用' : '未启用' }}
                </a-tag>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-500">时间工具</span>
                <a-tag :color="hasTimeToolsEnabled ? 'green' : 'gray'" size="small">
                  {{ hasTimeToolsEnabled ? '已启用' : '未启用' }}
                </a-tag>
              </div>
            </div>
          </div>

          <!-- 执行日志 -->
          <div class="space-y-2">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">执行日志</h4>
            <div class="bg-gray-50 rounded p-3 text-xs dark:bg-gray-900 max-h-40 overflow-y-auto">
              <div class="space-y-1 text-gray-600 dark:text-gray-400">
                <div>[{{ new Date().toLocaleTimeString() }}] Agent 初始化完成</div>
                <div>[{{ new Date().toLocaleTimeString() }}] 加载系统提示词</div>
                <div v-if="addedWorkflows.length > 0">[{{ new Date().toLocaleTimeString() }}] 加载工作流 {{ addedWorkflows.length }} 个</div>
                <div>[{{ new Date().toLocaleTimeString() }}] 等待用户输入...</div>
              </div>
            </div>
          </div>

          <!-- 性能指标 -->
          <div class="space-y-2">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white">性能指标</h4>
            <div class="space-y-2">
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-500">响应时间</span>
                <span class="text-green-600">< 100ms</span>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-500">内存使用</span>
                <span class="text-blue-600">45MB</span>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-500">Token 消耗</span>
                <span class="text-orange-600">1,234</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 工作流选择弹窗 -->
  <workflow
    ref="workflowModal"
    :addedWorkflowIds="addedWorkflowIds"
    @add="handleAddWorkflow"
    @remove="handleRemoveWorkflow"
  />

  <!-- 模型设置弹窗 -->
  <a-modal
    v-model:open="showModelSettingsPanel"
    title=""
    :footer="null"
    width="600px"
    :maskClosable="false"
    centered
  >
    <model-settings
      :model-name="getModelDisplayName(agentMode)"
      :initial-params="currentModelParams"
      @save="handleSaveModelParams"
      @cancel="showModelSettingsPanel = false"
    />
  </a-modal>
</template>

<script setup lang="ts">
import { PREFIX_DEBUG } from '@/apis/designer'
import { savePrompt, saveWorkFlowIds, getAgentPrompt, getWorkFlows } from '@/apis/model'
import {
  PlusOutlined,
  ShareAltOutlined,
  DeleteOutlined,
  UserOutlined,
  FileTextOutlined,
  TableOutlined,
  PictureOutlined,
  CodeOutlined,
  DatabaseOutlined,
  BranchesOutlined,
  FolderOutlined,
  SettingOutlined,
  ExpandOutlined,
  ReloadOutlined,
  BugOutlined,
  CloseOutlined,
  ClockCircleOutlined,
  FieldTimeOutlined,
  NumberOutlined,
  SwapOutlined,
  GlobalOutlined,
  CalendarOutlined
} from '@ant-design/icons-vue'
import { AIChatContainer } from '@yss/ai-chat-components'
import { v4 as uuidv4 } from 'uuid'
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import Workflow from './components/workflow.vue'
import ModelSettings from './components/modelSettings.vue'

const route = useRoute()
const agentMode = ref('llm')
const systemPrompt = ref('')
const chatKey = ref(0)
const TAB_ID = ref(uuidv4())
const AI_TOKEN = ref(uuidv4())
const loading = ref(false)
const showDebugPanel = ref(false)

// 模型设置相关状态
const showModelSettingsPanel = ref(false)
const currentModelParams = ref({})

// 记忆设置 - 只有长期记忆和文件盒子有开关
const memorySettings = ref({
  longTerm: false,
  fileBox: false
})

// 时间设置
const timeSettings = ref({
  getCurrentTime: false,
  getTimestamp: false,
  timestampConvert: false,
  timezoneConvert: false,
  weekdayCalculate: false
})

// 从路由参数获取 id
const agentId = computed(() => route.query.id as string)

// 计算是否有记忆模块启用
const hasMemoryEnabled = computed(() => {
  return memorySettings.value.longTerm || memorySettings.value.fileBox
})

// 计算是否有时间工具启用
const hasTimeToolsEnabled = computed(() => {
  return timeSettings.value.getCurrentTime || timeSettings.value.getTimestamp || timeSettings.value.timestampConvert || timeSettings.value.timezoneConvert || timeSettings.value.weekdayCalculate
})

let savePromptTimer: number | null = null

// 切换调试面板
const toggleDebugPanel = () => {
  showDebugPanel.value = !showDebugPanel.value
}

// 显示模型设置面板
const showModelSettings = () => {
  showModelSettingsPanel.value = true
}

// 处理添加组件
const handleAddComponent = (type: string) => {
  console.log(`添加组件: ${type}`)
  // 这里可以弹出对应的组件选择弹窗
  message.info(`添加${type}组件功能待实现`)
}

// 处理提示词输入变化
const handlePromptChange = () => {
  if (!agentId.value) {
    message.warning('ID不能为空')
    return
  }

  if (savePromptTimer) {
    clearTimeout(savePromptTimer)
  }

  savePromptTimer = setTimeout(async () => {
    try {
      const response = await savePrompt({
        prompt: systemPrompt.value,
        id: agentId.value
      })
      
      if (response.code === 200) {
        console.log('提示词保存成功')
      }
    } catch (error) {
      console.error('保存提示词失败:', error)
    }
  }, 1000)
}

// 获取系统提示词
const fetchAgentPrompt = async () => {
  if (!agentId.value) return
  
  loading.value = true
  try {
    const response = await getAgentPrompt({ id: agentId.value })
    if (response.code === 200 && response.data) {
      systemPrompt.value = (response.data as any) || ''
    }
  } catch (error) {
    console.error('获取提示词失败:', error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  if (agentId.value) {
    fetchAgentPrompt()
  }
})

// 监听 agentId 变化，重新获取数据
watch(() => agentId.value, (newId) => {
  if (newId) {
    fetchAgentPrompt()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (savePromptTimer) {
    clearTimeout(savePromptTimer)
  }
})

// 处理发送消息
const handleSendSSEMessage = async ({ question }: { question: string }) => {
  try {
    const messages = [{
      role: 'user', 
      content: question
    }]

    const response = await fetch('../aip/agent/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        sessionId: AI_TOKEN.value,
        tabId: TAB_ID.value,
        messages,
        agentId: agentId.value,
        workFlowIds: addedWorkflowIds.value
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return {
      code: 200,
      data: response,
      error: null
    }

  } catch (error) {
    console.error('发送消息失败:', error)
    return {
      code: 500,
      data: null,
      error: error instanceof Error ? error.message : '发送消息失败'
    }
  }
}

// 工作流相关
const workflowModal = ref()

// 显示工作流弹框
const showWorkflowModal = () => {
  if (workflowModal.value) {
    workflowModal.value.show()
  }
}

// 添加工作流相关状态
const addedWorkflows = ref<any[]>([])

// 计算已添加工作流的 ID 列表
const addedWorkflowIds = computed(() => addedWorkflows.value.map((w) => w.id))

// 处理添加工作流
const handleAddWorkflow = async (workflow: any) => {
  if (!agentId.value) {
    message.warning('ID不能为空')
    return
  }

  if (!addedWorkflows.value.find((w) => w.id === workflow.id)) {
    const originalWorkflows = [...addedWorkflows.value]
    
    addedWorkflows.value.push(workflow)
    
    try {
      const response = await saveWorkFlowIds({
        workFlowIds: addedWorkflows.value.map(w => w.id).join(','),
        id: agentId.value
      })
      
      if (response.code === 200) {
        console.log('工作流保存成功')
      } else {
        addedWorkflows.value = originalWorkflows
      }
    } catch (error) {
      console.error('保存工作流失败:', error)
      addedWorkflows.value = originalWorkflows
    }
  }
}

// 处理移除工作流
const removeWorkflow = async (workflow: any) => {
  if (!agentId.value) {
    message.warning('ID不能为空')
    return
  }

  const index = addedWorkflows.value.findIndex((w) => w.id === workflow.id)
  if (index !== -1) {
    const originalWorkflows = [...addedWorkflows.value]
    
    addedWorkflows.value.splice(index, 1)
    
    try {
      const response = await saveWorkFlowIds({
        workFlowIds: addedWorkflows.value.map(w => w.id).join(','),
        id: agentId.value
      })
      
      if (response.code === 200) {
        console.log('工作流移除成功')
        if (workflowModal.value) {
          workflowModal.value.updateWorkflowStatus(workflow.id, false)
        }
      } else {
        message.error((response as any).message || '移除失败')
        addedWorkflows.value = originalWorkflows
      }
    } catch (error) {
      console.error('移除工作流失败:', error)
      addedWorkflows.value = originalWorkflows
      message.error('移除失败，请稍后重试')
    }
  }
}

// 处理从弹框移除工作流
const handleRemoveWorkflow = (workflow: any) => {
  removeWorkflow(workflow)
}

// 处理保存模型参数
const handleSaveModelParams = (params: any) => {
  currentModelParams.value = params
  showModelSettingsPanel.value = false
}

// 获取模型显示名称
const getModelDisplayName = (mode: string) => {
  switch (mode) {
    case 'llm':
      return 'o1'
    case 'gpt4':
      return 'GPT-4'
    case 'claude':
      return 'Claude'
    default:
      return '未知模型'
  }
}
</script>

<style scoped>
:deep(.ant-textarea) {
  border-radius: 6px;
}

:deep(.ant-textarea.no-background) {
  border: none !important;
  background-color: white !important;
  box-shadow: none !important;
  padding: 8px 0 !important;
}

:deep(.ant-textarea.no-background:hover) {
  border: none !important;
  background-color: white !important;
  box-shadow: none !important;
}

:deep(.ant-textarea.no-background:focus) {
  border: none !important;
  background-color: white !important;
  box-shadow: none !important;
  outline: none !important;
}

:deep(.ant-textarea.no-background:focus-within) {
  border: none !important;
  background-color: white !important;
  box-shadow: none !important;
  outline: none !important;
}

:deep(.ant-switch) {
  min-width: 28px;
}

:deep(.ant-select-selector) {
  border-radius: 4px !important;
}

:deep(.ant-btn) {
  border-radius: 6px;
}
textarea{
  border:none!important;
  background:#fff!important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调试面板 */
@media (max-width: 1200px) {
  .debug-panel {
    width: 300px !important;
  }
}

@media (max-width: 768px) {
  .debug-panel {
    width: 250px !important;
  }
}
</style>
