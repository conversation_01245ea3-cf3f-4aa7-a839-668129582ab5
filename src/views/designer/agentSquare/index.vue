<template>
  <div class="h-full bg-global-background p-5">
    <ListComponent
      v-model:activeTab="activeTab"
      v-model:current="current"
      v-model:pageSize="pageSize"
      :list="agentList"
      :total="total"
      :col-count="3"
      :tabs="tabs"
      @tab-change="handleTabChange"
      @item-click="handleItemClick"
    >
    </ListComponent>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import ListComponent from '@/components/ListComponent/index.vue'
import {
  getAgentNavigation,
  getAgentPage,
  type AgentPageParams,
  type AgentPageResponse,
  type AgentRecord,
  type NavigationItem
} from '@/apis/model'
// 导入默认Agent图标
import agentDefaultIcon from '@/assets/agent-default.svg'

const router = useRouter()
// 分页相关
const current = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 标签页配置
const activeTab = ref('recommend')
const tabs = ref([
  { label: '推荐', value: 'recommend' }
])

// 获取导航数据
const getNavigationData = async () => {
  try {
    const response = await getAgentNavigation()
    if (response.code === 200 && response.data) {
      // 根据接口返回的数据结构重构 tabs
      tabs.value = [
        { label: '推荐', value: 'recommend' },
        ...response.data.map(item => ({
          label: item.name,
          value: item.param.agentType
        }))
      ]
    }
  } catch (error) {
    console.error('获取导航数据失败:', error)
  }
}

// 添加类型定义
interface AgentListItem {
  id: number
  title: string
  level: string
  tags: string[]
  description: string
  users: number
  messages: number
  avatar: string
  isCollected?: boolean
  _rawId?: string
  _templateId?: string
  _type?: string
}

const agentList = ref<AgentListItem[]>([])

// 完善获取列表数据的方法
const getAgentList = async () => {
  try {
    const params: AgentPageParams = {
      current: current.value,
      pageSize: pageSize.value,
      // 如果不是推荐标签，则使用当前选中的标签值作为 agentType
      agentType: activeTab.value === 'recommend' ? undefined : activeTab.value
    }

    const response = await getAgentPage(params)

    if (response.code === 200 && response.data) {
      // 转换接口数据为组件所需格式
      agentList.value = response.data.records.map(record => ({
        id: parseInt(record.id) || 0, // 转换为number类型，如果转换失败则为0
        title: record.project || '未命名智能体',
        level: getRandomLevel(), // 根据新的数据结构调整
        tags: [record.subsystem, record.module].filter(Boolean), // 使用子系统和模块作为标签
        description: record.description || '暂无描述',
        users: getRandomUsers(), // 临时随机数据
        messages: getRandomMessages(), // 临时随机数据
        avatar: getRandomAvatar(), // 返回导入的SVG图标路径
        // 保存原始数据到自定义属性
        _rawId: record.id,
        _templateId: record.templateId,
        _type: record.type
      }))

      // 设置总数
      total.value = response.data.total
    }
  } catch (error) {
    console.error('获取列表数据失败:', error)
    // 清空列表并将总数置为 0
    agentList.value = []
    total.value = 0
  }
}

// 辅助函数：获取随机等级（由于新接口没有code字段）
const getRandomLevel = (): string => {
  const levels = ['S级', 'A级', 'B级', 'C级']
  const randomIndex = Math.floor(Math.random() * levels.length)
  return levels[randomIndex]
}

// 辅助函数：获取随机头像URL（由于新接口没有agentType字段）
const getRandomAvatar = (): string => {
  // 返回导入的SVG图标路径
  return agentDefaultIcon
}

// 辅助函数：根据 code 确定等级（保留此函数以备将来使用）
const getLevelByCode = (code: string): string => {
  // 这里可以根据实际业务逻辑来确定等级
  const levelMap: Record<string, string> = {
    S: 'S级',
    A: 'A级',
    B: 'B级',
    C: 'C级'
  }

  // 示例：根据 code 的第一个字符确定等级
  const level = code.charAt(0).toUpperCase()
  return levelMap[level] || 'C级'
}

// 辅助函数：生成随机用户数（临时数据）
const getRandomUsers = (): number => {
  return Math.floor(Math.random() * 200) + 50
}

// 辅助函数：生成随机消息数（临时数据）
const getRandomMessages = (): number => {
  return Math.floor(Math.random() * 2000) + 500
}



// 处理标签切换
const handleTabChange = (value: string) => {
  current.value = 1 // 重置页码
  getAgentList()
}

// 处理卡片点击
const handleItemClick = (item: any) => {
  router.push({
    path: '/designer/agentSetting',
    query: {
      id: item._rawId || item.id.toString(),
      templateId: item._templateId,
      type: item._type
    }
  })
}

// 监听分页和每页条数变化
watch(
  [current, pageSize],
  () => {
    getAgentList()
  },
  { immediate: true }
)

// 初始化导航数据
onMounted(() => {
  getNavigationData()
})
</script>
