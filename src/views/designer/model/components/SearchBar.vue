<template>
  <div class="flex items-center justify-between">
    <a-input-search
      v-model:value="searchValue"
      placeholder="搜索模型..."
      class="w-[300px]"
      @search="onSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const searchValue = ref('')
const emit = defineEmits(['search'])

const onSearch = () => {
  emit('search', searchValue.value)
}
</script>