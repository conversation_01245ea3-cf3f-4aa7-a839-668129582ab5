<template>
  <div
    class="bg-white border border-border hover:border-primary cursor-pointer hover:shadow-md transition-all relative group p-8"
  >
    <!-- 标题区域 -->
    <div class="flex items-start justify-between mb-3">
      <div class="flex items-start gap-2">
        <div>
          <h3 class="text-base font-medium text-primary">
            {{ model.name }}
          </h3>
          <p class="text-primary text-xs mt-0.5">
            {{ model.provider }}
          </p>
        </div>
        <span
          v-if="model.status === '待发布'"
          class="px-1.5 py-0.5 text-xs rounded bg-orange-50 text-orange-500"
        >
          {{ model.status }}
        </span>
      </div>
      <!-- 添加删除和编辑按钮 -->
      <div 
        class="opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-2"
      >
        <EditOutlined
          class="text-gray-400 hover:text-primary text-lg cursor-pointer"
          @click.stop="handleAction('edit')"
        />
        <DeleteOutlined 
          class="text-gray-400 hover:text-red-500 text-lg cursor-pointer" 
          @click.stop="handleAction('delete')"
        />
      </div>
    </div>
  
    <!-- 描述 -->
    <p class="text-sm text-gray-600 mb-4 line-clamp-2">
      {{ model.description }}
    </p>
  
    <!-- 标签 -->
    <div class="flex flex-wrap gap-2 mb-4">
      <span
        v-for="tag in model.tags"
        :key="tag"
        class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600"
      >
        {{ tag }}
      </span>
    </div>
  
    <!-- 操作按钮 -->
    <div class="flex items-center gap-2">
      <a-button
        type="link"
        @click.stop="handleUseModel"
      >
        使用
      </a-button>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue'
import { Modal } from 'ant-design-vue'

  interface Model {
    id: number
    name: string
    description: string
    type: string
    status: string
    updateTime: string
    tags: string[]
  }
  
const props = defineProps<{
    model: Model
  }>()
  
const emit = defineEmits(['action'])
  
const handleUseModel = () => {
  const url = `/designer/modelChat?model=${encodeURIComponent(props.model.name)}`
  window.open(url, '_self')
}

const handleAction = (type: 'use' | 'train' | 'edit' | 'delete') => {
  if (type === 'use') {
    handleUseModel()
    return
  }
  if (type === 'delete') {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除模型"${props.model.name}"吗？`,
      okText: '确认',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => {
        emit('action', { type, model: props.model })
      }
    })
    return
  }
  emit('action', { type, model: props.model })
}
</script>