<template>
  <div class="h-full">
    <!-- 筛选区域 -->
    <div class="bg-white p-4 border-b">
      <div class="space-y-4">
        <!-- 供应商筛选 -->
        <div class="flex items-center">
          <span class="w-20 text-gray-600">供应商</span>
          <a-radio-group v-model:value="filters.provider" button-style="solid">
            <a-radio-button value="all">全部</a-radio-button>
            <a-radio-button v-for="provider in providers" :key="provider.value" :value="provider.value">
              {{ provider.name }}
            </a-radio-button>
          </a-radio-group>
        </div>

        <!-- 模型类型筛选 -->
        <div class="flex items-center">
          <span class="w-20 text-gray-600">模型类型</span>
          <a-radio-group v-model:value="filters.modelType" button-style="solid">
            <a-radio-button value="all">全部</a-radio-button>
            <a-radio-button v-for="type in modelTypes" :key="type.value" :value="type.value">
              {{ type.name }}
            </a-radio-button>
          </a-radio-group>
        </div>

        <!-- 上下文长度 -->
       <!-- <div class="flex items-center">
          <span class="w-20 text-gray-600">上下文长度</span>
          <a-radio-group v-model:value="filters.contentLength" button-style="solid">
            <a-radio-button value="all">全部</a-radio-button>
            <a-radio-button value="16k-">16K以下</a-radio-button>
            <a-radio-button value="16k-64k">16K-64K</a-radio-button>
            <a-radio-button value="64k+">64K以上</a-radio-button>
          </a-radio-group>
        </div> -->
      </div>
    </div>

    <!-- 添加白色背景的内容容器 -->
    <div class="bg-white flex-1">
      <!-- 内容区域 -->
      <div class="flex p-6">
        <!-- 搜索和排序区域 -->
        <div class="flex justify-between w-full mb-6">
          <div class="flex items-center">
            <span class="mr-2">共 {{ totalModels }} 个模型</span>
            <a-button type="primary" @click="handleAddModel">
              <template #icon><PlusOutlined /></template>
              添加模型
            </a-button>
          </div>
          <div class="flex items-center space-x-4">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="从全部模型中搜索"
              style="width: 250px"
              @search="handleSearch"
            />
           
            <a-radio-group v-model:value="viewMode" button-style="solid">
              <a-radio-button value="grid"><AppstoreOutlined /></a-radio-button>
              <a-radio-button value="list"><BarsOutlined /></a-radio-button>
            </a-radio-group>
          </div>
        </div>
      </div>

      <!-- 模型列表 -->
      <div class="px-6">
        <div :class="viewMode === 'grid' ? 'grid grid-cols-3 gap-6' : 'space-y-4'">
          <ModelCard
            v-for="model in filteredModels"
            :key="model.id"
            :model="model"
            :view-mode="viewMode"
            @action="handleModelAction"
          />
        </div>
      </div>
    </div>

    <!-- 添加模型对话框 -->
    <a-modal
      v-model:visible="modelFormVisible"
      :title="isEdit ? '编辑模型' : '添加模型'"
      @ok="handleModelFormSubmit"
      @cancel="handleCloseModal"
    >
      <a-form
        :model="modelForm"
        :rules="modelRules"
        ref="modelFormRef"
        layout="vertical"
      >
        <a-form-item label="模型名称" name="name">
          <a-input v-model:value="modelForm.name" placeholder="请输入模型名称" />
        </a-form-item>
        
        <a-form-item label="供应商" name="provider">
          <a-select v-model:value="modelForm.provider" placeholder="请选择供应商">
            <a-select-option 
              v-for="provider in providers" 
              :key="provider.value" 
              :value="provider.value"
            >
              {{ provider.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="模型类型" name="type">
          <a-select v-model:value="modelForm.type" placeholder="请选择模型类型">
            <a-select-option
              v-for="type in modelTypes"
              :key="type.value"
              :value="type.value"
            >
              {{ type.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="描述" name="description">
          <a-textarea 
            v-model:value="modelForm.description" 
            placeholder="请输入模型描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { PlusOutlined, DownOutlined, AppstoreOutlined, BarsOutlined } from '@ant-design/icons-vue'
import SearchBar from './components/SearchBar.vue'
import ModelCard from './components/ModelCard.vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'

interface Model {
  id: number
  name: string
  description: string
  type: string
  status: string
  updateTime: string
  tags: string[]
}

const selectedType = ref(['all'])

const modelList = ref<Model[]>([
  
  // ... 其他模型数据
])

// 筛选条件
const filters = ref({
  provider: 'all',
  modelType: 'all',
  //contentLength: 'all'
})

const searchKeyword = ref('')
const viewMode = ref('grid')
const sortBy = ref('newest')
const totalModels = ref(0)

// 添加模型类型数据
const modelTypes = ref<{ value: string; name: string }[]>([])

// 添加供应商数据
const providers = ref<{ value: string; name: string }[]>([])

// 根据筛选条件过滤模型
const filteredModels = computed(() => {
  let result = modelList.value

  if (filters.value.provider !== 'all') {
    result = result.filter(model => model.provider === filters.value.provider)
  }

  if (filters.value.modelType !== 'all') {
    result = result.filter(model => model.type === filters.value.modelType)
  }

  if (searchKeyword.value) {
    result = result.filter(model => 
      model.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      model.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  return result
})

const handleSearch = (keyword: string) => {
  console.log('搜索关键词:', keyword)
}

// 添加编辑状态标记
const isEdit = ref(false)

// 修改处理模型操作的方法
const handleModelAction = ({ type, model }) => {
  switch (type) {
    case 'use':
      console.log('使用模型:', model)
      break
    case 'train':
      console.log('训练模型:', model)
      break
    case 'edit':
      handleEditModel(model)
      break
    case 'delete':
      handleDeleteModel(model.id)
      break
  }
}

// 添加编辑模型的处理方法
const handleEditModel = (model: Model) => {
  isEdit.value = true
  modelForm.value = { ...model }
  modelFormVisible.value = true
}

// 添加删除模型的方法
const handleDeleteModel = async (id: number) => {
  try {
    const response = await fetch(`../aip/model/delete?id=${id}`, {
      method: 'GET'
    })
    const data = await response.json()
    if (data.code === 200) {
      message.success('删除模型成功')
      // 刷新模型列表
      fetchModelData()
    } else {
      message.error(data.message || '删除模型失败')
    }
  } catch (error) {
    console.error('删除模型失败:', error)
    message.error('删除模型失败')
  }
}

// 添加获取模型数据的方法
const fetchModelData = async () => {
  try {
    const response = await fetch('../aip/model/page')
    const data = await response.json()
    if (data.code === 200) {
      totalModels.value = data.data.total
      // 如果需要更新模型列表数据，也可以在这里更新
      modelList.value = data.data.records
    }
  } catch (error) {
    console.error('获取模型数据失败:', error)
  }
}

// 添加获取模型类型的方法
const fetchModelTypes = async () => {
  try {
    const response = await fetch('../aip/model/listType')
    const data = await response.json()
    if (data.code === 200) {
      modelTypes.value = data.data
    }
  } catch (error) {
    console.error('获取模型类型失败:', error)
  }
}

// 添加获取供应商列表的方法
const fetchProviders = async () => {
  try {
    const response = await fetch('../aip/model/listProvider')
    const data = await response.json()
    if (data.code === 200) {
      providers.value = data.data
    }
  } catch (error) {
    console.error('获取供应商列表失败:', error)
  }
}

// 添加表单相关的响应式变量
const modelFormVisible = ref(false)
const modelFormRef = ref<FormInstance>()
const modelForm = ref({
  name: '',
  provider: undefined,
  type: 'string',
  description: ''
})

// 添加检查模型名称是否存在的方法
const checkModelNameExist = async (name: string) => {
  try {
    const response = await fetch(`../aip/model/checkName?name=${encodeURIComponent(name)}`)
    const data = await response.json()
    // console.info(data.code === 200)
    return data.code !== 200 
  } catch (error) {
    console.error('检查模型名称失败:', error)
    return false
  }
}

// 修改表单验证规则，编辑时不检查模型名称是否存在
const modelRules = {
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    {
      validator: async (rule: any, value: string) => {
        if (value && !isEdit.value) { // 只在新增时检查名称是否存在
          const exists = await checkModelNameExist(value)
          if (exists) {
            throw new Error('模型名称已存在')
          }
        }
      },
      trigger: 'blur'
    }
  ],
  provider: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  type: [{ required: true, message: '请选择模型类型', trigger: 'change' }]
}

// 修改表单提交方法
const handleModelFormSubmit = () => {
  modelFormRef.value?.validate().then(async () => {
    try {
      const url = isEdit.value ? '../aip/model/update' : '../aip/model/create'
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(modelForm.value)
      })
      const data = await response.json()
      if (data.code === 200) {
        message.success(isEdit.value ? '更新模型成功' : '添加模型成功')
        handleCloseModal()
        // 刷新模型列表
        fetchModelData()
      } else {
        message.error(data.message || (isEdit.value ? '更新模型失败' : '添加模型失败'))
      }
    } catch (error) {
      console.error(isEdit.value ? '更新模型失败:' : '添加模型失败:', error)
      message.error(isEdit.value ? '更新模型失败' : '添加模型失败')
    }
  })
}

// 添加关闭模态框的方法
const handleCloseModal = () => {
  modelFormVisible.value = false
  modelFormRef.value?.resetFields()
  isEdit.value = false
}

// 更新添加模型处理方法
const handleAddModel = () => {
  isEdit.value = false
  // 重置表单数据
  modelForm.value = {
    name: '',
    provider: undefined,
    type: 'string',
    description: ''
  }
  modelFormVisible.value = true
}

// 在组件挂载时获取数据
onMounted(() => {
  fetchModelData()
  fetchModelTypes()
  fetchProviders()
})
</script>

<style scoped>
.model-plaza {
  padding: 20px;
  height: calc(100vh - 60px); /* 假设顶部导航/header高度为60px，请根据实际情况调整 */
  overflow-y: auto;
  box-sizing: border-box;
}

.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}
</style>