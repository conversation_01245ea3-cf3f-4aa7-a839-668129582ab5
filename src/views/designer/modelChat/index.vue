<template>
  <div class="bg-global-background">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-xl font-medium text-custom">提示词管理</h1>
      <div class="flex gap-4">
        <a-button type="primary" @click="handleNewPrompt" class="flex items-center">
          <template #icon><plus-outlined /></template>
          <span>新建提示词</span>
        </a-button>
      </div>
    </div>

    <!-- 提示词列表 -->
    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 gap-4">
      <YssCard
        v-for="prompt in currentPrompts"
        :key="prompt.id"
        :customContent="{title: prompt.title}"
        cardType="custom"
        class="cursor-pointer group shadow-sm hover:shadow-md transition-shadow duration-200 dark:!border dark:!border-border"
        @click="handleEdit(prompt)"
      >
        <template #custom>
          <div class="pt-4 pb-4">
            <p
              class="text-custom text-sm mb-3 overflow-hidden [-webkit-line-clamp:2] [-webkit-box-orient:vertical] [display:-webkit-box]"
              >{{ prompt.description }}</p
            >
            <div class="flex justify-between items-center text-xs text-[var(--guide-text-color)]">
              <span
                class="overflow-hidden text-ellipsis whitespace-nowrap"
                >{{ prompt.lastEdited }}</span
              >
              <a-tag color="blue" class="text-xs">
                {{ prompt.model }}
              </a-tag>
            </div>
          </div>
        </template>
      </YssCard>
    </div>

    <!-- 分页器 -->
    <div class="mt-6 flex items-center justify-end p-4">
      <a-pagination
        :current="currentPage"
        :pageSize="pageSize"
        :total="total"
        show-size-changer
        :show-total="(total) => `共 ${total} 条`"
        @change="handlePageChange"
        @show-size-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { PlusOutlined } from '@ant-design/icons-vue'
import  YssCard from '@yss-design/ui/components/Card'

const router = useRouter()

// 模拟数据
const prompts = ref([
  {
    id: 1,
    title: 'Text generation Prompt',
    description: 'Text generation prompt for coding assistance',
    model: 'gpt-4o-mini',
    type: 'Text generation',
    lastEdited: '16 hours ago'
  },
  {
    id: 2,
    title: 'Example Image Prompt',
    description: 'Image generation prompt example',
    model: 'gpt-3.5-turbo',
    type: 'Images',
    lastEdited: '17 hours ago'
  },
  // 添加更多示例数据...
  ...Array.from({ length: 30 }, (_, i) => ({
    id: i + 3,
    title: `Prompt ${i + 3}`,
    description: `Description for prompt ${i + 3}`,
    model: i % 3 === 0 ? 'gpt-4o-mini' : i % 3 === 1 ? 'gpt-3.5-turbo' : 'claude-2',
    type: i % 2 === 0 ? 'Text generation' : 'Images',
    lastEdited: `${i + 1} hours ago`
  }))
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(12)
const total = computed(() => prompts.value.length)

// 当前页的提示词
const currentPrompts = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return prompts.value.slice(start, end)
})

// 页面变化处理
const handlePageChange = (page: number) => {
  currentPage.value = page
}

// 每页条数变化处理
const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  // 重置到第一页
  currentPage.value = 1
}

// 新建提示词
const handleNewPrompt = () => {
  router.push('/designer/modelChat/new')
}

// 编辑提示词
const handleEdit = (prompt: any) => {
  router.push({
    name: 'prompt-detail',
    params: {
      id: prompt.id
    }
  })
}

// 删除提示词
const handleDelete = async (prompt: any) => {
  try {
    // 这里添加删除逻辑
    console.log('删除提示词:', prompt.id)
  } catch (error) {
    console.error('删除失败:', error)
  }
}
</script>

<style scoped>
</style>
