<template>
    <div class="h-full flex">
      <!-- 左侧参数面板 -->
      <div class="w-72 bg-white border-r border-input-border flex flex-col h-full !bg-global-background">
        <div class="p-4 flex-1 overflow-y-auto">
          <h2 class="text-lg font-medium mb-4 flex items-center justify-between">
            <div class="flex items-center text-custom">
              <setting-outlined class="mr-2" />
              模型参数
            </div>
          </h2>
          
          <!-- 当前模型 -->
          <div class="mb-6">
            <div class="text-sm text-gray-600 dark:!text-[var(--text-secondary)] mb-2">当前模型</div>
            <a-input
              :value="modelName"
              disabled
              class="!bg-area-fill !text-custom !border-input-border"
            />
          </div>
          
          <!-- System Prompt -->
          <div class="mb-6">
            <div class="text-sm text-gray-600 dark:!text-[var(--text-secondary)] mb-2">System Prompt</div>
            <a-textarea
              v-model:value="systemPrompt"
              :rows="8"
              :maxlength="1000"
              placeholder="设置 System Prompt"
              class="mb-2 system-prompt bg-area-fill !text-custom !border-input-border"
              :auto-size="{ minRows: 8, maxRows: 12 }"
            />
          </div>
  
          <!-- 参数调整 -->
          <div class="space-y-6">
            <div>
              <div class="flex justify-between text-sm mb-2 text-gray-600 dark:!text-[var(--text-secondary)]">
                <span>Top P</span>
                <span>{{ topP }}</span>
              </div>
              <a-slider
                v-model:value="topP"
                :min="0.1"
                :max="1.0"
                :step="0.1"
              />
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>0.1</span>
                <span>1.0</span>
              </div>
            </div>
  
            <div>
              <div class="flex justify-between text-sm mb-2 text-gray-600 dark:!text-[var(--text-secondary)]">
                <span>Temperature</span>
                <span>{{ temperature }}</span>
              </div>
              <a-slider
                v-model:value="temperature"
                :min="0.1"
                :max="1.9"
                :step="0.1"
              />
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>0.1</span>
                <span>1.9</span>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 中间输入区域 -->
      <div class="w-1/3 border-r border-input-border flex flex-col h-full !bg-global-background">
        <div class="p-4 flex flex-col h-full">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium flex items-center text-custom">
              <edit-outlined class="mr-2" />
              输入
            </h2>
            <div class="flex items-center gap-4">
              <a-radio-group v-model:value="inputMode" button-style="solid" size="middle">
                <a-radio-button value="message">Message</a-radio-button>
                <a-radio-button value="prompt">Prompt</a-radio-button>
              </a-radio-group>
              <a-button 
                v-if="!isLoading"
                type="primary" 
                :loading="isLoading"
                :disabled="isOverCharacterLimit"
                @click="handleSend"
                size="middle"
              >
                发送消息
              </a-button>
              <a-button
                v-else
                type="primary"
                danger
                @click="handleStop"
                size="middle"
              >
                停止生成
              </a-button>
            </div>
          </div>
  
          <div class="flex-1 overflow-hidden flex flex-col">
            <template v-if="inputMode === 'message'">
              <div class="flex flex-col flex-1 min-h-0">
                <!-- 消息列表 -->
                <div class="flex-1 overflow-y-auto">
                  <div class="space-y-4 mb-4">
                    <div v-for="input in messageInputs" :key="input.id" class="space-y-2">
                      <!-- 角色选择器和删除按钮 -->
                      <div class="flex items-center justify-between">
                        <a-select
                          v-model:value="input.role"
                          style="width: 120px"
                          size="small"
                          :bordered="false"
                          class="!px-0"
                        >
                          <a-select-option value="user">user</a-select-option>
                          <a-select-option value="assistant">assistant</a-select-option>
                        </a-select>
                        <a-button 
                          v-if="messageInputs.length > 1"
                          type="text" 
                          danger 
                          size="small"
                          @click="handleRemoveMessage(input.id)"
                        >
                          <delete-outlined />
                        </a-button>
                      </div>
                      
                      <!-- 消息输入框 -->
                      <div class="relative h-[120px]">
                        <a-textarea
                          v-model:value="input.content"
                          placeholder="请输入消息内容..."
                          class="absolute inset-0"
                          :auto-size="false"
                          :maxlength="2000"
                          @keypress.enter.prevent="handleSend"
                        />
                        <!-- 字数统计 -->
                        <div class="absolute right-2 bottom-2 z-10">
                          <span :class="{'text-gray-400': input.content.length <= 2000, 'text-red-500': input.content.length > 2000}" class="text-sm">
                            {{ input.content.length }}/2000
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 新增按钮 -->
                <div class="flex justify-center sticky bottom-0 pt-2">
                  <a-button type="dashed" block @click="handleAddMessage">
                    + 新增
                  </a-button>
                </div>
              </div>
            </template>
            <template v-else>
              <!-- Prompt输入框 -->
              <div class="h-full relative">
                <a-textarea
                  v-model:value="promptInput"
                  placeholder="请输入 Prompt 文本..."
                  class="h-full bg-area-fill !text-custom border-input-border"
                  :auto-size="false"
                  :maxlength="2000"
                  @keypress.enter.prevent="handleSend"
                />
                <!-- 字数统计 -->
                <div class="absolute right-2 bottom-2 z-10">
                  <span :class="{'text-gray-400': promptInput.length <= 2000, 'text-red-500': promptInput.length > 2000}" class="text-sm">
                    {{ promptInput.length }}/2000
                  </span>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
  
      <!-- 右侧输出区域 -->
      <div class="flex-1 bg-gray-50 flex flex-col !bg-global-background">
        <div class="p-4 border-b border-input-border">
          <h2 class="text-lg font-medium flex items-center text-custom">
            <eye-outlined class="mr-2" />
            预览
          </h2>
        </div>
        <div class="flex-1 p-4 overflow-y-auto" ref="chatContainer">
          <div v-for="(message, index) in messages" :key="index" class="mb-4 bg-area-fill">
            <div class=" p-4 rounded-lg shadow-sm">
              <div class="flex items-center justify-between mb-2">
                <div class="text-sm text-custom">
                  {{ 
                    message.role === 'user' 
                      ? '输入文本' 
                      : message.role === 'system' 
                        ? '系统提示词'
                        : '输出结果' 
                  }}
                </div>
                <a-tag :color="
                  message.role === 'user' 
                    ? 'blue' 
                    : message.role === 'system'
                      ? 'purple'
                      : 'green'
                ">
                  {{ 
                    message.role === 'user' 
                      ? 'Input' 
                      : message.role === 'system'
                        ? 'System'
                        : 'Output' 
                  }}
                </a-tag>
              </div>
              <div class="whitespace-pre-wrap" :class="{
                'text-gray-600': message.role === 'system',
                'text-custom': message.role === 'user'
              }">{{ message.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, nextTick, computed } from 'vue'
  import { message } from 'ant-design-vue'
  import { useTitle } from '@vueuse/core'
  import { useRoute } from 'vue-router'
  import { EditOutlined, SettingOutlined, EyeOutlined, DeleteOutlined, SaveOutlined } from '@ant-design/icons-vue'
  
  const route = useRoute()
  const modelName = computed(() => route.query.model || '')
  
  // 添加路由参数相关的计算属性
  const promptId = computed(() => route.params.id as string)
  const isNewPrompt = computed(() => promptId.value === 'new')
  
  // 动态设置页面标题
  const title = computed(() => `${isNewPrompt.value ? '新建' : '编辑'}提示词${modelName.value ? ` - ${modelName.value}` : ''}`)
  useTitle(title)
  
  interface ChatMessage {
    role: 'user' | 'assistant' | 'system'
    content: string
  }
  
  // 在 script setup 部分添加新的类型和状态
  interface MessageInput {
    id: number
    role: 'user' | 'assistant'
    content: string
  }
  
  const systemPrompt = ref('')
  const temperature = ref(0.7)
  const topP = ref(0.8)
  const messages = ref<ChatMessage[]>([])
  const messageInputs = ref<MessageInput[]>([
    { id: 1, role: 'user', content: '' }
  ])
  let nextInputId = 2
  const promptInput = ref('')
  const isLoading = ref(false)
  const chatContainer = ref<HTMLElement | null>(null)
  const inputMode = ref<'message' | 'prompt'>('prompt')
  const currentRole = ref<'user' | 'assistant'>('user')
  let currentEventSource: EventSource | null = null
  
  // 检查是否超过字数限制
  const isOverCharacterLimit = computed(() => {
    if (inputMode.value === 'message') {
      return messageInputs.value.some(input => input.content.length > 2000)
    } else {
      return promptInput.value.length > 2000
    }
  })
  
  // 停止生成
  const handleStop = () => {
    if (currentEventSource) {
      currentEventSource.close()
      currentEventSource = null
      isLoading.value = false
    }
  }
  
  // 发送消息
  const handleSend = async () => {
    if (isLoading.value) return
  
    // messages 清空
    messages.value = []
  
    if (systemPrompt.value.trim()) {
      messages.value.push({
        role: 'system',
        content: systemPrompt.value
      })
    }
  
    if (inputMode.value === 'message') {
      // 检查是否有空消息
      if (messageInputs.value.some(input => !input.content.trim())) {
        message.warning('请填写所有消息内容')
        return
      }
  
      // 添加所有消息到对话列表
      messageInputs.value.forEach(input => {
        messages.value.push({
          role: input.role,
          content: input.content.trim()
        })
      })
  
      // 不再清空输入
      // messageInputs.value = [{ id: 1, role: 'user', content: '' }]
      // nextInputId = 2
    } else {
      if (!promptInput.value.trim()) {
        message.warning('请输入 Prompt 文本')
        return
      }
      
      messages.value.push({
        role: 'user',
        content: promptInput.value.trim()
      })
     // promptInput.value = ''
    }
  
    isLoading.value = true
  
    try {
      // 构建请求参数
      const requestBody = {
        messages: messages.value,
        model: modelName.value,      
        temperature: temperature.value,
        topP: topP.value,
        customParams: {}
      }
  
      let body = JSON.stringify(requestBody)
  
      console.info('param', body)
  
      // 1. 首先发送POST请求获取会话ID
      const initResponse = await fetch('../aip/model/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: body
      })
      console.info('initResponse', initResponse)
      if (!initResponse.ok) {
        throw new Error('Failed to initialize chat session')
      }
  
      const initData = await initResponse.json()
      const sessionId = initData.data // 假设后端返回的会话ID在这个位置
  
      // 2. 使用会话ID建立SSE连接
      const eventSource = new EventSource(`../aip/model/completions/stream?sessionId=${sessionId}`, {
        withCredentials: true
      })
      currentEventSource = eventSource
  
      let responseContent = ''
      const assistantMessage: ChatMessage = {
        role: 'assistant' as const,
        content: ''
      }
      // 将消息添加到数组中
      const messageIndex = messages.value.length
      messages.value.push(assistantMessage)
  
      // 处理消息事件
      eventSource.onmessage = (event) => {
        try {
          if (event.data === '[DONE]') {
            eventSource.close()
            isLoading.value = false
            
            // 如果是message模式，将AI的回答添加到输入区域，并新增一个用户输入
            if (inputMode.value === 'message') {
              messageInputs.value.push({
                id: nextInputId++,
                role: 'assistant',
                content: responseContent
              })
              // 添加新的用户输入区域
              messageInputs.value.push({
                id: nextInputId++,
                role: 'user',
                content: ''
              })
            }
            
            return
          }
  
          const data = JSON.parse(event.data)
          console.info('sse', data)
          
          // 检查并获取新的内容
          const newContent = data.choices?.[0]?.delta?.content
          if (newContent) {
            // 累加新内容
            responseContent += newContent
            // 直接更新数组中的消息内容，触发响应式更新
            messages.value[messageIndex] = {
              ...messages.value[messageIndex],
              content: responseContent
            }
            // 强制 Vue 更新视图并滚动
            nextTick(() => {
              if (chatContainer.value) {
                chatContainer.value.scrollTop = chatContainer.value.scrollHeight
              }
            })
          }
        } catch (err: unknown) {
          const error = err as Error
          console.error('Error parsing SSE message:', error)
          messages.value[messageIndex] = {
            ...messages.value[messageIndex],
            content: '解析响应出错: ' + error.message
          }
          eventSource.close()
          currentEventSource = null
          isLoading.value = false
        }
      }
  
      // 处理错误
      eventSource.onerror = (error) => {
        const errorData = error as MessageEvent
        console.error('SSE Error:', error)
        messages.value[messageIndex] = {
            ...messages.value[messageIndex],
            content: '请求失败，请重试：' + errorData.data
          }
        eventSource.close()
        currentEventSource = null
        isLoading.value = false
        message.error('连接出错，请重试')
      }
  
      // 处理连接关闭
      eventSource.addEventListener('done', () => {
        eventSource.close()
        currentEventSource = null
        isLoading.value = false
      })
  
    } catch (error) {
      isLoading.value = false
      message.error(error instanceof Error ? error.message : '发送消息失败')
    }
  }
  
  // 添加新的消息输入
  const handleAddMessage = () => {
    messageInputs.value.push({
      id: nextInputId++,
      role: currentRole.value,
      content: ''
    })
  }
  
  // 删除消息输入
  const handleRemoveMessage = (id: number) => {
    const index = messageInputs.value.findIndex(item => item.id === id)
    if (index > -1) {
      messageInputs.value.splice(index, 1)
    }
  }
  
  // 加载配置
  onMounted(() => {
    console.log('当前路由参数:', {
      promptId: promptId.value,
      isNewPrompt: isNewPrompt.value,
    })
  })
  </script>
  
  <style scoped>
  .ant-slider {
    margin: 6px 0;
  }
  
  .h-full {
    height: 100vh;
  }
  
  :deep(.ant-input) {
    height: 100%;
    resize: none;
    font-size: 14px;
    line-height: 1.6;
    padding: 12px;
  }
  
  :deep(.system-prompt .ant-input) {
    font-size: 14px;
    line-height: 1.6;
    padding: 12px;
  }
  
  :deep(.ant-select-selector) {
    padding-left: 0 !important;
  }
  
  /* 设置 a-textarea 的 placeholder 颜色 */
  :deep(.ant-input::placeholder),
  :deep(.ant-input-textarea::placeholder) {
    color: var(--guide-text-color) !important;
    opacity: 1;
  }
  
  /* 添加 textarea 聚焦样式 */
  :deep(.ant-input:focus),
  :deep(.ant-input-focused) {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1) !important;
  }

  </style>
  