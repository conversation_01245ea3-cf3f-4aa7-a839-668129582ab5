export interface TemplateInfo {
  success: boolean;
  content?: string;
  error?: string;
}

export interface ServerInfo {
  success: boolean;
  tempDir?: string;
  accessUrl?: string;
  instanceId?: string;
  error?: string;
}

export interface TemplateItem {
  name: string;
  description: string;
  type: string;
  version: string;
  author: string;
  path: string;
}

export interface TemplatesResponse {
  success: boolean;
  templates?: TemplateItem[];
  error?: string;
}

export interface FileTreeNode {
  name: string;
  path: string;
  type?: 'file' | 'directory';
  isDirectory?: boolean;
  children?: FileTreeNode[];
  modifiedTime?: string;
  size?: number;
  extension?: string;
}

export interface FileTreeResponse {
  success: boolean;
  data?: FileTreeNode[];
  error?: string;
}

export interface FileContentResponse {
  success: boolean;
  content?: string;
  error?: string;
}

// 在使用处通过类型声明来扩展window接口
declare global {
  interface Window {
    electronAPI: {
      invoke(channel: 'getAvailableTemplates'): Promise<TemplatesResponse>;
      invoke(channel: 'getTemplateFile', templateName: string, fileName: string): Promise<TemplateInfo>;
      invoke(channel: 'startStaticServer', templateName: string, env: string): Promise<ServerInfo>;
      invoke(channel: 'updateFileContent', tempDir: string, fileName: string, content: string): Promise<boolean>;
      invoke(channel: 'get-file-tree', rootPath: string, maxDepth?: number): Promise<FileTreeResponse>;
      invoke(channel: 'read-file-content', filePath: string): Promise<FileContentResponse>;
      invoke(channel: string, ...args: unknown[]): Promise<unknown>;
    }
    folderFilesContext?: Map<string, Array<{filePath: string, content: string, isDirectory?: boolean}>>;
  }
} 