<template>
  <div class="flex flex-col bg-gray-100 text-gray-800 h-full">
    <!-- 模板选择器 -->
    <TemplateSelector
      v-if="showTemplateSelector"
      @select="handleTemplateSelect"
      @cancel="showTemplateSelector = false"
      :showCancel="selectedTemplate !== ''"
    />

    <!-- 顶部导航 -->
    <PageHeader
      v-model="activeMode"
      :currentTemplate="selectedTemplate"
      @selectTemplate="showTemplateSelector = true"
      @openPromptManager="showPromptManager = true"
    />

    <!-- 主要内容区域 - 根据模式选择显示 -->
    <!-- 预览模式 - 全屏 -->
    <div v-if="activeMode === 'preview'" class="flex-1 overflow-hidden">
      <PreviewPanel :previewUrl="previewUrl" class="h-full w-full" />
    </div>

    <!-- 代码模式 - 多区域 -->
    <div v-else class="flex flex-1 overflow-hidden bg-gray-50">
      <!-- 文件浏览区域 -->
      <div
        v-if="selectedTemplate"
        class="flex-shrink-0 transition-all duration-300 relative h-full shadow-md z-10"
        :class="[fileExplorerVisible ? 'w-[280px]' : 'w-[30px]']"
      >
        <FileExplorer
          v-show="fileExplorerVisible"
          :template-name="selectedTemplate"
          :root-path="baseTempDir"
          :file-tree-data="fileTreeData"
          :loading="fileTreeLoading"
          @select="handleFileSelect"
          @context-menu="handleContextMenu"
        />
        <!-- 文件树切换按钮 -->
        <div
          class="absolute top-1/2 -right-3 rounded-full h-7 w-7 bg-white text-blue-600 flex items-center justify-center cursor-pointer shadow-md z-10 hover:bg-blue-50 transition-colors"
          @click="toggleFileExplorer"
          :title="fileExplorerVisible ? '折叠文件树' : '展开文件树'"
        >
          <GoChevronLeft
            class="h-4 w-4 transition-transform duration-300"
            :class="{'rotate-180': !fileExplorerVisible}"
          />
        </div>
      </div>

      <!-- 代码和终端区域 - 自适应占比 -->
      <div class="flex flex-col overflow-hidden flex-1">
        <!-- 文件路径显示 -->
        <div v-if="selectedFilePath" class="text-xs text-gray-500 px-4 py-2 bg-white border-b flex items-center">
          <FolderOutlined class="mr-2 text-gray-400" />
          <span class="opacity-70">{{ baseTempDir }}/</span>
          <span class="font-medium ml-1">{{ getCurrentFileName(selectedFilePath) }}</span>
          <a-tag v-if="selectedFileIsDirectory" class="ml-2 text-xs">文件夹</a-tag>
        </div>
        
        <!-- 上下文文件提示条 -->
        <div v-if="selectedFiles.length > 0" class="bg-blue-50 text-blue-700 text-xs px-4 py-2 flex items-center justify-between border-b">
          <div class="flex items-center flex-1 overflow-hidden">
            <CodeOutlined class="mr-2" />
            <span>已添加 {{ selectedFiles.length }} 个文件到上下文</span>
            <span class="mx-2">|</span>
            <div class="flex-1 overflow-x-auto whitespace-nowrap hide-scrollbar">
              <a-tooltip v-for="(file, index) in selectedFiles.slice(0, 5)" :key="index" :title="file.filePath">
                <a-tag 
                  class="ml-1 cursor-pointer" 
                  size="small"
                >
                  {{ getShortFileName(file.filePath) }}
                </a-tag>
              </a-tooltip>
              <a-tag v-if="selectedFiles.length > 5" class="ml-1 bg-blue-100 border-blue-200">
                +{{ selectedFiles.length - 5 }} 个文件
              </a-tag>
            </div>
          </div>
          <div class="flex items-center ml-2">
            <a-button type="link" size="small" @click="contextModalVisible = true" class="text-blue-700 hover:text-blue-900">
              管理
            </a-button>
            <a-button type="link" size="small" @click="clearAllContext" class="text-blue-700 hover:text-blue-900">
              清除
            </a-button>
          </div>
        </div>

        <!-- 代码编辑器 -->
        <EditorPanel
          class="flex-1"
          :previewUrl="previewUrl"
          v-model="generatedCode"
          :currentFilePath="selectedFilePath"
          @save="handleFileSave"
        />

        <!-- 终端区域 -->
        <TerminalPanel v-model="terminalVisible" />
      </div>

 
    </div>

    <!-- 上下文文件管理模态框 -->
    <a-modal
      v-model:visible="contextModalVisible"
      title="上下文文件管理"
      :footer="null"
      width="600px"
    >
      <ContextFileList
        :contextFiles="selectedFiles"
        @remove="removeFileFromContext"
        @clear="clearAllContext"
        @preview="previewContextFile"
      />
    </a-modal>
    
    <!-- 文件预览模态框 -->
    <a-modal
      v-model:visible="filePreviewVisible"
      :title="previewFile.filePath ? getShortFileName(previewFile.filePath) : '文件预览'"
      :footer="null"
      width="80%"
      class="code-preview-modal"
    >
      <div class="text-xs text-gray-500 mb-2">{{ previewFile.filePath }}</div>
      <a-textarea
        v-model:value="previewFile.content"
        readonly
        :autoSize="{ minRows: 10, maxRows: 30 }"
        class="font-mono text-sm"
      />
    </a-modal>

    <!-- 提示词管理模态框 -->
    <a-modal
      v-model:visible="showPromptManager"
      title="提示词管理"
      :footer="null"
      width="800px"
      :destroyOnClose="false"
      @open="handlePromptManagerOpen"
    >
      <PromptManager ref="promptManagerRef" @usePrompt="handleUsePrompt" />
    </a-modal>
  </div>
       <!-- 右侧聊天区域 -->
       <ChatPanel
        ref="chatPanelRef"
        :tab-id="TAB_ID"
        :ai-token="AI_TOKEN"
        :generatedCode="generatedCode"
        :currentFilePath="selectedFilePath"
        :selectedFiles="selectedFiles"
        :folderFilesContext="folderFilesContext"
        @updateCode="codeChange"
        @processMessage="processChatMessage"
      />
</template>

<script setup lang="ts">
import { ref, provide, watch, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  DEFAULT_MODE,
  DEFAULT_TERMINAL_VISIBLE,
  generateUUID
} from './config'
import type { FileTreeNode, FileTreeResponse, TemplateItem } from './types'

// 组件导入
import PageHeader from './components/PageHeader.vue'
import EditorPanel from './components/CodeEditor.vue'
import PreviewPanel from './components/PreviewPanel.vue'
import TerminalPanel from './components/TerminalPanel.vue'
import ChatPanel from './components/ChatPanel.vue'
import TemplateSelector from './components/TemplateSelector.vue'
import FileExplorer from './components/FileExplorer.vue'
import { sendMessageAsGenerateCode } from '@/apis/designer'
import { GoChevronLeft } from 'vue-icons-plus/go'
import { FolderOutlined, CodeOutlined } from '@ant-design/icons-vue'
import ContextFileList from './components/ContextFileList.vue'
import PromptManager from './components/PromptManager.vue'
// 导入aiCodeingIpc中的函数
import {
  readFileContent,
  updateFileContent,
  deleteFile,
  startStaticServer,
  getFileTree
} from '@/apis/aiCodeingIpc'
import { type PromptDictionary } from '@/apis/aiCodeingPrompts'

const _attrName = 'id'

provide('injectUpdateCode', updateCode)
provide('attrName', _attrName)

// 模板选择
const showTemplateSelector = ref(true)
const selectedTemplate = ref('')

// 文件选择相关
const selectedFilePath = ref('')
const selectedFileIsDirectory = ref(false)
const fileExplorerVisible = ref(true)  // 控制文件树的显示状态
const fileTreeData = ref<FileTreeNode | null>(null) // 文件树数据
const fileTreeLoading = ref(false) // 文件树加载状态

// 代码和聊天状态
const generatedCode = ref('')
const TAB_ID = ref(generateUUID())
const AI_TOKEN = ref(generateUUID())
const previewUrl = ref('')
const baseTempDir = ref('')
let timer: number | undefined

// 界面控制
const activeMode = ref(DEFAULT_MODE)
const terminalVisible = ref(DEFAULT_TERMINAL_VISIBLE)

// 用于存储相关文件
const relatedFiles = ref<Array<{filePath: string, content: string, isDirectory?: boolean}>>([])

// 用于存储用户选择的文件
const selectedFiles = ref<Array<{filePath: string, content: string, isDirectory: boolean}>>([])

// 用于存储文件夹下的文件内容，键为文件夹路径，值为文件列表
const folderFilesContext = new Map<string, Array<{filePath: string, content: string, isDirectory?: boolean}>>()
// 将folderFilesContext绑定到window对象，使其他组件可以访问
window.folderFilesContext = folderFilesContext

// 提示词管理状态
const showPromptManager = ref(false)

// 使用更简单的类型声明
type ChatPanelType = { handleUsePrompt: (prompt: PromptDictionary) => void } | null;
type PromptManagerType = { fetchPromptList: () => Promise<void> } | null;

// 聊天面板引用
const chatPanelRef = ref<ChatPanelType>(null);

// 提示词管理引用
const promptManagerRef = ref<PromptManagerType>(null);

// 解析用户输入中的@文件引用并加载对应文件
const parseFileReferences = async (message: string) => {
  // 清空之前选中的文件
  selectedFiles.value = []

  // 使用正则表达式匹配 @ 后面跟着的文件名或路径
  // 格式可能是 @filename 或 @path/to/file 或 @"path with spaces"
  const regex = /@(?:"([^"]+)"|([^\s,.;:!?]+))/g
  const matches = [...message.matchAll(regex)]

  if (!matches.length) return message // 如果没有找到@引用，直接返回原消息

  // 遍历所有匹配项
  for (const match of matches) {
    const filePath = match[1] || match[2] // 捕获的文件路径
    console.log(`检测到文件引用: ${filePath}`)

    // 构造完整文件路径 (相对于baseTempDir)
    let fullPath = filePath
    if (!filePath.startsWith('/') && baseTempDir.value) {
      fullPath = `${baseTempDir.value}/${filePath}`
    }

    try {
      // 检查文件是否存在
      const fileExists = await readFileContent(fullPath)

      if (fileExists) {
        // 读取文件内容
        const result = await readFileContent(fullPath)

        if (result.success && result.content !== undefined) {
          // 检查是否已存在相同路径的文件
          const existingIndex = selectedFiles.value.findIndex(file => file.filePath === fullPath)
          if (existingIndex === -1) {
            // 添加到已选文件列表 - 从引用添加的始终视为文件，而非文件夹
            selectedFiles.value.push({
              filePath: fullPath,
              content: result.content,
              isDirectory: false
            })
            console.log(`已加载文件: ${fullPath}`)
          }
        }
      } else {
        console.warn(`文件不存在: ${fullPath}`)
      }
    } catch (error) {
      console.error(`加载文件 ${fullPath} 出错:`, error)
    }
  }

  // 返回处理后的消息，可以选择是否删除@引用
  // 这里选择保留引用，让AI知道用户引用了哪些文件
  return message
}

// 替换handleSendSSEMessage，添加消息拦截处理
const processChatMessage = async (message: string) => {
  // 解析@文件引用并加载文件
  await parseFileReferences(message)
}

// 切换文件树显示
const toggleFileExplorer = () => {
  fileExplorerVisible.value = !fileExplorerVisible.value
}

// 处理模板选择
const handleTemplateSelect = async (template: TemplateItem) => {
  selectedTemplate.value = template.name 
  // 使用 template.path (如果存在) 或 template.name 作为根路径加载文件树
  const rootPathForFileTree = template.path 
  baseTempDir.value = rootPathForFileTree 
  await loadFileTree() // 立即加载文件树
  showTemplateSelector.value = false
  // 初始化模板时，依然可以使用 template.name，或者根据实际需要调整
  await initWithTemplate(template.name) 
}

// 收集相关文件的函数
const collectRelatedFiles = async () => {
  // 先清空之前的相关文件
  relatedFiles.value = []

  // 如果没有选中文件，则不处理
  if (!selectedFilePath.value || selectedFileIsDirectory.value) return

  try {
    // 假设我们提取当前文件的导入语句，识别依赖文件
    // 这里简化处理，仅收集同目录下的非目录文件作为相关文件
    if (fileTreeData.value && fileTreeData.value.children) {
      // 获取当前文件所在目录
      const currentDir = selectedFilePath.value.substring(0, selectedFilePath.value.lastIndexOf('/'))

      // 查找同目录的其他文件
      const siblingFiles = fileTreeData.value.children
        .filter(node => {
          // 条件：不是目录，不是当前文件，在同一目录下
          return !node.isDirectory &&
                 node.path !== selectedFilePath.value &&
                 node.path.startsWith(currentDir)
        })

      // 对于每个相关文件，读取其内容
      for (const file of siblingFiles) {
        try {
          const result = await readFileContent(file.path)
          if (result.success && result.content !== undefined) {
            relatedFiles.value.push({
              filePath: file.path,
              content: result.content,
              isDirectory: false
            })
          }
        } catch (error) {
          console.error(`读取相关文件 ${file.path} 失败:`, error)
        }
      }
    }

    console.log(`收集到 ${relatedFiles.value.length} 个相关文件`)
  } catch (error) {
    console.error('收集相关文件出错:', error)
  }
}

// 处理文件选择
const handleFileSelect = async (path: string, isDirectory: boolean) => {
  selectedFilePath.value = path
  selectedFileIsDirectory.value = isDirectory

  if (isDirectory) {
    // 如果是文件夹，加载子目录内容
    await loadFolderContent(path)
  } else {
    // 如果是文件，获取文件内容并放入代码编辑器
    try {
      const result = await readFileContent(path)
      console.log('加载文件内容成功:', result)
      if (result.success && result.content !== undefined) {
        generatedCode.value = result.content
        console.log('加载文件内容成功:', path)

        // 文件加载成功后，收集相关文件
        await collectRelatedFiles()
      } else {
        console.error('获取文件内容失败:', result.error)
      }
    } catch (error) {
      console.error('读取文件内容出错:', error)
    }
  }
}

// 处理右键菜单事件
const handleContextMenu = async (action: string, path: string, isDirectory: boolean) => {
  try {
    // 根据不同的操作执行不同的处理
    switch (action) {
    case 'addToContext':
      // 添加到代码上下文
      if (isDirectory) {
        // 如果是文件夹，递归获取所有文件
        await addFolderToContext(path)
      } else {
        // 如果是文件，直接添加到选中文件列表
        await addFileToContext(path)
      }
      break
    case 'refresh':
      // 刷新文件树
      await refreshFileTree()
      break
    case 'delete':
      // 删除
      await handleDelete(path, isDirectory)
      break
    case 'clearContext':
      // 清除所有上下文
      await clearAllContext()
      break
    default:
      console.log(`未处理的右键菜单操作: ${action}`)
      break
    }
  } catch (error) {
    console.error(`处理右键菜单操作 ${action} 出错:`, error)
  }
}

// 删除
const handleDelete = async (path: string, isDirectory: boolean) => {
  try {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除${isDirectory ? '文件夹' : '文件'} "${path.split('/').pop()}" 吗？${isDirectory ? '此操作将删除文件夹及其所有内容。' : ''}`,
      okText: '删除',
      okType: 'danger',
      async onOk() {
        try {
          // 调用Electron API删除
          const result = await deleteFile(path)

          if (result.success) {
            message.success('删除成功')
            // 刷新文件树
            refreshFileTree()

            // 如果当前选中的是被删除的文件/文件夹，则清空选中
            if (selectedFilePath.value === path) {
              selectedFilePath.value = ''
              selectedFileIsDirectory.value = false
              generatedCode.value = ''
            }
          } else {
            message.error(result.error || '删除失败')
          }
        } catch (error) {
          message.error('删除失败')
          console.error('删除出错:', error)
        }
      }
    })
  } catch (error) {
    console.error('删除出错:', error)
  }
}

// 添加文件到代码上下文
const addFileToContext = async (filePath: string) => {
  try {
    // 检查文件是否已经在选中列表中
    const existingIndex = selectedFiles.value.findIndex(file => file.filePath === filePath)
    if (existingIndex !== -1) {
      console.log(`文件已在代码上下文中: ${filePath}`)
      return
    }

    // 读取文件内容
    const result = await readFileContent(filePath)

    if (result.success && result.content !== undefined) {
      // 添加到已选文件列表
      selectedFiles.value.push({
        filePath,
        content: result.content,
        isDirectory: false
      })
      console.log(`已添加文件到代码上下文: ${filePath}`)
    }

  } catch (error) {
    console.error(`添加文件到代码上下文失败: ${filePath}`, error)
  }
}

// 添加文件夹到代码上下文
const addFolderToContext = async (folderPath: string) => {
  try {
    // 检查文件夹是否已在选中列表中
    const existingIndex = selectedFiles.value.findIndex(file => file.filePath === folderPath)
    if (existingIndex === -1) {
      // 使用isDirectory标记为文件夹
      selectedFiles.value.push({
        filePath: folderPath,
        content: '目录', // 保留内容为"目录"以兼容旧代码
        isDirectory: true
      })
    }
    
    // 针对ChatPanel，递归收集文件夹下所有文件（不包含文件夹）
    const allFilesForChat: Array<{filePath: string, content: string, isDirectory?: boolean}> = []
    await collectFilesFromFolder(folderPath, allFilesForChat)
    
    // 将文件夹中的所有文件存储在folderFilesContext中，仅供ChatPanel使用
    folderFilesContext.set(folderPath, allFilesForChat)
  } catch (error) {
    console.error(`添加文件夹到代码上下文失败: ${folderPath}`, error)
  }
}

// 递归收集文件夹下所有文件，不包含文件夹本身
const collectFilesFromFolder = async (folderPath: string, filesList: Array<{filePath: string, content: string, isDirectory?: boolean}>) => {
  try {
    const result = await getFileTree(folderPath, 1) as FileTreeResponse
    
    if (result.success && result.data) {
      // 处理data可能是数组或单个对象的情况
      const folderData = Array.isArray(result.data) ? result.data[0] : result.data
      
      if (folderData && folderData.children) {
        for (const child of folderData.children) {
          if (child.isDirectory) {
            // 递归处理子文件夹中的文件，但不将文件夹本身添加到列表
            await collectFilesFromFolder(child.path, filesList)
          } else {
            // 读取文件内容并添加到列表
            const fileContent = await readFileContent(child.path)
            if (fileContent.success && fileContent.content !== undefined) {
              filesList.push({
                filePath: child.path,
                content: fileContent.content,
                isDirectory: false // 明确标记为非文件夹
              })
            }
          }
        }
      }
    }
  } catch (error) {
    console.error(`收集文件夹文件失败: ${folderPath}`, error)
  }
}

// 加载文件树
const loadFileTree = async () => {
  // 如果没有选择模板或者正在加载，则不执行
  if (!selectedTemplate.value || fileTreeLoading.value) return

  fileTreeLoading.value = true
  try {
    // 使用根路径（如果提供）或模板名称
    const rootPath = baseTempDir.value || selectedTemplate.value

    // 确保rootPath存在且非空
    if (!rootPath) {
      console.error('路径不存在，无法加载文件树')
      return
    }
    // 直接使用原始的window.electronAPI.invoke以确保与原始实现一致
    const result = await getFileTree(rootPath, 1) as FileTreeResponse 
    console.log(`加载文件树结果: ${rootPath}`, result)

    if (result.success && result.data) {
      // 处理返回的数据
      if (Array.isArray(result.data) && result.data.length > 0) {
        // 如果返回的是数组，取第一个元素作为根节点
        fileTreeData.value = result.data[0]
      } else if (!Array.isArray(result.data) && typeof result.data === 'object') {
        // 如果返回的是单个对象，直接作为根节点
        fileTreeData.value = result.data as unknown as FileTreeNode
      } else {
        fileTreeData.value = null
        console.error('返回的文件树格式不正确')
      }
    } else {
      console.error(result.error || '加载文件树失败')
    }
  } catch (e) {
    console.error('加载文件树出错:', e)
  } finally {
    fileTreeLoading.value = false
  }
}

// 加载文件夹内容
const loadFolderContent = async (folderPath: string) => {
  try {
    const result = await getFileTree(folderPath, 1) as FileTreeResponse

    if (result.success && result.data) {
      // 处理data可能是数组或单个对象的情况
      const folderData = Array.isArray(result.data) ? result.data[0] : result.data
      
      if (folderData && folderData.children) {
        // 使用原始结构的children属性
        const children = folderData.children || []
        
        // 更新文件树数据结构
        updateFileTreeData(fileTreeData.value, folderPath, children)
      }
    } else {
      console.error(result.error || '加载文件夹失败')
    }
  } catch (e) {
    console.error('加载文件夹内容出错:', e)
  }
}

// 递归更新文件树数据结构
const updateFileTreeData = (node: FileTreeNode | null, path: string, children: FileTreeNode[]) => {
  if (!node) return false

  // 如果当前节点就是要更新的节点
  if (node.path === path) {
    // 确保children是数组
    const normalizedChildren = Array.isArray(children) ? children : [children]

    // 直接更新子节点
    node.children = normalizedChildren
    return true
  }

  // 递归查找子节点
  let updated = false
  if (node.children && node.children.length > 0) {
    for (const child of node.children) {
      const childUpdated = updateFileTreeData(child, path, children)
      updated = updated || childUpdated
    }
  }

  return updated
}

// 监控代码变更
watch(
  () => generatedCode.value,
  (val) => {
    console.log('generatedCode change', val)
    // 修改为不自动保存，依靠用户手动保存触发
    if (!selectedFilePath.value || selectedFileIsDirectory.value) {
      // 如果没有选中文件或选中的是目录，则继续使用原来的逻辑
      sendUpdateCodeInterval()
    }
    // 注释掉自动保存的逻辑
    // else {
    //   if (timer) clearInterval(timer)
    //   timer = undefined
    //   sendUpdateCode(val)
    // }
  }
)

/**
 * 在代码中查找具有特定属性值的标签及其内容
 * @param {string} code - 要搜索的HTML代码
 * @param {string} attrValue - 要查找的属性值
 * @param {string} attrName - 属性名称，默认为'data-component'
 * @returns {string|null} - 找到的完整标签或null
 */
const findTagWithAttrValue = (code: string, attrValue: string, attrName = 'data-component'): string | null => {
  if (!code || !attrValue) return null

  // 创建一个正则表达式来查找具有特定属性值的标签
  // 这里使用非贪婪匹配来处理嵌套标签的情况
  const attrPattern = new RegExp(`<([^\\s>/]+)[^>]*${attrName}\\s*=\\s*["']${attrValue}["'][^>]*>`, 'i')
  const match = code.match(attrPattern)

  if (!match || match.index === undefined) return null

  // 获取起始标签的位置
  const startPos = match.index
  // 获取标签名
  const tagName = match[1]
  // 查找对应的结束标签

  // 这部分比较复杂，需要考虑嵌套的情况
  let openTagCount = 1
  let endPos = startPos + match[0].length

  while (openTagCount > 0 && endPos < code.length) {
    // 查找下一个开始或结束标签
    const openTag = code.indexOf(`<${tagName}`, endPos)
    const closeTag = code.indexOf(`</${tagName}>`, endPos)

    // 如果找不到关闭标签，返回null
    if (closeTag === -1) return null

    // 如果找到了开始标签，且它在关闭标签之前
    if (openTag !== -1 && openTag < closeTag) {
      openTagCount++
      endPos = openTag + 1
    } else {
      // 找到了关闭标签
      openTagCount--
      if (openTagCount === 0) {
        // 找到了匹配的关闭标签
        endPos = closeTag + `</${tagName}>`.length
      } else {
        endPos = closeTag + 1
      }
    }
  }

  // 提取完整标签及其内容
  return code.substring(startPos, endPos)
}

interface CodeUpdateData {
  attrValue: string;
  message: string;
}

async function updateCode(data: CodeUpdateData) {
  const foundTag = findTagWithAttrValue(generatedCode.value, data.attrValue, _attrName)
  if (!foundTag) return

  try {
    // 解析消息中的文件引用
    await parseFileReferences(data.message)

    // 构建包含所有文件的数据结构
    const allFiles: Array<{filePath: string, content: string}> = []

    // 添加当前编辑的文件（如果有）
    if (selectedFilePath.value) {
      allFiles.push({
        filePath: selectedFilePath.value,
        content: generatedCode.value
      })
    }

    // 添加其他选中的文件
    if (selectedFiles.value && selectedFiles.value.length > 0) {
      allFiles.push(...selectedFiles.value)
    }

    // 如果没有文件，则至少添加当前编辑的代码作为无路径文件
    if (allFiles.length === 0 && generatedCode.value) {
      allFiles.push({
        filePath: '',
        content: generatedCode.value
      })
    }

    const codeInfo = {
      files: allFiles,
      basePath: selectedFilePath.value ? selectedFilePath.value.split('/').slice(0, -1).join('/') : ''
    }

    await sendMessageAsGenerateCode({
      type: 'codeUpdate',
      question: data.message,
      originalCode: codeInfo,
      targetCode: foundTag
    }, {
      'Content-Type': 'application/json;charset=UTF-8',
      'aitoken': AI_TOKEN.value
    })
    return { code: 200, data: 'Success' }
  } catch (error) {
    console.error('Error sending message:', error)
    return { code: 500, message: '发送消息失败' }
  }
}

/**
 * 处理代码变更，支持从AI聊天获取的新代码
 * @param record 包含代码内容和文件路径的对象
 */
const codeChange = async (record) => {
  console.log('收到代码变更:', record)
  
  if (!record) return
  
  try {
    // 如果包含文件路径，则尝试更新或创建文件
    if (record.filePath) {
      // 检查文件是否存在
      const fileExists = await readFileContent(record.filePath)
      
      if (fileExists) {
        const filePath = record.filePath.split('/')
        const fileName = filePath.pop() || '' 
        // 更新已存在的文件
        await updateFileContent(
          filePath.join('/'),
          fileName,
          // getCurrentFileName(record.filePath), 
          record.code
        )
        message.success(`文件 ${getCurrentFileName(record.filePath)} 已更新`)
        
        // 如果当前正在编辑这个文件，也更新编辑器内容
        if (selectedFilePath.value === record.filePath) {
          generatedCode.value = record.code
        }
      } else {
        // 文件不存在的处理逻辑已被注释，暂不处理
      }
    } else {
      // 如果没有文件路径，则更新当前编辑器内容
      // generatedCode.value = record.code
    }
  } catch (error) {
    console.error('处理代码变更失败:', error)
    message.error('处理代码变更失败')
  }
}

// 获取当前文件名
const getCurrentFileName = (path: string) => {
  if (!path) return ''
  return path.split('/').pop() || ''
}

const sendUpdateCodeInterval = () => { //TODO: 修改为根据文件类型发送更新: 修改为根据文件类型发送更新
  if (timer) clearInterval(timer)
  timer = setInterval(() => {
    if (baseTempDir.value) {
      clearInterval(timer)
      timer = undefined
      sendUpdateCode(generatedCode.value)
    }
  }, 2000)
}

const sendUpdateCode = async (val: string) => {
  try {
    // 如果没有基础目录或没有选中文件，则不进行更新
    if (!baseTempDir.value || !selectedFilePath.value || selectedFileIsDirectory.value) {
      console.log('无法更新文件：未选择有效文件')
      return
    }

    // 获取当前文件名
    const fileName = getCurrentFileName(selectedFilePath.value)
    if (!fileName) {
      console.log('无法提取文件名，无法更新文件')
      return
    }

    console.log('更新文件:', selectedFilePath.value)

    // 更新指定文件的内容
    await updateFileContent(baseTempDir.value, fileName, val)
  } catch (e) {
    console.error('更新代码出错:', e)
  }
}

// 使用选定的模板初始化
const initWithTemplate = async (templateName: string) => {
  try {
    // 启动静态服务器
    const { success, tempDir, accessUrl, instanceId } = await startStaticServer(templateName, 'development')
    console.log('启动静态服务器:', success, tempDir, accessUrl, instanceId)

    if (!success) return

    if (accessUrl) previewUrl.value = accessUrl
    if (tempDir) {
      baseTempDir.value = tempDir
      console.log('设置模板根目录路径:', tempDir)
    }
  } catch (e) {
    console.error('初始化模板出错:', e)
  }
}

// 刷新文件树
const refreshFileTree = () => {
  loadFileTree()
}

// 清除所有上下文
const clearAllContext = async () => {
  try {
    if (selectedFiles.value.length === 0) {
      message.info('当前没有上下文文件')
      return
    }
    
    Modal.confirm({
      title: '确认清除',
      content: `确定要清除所有已添加的上下文文件吗？共 ${selectedFiles.value.length} 个`,
      okText: '清除',
      cancelText: '取消',
      onOk() {
        // 清空选中文件列表
        selectedFiles.value = []
        // 清空文件夹缓存
        folderFilesContext.clear()
        message.success('已清除所有上下文文件')
      }
    })
  } catch (error) {
    console.error('清除上下文出错:', error)
  }
}

// 从上下文中移除单个文件
const removeFileFromContext = (index: number) => {
  try {
    const removedFile = selectedFiles.value[index]
    const filePath = removedFile.filePath
    
    // 如果是文件夹，需要递归清除内部文件
    if (removedFile.isDirectory) {
      // 从folderFilesContext中移除此文件夹
      folderFilesContext.delete(filePath)
      
      // 递归检查并移除所有以该文件夹路径开头的子文件夹
      for (const key of [...folderFilesContext.keys()]) {
        if (key.startsWith(filePath + '/')) {
          folderFilesContext.delete(key)
        }
      }
    }
    
    // 从selectedFiles中移除
    selectedFiles.value.splice(index, 1)
    
    message.success(`已从上下文中移除${removedFile.isDirectory ? '文件夹' : '文件'}: ${getShortFileName(filePath)}`)
  } catch (error) {
    console.error('移除文件出错:', error)
  }
}

// 获取文件短名称（仅显示最后一级路径）
const getShortFileName = (filePath: string) => {
  if (!filePath) return ''
  const parts = filePath.split('/')
  return parts[parts.length - 1]
}

// 上下文文件管理模态框
const contextModalVisible = ref(false)
const filePreviewVisible = ref(false)
const previewFile = ref<{filePath: string, content: string}>({filePath: '', content: ''})

const previewContextFile = (file: {filePath: string, content: string, isDirectory?: boolean}) => {
  // 如果是文件夹，则不显示内容 (兼容两种判断文件夹的方式)
  if (file.isDirectory || file.content === '目录') {
    message.info('文件夹不支持预览内容')
    return
  }
  
  previewFile.value = {filePath: file.filePath, content: file.content}
  filePreviewVisible.value = true
  contextModalVisible.value = false
}

// 处理文件保存
const handleFileSave = async (filePath: string, content: string) => {
  try {
    if (!baseTempDir.value) {
      console.log('无法保存文件：未设置基础目录')
      message.warning('项目环境未就绪，无法保存文件')
      return
    }

    console.log('保存文件:', filePath)
    
    // 更新当前编辑内容
    generatedCode.value = content
    
    // 保存到文件系统
    const fileName = filePath.split('/').pop() || ''
    await updateFileContent(filePath, fileName, content)
    message.success(`文件 ${fileName} 已保存`)
  } catch (e) {
    console.error('保存文件出错:', e)
    message.error('保存文件失败')
  }
}

// 处理使用提示词
const handleUsePrompt = (prompt: PromptDictionary) => {
  if (prompt && prompt.content && chatPanelRef.value) {
    // 通过引用调用ChatPanel组件中的方法
    chatPanelRef.value.handleUsePrompt(prompt)
    // 关闭提示词管理模态框
    showPromptManager.value = false
  }
}

// 处理提示词管理面板打开
const handlePromptManagerOpen = () => {
  // 打开时强制刷新提示词列表
  if (promptManagerRef.value) {
    promptManagerRef.value.fetchPromptList()
  }
}

onMounted(() => {
  // 初始时，如果没有选择模板，则显示模板选择器
  if (!selectedTemplate.value) {
    showTemplateSelector.value = true
  }
})

</script>

<style scoped lang="scss">
:deep(.content-container .route-view-container) {
  height: 100% !important;
  padding: 0 !important;
}

/* 额外的强制选择器 */
:global(.content-container .route-view-container) {
  height: 100% !important;
  padding: 0 !important;
}

:deep(.ant-input) {
  background: #ffffff;
  border-color: #d9d9d9;
  color: #333333;
}

:deep(.ant-btn-text) {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.ant-btn-text:hover) {
  background: rgba(0, 0, 0, 0.04);
  color: #333333;
}

:deep(.custom-radio-group .ant-radio-button-wrapper) {
  background-color: #f5f5f5;
  border-color: #f5f5f5;
  color: #595959;
  border-radius: 999px;
}

:deep(.custom-radio-group .ant-radio-button-wrapper:hover) {
  color: #1890ff;
  background-color: #e6f7ff;
}

:deep(.custom-radio-group .ant-radio-button-wrapper-checked) {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

:deep(.ant-radio-button-wrapper:not(:first-child)::before) {
  display: none;
}

:deep(.custom-radio-group .ant-radio-button-wrapper:first-child) {
  border-radius: 999px 0 0 999px;
}

:deep(.custom-radio-group .ant-radio-button-wrapper:last-child) {
  border-radius: 0 999px 999px 0;
}

:deep(.ai-chat-container) {
    width: 100% !important;
    height: 100%;
    &>div {
      width: 100% !important;
      height: 100% !important;
    }
}

/* 隐藏滚动条但保留功能 */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}
</style>



