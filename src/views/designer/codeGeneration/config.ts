import { v4 as uuidv4 } from 'uuid'

export const DEFAULT_MODE = 'code'
export const DEFAULT_TERMINAL_VISIBLE = false

export const CHAT_WIDTH = '400px'
export const TOGGLE_BUTTON_WIDTH = '40px'

export const TERMINAL_HEIGHT_PERCENT = '30%'
export const TERMINAL_HEADER_HEIGHT = '10'

export const DEFAULT_CODE_TEMPLATE = `<template>
  <div class="container mx-auto p-4" >
    <h1 class="text-2xl font-bold mb-4">待办事项列表</h1>
    <div class="mb-4" id="app">
      <input
        v-model="newTodo"
        @keyup.enter="addTodo"
        class="border rounded px-2 py-1 mr-2"
        placeholder="添加新任务"
      />
      <button
        @click="addTodo"
        class="bg-blue-500 text-white px-3 py-1 rounded"
      >
        添加
      </button>
    </div>
    <ul class="space-y-2">
      <li
        v-for="(todo, index) in todos"
        :key="index"
        class="flex items-center"
      >
        <input
          type="checkbox"
          v-model="todo.completed"
          class="mr-2"
        />
        <span :class="{ 'line-through': todo.completed }">
          {{ todo.text }}
        </span>
        <button
          @click="removeTodo(index)"
          class="ml-auto text-red-500"
        >
          删除
        </button>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const newTodo = ref('')
const todos = ref([
  { text: '学习Vue 3', completed: false },
  { text: '完成项目', completed: false }
])

const addTodo = () => {
  if (newTodo.value.trim()) {
    todos.value.push({ text: newTodo.value, completed: false })
    newTodo.value = ''
  }
}

const removeTodo = (index) => {
  todos.value.splice(index, 1)
}
</script>`

// 生成唯一标识
export const generateUUID = () => uuidv4()

// 文件树右键菜单配置
export interface ContextMenuItem {
  label: string;
  action: string;
  icon?: string;
  disabled?: boolean;
  // 控制菜单项在文件/文件夹上的显示
  showOnFile?: boolean;
  showOnFolder?: boolean;
  divider?: boolean; // 是否在此项后添加分隔线
}

// 获取适用于特定项目类型的菜单项
export const getContextMenuItems = (isDirectory: boolean): ContextMenuItem[] => {
  return FILE_TREE_CONTEXT_MENU.filter(item => {
    if (isDirectory) {
      return item.showOnFolder !== false
    } else {
      return item.showOnFile !== false
    }
  })
}

export const FILE_TREE_CONTEXT_MENU: ContextMenuItem[] = [
  {
    label: '添加到代码上下文',
    action: 'addToContext',
    icon: 'code',
    showOnFile: true,
    showOnFolder: true
  },
  {
    label: '新建文件',
    action: 'newFile',
    icon: 'file-add',
    showOnFile: false,
    showOnFolder: true
  },
  {
    label: '新建文件夹',
    action: 'newFolder',
    icon: 'folder-add',
    showOnFile: false,
    showOnFolder: true,
    divider: true
  },
  {
    label: '重命名',
    action: 'rename',
    icon: 'edit',
    showOnFile: true,
    showOnFolder: true
  },
  {
    label: '删除',
    action: 'delete',
    icon: 'delete',
    showOnFile: true,
    showOnFolder: true
  }
]