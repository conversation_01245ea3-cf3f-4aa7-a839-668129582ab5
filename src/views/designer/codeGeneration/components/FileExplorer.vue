<template>
  <div class="h-full bg-white overflow-auto">
    <div v-if="loading" class="flex justify-center items-center p-6">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-500"></div>
    </div>

    <div v-else-if="error" class="p-4 text-red-500 text-sm bg-red-50 m-3 rounded">
      <div class="flex items-center">
        <GoAlert class="w-4 h-4 mr-2 flex-shrink-0" />
        <span>{{ error }}</span>
      </div>
    </div>

    <div v-else class="p-2">
      <template v-if="rootNode">
        <!-- 显示根节点 -->
        <FileTreeItem
          :item="rootNode"
          :selected-path="selectedPath"
          :auto-expand="true"
          :context-menu-items="FILE_TREE_CONTEXT_MENU"
          @select="handleSelect"
          @context-menu="handleContextMenu"
        />
      </template>
      <template v-else>
        <div class="text-gray-500 text-sm p-4 flex justify-center items-center bg-gray-50 m-2 rounded">
          <GoInfo class="w-4 h-4 mr-2" />
          此模板没有文件
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { GoSync, GoRepo, GoAlert, GoInfo } from 'vue-icons-plus/go'
import FileTreeItem from './FileTreeItem.vue'
import type { FileTreeNode } from '../types'
import { FILE_TREE_CONTEXT_MENU, type ContextMenuItem } from '../config'

const props = defineProps({
  templateName: {
    type: String,
    required: true
  },
  rootPath: {
    type: String,
    default: ''
  },
  fileTreeData: {
    type: Object as () => FileTreeNode | null,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  updateTrigger: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits<{
  (e: 'select', filePath: string, isDirectory: boolean): void;
  (e: 'refresh'): void;
  (e: 'context-menu', action: string, filePath: string, isDirectory: boolean): void;
}>()

const error = ref('')
const rootNode = computed(() => {
  // 保持引用完整性
  return props.fileTreeData ? props.fileTreeData : null
})
const selectedPath = ref('')


// 处理文件/文件夹选择
const handleSelect = (path: string, isDirectory: boolean) => {
  selectedPath.value = path
  emit('select', path, isDirectory)
}

// 处理右键菜单事件
const handleContextMenu = (action: string, path: string, isDirectory: boolean) => {
  emit('context-menu', action, path, isDirectory)
}
</script>

<style scoped>
:deep(.file-tree-item-container) {
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 4px;
}

:deep(.file-tree-item-container:hover) {
  background-color: rgba(79, 70, 229, 0.08);
}

:deep(.selected) {
  background-color: rgba(79, 70, 229, 0.1);
  font-weight: 500;
  color: #4f46e5;
}
</style>