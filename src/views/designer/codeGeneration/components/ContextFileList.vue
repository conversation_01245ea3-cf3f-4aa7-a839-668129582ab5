<template>
  <div class="w-full">
    <div v-if="contextFiles.length === 0" class="text-gray-500 text-center py-4">
      暂无上下文文件
    </div>
    <div v-else class="space-y-2">
      <div class="flex justify-between items-center mb-3">
        <div class="font-medium text-sm text-gray-700">上下文文件 ({{ contextFiles.length }})</div>
        <a-button type="link" size="small" @click="$emit('clear')" class="text-gray-600">
          清除所有
        </a-button>
      </div>
      <div class="max-h-[200px] overflow-auto pr-2">
        <div v-for="(file, index) in contextFiles" :key="index" 
             class="flex items-center justify-between bg-white p-2 rounded-md border border-gray-200 group hover:border-blue-300 transition-colors">
          <div class="flex items-center overflow-hidden">
            <FolderOutlined v-if="file.isDirectory || file.content === '目录'" class="text-amber-500 mr-2 flex-shrink-0" />
            <FileOutlined v-else class="text-gray-500 mr-2 flex-shrink-0" />
            <a-tooltip :title="file.filePath">
              <div class="truncate text-sm">{{ getShortFileName(file.filePath) }}</div>
            </a-tooltip>
            <a-tag v-if="file.isDirectory || file.content === '目录'" class="ml-2 text-xs">文件夹</a-tag>
          </div>
          <div class="flex items-center">
            <a-button 
              type="text" 
              size="small" 
              @click="$emit('preview', file)"
              class="opacity-0 group-hover:opacity-100 transition-opacity"
              :disabled="file.isDirectory || file.content === '目录'"
            >
              <EyeOutlined class="text-gray-500" />
            </a-button>
            <a-button 
              type="text" 
              size="small" 
              @click="$emit('remove', index)"
              class="opacity-0 group-hover:opacity-100 transition-opacity"
              danger
            >
              <CloseOutlined />
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FileOutlined, EyeOutlined, CloseOutlined, FolderOutlined } from '@ant-design/icons-vue'

defineProps<{
  contextFiles: Array<{filePath: string, content: string, isDirectory?: boolean}>
}>()

defineEmits<{
  (e: 'remove', index: number): void
  (e: 'clear'): void
  (e: 'preview', file: {filePath: string, content: string}): void
}>()

// 获取文件短名称（仅显示最后一级路径）
const getShortFileName = (filePath: string) => {
  if (!filePath) return ''
  const parts = filePath.split('/')
  return parts[parts.length - 1]
}
</script> 