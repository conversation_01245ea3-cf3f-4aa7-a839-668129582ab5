<template>
  <div class="flex items-center px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-700 shadow-md text-white">
    <div class="flex-1 flex items-center justify-start">
      <div class="flex items-center space-x-4">
        <div class="flex items-center">
          <span class="text-xl font-bold">AI 代码生成</span>
          <div class="h-4 w-px bg-blue-400 mx-4"></div>
          <div class="flex h-8 rounded-full overflow-hidden border border-blue-400 text-sm bg-blue-700/30">
            <a-radio-group v-model:value="mode" button-style="solid" size="small" class="custom-radio-group flex flex-row min-w-[60px]">
              <a-radio-button value="code" class="custom-radio-btn">
                <span class="flex items-center">
                  <GoCode class="mr-1" />代码
                </span>
              </a-radio-button>
              <a-radio-button value="preview" class="custom-radio-btn">
                <span class="flex items-center">
                  <GoEye class="mr-1" />预览
                </span>
              </a-radio-button>
            </a-radio-group>
          </div>
        </div>
        
        <!-- 选择模板按钮 -->
        <button 
          @click="$emit('selectTemplate')"
          class="px-4 py-1.5 bg-blue-500/20 text-white rounded-full hover:bg-blue-400/30 transition-colors flex items-center group"
        >
          <GoRepo class="mr-2 text-blue-200 group-hover:text-white transition-colors" />
          <span class="text-sm">选择模板</span>
        </button>
        
        <!-- 当前模板显示 -->
        <div v-if="currentTemplate" class="px-3 py-1 bg-blue-500/10 rounded-full text-sm text-blue-100">
          <span>当前模板: {{ currentTemplate }}</span>
        </div>
      </div>
      
      <!-- 额外操作区域 -->
      <div class="flex items-center ">
        <!-- 提示词管理按钮 -->
        <button 
          @click="$emit('openPromptManager')"
          class="px-4 py-1.5 bg-blue-500/20 text-white rounded-full hover:bg-blue-400/30 transition-colors flex items-center group"
        >
          <SettingOutlined class="mr-2 text-blue-200 group-hover:text-white transition-colors" />
          <span class="text-sm">提示词</span>
        </button>
        <slot name="extra"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { GoCode, GoEye, GoRepo } from 'vue-icons-plus/go'
import { SettingOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
  modelValue: string,
  currentTemplate?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'selectTemplate'): void
  (e: 'openPromptManager'): void
}>()

const mode = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})
</script>

<style scoped>
:deep(.custom-radio-group .ant-radio-button-wrapper) {
  background-color: transparent;
  border-color: transparent;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 999px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  height: 100%;
}

:deep(.custom-radio-group .ant-radio-button-wrapper:hover) {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

:deep(.custom-radio-group .ant-radio-button-wrapper-checked) {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: transparent;
  color: white;
  box-shadow: none;
}

:deep(.ant-radio-button-wrapper:not(:first-child)::before) {
  display: none;
}

:deep(.custom-radio-group .ant-radio-button-wrapper:first-child) {
  border-radius: 999px 0 0 999px;
}

:deep(.custom-radio-group .ant-radio-button-wrapper:last-child) {
  border-radius: 0 999px 999px 0;
}
</style> 