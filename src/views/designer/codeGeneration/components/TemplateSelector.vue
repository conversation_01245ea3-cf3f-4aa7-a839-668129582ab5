<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm">
    <div class="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] flex flex-col">
      <div class="p-5 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-indigo-50 to-blue-50">
        <h2 class="text-xl font-semibold text-gray-800">选择模板</h2>
        <button 
          v-if="showCancel"
          @click="$emit('cancel')" 
          class="text-gray-500 hover:text-gray-700 focus:outline-none"
        >
          <CloseOutlined class="w-5 h-5" />
        </button>
      </div>
      
      <!-- 模板列表 -->
      <div class="p-5 overflow-y-auto flex-1">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex justify-center py-12">
          <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-500"></div>
        </div>
        
        <!-- 空状态 -->
        <div v-else-if="!templates.length" class="text-center py-12 text-gray-500">
          <FrownOutlined class="text-3xl mb-3" />
          <p>没有找到可用的模板</p>
        </div>
        
        <!-- 模板卡片列表 -->
        <div v-else class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
          <div 
            v-for="template in templates"
            :key="template.name"
            class="border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors"
            :class="{'border-blue-500 bg-blue-50': selectedTemplate?.name === template.name}"
            @click="selectTemplate(template)"
          >
            <h3 class="text-lg font-medium">{{ template.name }}</h3>
            <p class="text-gray-500 text-sm mt-1">{{ template.description || '暂无描述' }}</p>
            <div class="flex items-center mt-2 text-xs text-gray-400">
              <span v-if="template.version">版本: {{ template.version }}</span>
              <span v-if="template.author" class="ml-4">作者: {{ template.author }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end p-4 border-t border-gray-200">
        <button 
          v-if="showCancel"
          @click="$emit('cancel')" 
          class="px-4 py-2 mr-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
        >
          取消
        </button>
        <button 
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          :disabled="!selectedTemplate || loading"
          @click="confirmSelection"
        >
          确认选择
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { FrownOutlined, CloseOutlined } from '@ant-design/icons-vue'
import type { TemplateItem } from '../types'

defineProps({
  showCancel: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits<{
  (e: 'select', template: TemplateItem): void;
  (e: 'cancel'): void;
}>()

const templates = ref<TemplateItem[]>([])
const selectedTemplate = ref<TemplateItem | null>(null)
const loading = ref(true)
const error = ref('')

onMounted(async () => {
  await loadTemplates()
})

const loadTemplates = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const res = await window.electronAPI.invoke('getAvailableTemplates')
    console.log(res, 'getAvailableTemplates')
    if (res.success && res.templates) {
      templates.value = res.templates
    } else {
      error.value = '获取模板列表失败'
    }
  } catch (e) {
    console.error('加载模板出错:', e)
    error.value = '加载模板出错: ' + ((e as Error).message || '未知错误')
  } finally {
    loading.value = false
  }
}

const selectTemplate = (template: TemplateItem) => {
  selectedTemplate.value = template
}

const confirmSelection = () => {
  if (selectedTemplate.value) {
    emit('select', selectedTemplate.value)
  }
}
</script> 