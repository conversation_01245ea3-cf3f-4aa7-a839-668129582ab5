<template>
  <div class="h-full bg-white">
    <div
      class="flex items-center px-3 py-1.5 bg-white border-b border-gray-200 text-xs text-gray-600"
    >
      <span class="px-2 py-1 rounded bg-gray-200 text-gray-800">预览</span>
      <span class="px-2 py-1 rounded bg-gray-200 text-gray-800 ml-2 cursor-pointer" @click="refreshClick">刷新</span>
    </div>
    <div
      class="h-[calc(100%-32px)] flex items-center justify-center bg-white relative"
      @mouseleave="hideHighlight"
    >
      <a-spin class="absolute left-1/2 top-1/2" :spinning="!previewUrl" />
      <iframe
        v-if="previewUrl"
        ref="previewFrame"
        :src="previewUrl"
        class="w-full h-full border-none"
        frameborder="0"
        sandbox="allow-same-origin allow-scripts"
        @load="setupIframeInit"
      ></iframe>
      <!-- 用于在父窗口中显示元素高亮 -->
      <div
        v-if="showHighlight"
        class="absolute border-2 border-blue-500 bg-orange-100/10 pointer-events-none z-10"
        :style="highlightStyle"
      ></div>

      <!-- 用于在父窗口中显示元素信息 -->
      <div
        v-if="showInfo"
        class="absolute bg-gray-800 border-blue-500 text-white px-2 py-1 rounded text-xs z-20 pointer-events-none"
        :style="infoStyle"
      >
        {{ elementInfo }}
      </div>
    </div>
    <a-modal
      title="修改"
      v-model:open="showChange"
      @ok="handleOk"
    >
      <a-input v-model:value="attrVal.message" placeholder="请输入修改要求" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import ContextMenu from '@imengyu/vue3-context-menu'

defineProps({
  previewUrl: {
    type: String,
    default: ''
  }
})

// 预览URL
const injectUpdateCode = inject('injectUpdateCode')
const attrName = inject('attrName')
// iframe引用
const previewFrame = ref(null)

const attrVal = ref({
  attrValue: '',
  message: ''
})
const showChange = ref(false)

// 高亮状态
const showHighlight = ref(false)
const highlightPos = ref({ left: 0, top: 0, width: 0, height: 0 })
const highlightStyle = computed(() => ({
  left: `${highlightPos.value.left}px`,
  top: `${highlightPos.value.top}px`,
  width: `${highlightPos.value.width}px`,
  height: `${highlightPos.value.height}px`
}))

// 信息提示状态
const showInfo = ref(false)
const infoPos = ref({ left: 0, top: 0 })
const elementInfo = ref('')
const infoStyle = computed(() => ({
  left: `${infoPos.value.left}px`,
  top: `${infoPos.value.top}px`
}))

// 隐藏高亮和信息提示
const hideHighlight = () => {
  showHighlight.value = false
  showInfo.value = false
}

// 处理iframe内的mouseover事件
const handleContainerMouseMove = (event) => {
  // 确保iframe已加载
  if (!previewFrame.value || !previewFrame.value.contentDocument) return

  // 获取鼠标下方的元素（在iframe内部，事件目标就是元素本身）
  const target = event.target

  // 如果没有找到元素或者是body/html元素，则隐藏高亮
  if (!target || target === previewFrame.value.contentDocument.body || target === previewFrame.value.contentDocument.documentElement) {
    hideHighlight()
    return
  }

  // 获取元素的位置和尺寸
  const targetRect = target.getBoundingClientRect()

  // 计算元素相对于父窗口的绝对位置
  const elementRect = {
    left: targetRect.left,
    top: targetRect.top,
    width: targetRect.width,
    height: targetRect.height
  }

  // 显示元素高亮
  showHighlight.value = true
  highlightPos.value = elementRect

  // 获取元素的class列表
  const classList = Array.from(target.classList).join(', ') || '无class'
  const tagName = target.tagName.toLowerCase()
  const id = target.id ? `#${target.id}` : ''

  // 显示元素信息
  showInfo.value = true
  infoPos.value = {
    left: elementRect.left,
    top: elementRect.top - 25
  }
  elementInfo.value = `${tagName}${id} | class: ${classList}`
}

// 处理iframe加载完成事件
const handleIframeLoad = () => {
  console.log('iframe加载完成')
}

// iframe初始化
const setupIframeInit = () => {
  nextTick(() => {
    handleGetElementInfo()
  })
}

const handleOk = () => {
  injectUpdateCode({...attrVal.value, attrName: attrName})
  showChange.value = false
  attrVal.value = {
    message: '',
    attrValue: ''
  }
}

const getDomWithAttr = (event, tar) => {
  // 获取鼠标下方的元素（在iframe内部，事件目标就是元素本身）
  const target = event.target

  // 递归向上查找具有指定属性的元素，并返回属性值
  function findElementWithAttr(element) {
    // 如果已经到达文档根节点或null，则停止查找
    if (!element || element === document.documentElement) {
      return null
    }
    
    // 检查当前元素是否有指定属性
    if (element.hasAttribute(tar)) {
      // 返回元素和属性值
      return {
        element: element,
        value: element.getAttribute(tar)
      }
    }
    
    // 如果当前元素没有指定属性，则继续检查父元素
    return findElementWithAttr(element.parentElement)
  }
  
  // 开始查找
  const result = findElementWithAttr(target)
  
  if (result) {
    console.log('找到具有属性', tar, '的元素:', result.element)
    console.log('属性值为:', result.value)
    attrVal.value = {
      attrValue: result.value,
      message: ''
    }
    showChange.value = true
    return result
  } else {
    console.log('未找到具有属性', tar, '的元素')
    attrVal.value = {
      attrValue: '',
      message: ''
    }
    showChange.value = false
    return null
  }
}

// 右键事件
const contextmenuFunc = (e) => {
  e.preventDefault()

  
  // 获取iframe的位置
  const iframeRect = previewFrame.value.getBoundingClientRect()
  
  // 计算正确的菜单显示位置（iframe的位置 + iframe内部的事件坐标）
  const x = iframeRect.left + e.clientX
  const y = iframeRect.top + e.clientY

  // 创建遮罩层
  const mask = document.createElement('div')
  mask.style.position = 'fixed'
  mask.style.top = '0'
  mask.style.left = '0'
  mask.style.width = '100%'
  mask.style.height = '100%'
  mask.style.backgroundColor = 'rgba(0, 0, 0, 0.3)'
  mask.style.zIndex = '99'
  document.body.appendChild(mask)

  ContextMenu.showContextMenu({
    theme: 'flat',
    x: x,
    y: y,
    items: [
      {
        label: '修改',
        onClick: () => {
          getDomWithAttr(e, attrName)
          document.body.removeChild(mask) // 点击后移除遮罩
        }
      }
    ],
    // 添加关闭回调，移除遮罩
    onClose: () => {
      if (document.body.contains(mask)) {
        document.body.removeChild(mask)
      }
    }
  })
}

const handleGetElementInfo = async () => {

  const iframeDoc = previewFrame.value?.contentDocument || previewFrame.value?.contentWindow?.document
  console.log('==================>iframeDoc', iframeDoc)
  iframeDoc.addEventListener('mouseover', handleContainerMouseMove)
  iframeDoc.addEventListener('mouseup', handleMouseUp)
  iframeDoc.addEventListener('contextmenu', contextmenuFunc)
}

const handleMouseUp = () => {
  // handleGetElementInfo()
  // console.log(getCssPath(selectedElement))
}

const refreshClick = () => {
  if (previewFrame.value && previewFrame.value.contentWindow) {
    previewFrame.value.contentWindow.location.reload()
  }
}

onMounted(() => {
  // 监听iframe加载完成事件
  if (previewFrame.value) {
    previewFrame.value.addEventListener('load', handleIframeLoad)
  }
})

onUnmounted(() => {
  // 移除事件监听
  if (previewFrame.value) {
    previewFrame.value.removeEventListener('load', handleIframeLoad)

    // 移除iframe内部的事件监听
    const iframeDoc = previewFrame.value?.contentDocument || previewFrame.value?.contentWindow?.document
    if (iframeDoc) {
      iframeDoc.removeEventListener('mouseover', handleContainerMouseMove)
      iframeDoc.removeEventListener('mouseup', handleMouseUp)
      iframeDoc.removeEventListener('contextmenu', contextmenuFunc)
    }
  }
})
</script>
