<template>
  <div class="h-full bg-white flex flex-col">
    <!-- 编辑器和预览区域 -->
    <Splitpanes class="default-theme flex-1">
      <Pane
        :size="50"
        :min-size="20"
        :max-size="80"
      >
        <PreviewPanel :previewUrl="previewUrl" />
      </Pane>
      <Pane
        :size="50"
        :min-size="20"
        :max-size="80"
        class="flex flex-col"
      >
        <!-- 编辑器标签页 - 只在编辑器上方 -->
        <div class="editor-tabs flex bg-gray-100 border-b overflow-x-auto hide-scrollbar">
          <div v-if="openedFiles.length === 0" class="text-xs text-gray-500 p-2">
            没有打开的文件
          </div>
          <div 
            v-for="(file, index) in openedFiles" 
            :key="file.id"
            class="tab flex items-center px-3 py-1.5 text-xs cursor-pointer relative group"
            :class="{
              'bg-white border-t border-l border-r border-gray-200 text-blue-600 font-medium': activeTabIndex === index,
              'text-gray-600 hover:bg-gray-200': activeTabIndex !== index,
              'tab-unsaved': file.hasChanges
            }"
            @click="switchTab(index)"
          >
            <div class="flex items-center max-w-[160px] overflow-hidden">
              <span class="truncate">{{ file.fileName }}</span>
              <span v-if="file.hasChanges" class="ml-1 text-blue-600 font-bold">●</span>
            </div>
            <button 
              class="close-btn ml-2 opacity-0 group-hover:opacity-100 hover:bg-gray-200 rounded-full p-0.5 flex items-center justify-center"
              :class="{'opacity-100': activeTabIndex === index}"
              @click.stop="attemptCloseTab(index)"
            >
              <CloseOutlined class="text-gray-500 text-xs" />
            </button>
          </div>
        </div>

        <!-- 编辑器区域 -->
        <div class="flex-1">
          <div v-if="activeFile" class="h-full">
            <CodeEditor
              :initialCode="activeFile.content"
              :initialLanguage="getLanguageFromFileName(activeFile.fileName)"
              :readOnly="false"
              class="h-full"
              theme="vs-light"
              @code-change="handleCodeChange"
              @save="handleSave"
              :key="activeFile.id"
            />
          </div>
          <div v-else class="h-full flex items-center justify-center text-gray-500 bg-gray-50">
            <div class="text-center">
              <FileOutlined class="text-4xl mb-2" />
              <p>请从左侧文件树选择一个文件</p>
            </div>
          </div>
        </div>
      </Pane>
    </Splitpanes>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { Splitpanes, Pane } from 'splitpanes'
import { CloseOutlined, FileOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import PreviewPanel from './PreviewPanel.vue'
import { CodeEditor } from '@/components'
import { readFileContent } from '@/apis/aiCodeingIpc'

interface OpenedFile {
  id: string
  fileName: string
  filePath: string
  content: string
  hasChanges: boolean
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

// 获取文件语言类型
const getLanguageFromFileName = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase() || ''
  const languageMap: Record<string, string> = {
    'js': 'javascript',
    'ts': 'typescript',
    'html': 'html',
    'css': 'css',
    'scss': 'scss',
    'json': 'json',
    'vue': 'vue',
    'java': 'java',
    'py': 'python',
    'cs': 'csharp',
    'cpp': 'cpp',
    'c': 'c',
    'go': 'go',
    'rb': 'ruby',
    'php': 'php'
  }
  return languageMap[ext] || 'plaintext'
}

const props = defineProps<{
  modelValue: string
  previewUrl: string
  currentFilePath?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'save', filePath: string, content: string): void
  (e: 'file-closed', filePath: string): void
  (e: 'file-opened', filePath: string): void
  (e: 'active-file-changed', filePath: string): void
}>()

// 打开的文件列表
const openedFiles = ref<OpenedFile[]>([])
// 当前活动标签索引
const activeTabIndex = ref(-1)
// 计算活动文件
const activeFile = computed(() => {
  return activeTabIndex.value >= 0 ? openedFiles.value[activeTabIndex.value] : null
})

// 当前编辑器内容（用于同步不同标签间的内容）
const currentEditorContent = ref('')
// 跟踪已打开的文件路径，避免重复打开
const openedFilePaths = ref(new Set<string>())
// 最近关闭的文件路径，用于防止监听器重新打开刚刚关闭的文件
const recentlyClosedPaths = ref(new Set<string>())

// 打开文件
const openFile = (filePath: string, content: string) => {
  // 如果路径为空或者是目录，不处理
  if (!filePath || filePath.endsWith('/') || selectedFileIsDirectory(filePath)) {
    return
  }
  
  // 如果文件最近被关闭过，从最近关闭列表中移除
  if (recentlyClosedPaths.value.has(filePath)) {
    recentlyClosedPaths.value.delete(filePath)
  }

  // 如果文件已经打开，切换到对应标签
  if (openedFilePaths.value.has(filePath)) {
    const existingIndex = openedFiles.value.findIndex(file => file.filePath === filePath)
    if (existingIndex >= 0) {
      switchTab(existingIndex)
      // 更新已打开文件的内容
      openedFiles.value[existingIndex].content = content
      currentEditorContent.value = content
      return
    }
  }
  
  const fileName = filePath.split('/').pop() || 'untitled'
  
  // 保存当前内容状态
  if (activeTabIndex.value >= 0 && activeTabIndex.value < openedFiles.value.length) {
    openedFiles.value[activeTabIndex.value].content = currentEditorContent.value
  }
  
  // 创建新标签
  const newFile = {
    id: generateId(),
    fileName,
    filePath,
    content,
    hasChanges: false
  }
  
  // 添加到已打开文件列表和路径集合
  openedFiles.value.push(newFile)
  openedFilePaths.value.add(filePath)
  
  // 更新当前编辑器内容状态
  currentEditorContent.value = content
  
  // 切换到新标签
  activeTabIndex.value = openedFiles.value.length - 1
  
  // 触发内容更新
  emit('update:modelValue', content)
  
  // 通知父组件文件已打开
  emit('file-opened', filePath)
}

// 更新或打开文件
const updateOrOpenFile = (filePath: string, content: string) => {
  if (!filePath) return
  
  // 如果文件最近被关闭，不打开
  if (recentlyClosedPaths.value.has(filePath)) {
    return
  }
  
  const existingIndex = openedFiles.value.findIndex(file => file.filePath === filePath)
  
  if (existingIndex >= 0) {
    // 更新已存在的文件
    openedFiles.value[existingIndex].content = content
    openedFiles.value[existingIndex].hasChanges = false
    
    // 如果不是当前活动标签，则切换到此标签
    if (activeTabIndex.value !== existingIndex) {
      // 保存当前标签内容
      if (activeTabIndex.value >= 0 && activeTabIndex.value < openedFiles.value.length) {
        openedFiles.value[activeTabIndex.value].content = currentEditorContent.value
      }
      
      // 切换标签
      activeTabIndex.value = existingIndex
      
      // 更新当前编辑器内容状态
      currentEditorContent.value = content
    } else {
      // 如果是当前活动标签，则直接更新当前编辑器内容状态
      currentEditorContent.value = content
    }
    
    // 触发内容更新
    emit('update:modelValue', content)
  } else {
    // 打开新文件
    openFile(filePath, content)
  }
}

// 监听modelValue变化，自动打开文件
watch(() => props.modelValue, (newValue, oldValue) => {
  if (newValue === oldValue) return // 避免不必要的处理
  
  console.log('【CodeEditor】modelValue changed:', newValue ? newValue.substring(0, 30) + '...' : 'empty')
  console.log('【CodeEditor】currentFilePath:', props.currentFilePath)
  console.log('【CodeEditor】是否在最近关闭列表中:', recentlyClosedPaths.value.has(props.currentFilePath || ''))
  
  if (newValue && props.currentFilePath && !recentlyClosedPaths.value.has(props.currentFilePath)) {
    // 确认这不是一个目录
    if (!selectedFileIsDirectory(props.currentFilePath)) {
      currentEditorContent.value = newValue
      updateOrOpenFile(props.currentFilePath, newValue)
    }
  }
}, { immediate: true })

// 监听currentFilePath变化，打开新文件
watch(() => props.currentFilePath, (newPath, oldPath) => {
  if (newPath === oldPath) return // 避免不必要的处理
  
  console.log('【CodeEditor】currentFilePath changed:', newPath)
  console.log('【CodeEditor】是否在最近关闭列表中:', recentlyClosedPaths.value.has(newPath || ''))
  
  if (newPath && !recentlyClosedPaths.value.has(newPath)) {
    // 检查是否是目录，如果是目录则不打开
    if (newPath.endsWith('/') || selectedFileIsDirectory(newPath)) {
      console.log('【CodeEditor】检测到目录，不打开:', newPath)
      return
    }
    
    console.log('【CodeEditor】开始打开或切换到文件:', newPath)
    
    // 检查文件是否已打开，如果已打开则切换到该标签
    const existingIndex = openedFiles.value.findIndex(file => file.filePath === newPath)
    if (existingIndex >= 0) {
      console.log('【CodeEditor】文件已打开，切换到标签:', existingIndex)
      switchTab(existingIndex)
    } else if (props.modelValue) {
      console.log('【CodeEditor】文件未打开，创建新标签')
      openFile(newPath, props.modelValue)
    }
  }
})

// 判断是否为目录的函数
const selectedFileIsDirectory = (path: string) => {
  // 这里可能需要根据您的项目实际情况调整判断逻辑
  // 一个简单的方法是检查路径是否以斜杠结尾
  // 或者检查是否没有文件扩展名
  return path.endsWith('/') || !path.includes('.')
}

// 切换标签
const switchTab = (index: number) => {
  if (index >= 0 && index < openedFiles.value.length) {
    // 保存当前标签的内容状态，以防止切换时丢失未保存的更改
    if (activeTabIndex.value >= 0 && activeTabIndex.value < openedFiles.value.length) {
      // 将当前编辑器内容保存到当前标签对象中
      const currentFile = openedFiles.value[activeTabIndex.value]
      currentFile.content = currentEditorContent.value
      
      // 检查文件变更状态
      checkFileChangeStatus(currentFile, currentEditorContent.value)
    }
    
    // 切换到新标签
    activeTabIndex.value = index
    const file = openedFiles.value[index]
    
    // 更新当前编辑器内容状态
    currentEditorContent.value = file.content
    
    // 更新编辑器内容为当前选中标签的内容
    emit('update:modelValue', file.content)
    
    // 通知父组件当前活动文件已更改
    emit('active-file-changed', file.filePath)
  }
}

// 尝试关闭标签
const attemptCloseTab = (index: number) => {
  if (index < 0 || index >= openedFiles.value.length) return
  
  console.log('【CodeEditor】尝试关闭标签:', index, openedFiles.value[index].filePath)
  
  const file = openedFiles.value[index]
  
  if (file.hasChanges) {
    // 有未保存的更改，使用确认框
    
    // 使用ant-design-vue的确认框
    import('ant-design-vue').then(({ Modal }) => {
      Modal.confirm({
        title: '文件未保存',
        content: `${file.fileName} 有未保存的更改，确定要关闭吗？`,
        okText: '不保存并关闭',
        cancelText: '取消',
        okType: 'danger',
        onOk() {
          // 直接关闭标签
          console.log('【CodeEditor】确认关闭未保存的标签')
          closeTab(index)
        },
        onCancel() {
          // 什么都不做，保持标签打开
          console.log('【CodeEditor】取消关闭标签')
        },
        // 添加自定义按钮
        class: 'custom-modal',
        icon: h(ExclamationCircleOutlined, { style: { color: '#faad14' } }),
        // 使用标准的slots代替extra
        okButtonProps: { danger: true }
      })
    })
  } else {
    // 没有未保存的更改，直接关闭
    console.log('【CodeEditor】直接关闭标签，无未保存内容')
    closeTab(index)
  }
}

// 关闭标签
const closeTab = (index: number) => {
  if (index < 0 || index >= openedFiles.value.length) return
  
  // 获取将要关闭的文件路径
  const filePath = openedFiles.value[index].filePath
  
  console.log('【CodeEditor】关闭标签，文件路径:', filePath)
  
  // 如果关闭的是当前活动标签，则先保存其内容
  if (index === activeTabIndex.value) {
    openedFiles.value[index].content = currentEditorContent.value
  }
  
  // 从已打开路径集合中移除
  openedFilePaths.value.delete(filePath)
  
  // 添加到最近关闭路径集合，设置一个超时后删除
  recentlyClosedPaths.value.add(filePath)
  console.log('【CodeEditor】已添加到最近关闭列表:', Array.from(recentlyClosedPaths.value))
  
  // 5秒后从最近关闭集合中移除
  setTimeout(() => {
    console.log('【CodeEditor】从最近关闭列表中移除:', filePath)
    recentlyClosedPaths.value.delete(filePath)
  }, 5000)
  
  // 通知父组件文件已关闭
  emit('file-closed', filePath)
  
  // 从打开文件列表中移除
  openedFiles.value.splice(index, 1)
  
  // 调整当前活动标签
  let newActiveIndex = activeTabIndex.value
  
  // 如果关闭的是当前活动标签或者其之前的标签
  if (index <= activeTabIndex.value) {
    newActiveIndex = activeTabIndex.value - 1
    
    // 确保不会是负数
    if (newActiveIndex < 0 && openedFiles.value.length > 0) {
      newActiveIndex = 0
    } else if (openedFiles.value.length === 0) {
      newActiveIndex = -1
    }
  }
  
  console.log('【CodeEditor】新的活动标签索引:', newActiveIndex)
  activeTabIndex.value = newActiveIndex
  
  // 如果关闭后没有标签了，清空编辑区内容
  if (openedFiles.value.length === 0) {
    console.log('【CodeEditor】关闭后没有标签了，清空内容')
    currentEditorContent.value = ''
    emit('update:modelValue', '')
  } else if (activeTabIndex.value >= 0) {
    // 更新当前编辑内容为新活动标签的内容
    const activeFile = openedFiles.value[activeTabIndex.value]
    console.log('【CodeEditor】切换到新活动标签:', activeFile.filePath)
    currentEditorContent.value = activeFile.content
    emit('update:modelValue', activeFile.content)
    // 通知父组件当前活动文件已更改
    emit('active-file-changed', activeFile.filePath)
  }
}

// 处理代码变更
const handleCodeChange = (value: string) => {
  // 更新当前编辑器内容状态
  currentEditorContent.value = value
  
  if (activeTabIndex.value >= 0) {
    const file = openedFiles.value[activeTabIndex.value]
    
    // 更新文件内容
    file.content = value
    
    // 判断是否需要调用IPC来获取文件的真实内容并比较
    checkFileChangeStatus(file, value)
  }
}

// 检查文件是否有变更
const checkFileChangeStatus = async (file: OpenedFile, currentContent: string) => {
  try {
    // 调用IPC获取文件真实内容
    const result = await readFileContent(file.filePath)
    
    if (result.success && result.content !== undefined) {
      // 比较编辑器内容与文件真实内容
      file.hasChanges = currentContent !== result.content
    }
  } catch (error) {
    console.error('检查文件变更状态失败:', error)
  }
}

// 处理保存
const handleSave = (value: string) => {
  if (activeTabIndex.value >= 0) {
    const file = openedFiles.value[activeTabIndex.value]
    // 保存时更新内容并清除未保存状态
    file.content = value
    currentEditorContent.value = value
    file.hasChanges = false // 立即将状态设为已保存
    emit('update:modelValue', value)
    emit('save', file.filePath, value)
    
    // 保存成功后延迟一小段时间再次检查文件状态
    // 这是为了确保文件系统已完成写入操作
    setTimeout(() => {
      checkFileChangeStatus(file, value)
    }, 300)
  }
}
</script>

<style scoped>
.editor-tabs {
  height: 36px;
}

.tab {
  height: 36px;
  border-top: 2px solid transparent;
}

.tab.active {
  border-top: 2px solid #1890ff;
}

.tab-unsaved::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #1890ff;
  opacity: 0.6;
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* 确保tab在hover时显示关闭按钮 */
.tab {
  position: relative;
}

.tab:hover .close-btn {
  opacity: 1;
}
</style>
