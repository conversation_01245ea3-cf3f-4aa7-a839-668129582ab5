<template>
  <div class="file-tree-item">
    <!-- 重命名输入框 -->
    <div v-if="isRenaming" class="py-1.5 px-3 mb-1">
      <input
        ref="renameInput"
        v-model="newName"
        class="w-full border rounded px-2 py-1 text-sm"
        :placeholder="`重命名 ${item.name}...`"
        @keyup.enter="confirmRename"
        @keyup.esc="cancelRename"
        @blur="cancelRename"
      />
    </div>

    <!-- 正常显示 -->
    <div
      v-else
      class="file-tree-item-container py-1.5 px-3 rounded-md flex items-center text-sm mb-1"
      :class="{ 'selected': item.path === selectedPath }"
      @click="handleItemClick"
      @contextmenu="handleContextMenu"
    >
      <!-- 文件夹图标 -->
      <GoFileDirectory v-if="item.isDirectory" class="w-4 h-4 mr-2 text-amber-500" />

      <!-- 文件图标根据扩展名 -->
      <template v-else>
        <!-- Vue文件 -->
        <div v-if="getFileExtension(item.name) === 'vue'" class="w-4 h-4 mr-2 text-green-500">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19.8 6.801h-4.044L12 1.2 8.244 6.801H4.2L12 21.6 19.8 6.801zm-7.8 12.072L4.2 6.801l4.312.012L12 2.49l3.48 4.323L19.8 6.801 12 18.873z"/></svg>
        </div>
        <!-- JS文件 -->
        <div v-else-if="getFileExtension(item.name) === 'js'" class="w-4 h-4 mr-2 text-yellow-500">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3 3h18v18H3V3zm16.525 13.707c-.131-.821-.666-1.511-2.252-2.155-.552-.259-1.165-.438-1.349-.854-.068-.248-.078-.382-.034-.529.113-.484.687-.629 1.137-.495.293.09.563.315.732.676.775-.507.775-.507 1.316-.844-.203-.314-.304-.451-.439-.586-.473-.528-1.103-.798-2.126-.775l-.528.067c-.507.124-.991.395-1.283.754-.855.968-.608 2.655.427 3.354 1.023.765 2.521.933 2.712 1.653.18.878-.652 1.159-1.475 1.058-.607-.136-.945-.439-1.316-1.002l-1.372.788c.157.359.337.517.607.832 1.305 1.316 4.568 1.249 5.153-.754.021-.067.176-.528.056-1.237l.034.049zm-6.737-5.434h-1.686c0 1.453-.007 2.898-.007 4.354 0 .924.047 1.772-.104 2.033-.247.517-.886.451-1.175.359-.297-.146-.448-.349-.623-.641-.047-.078-.082-.146-.095-.146l-1.368.844c.229.473.563.879.994 1.137.641.383 1.502.507 2.404.305.588-.17 1.095-.519 1.358-1.059.384-.697.302-1.553.299-2.509.008-1.541 0-3.083 0-4.635l.003-.042z"/></svg>
        </div>
        <!-- TS文件 -->
        <div v-else-if="getFileExtension(item.name) === 'ts'" class="w-4 h-4 mr-2 text-blue-500">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3 3h18v18H3V3zm10.169 10.68h3.744V12.3h-9.806v1.38h3.744v6.796h2.318V13.68zM13.01 8.808c.528.356 1.333.493 2.079.328a2.63 2.63 0 0 0 1.591-1.052c.099-.144.275-.41.332-.638h-2.076a1.291 1.291 0 0 1-.858.374c-.94 0-1.163-.893-1.163-1.207 0-.415.275-1.53 1.577-1.53.858 0 1.134.638 1.134 1.207v.249h2.076v-.381c0-1.105-.638-2.991-3.21-2.991-1.958 0-3.516 1.349-3.516 3.478 0 2.21 1.53 3.651 3.545 3.651l-.001-.015c.502-.008.957-.117 1.359-.329v-.908c-.293.193-.695.306-1.165.306-1.232 0-1.577-1.029-1.695-1.588l.99.046z"/></svg>
        </div>
        <!-- JSON文件 -->
        <div v-else-if="getFileExtension(item.name) === 'json'" class="w-4 h-4 mr-2 text-purple-500">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M5 3h2v2H5v5a2 2 0 0 1-2 2 2 2 0 0 1 2 2v5h2v2H5c-1.657 0-3-1.343-3-3v-4a2 2 0 0 0-2-2 2 2 0 0 0 2-2V6c0-1.657 1.343-3 3-3zm14 0c1.657 0 3 1.343 3 3v4a2 2 0 0 0 2 2 2 2 0 0 0-2 2v4c0 1.657-1.343 3-3 3h-2v-2h2v-5a2 2 0 0 1 2-2 2 2 0 0 1-2-2V5h-2V3h2zM12 6a6 6 0 1 1 0 12 6 6 0 0 1 0-12zm0 12c3.314 0 6-2.686 6-6s-2.686-6-6-6-6 2.686-6 6 2.686 6 6 6zm-1-7h2v2h-2v2h-2v-2H9v-2h2V9h2v2z"/></svg>
        </div>
        <!-- 默认文件图标 -->
        <GoFile v-else class="w-4 h-4 mr-2 text-indigo-500" />
      </template>

      <span class="truncate">{{ item.name }}</span>

      <!-- 展开/折叠箭头 -->
      <GoChevronRight
        v-if="item.isDirectory"
        class="w-4 h-4 ml-auto transition-transform duration-200"
        :class="{ 'transform rotate-90': isExpanded }"
      />
    </div>

    <!-- 递归显示子项 -->
    <div
      v-if="item.isDirectory && isExpanded"
      class="pl-4 mt-1"
    >
      <div v-if="loading" class="py-2 flex justify-center">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500"></div>
      </div>

      <template v-else>
        <!-- 新建文件输入框 -->
        <div v-if="isCreatingFile" class="py-1.5 px-3 mb-1">
          <input
            ref="newFileInput"
            v-model="newFileName"
            class="w-full border rounded px-2 py-1 text-sm"
            placeholder="输入文件名..."
            @keyup.enter="confirmCreateFile"
            @keyup.esc="cancelCreate"
            @blur="cancelCreate"
          />
        </div>

        <!-- 新建文件夹输入框 -->
        <div v-else-if="isCreatingFolder" class="py-1.5 px-3 mb-1">
          <input
            ref="newFolderInput"
            v-model="newFolderName"
            class="w-full border rounded px-2 py-1 text-sm"
            placeholder="输入文件夹名..."
            @keyup.enter="confirmCreateFolder"
            @keyup.esc="cancelCreate"
            @blur="cancelCreate"
          />
        </div>

        <!-- 子项列表 -->
        <div v-if="displayChildren && displayChildren.length > 0">
          <div v-for="child in displayChildren" :key="child.path" class="mb-1">
            <FileTreeItem
              :item="child"
              :selected-path="selectedPath"
              :context-menu-items="contextMenuItems"
              @select="handleChildSelect"
              @context-menu="handleChildContextMenu"
            />
          </div>
        </div>

        <div v-else-if="!isCreatingFile && !isCreatingFolder" class="py-1 text-xs text-gray-500 italic">
          文件夹为空
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import type { FileTreeNode } from '../types'
import { GoFileDirectory, GoFile, GoChevronRight } from 'vue-icons-plus/go'
import ContextMenu from '@imengyu/vue3-context-menu'
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
import { getContextMenuItems, type ContextMenuItem } from '../config'

const props = defineProps({
  item: {
    type: Object as () => FileTreeNode,
    required: true
  },
  selectedPath: {
    type: String,
    default: ''
  },
  autoExpand: {
    type: Boolean,
    default: false
  },
  contextMenuItems: {
    type: Array as () => ContextMenuItem[],
    default: () => []
  }
})

const emit = defineEmits<{
  (e: 'select', filePath: string, isDirectory: boolean): void
  (e: 'context-menu', action: string, filePath: string, isDirectory: boolean): void
}>()

const isExpanded = ref(false)
const loading = ref(false)
const displayChildren = computed(() => {
  if (!props.item.children) return []
  return Array.isArray(props.item.children) ? props.item.children : []
})

// 内联编辑状态
const isCreatingFile = ref(false)
const isCreatingFolder = ref(false)
const isRenaming = ref(false)
const newFileName = ref('')
const newFolderName = ref('')
const newName = ref('')
const newFileInput = ref<HTMLInputElement | null>(null)
const newFolderInput = ref<HTMLInputElement | null>(null)
const renameInput = ref<HTMLInputElement | null>(null)

// 获取文件扩展名
const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || ''
}

onMounted(() => {
  // 如果设置了自动展开，或者是根节点，则自动展开
  if (props.autoExpand || isRootNode()) {
    expandFolder()
  }
})

// 判断是否为根节点
const isRootNode = () => {
  // 简单判断：如果没有斜杠或只有一个斜杠则可能是根节点
  const path = props.item.path
  return !path.includes('/') || path.split('/').length <= 3
}

// 处理点击事件
const handleItemClick = async () => {
  const isDirectory = props.item.isDirectory || false

  // 先通知父组件选择了此项
  emit('select', props.item.path, isDirectory)

  // 如果是文件夹，处理展开/折叠
  if (isDirectory) {
    if (isExpanded.value) {
      // 已展开，折叠
      isExpanded.value = false
    } else {
      // 未展开，展开
      expandFolder()
    }
  }
}

// 展开文件夹
const expandFolder = async () => {
  if (!props.item.isDirectory) return

  isExpanded.value = true

  // 只有在没有子节点数据时才加载
  if (!props.item.children || props.item.children.length === 0) {
    loading.value = true

    try {
      // 从API获取数据
      const result = await window.electronAPI.invoke('getFileTree', props.item.path) as { success: boolean; data: FileTreeNode[]; error?: string }

      if (result.error) {
        console.error('加载子目录失败:', result.error)
      }
    } catch (e) {
      console.error('加载子文件夹内容出错:', e)
    } finally {
      loading.value = false
    }
  }
}

// 处理子项选择事件
const handleChildSelect = (path: string, isDirectory: boolean) => {
  emit('select', path, isDirectory)
}

// 处理子项右键菜单事件
const handleChildContextMenu = (action: string, path: string, isDirectory: boolean) => {
  // 直接传递给父组件
  emit('context-menu', action, path, isDirectory)
}

// 获取菜单项对应的图标
const getMenuIcon = (iconName?: string) => {
  if (!iconName) return ''

  switch (iconName) {
    case 'code': return '<svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16"><path d="M4.72 3.22a.75.75 0 0 1 1.06 1.06L2.56 8l3.22 3.72a.75.75 0 1 1-1.12 1.06l-3.5-4a.75.75 0 0 1 0-1.06l3.5-4a.75.75 0 0 1 .06-.04Zm7.56 0a.75.75 0 0 1 .06.04l3.5 4a.75.75 0 0 1 0 1.06l-3.5 4a.75.75 0 1 1-1.12-1.06L14.44 8l-3.22-3.72a.75.75 0 0 1 1.06-1.06Z"></path></svg>'
    case 'file-add': return '<svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16"><path d="M2 1.75C2 .784 2.784 0 3.75 0h6.586c.464 0 .909.184 1.237.513l2.914 2.914c.329.328.513.773.513 1.237v9.586A1.75 1.75 0 0 1 13.25 16h-9.5A1.75 1.75 0 0 1 2 14.25Zm1.75-.25a.25.25 0 0 0-.25.25v12.5c0 .138.112.25.25.25h9.5a.25.25 0 0 0 .25-.25V4.664a.25.25 0 0 0-.073-.177l-2.914-2.914a.25.25 0 0 0-.177-.073Zm7 9.25a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5a.75.75 0 0 1 .75-.75Zm-1.25-3.75a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 0 1.5h-1.5a.75.75 0 0 1-.75-.75ZM8 8.75a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5a.75.75 0 0 1 .75.75ZM6.75 7a.75.75 0 0 1 0-1.5h1.5a.75.75 0 0 1 0 1.5Z"></path></svg>'
    case 'folder-add': return '<svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16"><path d="M.513 1.513A1.75 1.75 0 0 1 1.75 1h3.5c.55 0 1.07.26 1.4.7l.9 1.2a.25.25 0 0 0 .2.1H13a1 1 0 0 1 1 1v.5H2.75a.75.75 0 0 0 0 1.5h11.978a1 1 0 0 1 .997 1.1L15.1 12.9A1.75 1.75 0 0 1 13.35 14H2.75A1.75 1.75 0 0 1 1 12.25V2.75c0-.464.184-.91.513-1.237ZM12.05 7.5H9.5v2.55a.75.75 0 0 1-1.5 0V7.5H5.45a.75.75 0 0 1 0-1.5H8V3.45a.75.75 0 0 1 1.5 0V6h2.55a.75.75 0 0 1 0 1.5Z"></path></svg>'
    case 'edit': return '<svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16"><path d="M11.013 1.427a1.75 1.75 0 0 1 2.474 0l1.086 1.086a1.75 1.75 0 0 1 0 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 0 1-.927-.928l.929-3.25c.081-.286.235-.547.445-.758l8.61-8.61Zm.176 4.823L9.75 4.81l-6.286 6.287a.253.253 0 0 0-.064.108l-.558 1.953 1.953-.558a.253.253 0 0 0 .108-.064Zm1.238-3.763a.25.25 0 0 0-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 0 0 0-.354Z"></path></svg>'
    case 'delete': return '<svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16"><path d="M11 1.75V3h2.25a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1 0-1.5H5V1.75C5 .784 5.784 0 6.75 0h2.5C10.216 0 11 .784 11 1.75ZM4.496 6.675l.66 6.6a.25.25 0 0 0 .249.225h5.19a.25.25 0 0 0 .249-.225l.66-6.6a.75.75 0 0 1 1.492.149l-.66 6.6A1.748 1.748 0 0 1 10.595 15h-5.19a1.75 1.75 0 0 1-1.741-1.575l-.66-6.6a.75.75 0 1 1 1.492-.15ZM6.5 1.75V3h3V1.75a.25.25 0 0 0-.25-.25h-2.5a.25.25 0 0 0-.25.25Z"></path></svg>'
    default: return ''
  }
}

// 处理右键菜单
const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()

  const isDirectory = props.item.isDirectory || false

  // 如果没有菜单项，则不显示右键菜单
  if (!props.contextMenuItems || props.contextMenuItems.length === 0) return

  // 根据文件/文件夹类型过滤菜单项
  const filteredItems = getContextMenuItems(isDirectory)

  // 将配置的菜单项转换为右键菜单需要的格式
  const menuItems = filteredItems.map(item => ({
    label: item.label,
    disabled: item.disabled,
    icon: getMenuIcon(item.icon),
    divided: item.divider,
    onClick: () => {
      // 根据不同的操作执行不同的处理
      switch (item.action) {
        case 'newFile':
          if (isDirectory) {
            startCreateFile()
          }
          break
        case 'newFolder':
          if (isDirectory) {
            startCreateFolder()
          }
          break
        case 'rename':
          startRename()
          break
        default:
          // 其他操作传递给父组件处理
          emit('context-menu', item.action, props.item.path, isDirectory)
          break
      }
    }
  }))

  ContextMenu.showContextMenu({
    x: event.clientX,
    y: event.clientY,
    items: menuItems,
    theme: 'default',
    minWidth: 180,
    zIndex: 3000,
    customClass: 'file-tree-context-menu'
  })
}

// 开始创建文件
const startCreateFile = () => {
  // 确保是文件夹
  if (!props.item.isDirectory) return

  // 重置状态
  isCreatingFile.value = true
  isCreatingFolder.value = false
  isRenaming.value = false
  newFileName.value = ''

  // 展开文件夹
  isExpanded.value = true

  // 等待DOM更新后聚焦输入框
  setTimeout(() => {
    if (newFileInput.value) {
      newFileInput.value.focus()
    }
  }, 50)
}

// 开始创建文件夹
const startCreateFolder = () => {
  // 确保是文件夹
  if (!props.item.isDirectory) return

  // 重置状态
  isCreatingFile.value = false
  isCreatingFolder.value = true
  isRenaming.value = false
  newFolderName.value = ''

  // 展开文件夹
  isExpanded.value = true

  // 等待DOM更新后聚焦输入框
  setTimeout(() => {
    if (newFolderInput.value) {
      newFolderInput.value.focus()
    }
  }, 50)
}

// 开始重命名
const startRename = () => {
  // 重置状态
  isCreatingFile.value = false
  isCreatingFolder.value = false
  isRenaming.value = true
  newName.value = props.item.name

  // 等待DOM更新后聚焦输入框
  setTimeout(() => {
    if (renameInput.value) {
      renameInput.value.focus()
    }
  }, 50)
}

// 确认创建文件
const confirmCreateFile = async () => {
  if (!newFileName.value.trim()) {
    cancelCreate()
    return
  }

  try {
    // 构建完整文件路径
    const filePath = `${props.item.path}/${newFileName.value.trim()}`

    // 调用Electron API创建文件
    const result = await window.electronAPI.invoke('createFile', filePath, '') as { success: boolean; error?: string }

    if (result.success) {
      // 刷新父组件
      emit('context-menu', 'refresh', props.item.path, true)
    }
  } catch (error) {
    console.error('创建文件出错:', error)
  } finally {
    cancelCreate()
  }
}

// 确认创建文件夹
const confirmCreateFolder = async () => {
  if (!newFolderName.value.trim()) {
    cancelCreate()
    return
  }

  try {
    // 构建完整文件夹路径
    const folderPath = `${props.item.path}/${newFolderName.value.trim()}`

    // 调用Electron API创建文件夹
    const result = await window.electronAPI.invoke('createFolder', folderPath) as { success: boolean; error?: string }

    if (result.success) {
      // 刷新父组件
      emit('context-menu', 'refresh', props.item.path, true)
    }
  } catch (error) {
    console.error('创建文件夹出错:', error)
  } finally {
    cancelCreate()
  }
}

// 确认重命名
const confirmRename = async () => {
  if (!newName.value.trim() || newName.value.trim() === props.item.name) {
    cancelRename()
    return
  }

  try {
    // 构建新路径
    const parentPath = props.item.path.substring(0, props.item.path.lastIndexOf('/'))
    const newPath = `${parentPath}/${newName.value.trim()}`

    // 调用Electron API重命名
    const result = await window.electronAPI.invoke('rename', props.item.path, newPath) as { success: boolean; error?: string }

    if (result.success) {
      // 刷新父组件
      emit('context-menu', 'refresh', parentPath, true)
    }
  } catch (error) {
    console.error('重命名出错:', error)
  } finally {
    cancelRename()
  }
}

// 取消创建
const cancelCreate = () => {
  isCreatingFile.value = false
  isCreatingFolder.value = false
  newFileName.value = ''
  newFolderName.value = ''
}

// 取消重命名
const cancelRename = () => {
  isRenaming.value = false
  newName.value = ''
}
</script>

<style scoped>
.file-tree-item {
  width: 100%;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.file-tree-item-container {
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.file-tree-item-container:hover {
  background-color: rgba(79, 70, 229, 0.08);
}

.selected {
  background-color: rgba(79, 70, 229, 0.12);
  font-weight: 500;
  color: #4338ca;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
</style>

<style>
/* 自定义右键菜单样式 */
.file-tree-context-menu {
  --context-menu-bg: #ffffff;
  --context-menu-hover-bg: #f3f4f6;
  --context-menu-text-color: #374151;
  --context-menu-hover-text-color: #4f46e5;
  --context-menu-border-radius: 6px;
  --context-menu-padding: 6px 0;
  --context-menu-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --context-menu-border-color: #e5e7eb;
}

.file-tree-context-menu .context-menu-item {
  padding: 8px 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.file-tree-context-menu .context-menu-item:hover {
  background-color: var(--context-menu-hover-bg);
  color: var(--context-menu-hover-text-color);
}

.file-tree-context-menu .context-menu-item svg {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.file-tree-context-menu .context-menu-item.divided {
  border-bottom: 1px solid var(--context-menu-border-color);
  margin-bottom: 4px;
  padding-bottom: 12px;
}
</style>