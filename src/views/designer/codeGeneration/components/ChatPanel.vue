<template>
  <AIChatContainer
    :key="chatKey"
    :sseUrl="`${generationCodeUrl}?aitoken=${aiToken}`"
    :systemShowChatHeader="false"
    class="chat-container !h-[90vh]"
    :sendSSEMessage="handleSendSSEMessage"
    :menuList="[]"
    :selectInfo="{
      name: '',
      data: []
    }"
    :floatingMode="useFloatingMode"
    :floatingBallConfig="floatingBallConfig"
    :initialPosition="{ x: -20, y: 100 }"
    @message-received="handleMessageReceived"
    @before-send="handleBeforeSend"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { AIChatContainer } from '@yss/ai-chat-components'
import { CHAT_WIDTH } from '../config'
import { generationCodeUrl, sendMessageAsGenerateCode } from '@/apis/designer'
import agentDefault from '@/assets/agent-default.svg'

const emit = defineEmits(['updateCode', 'processMessage'])

const props = defineProps<{
  tabId: string
  aiToken: string
  generatedCode: string
  currentFilePath?: string
  selectedFiles?: Array<{filePath: string, content: string, isDirectory?: boolean}>
  folderFilesContext?: Map<string, Array<{filePath: string, content: string, isDirectory?: boolean}>>
}>()

const chatKey = ref(0)
const chatWidth = CHAT_WIDTH

// 悬浮球模式控制
const useFloatingMode = ref(true)

// 悬浮球配置
const floatingBallConfig = ref({
  // background: '',
  // backgroundSize: 'cover',
  // backgroundPosition: 'center',
  // size: 60
  style: {},
  background: agentDefault,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  size: 60
})

// 在发送消息前拦截，处理@引用
const handleBeforeSend = (data: { question: string }) => {
  // 通知父组件处理消息中的@引用
  emit('processMessage', data.question)
}

// 处理发送消息
const handleSendSSEMessage = async ({ question }) => {
  try {
    // 构建包含所有文件的数据结构 - 将当前文件和选中文件平等对待
    const allFiles: Array<{filePath: string, content: string}> = []
    
    // 添加当前编辑的文件（如果有）
    // if (props.currentFilePath) {
    //   allFiles.push({
    //     filePath: props.currentFilePath,
    //     content: props.generatedCode
    //   })
    // }
    
    // 添加其他选中的文件（不包括文件夹）
    if (props.selectedFiles && props.selectedFiles.length > 0) {
      // 过滤掉文件夹（使用isDirectory属性判断）
      const fileEntries = props.selectedFiles.filter(file => {
        // 兼容两种判断方式：通过isDirectory属性或content === '目录'
        const isFolder = file.isDirectory || file.content === '目录'
      
        return !isFolder
      })
      allFiles.push(...fileEntries)
      
      // 添加文件夹中的所有文件
      if (props.folderFilesContext) {
        // 找出所有文件夹条目
        const folderEntries = props.selectedFiles.filter(file => file.isDirectory || file.content === '目录')
        
        // 对每个文件夹，从folderFilesContext中提取其中的文件
        for (const folder of folderEntries) {
          const folderFiles = props.folderFilesContext.get(folder.filePath)
          if (folderFiles && folderFiles.length > 0) {
            // 只添加非文件夹的文件
            const nonDirectoryFiles = folderFiles.filter(file => {
              const isFolder = file.isDirectory || file.content === '目录'
              return !isFolder
            })
            console.log('添加文件夹中的文件:', nonDirectoryFiles)
            allFiles.push(...nonDirectoryFiles)
          }
        }
      }
    }
    
    // 确保文件路径去重
    const uniqueFiles = allFiles.reduce((acc, current) => {
      const x = acc.find(item => item.filePath === current.filePath)
      if (!x) {
        return acc.concat([current])
      } else {
        return acc
      }
    }, [] as Array<{filePath: string, content: string}>)
    
    // 如果没有文件，则至少添加当前编辑的代码作为无路径文件
    if (uniqueFiles.length === 0 && props.generatedCode) {
      uniqueFiles.push({
        filePath: '',
        content: props.generatedCode
      })
    }
    
    const codeInfo = {
      files: uniqueFiles,
      basePath: props.currentFilePath ? props.currentFilePath.split('/').slice(0, -1).join('/') : ''
    }
    
    await sendMessageAsGenerateCode({
      type: 'codeGenerate',
      question: question,
      codeInfo: codeInfo // 传递所有文件
    }, {
      'Content-Type': 'application/json;charset=UTF-8',
      'aitoken': props.aiToken
    })
    return { code: 200, data: 'Success' }
  } catch (error) {
    console.error('Error sending message:', error)
    return { code: 500, message: '发送消息失败' }
  }
}

// 消息处理策略对象
const messageHandlers = {
  // 处理Message3003类型消息
  'Message3003': () => {
    // 当前不需要特殊处理，直接返回
    return null
  },
  
  // 处理代码类型消息
  'code': (msgData) => {
    const codeTemplate = msgData.data?.component?.props?.steps[0]?.data?.component?.data
    const filePath = msgData.data?.component?.props?.steps[0]?.data?.component?.filePath
    if (!codeTemplate) return null
    
    const codeWithoutMarkdown = codeTemplate.replace(/^```[\w]*\n|\n```$/g, '')
    const escapedCode = codeWithoutMarkdown
      .replace(/`/g, '\\`')  // 转义反引号
      .replace(/\$\{/g, '\\${') // 转义${表达式
    
    return {
      ...msgData.data?.component?.props?.steps[0]?.data?.component,
      code: escapedCode,
      filePath: filePath
    }
  },
  
  // 默认处理器
  'default': () => {
    // 默认情况下不做任何处理
    return null
  }
}

// 中间件式消息处理
const handleMessageReceived = (msgData) => {
  console.log('msgDataSSE', msgData)
  
  // 确定消息类型
  let messageType = 'default'
  
  if (msgData.data?.component?.type === 'Message3003') {
    messageType = 'Message3003'
  } else if (msgData.data?.component?.props?.steps?.[0]?.data?.component?.data && msgData.data?.component?.props?.steps?.[0]?.data?.component?.filePath) {
    messageType = 'code'
  }
  
  // 根据消息类型选择对应的处理器
  const handler = messageHandlers[messageType] || messageHandlers.default
  const result = handler(msgData)
  
  // 如果处理结果有效，则更新代码
  if (result) {
    emit('updateCode', result)
  }
}
</script>
