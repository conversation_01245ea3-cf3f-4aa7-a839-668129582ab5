<template>
  <div class="prompt-manager">
    <a-spin :spinning="loading">
      <!-- 搜索区域 -->
      <div class="mb-4">
        <a-input-search
          v-model:value="searchKeyword"
          placeholder="搜索提示词"
          class="w-full"
          allow-clear
          @search="handleSearch"
        >
          <template #prefix>
            <search-outlined />
          </template>
        </a-input-search>
      </div>

      <!-- 提示词列表表格 -->
      <a-table
        :dataSource="filteredPromptList"
        :columns="columns"
        :pagination="{ pageSize: 10 }"
        rowKey="id"
        class="mb-4"
        :scroll="{ y: 'calc(70vh - 250px)' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'content'">
            <div class="truncate max-w-md" :title="record.content">
              {{ record.content.substring(0, 100) }}{{ record.content.length > 100 ? '...' : '' }}
            </div>
          </template>
          <template v-if="column.key === 'action'">
            <div class="flex space-x-2">
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-button type="link" size="small" @click="handleUsePrompt(record)">使用</a-button>
            </div>
          </template>
        </template>
      </a-table>
    </a-spin>

    <!-- 编辑提示词模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      width="800px"
      @ok="handleSave"
      :okText="'保存'"
      :cancelText="'取消'"
    >
      <a-form :model="currentPrompt" layout="vertical">
        <a-form-item label="标签名称" name="tagName" :rules="[{ required: true, message: '请输入标签名称' }]">
          <a-input v-model:value="currentPrompt.tagName" placeholder="请输入标签名称" />
        </a-form-item>
        <a-form-item label="标签" name="tag" :rules="[{ required: true, message: '请输入标签' }]">
          <a-input v-model:value="currentPrompt.tag" placeholder="请输入标签" />
        </a-form-item>
        <a-form-item label="内容" name="content" :rules="[{ required: true, message: '请输入内容' }]">
          <a-textarea
            v-model:value="currentPrompt.content"
            :rows="12"
            placeholder="请输入提示词内容"
            :auto-size="{ minRows: 12, maxRows: 20 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { getSysDictionaryListApi, editSysDictionaryApi, type PromptDictionary } from '@/apis/aiCodeingPrompts'

const emit = defineEmits(['usePrompt'])

// 状态
const loading = ref(false)
const promptList = ref<PromptDictionary[]>([])
const searchKeyword = ref('')
const modalVisible = ref(false)
const modalTitle = ref('编辑提示词')
const currentPrompt = ref<PromptDictionary>({
  id: 0,
  tagName: '',
  tag: '',
  content: '',
  parentId: 0,
  isDel: 0,
  level: 0,
  sentenceMatchingEmbedding: null,
  sentenceMatching: null
})

// 表格列定义
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 60
  },
  {
    title: '标签名称',
    dataIndex: 'tagName',
    key: 'tagName',
    width: 120,
    ellipsis: true
  },
  {
    title: '标签',
    dataIndex: 'tag',
    key: 'tag',
    width: 120,
    ellipsis: true
  },
  {
    title: '内容预览',
    dataIndex: 'content',
    key: 'content',
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

// 过滤后的提示词列表
const filteredPromptList = computed(() => {
  if (!searchKeyword.value) {
    return promptList.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return promptList.value.filter(item => 
    item.tagName.toLowerCase().includes(keyword) || 
    item.tag.toLowerCase().includes(keyword) ||
    item.content.toLowerCase().includes(keyword)
  )
})

/**
 * 获取提示词列表
 * @description 从API获取系统字典列表数据
 */
const fetchPromptList = async () => {
  try {
    loading.value = true
    const res = await getSysDictionaryListApi()
    if ( res.code === 200) {
      promptList.value = res.data || []
    } else {
      message.error(res.data?.message || '获取提示词列表失败')
    }
  } catch (error) {
    console.error('获取提示词列表出错:', error)
    message.error('获取提示词列表出错')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 * @description 根据关键词过滤提示词列表
 */
const handleSearch = () => {
  // 计算属性已经处理了搜索过滤逻辑
}

/**
 * 处理编辑
 * @param {PromptDictionary} record - 要编辑的提示词记录
 */
const handleEdit = (record: PromptDictionary) => {
  currentPrompt.value = { ...record }
  modalVisible.value = true
  modalTitle.value = `编辑提示词 - ${record.tagName}`
}

/**
 * 处理保存
 * @description 保存编辑后的提示词
 */
const handleSave = async () => {
  try {
    loading.value = true
    const res = await editSysDictionaryApi(currentPrompt.value)
    if ( res.code === 200) {
      message.success('保存成功')
      modalVisible.value = false
      await fetchPromptList()
    } else {
      message.error(res?.message || '保存失败')
    }
  } catch (error) {
    console.error('保存提示词出错:', error)
    message.error('保存提示词出错')
  } finally {
    loading.value = false
  }
}

/**
 * 使用提示词
 * @param {PromptDictionary} record - 要使用的提示词记录
 */
const handleUsePrompt = (record: PromptDictionary) => {
  emit('usePrompt', record)
  message.success(`已选择提示词: ${record.tagName}`)
}

onMounted(() => {
  // 组件挂载时立即加载提示词列表
  fetchPromptList()
})

// 暴露方法供父组件调用
defineExpose({
  fetchPromptList
})
</script>

<style lang="scss" scoped>
.prompt-manager {
  max-height: 70vh;
  overflow-y: auto;
}
</style>