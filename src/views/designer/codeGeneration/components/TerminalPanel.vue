<template>
  <div
    :class="[
      'border-t border-gray-200 transition-all duration-300',
      visible ? 'h-[30%]' : 'h-10'
    ]"
  >
    <div
      class="flex items-center justify-between px-4 py-2 bg-white cursor-pointer group"
      @click="toggleVisible"
    >
      <div class="flex items-center space-x-2">
        <code-sandbox-outlined class="text-gray-600 text-base" />
        <span class="text-sm text-gray-600">终端</span>
      </div>
      <div class="flex items-center space-x-2">
        <span v-if="visible" class="hidden group-hover:inline-block text-xs text-gray-500 mr-1"
          >收起</span
        >
        <span v-else class="hidden group-hover:inline-block text-xs text-gray-500 mr-1">展开</span>
        <down-outlined
          class="text-gray-600 transition-transform duration-200"
          :class="{ 'rotate-180': visible }"
        />
      </div>
    </div>
    <div v-show="visible" class="h-[calc(100%-32px)]">
      <Message3003 class="h-full" :projectList="[]" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Message3003 } from '@yss/ai-chat-components'
import {
  CodeSandboxOutlined,
  DownOutlined,
} from '@ant-design/icons-vue'
import { TERMINAL_HEIGHT_PERCENT } from '../config'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const visible = ref(props.modelValue)

const toggleVisible = () => {
  visible.value = !visible.value
  emit('update:modelValue', visible.value)
}
</script>
