import dayjs from 'dayjs'

// 创建一个函数来生成列配置，接收页码参数
export const getColumns = (page = { current: 1, pageSize: 10 }) => [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '描述',
    key: 'description',
    slots: { customRender: 'description' },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 120,
    slots: { customRender: 'action' },
  },
]

// 为了兼容现有代码，也导出 COLUMNS 常量
export const COLUMNS = getColumns()
