<template>
  <a-modal
    :visible="visible"
    :title="!props.formData?.dir ? '编辑节点' : '新增节点'"
    @cancel="handleCancel"
    @ok="handleSubmit"
    @update:visible="(val) => emit('update:visible', val)"
    width="1200px"
  >
    <div class="flex h-[600px]">
      <!-- 左侧表单 -->
      <div class="w-1/2 pr-4 border-r overflow-y-auto">
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
        >
          <a-form-item label="类型" name="type">
            <a-select v-model:value="form.type" placeholder="请选择类型">
              <a-select-option v-for="item in typeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="名称" name="name">
            <a-input v-model:value="form.name" placeholder="请输入名称" />
          </a-form-item>
          <a-form-item label="配置" name="content">
            <div class="json-editor-wrapper">
              <JsonEditor
                v-model="form.content"
                @change="handleJsonChange"
              />
            </div>
          </a-form-item>
        </a-form>
      </div>

      <!-- 右侧预览 -->
      <div class="w-1/2 pl-4 overflow-y-auto">
        <div class="flex justify-between items-center mb-3">
          <span class="font-medium">预览效果</span>
          <a-button type="link" class="!p-0" @click="handleExpand">
            <template #icon>
              <ExpandOutlined />
            </template>
          </a-button>
        </div>
        <div class="preview-content">
          <NodeConfigPreview v-if="previewConfig" :node-data="previewConfig" />
          <div v-else class="text-center py-20"> 请输入有效的节点配置进行预览 </div>
        </div>
      </div>
    </div>

    <!-- 全屏预览 -->
    <a-modal v-model:visible="expandVisible" title="预览效果" width="80%" :footer="null" centered>
      <div class="h-[80vh] overflow-y-auto">
        <NodeConfigPreview v-if="previewConfig" :node-data="previewConfig" />
        <div v-else class="text-center py-20"> 请输入有效的节点配置进行预览 </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import type { DirectoryData } from '../types'
import { createNode, updateNode, getDesignerEnumList } from '@/apis/designer'
import JsonEditor from '@/views/designer/canvas/components/jsonEditor/index.vue'
import NodeConfigPreview from './NodeConfigPreview.vue'
import { ExpandOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
  visible: boolean
  formData: DirectoryData | null
}>()

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref<FormInstance>()
const form = ref({
  type: '',
  name: '',
  content: '{}'  // 设置默认值为空对象字符串
})

// 添加预览配置
const previewConfig = ref<any>(null)

// 添加展开预览的状态和方法
const expandVisible = ref(false)
const handleExpand = () => {
  expandVisible.value = true
}

const rules = {
  type: [{ required: true, message: '请选择类型' }],
  name: [{ required: true, message: '请输入名称' }],
  content: [{ required: true, message: '请输入配置' }]
}

const typeOptions = ref<{ label: string; value: string }[]>([])

// 加载类型选项
const loadTypeOptions = async () => {
  try {
    const { data } = await getDesignerEnumList('nodeType')
    const enumContent = data?.[0]?.content || []
    typeOptions.value = enumContent.map((item: string) => ({
      value: item,
      label: item
    }))
  } catch (error) {
    console.error('加载节点类型失败:', error)
    message.error('加载节点类型失败')
  }
}

// 处理 JSON 变化
const handleJsonChange = () => {
  try {
    if (!form.value.content) {
      previewConfig.value = null
      return
    }

    const config = typeof form.value.content === 'string'
      ? JSON.parse(form.value.content)
      : form.value.content

    previewConfig.value = config ? {
      getData: () => ({
        type: form.value.type,
        ...config,
        // 确保formValues是一个新对象而不是引用
        formValues: { ...config.formValues }
      })
    } : null
  } catch (error) {
    previewConfig.value = null
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    type: '',
    name: '',
    content: '{}'
  }
  previewConfig.value = null
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 初始化表单数据
const initFormData = () => {
  if (props.formData) {
    if (props.formData.dir) {
      const type = typeOptions.value.some(option => option.value === props.formData?.name)
        ? props.formData.name
        : typeOptions[0]?.value || ''
      form.value = {
        type: type || '',
        name: '',
        content: JSON.stringify({}, null, 2)
      }
    }
    else {
      form.value = {
      type: props.formData.type || '',
      name: props.formData.name || '',
      content: typeof props.formData.content === 'string'
        ? props.formData.content
        : JSON.stringify(props.formData.content || {}, null, 2)
    }
    }
    nextTick(() => {
      handleJsonChange()
    })
  } else {
    resetForm()
  }
}

// 监听表单数据变化
watch(() => props.formData, () => {
  initFormData()
}, { immediate: true })

// 监听表单内容变化
watch(() => form.value.content, () => {
  handleJsonChange()
}, { immediate: true })

// 组件挂载时加载类型选项
onMounted(() => {
  loadTypeOptions()
})

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    // 确保content始终是字符串
    const contentValue = typeof form.value.content === 'string'
      ? form.value.content
      : JSON.stringify(form.value.content)

    const formData = {
      ...form.value,
      content: contentValue,
      directoryId: props.formData?.directoryId || ''
    }

    if (props.formData?.dir) {
      formData.directoryId = props.formData.id
    }
    console.log('params', formData)
    if (!props.formData?.dir && props.formData?.id) {
      await updateNode(props.formData.id, { ...formData, id: props.formData.id })
    } else {
      await createNode(formData)
    }

    message.success('保存成功')
    emit('success')
    emit('update:visible', false)
    resetForm()
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败: ' + (error as Error).message)
  }
}
</script>

<style scoped lang="scss">
.json-editor-wrapper {
  height: 460px;
  @apply border border-gray-200 rounded overflow-hidden;
}

/* 自定义滚动条样式 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: var(--guide-text-color) var(--global-background-color);
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: var(--global-background-color);
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: var(--guide-text-color);
  border-radius: 3px;
}

.preview-content {
  min-height: 300px;
}
</style>
