<template>
  <div class="border rounded-md">
    <!-- 移除 bg-gray-50，让标题部分也能跟随滚动 -->
    <div class="p-4 border-b">
      <div class="text-lg font-medium">{{ nodeConfig?.title }}</div>
      <div class="mt-1 text-sm text-gray-500">{{ nodeConfig?.description }}</div>
      <div class="mt-1 text-sm text-gray-500" v-if="nodeConfig?.shapeId">
        <svg class="icon text-primary" preserveAspectRatio="xMidYMid meet">
          <use :xlink:href="`${shapesUrl}#${nodeConfig?.shapeId}`" fill="currentColor" stroke="none"></use>
        </svg>
      </div>
    </div>

    <!-- 配置内容区 -->
    <div>
      <template v-for="(item, index) in nodeConfig?.components" :key="index">
        <!-- 主组件 -->
        <div class="p-4 border-b relative" v-show="shouldShowComponent(item)">
          <div class="mb-2 text-sm font-medium">
            {{ item.label }}
          </div>

          <component
            :is="getConfigComponent(item.type)"
            v-if="isCustomComponent(item.type)"
            :key="`custom-${item.field}`"
            v-model="formData[item.field]"
            v-bind="item.props || {}"
            class="w-full"
            disabled
          />
          <component
            :is="item.type"
            v-else
            :key="`antd-${item.field}`"
            v-model:value="formData[item.field]"
            v-bind="item.props || {}"
            class="w-full"
            disabled
          />

          <!-- 子组件 -->
          <template v-if="item.children">
            <div
              v-for="(child, childIndex) in item.children"
              :key="`${index}-${childIndex}`"
              class="mt-4"
              v-show="shouldShowChild(child)"
            >
              <div class="mb-2 text-sm font-medium">
                {{ child.label }}
              </div>

              <component
                :is="getConfigComponent(child.type)"
                v-if="isCustomComponent(child.type)"
                :key="`custom-${child.field}`"
                v-model="formData[child.field]"
                v-bind="child.props || {}"
                class="w-full"
                disabled
              />
              <component
                :is="child.type"
                v-else
                :key="`antd-${child.field}`"
                v-model:value="formData[child.field]"
                v-bind="child.props || {}"
                class="w-full"
                disabled
              />
            </div>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect, toRefs } from 'vue'
import { getConfigComponent } from '@/views/designer/canvas/components/configRender'
import shapesUrl from '@/assets/shapes.svg?url'
const props = defineProps<{
  nodeData: any
}>()

// 使用toRefs将props解构为响应式对象
const { nodeData } = toRefs(props)

// 获取节点配置
const nodeConfig = computed(() => {
  if (!nodeData.value) return null
  const data = nodeData.value.getData()
  return data
})

// 使用计算属性替代直接修改
const formData = computed(() => {
  if (!nodeData.value) return {}
  const data = nodeData.value.getData()
  return data.formValues || {}
})

// 判断组件是否显示
const shouldShowComponent = (item: any) => {
  if (!item.show) return true
  return item.show(formData.value)
}

// 判断是否为自定义组件
const isCustomComponent = (type: string) => {
  return !type.startsWith('a-')
}

// 子组件显示逻辑
const shouldShowChild = (child: any) => {
  if (!child.showOn) return true
  if (!Array.isArray(child.showOn)) {
    const { field, value } = child.showOn
    return formData.value[field] === value
  }
  return child.showOn.every(condition => {
    const { field, value } = condition
    return formData.value[field] === value
  })
}
</script>

<style scoped>
/* 移除 config-content 相关的样式 */
</style>
