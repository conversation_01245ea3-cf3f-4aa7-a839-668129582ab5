<template>
  <div class="p-5 h-full bg-area-fill">
    <div class="global-filter mb-2">
      <a-input
        v-model:value="searchText"
        placeholder="请输入名称"
        allow-clear
        style="width: 300px"
      />
      <a-button type="primary" @click="handleExport" class="ml-2">导出节点</a-button>
      <a-button type="primary" @click="handleImport" class="ml-2">导入节点</a-button>
    </div>

    <YssTable
      :columns="columns" 
      :data="filteredData"
      :expandedRowKeys="expandedKeys"
      @expand="handleNodeExpand"
     >
      <!-- 操作按钮 -->
      <template #description="{ record }">
        <div>{{ compDesc(record) }}</div>
      </template>

      <!-- 展开图标 -->
      <template #expandIcon="{ expanded, onExpand, record }">
        <a-button type="link" @click.stop="onExpand(record, $event)" :class="record.children.length > 0 ? 'visible' : 'invisible'">
          <RightOutlined v-if="!expanded" />
          <DownOutlined v-else />
        </a-button>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-tooltip title="新建" v-if="record?.dir">
            <a-button type="link" @click="handleAdd(record)" class="ml-2">
              <template #icon>
                <PlusOutlined />
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="编辑" v-if="!record?.dir">
            <a-button type="link" @click="handleEdit(record)" class="ml-2">
            <template #icon>
              <EditOutlined />
            </template>
          </a-button>
          </a-tooltip>
          <a-tooltip title="删除" v-if="!record?.dir">
            <a-button type="link" danger @click="confirmDelete(record)">
            <template #icon>
              <DeleteOutlined />
            </template>
          </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </YssTable>

    <!-- 弹窗 -->
    <NodeForm v-model:visible="formVisible" :form-data="currentNode" @success="handleSuccess" />

    <a-modal v-model:visible="jsonVisible" title="JSON 数据" width="800px" :footer="null">
      <pre>{{ JSON.stringify(currentJson, null, 2) }}</pre>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, reactive, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import NodeForm from './components/NodeForm.vue'
import { deleteNode, updateNode, getFileTree, exportNodesToExcel, importNodesFromExcel } from '@/apis/designer'
import { YssTable } from '@yss-design/ui'
import type { DirectoryData } from './types'
import { PlusOutlined, EditOutlined, DeleteOutlined, RightOutlined, DownOutlined } from '@ant-design/icons-vue'
import { selectFile } from '@/utils/selectFile'
import { getColumns } from './constant'

// 使用计算属性获取列配置
const columns = computed(() => getColumns(page))

const treeData = ref<DirectoryData[]>([])
const formVisible = ref(false)
const currentNode = ref<DirectoryData | null>(null)
const jsonVisible = ref(false)
const currentJson = ref<any>(null)

// 分页状态
const page = reactive({
  current: 1,
  pageSize: 10
})

// 搜索文本
const searchText = ref('');

// 计算过滤后的数据
const filteredData = computed(() => {
  if (!searchText.value) return treeData.value;
  
  const lowerCaseSearch = searchText.value.toLowerCase();
  return filterTreeNodes(JSON.parse(JSON.stringify(treeData.value)), lowerCaseSearch);
});

watch(
  () => searchText.value, 
  (newVal) => {
    if (newVal) {
      const filterDataId = flattenTreeData(filteredData.value);
      if (filterDataId && filterDataId.length > 0) {
        filterDataId.forEach((item) => {
          handleExpandCurrentNode(item);
        });
      }
    } else {
      if (treeData.value && treeData.value.length > 0) {
        const firstLevelKeys = treeData.value.map((node) => node.id);
        expandedKeys.value = Array.from(new Set(firstLevelKeys));
      }
    }
  },
);

const compDesc = (record: any) => {
  if (record?.content) {
    try {
      const content = JSON.parse(record?.content || '[]');
      return content.description || '';
    } catch (e) {
      console.error('解析 content 失败:', e);
      return '';
    }
  }
  return '';
};

// 导出节点
const handleExport = async() => {
  const data = [...filteredData.value];
  const params = JSON.parse(JSON.stringify(flattenTreeData(data)));
  const response = await exportNodesToExcel(params);

  // 创建下载链接
  const url = window.URL.createObjectURL(new Blob([response], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" }));
  const link = document.createElement('a');
  link.href = url;
  
  const fileName = 'nodes.xlsx';
  
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  
  // 清理
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);

}
// 导入节点
const handleImport = async() => {
  console.log("导入")
  const [file] = await selectFile()

  // 上传文件
  const formData = new FormData()
  formData.append('file', file)
  try {
    const res = await importNodesFromExcel(formData)
    if (res.code == 200) {
      message.success(res.data)
      fetchData()
    }
  } catch (error) {
    message.error('文件上传失败！')
  }
}

// 节点数据扁平化
const flattenTreeData = (data) => {
  let res = [];
  if (data.length == 0) return res;
  for (let i = 0; i < data.length; i++) {
    if (data[i].children.length > 0) {
      res = res.concat(flattenTreeData(data[i].children));
    }
    else {
      res.push(data[i]?.id);
    }
  }
  return res;
}

// 过滤树节点，保留匹配的节点及其父节点
function filterTreeNodes(data, keyword: string) {
  if (!keyword) return data;

  // 递归过滤节点
  const filterNodes = (nodes) => {
    if (!Array.isArray(nodes)) return [];

    return nodes.filter((node) => {
      // 获取节点显示文本
      const nodeLabel = node.label || node.name || '';

      // 检查当前节点是否匹配
      const isMatch = nodeLabel.toLowerCase().includes(keyword.toLowerCase());

      // 递归过滤子节点
      if (Array.isArray(node.children) && node.children.length > 0) {
        const filteredChildren = filterNodes(node.children);
        // 如果有匹配的子节点，保留当前节点并更新子节点
        if (filteredChildren.length > 0) {
          node.children = filteredChildren;
          return true;
        }
      }

      // 如果当前节点匹配或有匹配的子节点，则保留
      return isMatch;
    });
  };

  return filterNodes(data);
}

// 查找节点路径
const findNodePath = (nodes: DirectoryData[], targetId: string) => {
  const path: string[] = [];

  const find = (nodes: DirectoryData[], targetId: string) => {
    for (const node of nodes) {
      if (node.id === targetId) {
        path.push(node.id);
        return true;
      }
      if (node.children) {
        path.push(node.id);
        if (find(node.children, targetId)) {
          return true;
        }
        path.pop();
      }
    }
    return false;
  };

  find(nodes, targetId);
  return path;
};

const handleExpandCurrentNode = (nodeId: string) => {
  const path = findNodePath(treeData.value, nodeId);
  if (path && path.length > 0) {
    expandedKeys.value = Array.from(new Set([...expandedKeys.value, ...path]));
  }
};

// 展开节点
const expandedKeys = ref<string[]>([]);
  const handleNodeExpand = (expanded: boolean, record: any) => {
  const key = record.id;
  if (expanded) {
    expandedKeys.value = [...expandedKeys.value, key];
  } else {
    expandedKeys.value = expandedKeys.value.filter(k => k !== key);
  }
};

// 新增节点
const handleAdd = (record) => {
  currentNode.value = { ...record }
  formVisible.value = true
}

// 编辑节点
const handleEdit = (record: DirectoryData) => {
  currentNode.value = { ...record }
  formVisible.value = true
}

// 删除节点
const handleDelete = async (record: DirectoryData) => {
  try {
    await deleteNode(record.id)
    message.success('删除成功')
    fetchData()
  } catch (error) {
    message.error('删除失败: ' + (error as Error).message)
  }
}

// 表单提交成功
const handleSuccess = () => {
  formVisible.value = false
  fetchData()
}

// 删除确认
const confirmDelete = (record: DirectoryData) => {
  Modal.confirm({
    title: '确定要删除这个节点吗？',
    okText: '确定',
    cancelText: '取消',
    centered: true,
    onOk: () => handleDelete(record)
  })
}

// 获取文件树
const fetchData = async () => {
  const res = await getFileTree({ type: 'node' })
  if(res.code !== 200){
      return
    }
    if (!res.data) {
      treeData.value = []
      return
    }
    const projectData = reselectTreeData(res.data, 0)
    treeData.value = projectData
}

const reselectTreeData = (nodes, level) => {
  if (!nodes || !Array.isArray(nodes)) return []
  level++
  return nodes.map(item => {
    if (!item || !item.name) return null
    const combinedChildren = [
      ...(Array.isArray(item.children) ? item.children : []),
      ...(Array.isArray(item.contents) ? item.contents : [])
    ]

    return {
      children: combinedChildren.length > 0 ? reselectTreeData(combinedChildren, level) : [],
      level,
      id: item.id,
      key: item.id,
      name: item.name,
      label: item.name,
      parentId: item.parentId,
      dir: Array.isArray(item.children),
      createTime: item.createTime,
      updateTime: item.updateTime,
      type: item.type,
      json: item.json,
      directoryId: item.directoryId,
      extension: item.extension,
      content: item.content,
    }
  }).filter(Boolean)
}

// 初始化
onMounted(() => {
  fetchData();
  setTimeout(() => {
    if (treeData.value && treeData.value.length > 0) {
      const firstLevelKeys = treeData.value.map((node) => node.id);
      expandedKeys.value = Array.from(new Set(firstLevelKeys));
    }
  }, 800);
});
</script>

<style scoped>
:deep(.ant-tree-node-content-wrapper) {
  position: relative;
}

:deep(.ant-tree-node-content-wrapper:hover) {
  background-color: rgba(0, 0, 0, 0.04);
}

:deep(.ant-tree-node-content-wrapper:hover) .opacity-0 {
  opacity: 1 !important;
}

:deep(.ant-tree-node-content-wrapper) .ant-btn {
  padding: 0 4px;
  height: 24px;
  line-height: 24px;
}

:deep(.ant-tree-node-content-wrapper) .ant-btn .anticon {
  font-size: 14px;
  position: relative;
  top: -4px;
}

:deep(.ant-table .ant-btn:focus),
:deep(.ant-table .ant-btn:active),
:deep(.ant-table .ant-btn:focus-visible) {
  box-shadow: none !important;
  outline: none !important;
}
</style>