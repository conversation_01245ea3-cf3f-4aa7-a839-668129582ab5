<template>
  <a-modal
    :visible="visible"
    :title="formData ? '编辑数据源' : '新增数据源'"
    width="600px"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <template #footer>
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        确认
      </a-button>
    </template>

    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      layout="vertical"
    >
      <!-- 数据源编码 -->
      <a-form-item
        label="数据源编码"
        name="code"
        required
      >
        <a-input
          v-model:value="form.code"
          placeholder="请输入数据源编码"
        />
      </a-form-item>

      <!-- 数据源名称 -->
      <a-form-item
        label="数据源名称"
        name="name"
        required
      >
        <a-input
          v-model:value="form.name"
          placeholder="请输入数据源名称"
        />
      </a-form-item>

      <!-- 数据库类型 -->
      <a-form-item
        label="数据库类型"
        name="dbType"
        required
      >
        <a-select
          v-model:value="form.dbType"
          placeholder="请选择数据库类型"
          :loading="dbTypesLoading"
          @change="handleDbTypeChange"
        >
          <a-select-option
            v-for="item in dbTypes"
            :key="item.code"
            :value="item.code"
          >
            {{ item.description }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <!-- 驱动类 -->
      <a-form-item
        label="驱动类"
        name="dbDriver"
        required
      >
        <a-input
          v-model:value="form.dbDriver"
          placeholder="请输入驱动类"
        />
      </a-form-item>

      <!-- 数据源地址 -->
      <a-form-item
        label="数据源地址"
        name="dbUrl"
        required
      >
        <a-input
          v-model:value="form.dbUrl"
          placeholder="请输入数据源地址"
        />
      </a-form-item>

      <!-- 用户名 -->
      <a-form-item
        label="用户名"
        name="dbUsername"
        required
      >
        <a-input
          v-model:value="form.dbUsername"
          placeholder="请输入用户名"
        />
      </a-form-item>

      <!-- 密码 -->
      <a-form-item
        label="密码"
        name="dbPassword"
        required
      >
        <div class="flex items-center gap-2">
          <a-input-password
            v-model:value="form.dbPassword"
            placeholder="请输入密码"
          />
          <a-button
            type="primary"
            :loading="testLoading"
            @click="handleTest"
          >
            测试
          </a-button>
        </div>
      </a-form-item>

      <!-- 数据库名称 -->
      <a-form-item
        label="数据库名称"
        name="dbName"
        required
      >
        <a-input
          v-model:value="form.dbName"
          placeholder="请输入数据库名称"
        />
      </a-form-item>

      <!-- 备注 -->
      <a-form-item
        label="备注"
        name="remark"
      >
        <a-textarea
          v-model:value="form.remark"
          placeholder="请输入备注"
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import {
  saveDataSource,
  updateDataSource,
  testDatabaseConnection,
  getDatabaseTypes,
  type DbTypeModel
} from '@/apis/designer'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref<FormInstance>()
const loading = ref(false)
const testLoading = ref(false)

// 数据库类型列表
const dbTypes = ref<DbTypeModel[]>([])
const dbTypesLoading = ref(false)

// 表单数据
const form = reactive({
  id: '',
  code: '',
  name: '',
  dbType: undefined,
  dbDriver: '',
  dbUrl: '',
  dbUsername: '',
  dbPassword: '',
  dbName: '',
  remark: ''
})

// 表单验证规则
const rules = {
  code: [{ required: true, message: '请输入数据源编码' }],
  name: [{ required: true, message: '请输入数据源名称' }],
  dbType: [{ required: true, message: '请选择数据库类型' }],
  dbDriver: [{ required: true, message: '请输入驱动类' }],
  dbUrl: [{ required: true, message: '请输入数据源地址' }],
  dbUsername: [{ required: true, message: '请输入用户名' }],
  dbPassword: [{ required: true, message: '请输入密码' }],
  dbName: [{ required: true, message: '请输入数据库名称' }]
}

// 获取数据库类型列表
const fetchDbTypes = async () => {
  dbTypesLoading.value = true
  try {
    const response = await getDatabaseTypes()
    if (response.code === 200) {
      dbTypes.value = response.result
    } else {
      message.error(response.message || '获取数据库类型失败')
    }
  } catch (error) {
    // message.error('获取数据库类型失败')
  } finally {
    dbTypesLoading.value = false
  }
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newVal) => {
    if (newVal) {
      // 编辑模式，填充表单
      Object.assign(form, {
        id: newVal.id || '',
        code: newVal.code || '',
        name: newVal.name || '',
        dbType: newVal.dbType || undefined,
        dbDriver: newVal.dbDriver || '',
        dbUrl: newVal.dbUrl || '',
        dbUsername: newVal.dbUsername || '',
        dbPassword: newVal.dbPassword || '',
        dbName: newVal.dbName || '',
        remark: newVal.remark || ''
      })
    } else {
      // 新增模式，重置表单
      Object.assign(form, {
        id: '',
        code: '',
        name: '',
        dbType: undefined,
        dbDriver: '',
        dbUrl: '',
        dbUsername: '',
        dbPassword: '',
        dbName: '',
        remark: ''
      })
    }
  },
  { immediate: true }
)

// 处理数据库类型变化
const handleDbTypeChange = (value: string) => {
  // 根据选择的数据库类型自动设置驱动类
  const selectedType = dbTypes.value.find((item) => item.code === value)
  if (selectedType) {
    form.dbDriver = selectedType.driverClass
  }
}

// 测试连接
const handleTest = async () => {
  const { code, name, dbType, dbDriver, dbUrl, dbUsername, dbPassword, dbName } = form
  if (!dbType || !dbUrl || !dbUsername || !dbPassword) {
    message.warning('请填写数据库类型、地址、用户名和密码')
    return
  }

  testLoading.value = true
  try {
    const response = await testDatabaseConnection({
      code,
      name,
      dbType,
      dbDriver,
      dbUrl,
      dbUsername,
      dbPassword,
      dbName
    })

    if (response.code === 200) {
      message.success('连接测试成功')
    }
  } catch (error) {
  } finally {
    testLoading.value = false
  }
}

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate().then(async () => {
    loading.value = true
    try {
      const isEdit = !!form.id
      const api = isEdit ? updateDataSource : saveDataSource
      const response = await api(form)

      if (response.code === 200) {
        message.success(`${isEdit ? '编辑' : '新增'}成功`)
        emit('success')
        emit('update:visible', false)
      } else {
        message.error(response.message || `${isEdit ? '编辑' : '新增'}失败`)
      }
    } catch (error) {
      message.error(`${form.id ? '编辑' : '新增'}失败`)
    } finally {
      loading.value = false
    }
  })
}

// 取消
const handleCancel = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

// 初始化
onMounted(() => {
  fetchDbTypes()
})
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style> 