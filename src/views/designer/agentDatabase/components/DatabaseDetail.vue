<template>
  <a-modal
    :visible="visible"
    :title="`${dataSourceName} 数据库表结构`"
    width="1200px"
    :footer="null"
    @cancel="handleClose"
  >
    <div class="database-detail">
      <!-- 顶部操作区 -->
      <!-- <div class="mb-4 flex justify-between items-center">
        <div class="flex gap-2">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索表名"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-button
            type="primary"
            @click="handleRefresh"
          >
            <SyncOutlined />
            刷新
          </a-button>
        </div>
        <a-button @click="handleClose">
          关闭
        </a-button>
      </div> -->

      <!-- 表格树 -->
      <a-spin :spinning="loading">
        <YssTable
          :columns="columns"
          :data="filteredTables"
          :row-key="(record) => record.key"
          :show-pagination="false"
          :scroll="{ y: 500 }"
          :expandable="{
            defaultExpandAllRows: false
          }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'tableName'">
              <div class="flex items-center">
                <template v-if="!record.isColumn">
                  <TableOutlined class="mr-2 text-blue-500" />
                  <span class="font-medium">{{ record.tableName }}</span>
                </template>
                <template v-else>
                  <div class="ml-2 flex items-center">
                    <FieldBinaryOutlined class="mr-2 text-gray-500" />
                    <span>{{ record.columnName }}</span>
                    <a-tag
                      v-if="record.isPrimary === '1'"
                      color="blue"
                      class="ml-2"
                    >
                      主键
                    </a-tag>
                  </div>
                </template>
              </div>
            </template>

            <template v-if="column.dataIndex === 'tableCode'">
              <span v-if="!record.isColumn">{{ record.tableCode }}</span>
              <span v-else>{{ record.columnCode }}</span>
            </template>

            <template v-if="column.dataIndex === 'tableDescription'">
              <span v-if="!record.isColumn">{{ record.tableDescription }}</span>
              <span v-else>{{ record.columnDescription }}</span>
            </template>

            <template v-if="column.dataIndex === 'dataType'">
              <span v-if="record.isColumn">{{ record.dataType }}</span>
            </template>

            <template v-if="column.dataIndex === 'isPrimary'">
              <template v-if="record.isColumn">
                <a-tag :color="record.isPrimary === '1' ? 'blue' : 'default'">
                  {{ record.isPrimary === '1' ? '是' : '否' }}
                </a-tag>
              </template>
            </template>

            <template v-if="column.dataIndex === 'isNullable'">
              <template v-if="record.isColumn">
                <a-tag :color="record.isNullable === '1' ? 'green' : 'red'">
                  {{ record.isNullable === '1' ? '是' : '否' }}
                </a-tag>
              </template>
            </template>

            <template v-if="column.dataIndex === 'columnOrder'">
              <span v-if="record.isColumn">{{ record.columnOrder }}</span>
            </template>
          </template>
        </YssTable>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { 
  TableOutlined, 
  FieldBinaryOutlined, 
  SyncOutlined, 
  RightOutlined, 
  DownOutlined 
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { getDataSourceTables, syncDatabaseTables } from '@/apis/designer'
import { YssTable } from '@yss-design/ui'

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dataSourceId: {
    type: String,
    required: true
  },
  dataSourceCode: {
    type: String,
    required: true
  },
  dataSourceName: {
    type: String,
    default: '数据源'
  }
})

const emit = defineEmits(['update:visible', 'close'])

// 表格列定义
const columns = [
  {
    title: '表名/列名',
    dataIndex: 'tableName',
    key: 'tableName',
    width: 300
  },
  {
    title: '表代码/列代码',
    dataIndex: 'tableCode',
    key: 'tableCode',
    width: 200
  },
  {
    title: '表描述/列描述',
    dataIndex: 'tableDescription',
    key: 'tableDescription',
    width: 250
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
    key: 'dataType',
    width: 150
  },
  {
    title: '是否主键',
    dataIndex: 'isPrimary',
    key: 'isPrimary',
    width: 100
  },
  {
    title: '是否可为空',
    dataIndex: 'isNullable',
    key: 'isNullable',
    width: 120
  },
  {
    title: '列顺序',
    dataIndex: 'columnOrder',
    key: 'columnOrder',
    width: 100
  }
]

// 数据状态
const loading = ref(false)
const tables = ref<any[]>([])
const searchText = ref('')

// 过滤后的表格数据
const filteredTables = computed(() => {
  if (!searchText.value) {
    return tables.value
  }
  
  const searchLower = searchText.value.toLowerCase()
  return tables.value.filter(table => 
    table.tableName.toLowerCase().includes(searchLower) || 
    table.tableCode.toLowerCase().includes(searchLower) ||
    table.tableDescription?.toLowerCase().includes(searchLower)
  )
})

// 获取表结构数据
const fetchTables = async () => {
  if (!props.dataSourceCode) {
    return
  }
  
  loading.value = true
  try {
    const response = await getDataSourceTables(props.dataSourceCode)
    if (response.code === 200) {
      // 转换数据为树形结构
      tables.value = response.result.map((table: TableInfo) => {
        return {
          key: table.tableCode,
          tableName: table.tableName,
          tableCode: table.tableCode,
          tableDescription: table.tableDescription || '',
          isColumn: false,
          children: table.columns.map((column: ColumnInfo) => ({
            key: `${table.tableCode}-${column.columnCode}`,
            tableName: table.tableName,
            tableCode: table.tableCode,
            tableDescription: table.tableDescription || '',
            columnName: column.columnName,
            columnCode: column.columnCode,
            columnDescription: column.columnDescription || '',
            dataType: column.dataType,
            isPrimary: column.isPrimary === '1',
            isNullable: column.isNullable === '1',
            columnOrder: column.columnOrder,
            isColumn: true
          }))
        }
      })
    } else {
      message.error(response.message || '获取表结构失败')
    }
  } catch (error) {
    message.error('获取表结构失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (value: string) => {
  searchText.value = value
}

// 刷新数据
const handleRefresh = async () => {
  if (!props.dataSourceId) {
    message.warning('数据源ID不能为空')
    return
  }
  
  try {
    const response = await syncDatabaseTables(props.dataSourceId)
    if (response.code === 200) {
      message.success('同步成功')
      fetchTables()
    } else {
      message.error(response.message || '同步失败')
    }
  } catch (error) {
    message.error('同步失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  // 清空数据
  tables.value = []
  searchText.value = ''
  
  // 通知父组件更新 visible 状态
  emit('update:visible', false)
  
  // 触发关闭事件，父组件可以执行额外的清理操作
  emit('close')
}

// 监听属性变化
watch(
  () => props.dataSourceCode,
  (newVal) => {
    if (newVal && props.visible) {
      fetchTables()
    }
  }
)

watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.dataSourceCode) {
      fetchTables()
    }
  }
)
</script>

<style scoped>
.database-detail {
  display: flex;
  flex-direction: column;
  height: 600px;
}

:deep(.ant-table-wrapper) {
  flex: 1;
  overflow: auto;
}

:deep(.ant-table-row-expand-icon) {
  margin-right: 8px;
}

.expand-button {
  padding: 0;
  margin-right: 8px;
}

:deep(.ant-tag) {
  margin-right: 0;
}
</style>
