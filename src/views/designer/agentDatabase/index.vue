<template>
  <div class="min-h-full bg-global-background rounded-lg">
    <BaseTable
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :search-schema="searchSchema"
      :total="total"
      :current="current"
      :pageSize="pageSize"
      @search="handleSearch"
      @reset="handleReset"
      @page-change="handlePageChange"
    >
      <!-- 操作按钮区域 -->
      <template #actions>
        <div class="flex items-center gap-2">
          <a-button type="primary" class="flex items-center" @click="handleAdd">
            <PlusOutlined class="mr-1" />
            新增
          </a-button>
        </div>
      </template>

      <!-- 数据库类型列 -->
      <template #dbType="{ record }">
        <a-tag class="px-3 py-1 text-sm dark:!text-white dark:!bg-input-fill dark:!border-input-border">
          {{ record.dbType }}
        </a-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-tooltip title="编辑">
            <a-button type="link" @click="handleEdit(record)">
              <template #icon><EditOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="同步数据库表结构">
            <a-button type="link" @click="handleSync(record)">
              <template #icon><SyncOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="查看数据库表结构">
            <a-button type="link" @click="handleViewDetail(record)">
              <template #icon><EyeOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="删除数据源">
            <a-button type="link" danger @click="handleDelete(record)">
              <template #icon><DeleteOutlined /></template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>

    <!-- 数据源表单弹窗 -->
    <DatabaseForm
      v-model:visible="formVisible"
      :form-data="currentRecord"
      @success="handleSuccess"
    />

    <!-- 表结构查看弹窗 -->

    <database-detail
      :visible="detailVisible"
      :dataSourceId="selectedDataSource?.id"
      :dataSourceCode="selectedDataSource?.code"
      :dataSourceName="selectedDataSource?.name"
      @close="detailVisible = false"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ExportOutlined,
  ImportOutlined,
  EditOutlined,
  SyncOutlined,
  EllipsisOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import BaseTable from '@/components/BaseTable/index.vue'
import DatabaseForm from './components/DatabaseForm.vue'
import DatabaseDetail from './components/DatabaseDetail.vue'
import { columns, searchSchema } from './constants'
import {
  getDataSourceList,
  deleteDataSource,
  syncDatabaseTables,
  type DataSourceModel,
  type DataSourceQueryDTO,
  type TableInfo
} from '@/apis/designer'

// 表格数据
const dataSource = ref<DataSourceModel[]>([])
const loading = ref(false)
const total = ref(0)
const current = ref(1)
const pageSize = ref(10)
const formVisible = ref(false)
const currentRecord = ref<DataSourceModel | null>(null)
const searchParams = ref<Partial<DataSourceQueryDTO>>({})

// 表结构查看相关
const detailVisible = ref(false)
const selectedDataSource = ref<DataSourceModel | null>(null)

// 获取数据
const fetchData = async (params: Partial<DataSourceQueryDTO> = {}) => {
  loading.value = true
  try {
    const queryParams: DataSourceQueryDTO = {
      pageNo: params.pageNo || current.value,
      pageSize: params.pageSize || pageSize.value,
      code: params.code,
      name: params.name,
      dbType: params.dbType
    }

    const response = await getDataSourceList(queryParams)

    if (response.code === 200 && response.success) {
      dataSource.value = response.result.records
      total.value = response.result.total
      current.value = queryParams.pageNo || 1
      pageSize.value = queryParams.pageSize || 10
    } else {
      message.error(response.message || '获取数据失败')
    }
  } catch (error) {
    message.error('获取数据失败')
    dataSource.value = []
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (params: any) => {
  // 将BaseTable组件的搜索参数转换为API所需格式
  const apiParams: Partial<DataSourceQueryDTO> = {
    code: params.code,
    name: params.name,
    dbType: params.dbType
  }

  searchParams.value = apiParams
  fetchData({
    ...apiParams,
    pageNo: 1
  })
}

// 重置处理
const handleReset = () => {
  searchParams.value = {}
  current.value = 1
  pageSize.value = 10
  fetchData()
}

// 分页处理
const handlePageChange = ({ current: newCurrent, pageSize: newPageSize }) => {
  fetchData({
    ...searchParams.value,
    pageNo: newCurrent,
    pageSize: newPageSize
  })
}

// 新增处理
const handleAdd = () => {
  currentRecord.value = null
  formVisible.value = true
}

// 编辑处理
const handleEdit = (record: DataSourceModel) => {
  currentRecord.value = record
  formVisible.value = true
}

// 删除处理
const handleDelete = (record: DataSourceModel) => {
  if (!record.id) return

  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该数据源吗？',
    async onOk() {
      try {
        const response = await deleteDataSource(record.id)
        if (response.code === 200) {
          message.success('删除成功')
          fetchData(searchParams.value)
        } else {
          message.error(response.message || '删除失败')
        }
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

// 同步数据库表结构
const handleSync = (record: DataSourceModel) => {
  if (!record.id) return

  Modal.confirm({
    title: '确认同步',
    content: '确定要同步该数据源的表结构吗？',
    async onOk() {
      try {
        const response = await syncDatabaseTables(record.id)
        if (response.code === 200) {
          message.success('同步成功')
        } else {
          message.error(response.message || '同步失败')
        }
      } catch (error) {
        message.error('同步失败')
      }
    }
  })
}

// 查看表结构详情
const handleViewDetail = (record: DataSourceModel) => {
  selectedDataSource.value = record
  detailVisible.value = true
}

// 表单提交成功处理
const handleSuccess = () => {
  fetchData(searchParams.value)
  formVisible.value = false
}

// 初始化
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
/* 调整弹窗内容区域的高度 */
:deep(.ant-modal-body) {
  max-height: calc(90vh - 110px);
  overflow-y: auto;
}
</style>
