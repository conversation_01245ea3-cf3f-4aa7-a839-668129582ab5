import { getDatabaseTypes } from '@/apis/designer'

// 数据库类型选项
const dbTypeOptions: { label: string; value: string }[] = []

// 获取数据库类型
const fetchDbTypes = async () => {
  try {
    const response = await getDatabaseTypes()
    if (response.code === 200) {
      const options = response.result.map((item) => ({
        label: item.description,
        value: item.code
      }))
      dbTypeOptions.push(...options)
    }
  } catch (error) {
    console.error('获取数据库类型失败:', error)
  }
}

// 初始化获取数据库类型
fetchDbTypes()

// 表格列定义
export const columns = [
  {
    title: '数据源编码',
    dataIndex: 'code',
    key: 'code',
    width: 150
  },
  {
    title: '数据源名称',
    dataIndex: 'name',
    key: 'name',
    width: 180
  },
  {
    title: '数据库类型',
    dataIndex: 'dbType',
    key: 'dbType',
    width: 120,
    slots: { customRender: 'dbType' }
  },
  {
    title: '驱动类',
    dataIndex: 'dbDriver',
    key: 'dbDriver',
    width: 200,
    ellipsis: true
  },
  {
    title: '数据源地址',
    dataIndex: 'dbUrl',
    key: 'dbUrl',
    width: 250,
    ellipsis: true
  },
  {
    title: '用户名',
    dataIndex: 'dbUsername',
    key: 'dbUsername',
    width: 120
  },
  {
    title: '数据库名',
    dataIndex: 'dbName',
    key: 'dbName',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 220,
    slots: { customRender: 'action' }
  }
]

// 搜索表单配置
export const searchSchema = [
  {
    key: 'name',
    label: '数据源名称',
    type: 'input',
    placeholder: '请输入数据源名称'
  },
  {
    key: 'dbType',
    label: '数据库类型',
    type: 'select',
    placeholder: '请选择数据库类型',
    options: dbTypeOptions
  }
]

// 导出数据库类型选项，方便其他组件使用
export { dbTypeOptions, fetchDbTypes }