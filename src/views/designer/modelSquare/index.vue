<template>
  <div class="min-h-[calc(100vh-64px)] bg-global-background">
    <a-row :gutter="24">
      <!-- 左侧筛选区域 -->
      <a-col :span="6" style="padding: 0px">
        <div class="bg-global-background rounded-lg p-2 shadow-sm">
          <template v-for="section in navigationData" :key="section.value">
            <!-- 筛选部分 -->
            <div class="mb-6">
              <h3 class="text-base font-semibold text-gray-900 dark:!text-custom mb-4">
                {{ section.name }}
              </h3>
              <div class="flex flex-wrap gap-2">
                <a-tag
                  v-for="item in section.details"
                  :key="item.name"
                  :class="[
                    'cursor-pointer transition-all duration-300 px-3 py-1 rounded-md',
                    getIsSelected(section.value, item)
                      ? 'bg-blue-50 text-blue-600 border-blue-600 dark:!bg-[var(--select-color)] dark:!text-primary dark:!border-border'
                      : 'hover:text-blue-600 hover:border-blue-600 border-input-border bg-area-fill text-custom dark:hover:!text-[var(--grey-auxiliary-color)]'
                  ]"
                  @click="handleFilterClick(section.value, item)"
                >
                  {{ item.name }}
                </a-tag>
              </div>
            </div>

            <a-divider v-if="section.value !== 'size'" class="my-4 border-gray-200 dark:!border-border" />
          </template>
        </div>
      </a-col>

      <!-- 分割线 -->
      <a-col :span="1" class="flex justify-center p-0" style="max-width: 16px;">
        <a-divider
          type="vertical"
          class="h-full m-0"
          style="border-inline-start: 1px solid rgba(5, 5, 5, 0.06);"
        />
      </a-col>

      <!-- 右侧内容区域 -->
      <a-col :span="17">
        <ListComponent
          v-model:activeTab="activeTab"
          v-model:current="current"
          v-model:pageSize="pageSize"
          :list="agentList"
          :total="total"
          :col-count="2"
          :tabs="tabs"
          @tab-change="handleTabChange"
          @item-click="handleItemClick"
          @collect="handleCollect"
          @page-change="handlePageChange"
        >
          <template #header>
            <div class="flex justify-between items-center mb-6">
              <div class="flex items-center gap-2">
                <a-input
                  v-model:value="searchKeyword"
                  placeholder="请输入搜索关键词"
                  class="w-64 bg-input-fill text-custom border-input-border"
                  @press-enter="handleSearch"
                />
                <a-button type="primary" @click="handleSearch"> 搜索 </a-button>
              </div>
              <!-- <div>
                <a-radio-group
                  v-model:value="searchParams.sort"
                  button-style="solid"
                >
                  <a-radio-button value="latest">
                    最新
                  </a-radio-button>
                  <a-radio-button value="hot">
                    最热
                  </a-radio-button>
                  <a-radio-button value="recommend">
                    推荐
                  </a-radio-button>
                </a-radio-group>
              </div> -->
            </div>
          </template>
        </ListComponent>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  MessageOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  SoundOutlined,
  LinkOutlined,
  SortAscendingOutlined
} from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import ListComponent from '@/components/ListComponent/index.vue'
import { getModelPage, getModelNavigation } from '@/apis/model'
import type { ModelRecord, NavigationItem } from '@/apis/model'
import DeepSeekImg from '@/assets/DeepSeek.png'
import MetaImg from '@/assets/Meta.png'
import QwenImg from '@/assets/Qwen.png'

const router = useRouter()

// 搜索参数
const searchParams = reactive({
  current: 1,
  pageSize: 10,
  name: '',
  usageType: undefined,
  groupOrProvider: undefined,
  contentLength: undefined,
  size: undefined,
  architectureType: undefined,
  sort: 'latest'
})

// 搜索关键词
const searchKeyword = ref('')

// 列表组件相关参数
const activeTab = ref('latest')
const current = ref(1)
const pageSize = ref(10)
const total = ref(0)
const agentList = ref<any[]>([])

// 导航数据
const navigationData = ref<NavigationItem[]>([])

// 标签页配置
const tabs = [
  { key: 'latest', title: '最新' },
  { key: 'hot', title: '热门' },
  { key: 'collected', title: '已收藏' }
]

// 选中的筛选项
const selectedFilters = reactive({
  usageType: '',
  groupOrProvider: '',
  contentLength: '',
  size: ''
})

// 获取导航数据
const fetchNavigationData = async () => {
  try {
    const res = await getModelNavigation()
    if (res?.code === 200) {
      navigationData.value = res.data
    } else {
      message.error(res?.message || '获取导航数据失败')
    }
  } catch (error) {
    message.error('获取导航数据失败')
  }
}

// 判断筛选项是否被选中
const getIsSelected = (sectionValue: string, item: any) => {
  switch (sectionValue) {
  case 'usageType':
    return selectedFilters.usageType === item.param.value
  case 'groupOrProvider':
    return selectedFilters.groupOrProvider === `${item.param.group}-${item.param.provider}`
  case 'contentLength':
    return selectedFilters.contentLength === `${item.param.min}-${item.param.minOp}`
  case 'size':
    if (item.param.architecture) {
      return selectedFilters.size === item.param.architecture
    } else {
      return selectedFilters.size === `${item.param.min || ''}-${item.param.max || ''}-${item.param.minOp || ''}-${item.param.maxOp || ''}`
    }
  default:
    return false
  }
}

// 处理筛选项点击
const handleFilterClick = (sectionValue: string, item: any) => {
  switch (sectionValue) {
  case 'usageType':
    if (selectedFilters.usageType === item.param.value) {
      selectedFilters.usageType = ''
      searchParams.usageType = undefined
    } else {
      selectedFilters.usageType = item.param.value
      searchParams.usageType = { value: item.param.value }
    }
    break
  case 'groupOrProvider':
    const groupProviderValue = `${item.param.group}-${item.param.provider}`
    if (selectedFilters.groupOrProvider === groupProviderValue) {
      selectedFilters.groupOrProvider = ''
      searchParams.groupOrProvider = undefined
    } else {
      selectedFilters.groupOrProvider = groupProviderValue
      searchParams.groupOrProvider = item.param
    }
    break
  case 'contentLength':
    const contentLengthValue = `${item.param.min}-${item.param.minOp}`
    if (selectedFilters.contentLength === contentLengthValue) {
      selectedFilters.contentLength = ''
      searchParams.contentLength = undefined
    } else {
      selectedFilters.contentLength = contentLengthValue
      searchParams.contentLength = item.param
    }
    break
  case 'size':
    let sizeValue
    if (item.param.architecture) {
      sizeValue = item.param.architecture
    } else {
      sizeValue = `${item.param.min || ''}-${item.param.max || ''}-${item.param.minOp || ''}-${item.param.maxOp || ''}`
    }

    if (selectedFilters.size === sizeValue) {
      selectedFilters.size = ''
      searchParams.size = undefined
    } else {
      selectedFilters.size = sizeValue
      searchParams.size = item.param
    }
    break
  default:
    break
  }

  updateModelList()
}

// 处理搜索
const handleSearch = () => {
  searchParams.name = searchKeyword.value
  updateModelList()
}

// 处理标签页切换
const handleTabChange = (key: string) => {
  activeTab.value = key
  // 根据标签页更新搜索参数
  searchParams.sort = key
  current.value = 1
  searchParams.current = 1
  fetchAgentList()
}

// 处理列表项点击
const handleItemClick = (item: any) => {
  router.push({
    path: '/designer/modelSetting',
    query: { modelId: item.id }
  })
}

// 处理分页变化
const handlePageChange = (page: number, size: number) => {
  current.value = page
  pageSize.value = size
  searchParams.current = page
  searchParams.pageSize = size
  fetchAgentList()
}

// 获取模型列表数据
const fetchAgentList = async () => {
  try {
    const res = await getModelPage(searchParams)
    if (res?.code === 200) {
      const { records, total: totalCount } = res.data
      // 转换API返回的数据格式为组件需要的格式
      agentList.value = records.map(record => ({
        id: record.id,
        title: record.name,
        description: record.description,
        level: record.usageType,
        tags: [record.groupType, record.provider, record.architectureType].filter(Boolean),
        users: record.usageCount || 0,
        messages: record.messageCount || 0,
        avatar: record.provider === 'DeepSeek' ? DeepSeekImg : record.provider === 'Meta' ? MetaImg : QwenImg,
        isCollected: record.isCollected || false
      }))
      total.value = totalCount
    } else {
      message.error(res?.message || '获取数据失败')
    }
  } catch (error) {
    message.error('获取数据失败')
  }
}

// 修改更新模型列表方法
const updateModelList = () => {
  current.value = 1
  searchParams.current = 1
  fetchAgentList()
}

// 组件挂载时加载初始数据
onMounted(() => {
  fetchNavigationData()
  fetchAgentList()
})

// 收藏模型
const handleCollect = async (model) => {
  try {
    // 这里应该调用实际的收藏API
    // await collectModel(model.id)

    // 更新本地数据状态
    const index = agentList.value.findIndex(item => item.id === model.id)
    if (index !== -1) {
      agentList.value[index].isCollected = !agentList.value[index].isCollected
    }

    message.success(agentList.value[index].isCollected ? '收藏成功' : '取消收藏成功')
  } catch (error) {
    message.error('操作失败')
  }
}
</script>

<style>
/* 隐藏滚动条但保持滚动功能 */
.overflow-y-auto {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.overflow-y-auto::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
</style>
