<template>
  <div class="flex flex-col bg-global-background min-h-full">
    <a-row class="h-full">
      <!-- 左侧布局 -->
      <a-col :span="8" class="border-r border-gray-200 dark:!border-border overflow-auto h-[calc(100vh-100px)]">
        <div class="p-6">
          <!-- 模型选择 -->
          <div class="mb-6">
            <div class="text-sm text-gray-600 dark:!text-custom mb-2"> Model </div>
            <a-select
              v-model:value="settings.model.code"
              class="w-full"
              @change="(value) => {
                const selectedModel = modelOptions.find(model => model.code === value)
                if (selectedModel) {
                  settings.model = {
                    code: selectedModel.code,
                    provider: selectedModel.provider
                  }
                }
              }"
            >
              <a-select-option v-for="model in modelOptions" :key="model.code" :value="model.code">
                {{ model.name || model.code }}
              </a-select-option>
            </a-select>
          </div>

          <!-- System Prompt -->
          <div class="mb-6">
            <div class="flex items-center mb-2">
              <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">System Prompt</span>
              <a-tooltip>
                <template #title> 系统提示词，用于设定AI助手的行为和角色定位 </template>
                <QuestionCircleOutlined class="ml-1 text-gray-400" />
              </a-tooltip>
            </div>
            <a-textarea
              v-model:value="settings.systemPrompt"
              class="bg-area-fill text-custom border-input-border"
              :rows="8"
              placeholder="请输入系统提示词"
            />
          </div>

          <!-- Max Tokens -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Max Tokens</span>
                <a-tooltip>
                  <template #title> 最大生成长度 </template>
                  <QuestionCircleOutlined class="ml-1 text-gray-400" />
                </a-tooltip>
              </div>
              <a-input-number
                v-model:value="settings.maxTokens"
                :min="1"
                :max="8192"
                class="w-24 bg-input-fill !text-custom border-input-border"
              />
            </div>
            <a-slider v-model:value="settings.maxTokens" :min="1" :max="8192" />
          </div>

          <!-- Temperature -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Temperature</span>
                <a-tooltip>
                  <template #title> 温度参数，控制输出的随机性 </template>
                  <QuestionCircleOutlined class="ml-1 text-gray-400" />
                </a-tooltip>
              </div>
              <a-input-number
                v-model:value="settings.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                class="w-24  bg-input-fill !text-custom border-input-border"
              />
            </div>
            <a-slider v-model:value="settings.temperature" :min="0" :max="2" :step="0.1" />
          </div>

          <!-- Top-P -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Top-P</span>
                <a-tooltip>
                  <template #title> 核采样阈值 </template>
                  <QuestionCircleOutlined class="ml-1 text-gray-400" />
                </a-tooltip>
              </div>
              <a-input-number
                v-model:value="settings.topP"
                :min="0"
                :max="1"
                :step="0.05"
                class="w-24 bg-input-fill !text-custom border-input-border"
              />
            </div>
            <a-slider v-model:value="settings.topP" :min="0" :max="1" :step="0.05" />
          </div>

          <!-- Top-K -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Top-K</span>
                <a-tooltip>
                  <template #title> 保留最高概率的K个词 </template>
                  <QuestionCircleOutlined class="ml-1 text-gray-400" />
                </a-tooltip>
              </div>
              <a-input-number
                v-model:value="settings.topK"
                :min="1"
                :max="100"
                class="w-24 bg-input-fill !text-custom border-input-border"
              />
            </div>
            <a-slider v-model:value="settings.topK" :min="1" :max="100" />
          </div>

          <!-- Frequency Penalty -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Frequency Penalty</span>
                <a-tooltip>
                  <template #title> 频率惩罚参数 </template>
                  <QuestionCircleOutlined class="ml-1 text-gray-400" />
                </a-tooltip>
              </div>
              <a-input-number
                v-model:value="settings.frequencyPenalty"
                :min="0"
                :max="2"
                :step="0.1"
                class="w-24 bg-input-fill !text-custom border-input-border"
              />
            </div>
            <a-slider v-model:value="settings.frequencyPenalty" :min="0" :max="2" :step="0.1" />
          </div>

          <!-- 添加对比模型按钮 -->
          <div class="flex gap-4 items-center justify-center">
            <!-- <a-button
              v-if="!showComparison"
              class="flex items-center mb-4"
              @click="handleAddComparison"
            >
              添加对比模型
            </a-button> -->
          </div>

          <!-- 对比模型区域 -->
          <div v-if="showComparison">
            <div class="flex gap-4">
              <a-button class="flex-1" @click="handleReset"> 取消对比 </a-button>

              <a-button class="flex-1" @click="handleSync"> 同步参数 </a-button>
            </div>
            <!-- 复制一份相同的设置区域 -->
            <div class="border-gray-200 pt-6 mb-6">
              <!-- 模型选择 -->
              <div class="mb-6">
                <div class="text-sm text-gray-600 dark:!text-custom mb-2"> Model </div>
                <a-select
                  v-model:value="comparisonSettings.model.code"
                  class="w-full"
                  @change="(value) => {
                    const selectedModel = modelOptions.find(model => model.code === value)
                    if (selectedModel) {
                      comparisonSettings.model = {
                        code: selectedModel.code,
                        provider: selectedModel.provider
                      }
                    }
                  }"
                >
                  <a-select-option
                    v-for="model in modelOptions"
                    :key="model.code"
                    :value="model.code"
                  >
                    {{ model.name || model.code }}
                  </a-select-option>
                </a-select>
              </div>

              <!-- System Prompt -->
              <div class="mb-6">
                <div class="flex items-center mb-2">
                  <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">System Prompt</span>
                  <a-tooltip>
                    <template #title> 系统提示词，用于设定AI助手的行为和角色定位 </template>
                    <QuestionCircleOutlined class="ml-1 text-gray-400" />
                  </a-tooltip>
                </div>
                <a-textarea
                  v-model:value="comparisonSettings.systemPrompt"
                  class="bg-area-fill text-custom border-input-border"
                  :rows="4"
                  placeholder="请输入系统提示词"
                />
              </div>

              <!-- Max Tokens -->
              <div class="mb-6">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Max Tokens</span>
                    <a-tooltip>
                      <template #title> 最大生成长度 </template>
                      <QuestionCircleOutlined class="ml-1 text-gray-400" />
                    </a-tooltip>
                  </div>
                  <a-input-number
                    v-model:value="comparisonSettings.maxTokens"
                    :min="1"
                    :max="8192"
                    class="w-24 bg-input-fill !text-custom border-input-border"
                  />
                </div>
                <a-slider v-model:value="comparisonSettings.maxTokens" :min="1" :max="8192" />
              </div>

              <!-- Temperature -->
              <div class="mb-6">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Temperature</span>
                    <a-tooltip>
                      <template #title> 温度参数，控制输出的随机性 </template>
                      <QuestionCircleOutlined class="ml-1 text-gray-400" />
                    </a-tooltip>
                  </div>
                  <a-input-number
                    v-model:value="comparisonSettings.temperature"
                    :min="0"
                    :max="2"
                    :step="0.1"
                    class="w-24 bg-input-fill !text-custom border-input-border"
                  />
                </div>
                <a-slider
                  v-model:value="comparisonSettings.temperature"
                  :min="0"
                  :max="2"
                  :step="0.1"
                />
              </div>

              <!-- Top-P -->
              <div class="mb-6">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Top-P</span>
                    <a-tooltip>
                      <template #title> 核采样阈值 </template>
                      <QuestionCircleOutlined class="ml-1 text-gray-400" />
                    </a-tooltip>
                  </div>
                  <a-input-number
                    v-model:value="comparisonSettings.topP"
                    :min="0"
                    :max="1"
                    :step="0.05"
                    class="w-24 bg-input-fill !text-custom border-input-border"
                  />
                </div>
                <a-slider v-model:value="comparisonSettings.topP" :min="0" :max="1" :step="0.05" />
              </div>

              <!-- Top-K -->
              <div class="mb-6">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Top-K</span>
                    <a-tooltip>
                      <template #title> 保留最高概率的K个词 </template>
                      <QuestionCircleOutlined class="ml-1 text-gray-400" />
                    </a-tooltip>
                  </div>
                  <a-input-number
                    v-model:value="comparisonSettings.topK"
                    :min="1"
                    :max="100"
                    class="w-24 bg-input-fill !text-custom border-input-border"
                  />
                </div>
                <a-slider v-model:value="comparisonSettings.topK" :min="1" :max="100" />
              </div>

              <!-- Frequency Penalty -->
              <div class="mb-6">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center">
                    <span class="text-sm text-gray-600 dark:!text-[var(--text-secondary)]">Frequency Penalty</span>
                    <a-tooltip>
                      <template #title> 频率惩罚参数 </template>
                      <QuestionCircleOutlined class="ml-1 text-gray-400" />
                    </a-tooltip>
                  </div>
                  <a-input-number
                    v-model:value="comparisonSettings.frequencyPenalty"
                    :min="0"
                    :max="2"
                    :step="0.1"
                    class="w-24 bg-input-fill !text-custom border-input-border"
                  />
                </div>
                <a-slider
                  v-model:value="comparisonSettings.frequencyPenalty"
                  :min="0"
                  :max="2"
                  :step="0.1"
                />
              </div>
            </div>
          </div>
        </div>
      </a-col>

      <!-- 右侧内容区域 -->
      <a-col :span="16" class="h-full">
        <div class="h-full flex flex-col flex-1">
          <!-- 上方区域：对比模型和历史问题 -->
          <div class="p-6 pb-0">
            <!-- 对话区域 -->
            <div class="mb-6">
              <!-- 单个模型对话区域 -->
              <div v-if="!showComparison" class="bg-area-fill p-4 mb-4 rounded-lg">
                <div class="text-base text-gray-900 dark:!text-custom">
                  {{ settings.model.code }}
                </div>
              </div>

              <!-- 对比模型对话区域 -->
              <div v-if="showComparison" class="flex gap-4">
                <!-- 左侧模型对话 -->
                <div class="flex-1 bg-gray-50 p-4 rounded-lg">
                  <div class="text-base text-gray-900 dark:!text-custom">
                    {{ settings.model.code }}
                  </div>
                </div>

                <!-- 右侧模型对话 -->
                <div class="flex-1 bg-gray-50 p-4 rounded-lg">
                  <div class="text-base text-gray-900 dark:!text-custom">
                    {{ comparisonSettings.model.code }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 历史问题列表 -->
            <!-- <div class="flex flex-wrap gap-2 mb-4">
              <a-button 
                v-for="question in historyQuestions" 
                :key="question"
                type="default"
                class="bg-gray-50 border border-gray-200 hover:border-purple-400 hover:text-purple-600"
              >
                {{ question }}
              </a-button>
            </div> -->
          </div>

          <!-- 下方区域：聊天容器 -->
          <div class="flex-1 h-full p-5">
            <AIChatContainer
              ref="chatContainer"
              :key="chatKey"
              :width="'100%'"
              :maxHeight="'calc(100vh - 300px)'"
              :systemShowChatHeader="true"
              class="h-full dark:!bg-area-fill"
              :sseUrl="`${PREFIX_DEBUG}?stream=true&tabId=${TAB_ID}&aip-session=${AI_TOKEN}`"
              :sendSSEMessage="handleSendSSEMessage"
              :menuList="[]"
              :selectInfo="{
                name: '',
                data: []
              }"
            />
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  QuestionCircleOutlined,
} from '@ant-design/icons-vue'
import { AIChatContainer } from '@yss/ai-chat-components'
import { PREFIX_DEBUG  } from '@/apis/designer'
import { getModelPage } from '@/apis/model'
import { v4 as uuidv4 } from 'uuid'


const showComparison = ref(false)

interface ModelSettings {
  model: {
    code: string
    provider: string
  }
  systemPrompt: string
  maxTokens: number
  temperature: number
  topP: number
  topK: number
  frequencyPenalty: number
}

const settings = ref<ModelSettings>({
  model: {
    code: 'Qwen-14B',
    provider: 'qwen'
  },
  systemPrompt: '',
  maxTokens: 8000,
  temperature: 20.0,
  topP: 10.0,
  topK: 100.0,
  frequencyPenalty: 5.0
})

// 对比模型设置也需要更新
const comparisonSettings = ref<ModelSettings>({ ...settings.value })

const handleAddComparison = () => {
  showComparison.value = true
}

const handleReset = () => {
  showComparison.value = false
}

const handleSync = () => {
  comparisonSettings.value = { ...settings.value }
}

// 历史问题列表
const historyQuestions = ref([
  '有诺贝尔数学奖吗？',
  '鲨鱼为什么也会被淹死',
  '喝酒脸红是会喝酒的表现吗？',
  '如何评价周杰伦在歌坛的地位'
])

// 添加 AIChatContainer 需要的变量
const chatKey = ref(0)
const TAB_ID = ref(uuidv4())
const AI_TOKEN = ref(uuidv4())

// 添加模型列表的类型定义
interface ModelOption {
  code: string
  provider: string
  name?: string
  // 根据实际接口返回数据补充其他字段
}

// 添加模型列表的响应式数据
const modelOptions = ref<ModelOption[]>([])

// 获取模型列表数据
const fetchModelOptions = async () => {
  try {
    const response = await getModelPage({})
    if (response.code === 200 && response.data) {
      modelOptions.value = response.data.records

      // 如果当前没有选中的模型，默认选择第一个
      if (modelOptions.value.length > 0 && !settings.value.model.code) {
        const defaultModel = modelOptions.value[0]
        settings.value.model = {
          code: defaultModel.code,
          provider: defaultModel.provider
        }
      }
    }
  } catch (error) {
    console.error('Failed to fetch model options:', error)
  }
}

// 组件挂载时获取模型列表
onMounted(() => {
  fetchModelOptions()
})

// 在组件顶部添加全局变量
const responseContent = ref<string[]>([])

const handleSendSSEMessage = async ({ question }: { question: string }) => {
  try {
    // 每次发送新消息时清空响应内容数组
    responseContent.value = []

    // 构建消息数组
    const messages = []
    if (settings.value.systemPrompt) {
      messages.push({
        role: 'system',
        content: settings.value.systemPrompt
      })
    }
    messages.push({
      role: 'user',
      content: question
    })

    // 修改请求路径，确保使用正确的API路径
    const response = await fetch('../aip/model/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 添加必要的认证头
        'Accept': 'application/json'
      },
      credentials: 'include', // 添加凭证
      body: JSON.stringify({
        sessionId: AI_TOKEN.value,
        tabId:TAB_ID.value,//
        model: settings.value.model,
        messages,
        maxTokens: settings.value.maxTokens,
        temperature: settings.value.temperature,
        topP: settings.value.topP,
        topK: settings.value.topK,
        frequencyPenalty: settings.value.frequencyPenalty
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
  } catch (error) {
    return {
      code: 500,
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}
</script>

<style scoped>
.ant-row {
  height: 100%;
}

/* 自定义滚动条样式 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #E5E7EB transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #E5E7EB;
  border-radius: 3px;
}
</style>
