<template>
  <div class="min-h-full bg-global-background flex items-center">
    <div class="flex gap-8 w-full p-4 bg-area-fill items-center dark:!bg-black">
      <!-- 左侧图片 -->
      <div class="w-[42%] h-full">
        <div class="w-full h-full flex items-center justify-center">
          <video
            src="../../assets/nav.mp4"
            class="w-full h-full object-contain bg-white"
            loop
            autoplay
            muted
          ></video>
        </div>
      </div>

      <!-- 右侧菜单 -->
      <div class="flex-1 flex flex-col gap-2.5 h-full">
        <router-link
          v-for="menu in homeMenus"
          :key="menu.title"
          :to="menu.link"
          class="flex-1 p-6 rounded-lg hover:bg-primary dark:hover:!bg-primary-light transition-colors group"
        >
          <h2 class="text-xl font-bold mb-2 text-primary group-hover:!text-white">
            {{ menu.title }}
          </h2>
          <p class="text-sm text-primary group-hover:!text-white/90">
            {{ menu.description }}
          </p>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { homeMenus } from './constants'
import { useAppStore } from '../../store/app'
import { onMounted } from 'vue'

const appStore = useAppStore()

// 在组件挂载时加载枚举数据
onMounted(async () => {
  // 如果枚举数据还没有加载，则进行加载
  if (!appStore.enumLoaded) {
    try {
      await appStore.fetchEnumList()
      console.log('枚举数据加载成功')
    } catch (error) {
      console.error('枚举数据加载失败:', error)
    }
  }
})
</script>

<style></style>
