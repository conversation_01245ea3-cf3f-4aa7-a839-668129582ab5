export const homeMenus = [
  {
    title: 'NPDP产品研发过程',
    description: '产品版本管理、需求处理、L3-L4-L5生模型、核心功能验证、原型MVP',
    icon: '产品图标路径',
    link: '/aiProducts'
  },
  {
    title: 'Agent数智员工',
    description: 'AgentFlow编排、Agent设计、MetaTeam接入端点、数据/能力/算法库对接',
    icon: '工程师图标路径',
    link: '/designer/files'
  },
  {
    title: '质量过程管理',
    description: '产品、POC、项目质量过程、投产迭代流程、CI/CD工具、环境资源数据库',
    icon: '质量门图标路径',
    link: 'http://*************:8085/#/testmanagement'
  },
  {
    title: '参数配置与系统管理',
    description:
      '环境治理、知识化过程管理、甲方大模型训练、推理模型版本迭代、模型能力评测、运行环境监控、日志收集、运维工具箱',
    icon: '运行时图标路径',
    link: '/operations/logManagement'
  }
]
