<template>
  <a-result status="403" title="403" sub-title="对不起，您没有权限访问此页面。">
    <template #extra>
      <a-space>
        <a-button type="primary" @click="goToHome">返回首页</a-button>
        <a-button @click="goBack">返回上一页</a-button>
      </a-space>
    </template>
  </a-result>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goToHome = () => {
  router.push('/')
}

const goBack = () => {
  router.back()
}
</script>

<style scoped>
:deep(.ant-result-title) {
  color: var(--text-color) !important;
}
:deep(.ant-result-subtitle) {
  color: var(--guide-text-color) !important;
}
</style>
