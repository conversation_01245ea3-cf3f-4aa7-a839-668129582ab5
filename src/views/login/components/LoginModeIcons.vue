<template>
  <div>
    <div class="flex items-center justify-between">
      <!-- 时间密码 -->
      <div class="relative group" :class="{'cursor-pointer': !disabled, 'cursor-not-allowed': disabled}">
        <!-- <div
          v-if="modelValue === 'time'"
          class="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-gray-800 text-text-white text-xs px-2 py-1 rounded whitespace-nowrap"
        >
          TOTP
        </div> -->
        <div
          class="w-14 h-14 border border-border rounded-lg flex items-center justify-center bg-input-fill border-input-border"
          :class="{ 
            '!bg-[var(--login-btn-color)] dark:hover:!bg-primary-hover dark:!border-border !text-white': modelValue === 'time' && !disabled,
            'opacity-60': disabled 
          }"
          @click="!disabled && $emit('update:modelValue', 'time')"
        >
          <ClockCircleOutlined
            class="text-xl text-gray-600"
            :class="{'!text-white': modelValue === 'time' && !disabled}"
          />
        </div>
        <div
          class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black/75 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap"
        >
          时间密码登录
        </div>
      </div>

      <!-- 手机验证 -->
      <div class="relative cursor-not-allowed">
        <div
          class="w-14 h-14 border border-border rounded-lg flex items-center justify-center bg-input-fill border-input-border"
          :class="{'opacity-60': disabled, 'opacity-40': !disabled}"
        >
          <MobileOutlined class="text-xl text-gray-600 dark:!text-custom" />
        </div>
      </div>

      <!-- 邮箱验证 -->
      <div class="relative cursor-not-allowed">
        <div
          class="w-14 h-14 border border-border rounded-lg flex items-center justify-center bg-input-fill border-input-border"
          :class="{'opacity-60': disabled, 'opacity-40': !disabled}"
        >
          <MailOutlined class="text-xl text-gray-600 dark:!text-custom" />
        </div>
      </div>

      <!-- 点击验证 -->
      <div class="relative cursor-not-allowed">
        <div
          class="w-14 h-14 border border-border rounded-lg flex items-center justify-center bg-input-fill border-input-border"
          :class="{'opacity-60': disabled, 'opacity-40': !disabled}"
        >
          <QrcodeOutlined class="text-xl text-gray-600 dark:!text-custom" />
        </div>
      </div>

      <!-- 指纹登录 -->
      <div class="relative cursor-not-allowed">
        <div
          class="w-14 h-14 border border-border rounded-lg flex items-center justify-center bg-input-fill border-input-border"
          :class="{'opacity-60': disabled, 'opacity-40': !disabled}"
        >
          <ScanOutlined class="text-xl text-gray-600 dark:!text-custom" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  ClockCircleOutlined,
  ScanOutlined,
  MobileOutlined,
  MailOutlined,
  QrcodeOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  modelValue: {
    type: String,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])
</script>
