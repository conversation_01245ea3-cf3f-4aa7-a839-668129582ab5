<template>
  <div class="grid grid-cols-6 gap-4">
    <input
      v-for="i in 6"
      :key="i"
      ref="inputRefs"
      v-model="codeArray[i-1]"
      type="text"
      maxlength="1"
      class="w-10 h-10 text-center border rounded bg-input-fill border-input-border text-custom outline-none"
      @input="handleInput($event, i)"
      @keydown.delete="handleDelete($event, i)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => Array(6).fill('') // 提供默认值
  },
  autoFocus: {
    type: Boolean,
    default: false  // 修改为默认不自动获取焦点
  }
})

const emit = defineEmits(['update:modelValue', 'complete'])
const inputRefs = ref([])

// 使用本地变量，避免直接修改 props
const codeArray = ref(Array(6).fill(''))
// 添加一个标志，防止重复触发complete事件
const hasEmittedComplete = ref(false)

// 初始化 codeArray
onMounted(() => {
  if (Array.isArray(props.modelValue) && props.modelValue.length === 6) {
    codeArray.value = [...props.modelValue]
  }

  // 只有当autoFocus为true时才自动获取焦点
  if (props.autoFocus && inputRefs.value[0]) {
    inputRefs.value[0].focus()
  }
})

// 监听 codeArray 变化，但避免无限循环
let isUpdatingFromProps = false

// 监听 props.modelValue 变化
watch(() => props.modelValue, (newVal) => {
  if (isUpdatingFromProps) return

  if (Array.isArray(newVal) && newVal.length === 6) {
    isUpdatingFromProps = true
    codeArray.value = [...newVal]
    nextTick(() => {
      isUpdatingFromProps = false
    })
  }
}, { deep: true })

// 监听 codeArray 变化
watch(codeArray, (newVal) => {
  if (isUpdatingFromProps) return

  isUpdatingFromProps = true
  emit('update:modelValue', newVal)

  // 检查是否所有输入框都已填写且未触发过complete事件
  if (newVal.every(code => code !== '') && !hasEmittedComplete.value) {
    hasEmittedComplete.value = true
    emit('complete', newVal)
  } else if (!newVal.every(code => code !== '')) {
    // 如果有输入框被清空，重置标志
    hasEmittedComplete.value = false
  }

  nextTick(() => {
    isUpdatingFromProps = false
  })
}, { deep: true })

const handleInput = (event: Event, index: number) => {
  const target = event.target as HTMLInputElement
  const value = target.value
  if (value && index < 6) {
    const nextInput = inputRefs.value[index]
    if (nextInput) nextInput.focus()
  }
}

const handleDelete = (event: KeyboardEvent, index: number) => {
  if (!codeArray.value[index - 1] && index > 1) {
    const prevInput = inputRefs.value[index - 2]
    if (prevInput) prevInput.focus()
  }
}

// 添加 clear 方法
defineExpose({
  clear: () => {
    // 清空输入框的值
    codeArray.value = Array(6).fill('')
    // 重置标志
    hasEmittedComplete.value = false
    // 重置焦点到第一个输入框
    if (inputRefs.value[0]) {
      inputRefs.value[0].focus()
    }
  }
})
</script>
