<template>
  <div
    class="qrcode-verification"
    :style="{
      height: '500px',
      marginTop: '60px'
    }"
  >
    <!-- 二维码区域 - 仅在 showQRCode 为 true 且未提交时显示 -->
    <div class="qrcode-container" v-if="qrCodeUrl">
      <div class="qrcode-tip">
        <p>请先下载TOTP应用或您的手机上已有TOTP应用</p>
      </div>
      <div class="qrcode-wrapper">
        <img :src="qrCodeUrl" alt="验证二维码" class="qrcode-image" />
      </div>
      <div class="qrcode-tip">
        <p>使用您的 TOTP 应用扫描二维码快速验证!</p>
      </div>
    </div>

    <!-- 验证码输入区域 -->
    <div class="verification-container">
      <div class="qrcode-tip">
        <p>请确认TOTP验证码</p>
      </div>
      <div class="verification-input">
        <VerificationInput
          ref="verificationInputRef"
          :model-value="verificationCode"
          :auto-focus="autoFocus"
          @update:model-value="handleVerificationUpdate"
          @complete="handleVerificationComplete"
        />
      </div>

      <div class="verification-actions">
        <button
          type="submit"
          class="w-full bg-gray-800 hover:bg-gray-700 text-white rounded-md py-3 font-medium"
          @click="handleSubmit"
        >
          注册
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import VerificationInput from '@/views/login/components/VerificationInput.vue'

const props = defineProps({
  qrCodeUrl: {
    type: String,
    default: ''
  },
  codeLength: {
    type: Number,
    default: 6
  },
  autoFocus: {
    type: Boolean,
    default: false
  }

})

const emit = defineEmits(['register'])

const verificationInputRef = ref()
const verificationCode = ref<string[]>([])

const handleVerificationUpdate = (value: string[]) => {
  verificationCode.value = value
}

const clearVerificationCode = () => {
  verificationCode.value = []
  // 如果 VerificationInput 组件提供了 clear 方法，可以直接调用
  if (verificationInputRef.value?.clear) {
    verificationInputRef.value.clear()
  }
  // 强制更新输入框的值
  handleVerificationUpdate([])
}

const handleVerificationComplete = (code: string[]) => {
  verificationCode.value = code
}
const handleSubmit = () => {
  if (verificationCode.value.length === props.codeLength) {
    const code = verificationCode.value.join('')
    emit('register', code)
    // 使用新的清空方法
    setTimeout(() => {
      clearVerificationCode()
    }, 500)
  }
}
</script>

<style scoped>
.qrcode-verification {
  @apply flex flex-col items-center space-y-6 p-6 bg-white h-[500px];
}

.qrcode-container {
  @apply flex flex-col items-center space-y-4;
}

.qrcode-wrapper {
  @apply w-48 h-48 border border-gray-200 rounded-lg flex items-center justify-center bg-white;
}

.qrcode-image {
  @apply w-full h-full object-contain;
}

.qrcode-placeholder {
  @apply text-gray-400 text-2xl;
}

.qrcode-tip {
  @apply text-center space-y-1;
}

.verification-container {
  @apply w-full max-w-xs space-y-4;
}

.verification-input {
  @apply flex justify-center;
}

.verification-actions {
  @apply flex justify-center;
}

:deep(.ant-btn-link) {
  @apply text-primary hover:text-primary-hover dark:!text-[var(--grey-auxiliary-color)];
}
</style>
