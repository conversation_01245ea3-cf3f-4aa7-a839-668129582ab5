<template>
  <a-form
    ref="formRef"
    :model="formState"
    layout="vertical"
    name="forgotPassword"
    autocomplete="off"
    size="large"
  >
    <a-form-item
      name="username"
      label="用户名"
      :rules="formRules.username"
    >
      <a-input
        v-model:value="formState.username"
        placeholder="请输入用户名"
        :disabled="showVerification"
      >
        <template #prefix>
          <UserOutlined />
        </template>
      </a-input>
    </a-form-item>

    <a-form-item
      name="newPassword"
      label="新密码"
      :rules="formRules.newPassword"
    >
      <a-input-password
        v-model:value="formState.newPassword"
        placeholder="请输入新密码"
        :disabled="showVerification"
      >
        <template #prefix>
          <LockOutlined />
        </template>
      </a-input-password>
    </a-form-item>

    <a-form-item
      name="confirmPassword"
      label="确认密码"
      :rules="formRules.confirmPassword"
    >
      <a-input-password
        v-model:value="formState.confirmPassword"
        placeholder="请确认新密码"
        :disabled="showVerification"
      >
        <template #prefix>
          <LockOutlined />
        </template>
      </a-input-password>
    </a-form-item>

    <!-- 验证码输入框 -->
    <a-form-item
      v-if="showVerification"
      name="totpCode"
      label="验证码"
      :rules="formRules.totpCode"
    >
      <div class="verification-input-container">
        <VerificationInput
          :model-value="formState.totpCode"
          :auto-focus="true"
          @update:model-value="handleVerificationUpdate"
          @complete="handleVerificationComplete"
        />
      </div>
    </a-form-item>

    <a-form-item>
      <button
        type="submit"
        class="w-full bg-gray-800 hover:bg-gray-700 dark:!border dark:!bg-[var(--login-btn-color)]  dark:hover:!bg-primary-hover dark:!border-border dark:hover:bg-gray-700 text-white rounded-md py-3 font-medium"
        @click="handleSubmit"
      >
        {{ showVerification ? '确认重置' : '提交' }}
      </button>
    </a-form-item>

    <div class="text-right">
      <span
        class="text-primary hover:text-primary-hover dark:!text-[var(--grey-auxiliary-color)] cursor-pointer"
        @click="$emit('switch-mode', 'login')"
      >返回登录</span>
    </div>
  </a-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { forgotPassword } from '@/apis/system'
import type { ForgotPasswordParams } from '@/apis/system'
import type { FormInstance } from 'ant-design-vue'
import VerificationInput from './VerificationInput.vue'

const emit = defineEmits(['switch-mode'])
const formRef = ref<FormInstance>()
const showVerification = ref(false)

// 验证函数
const validateConfirmPassword = async (_rule: any, value: string) => {
  if (value !== formState.newPassword) {
    throw new Error('两次输入的密码不一致')
  }
}

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名' },
    {
      pattern: /^(?=.*[a-zA-Z])[a-zA-Z0-9]{4,20}$/,
      message: '用户名必须包含字母，长度4-20位，只能包含字母和数字'
    }
  ],
  totpCode: [
    { required: true, message: '请输入验证码，验证码必须是6位' }
    // { len: 6, message: '验证码必须是6位' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码' },
    { min: 6, message: '密码长度不能小于6位' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码' },
    { validator: validateConfirmPassword }
  ]
}

const formState = reactive({
  username: '',
  totpCode: [],
  newPassword: '',
  confirmPassword: ''
})

// 处理验证码更新
const handleVerificationUpdate = (value: string[]) => {
  formState.totpCode = value
}

// 处理验证码完成输入
const handleVerificationComplete = (code: string[]) => {
  formState.totpCode = code
}

// 验证第一步表单
// const validateFirstStep = async () => {
//   try {
//     await formRef.value?.validateFields(['username', 'newPassword', 'confirmPassword'])
//     return true
//   } catch (error) {
//     return false
//   }
// }

const handleSubmit = async () => {
  try {
    // 第二步：验证
    await formRef.value?.validate()

    const forgotData: ForgotPasswordParams = {
      userName: formState.username,
      totpCode: formState.totpCode.join(''),
      newPwd: formState.newPassword,
      confirmPwd: formState.confirmPassword
    }

    const response = await forgotPassword(forgotData)

    if (response.code === 200) {
      if (response.data.type === 1) {
        showVerification.value = true
      } else if (response.data.type === 2) {
        message.success('密码重置成功')
        emit('switch-mode', 'login')
      }
    }
  } catch (error: any) {
    console.error('操作失败:', error)
    message.error(error.message || '操作失败，请稍后重试')
  }
}
</script>

<style scoped>
.text-primary {
  @apply text-blue-600;
}
.text-primary-hover {
  @apply text-blue-700;
}
.verification-input-container {
  @apply flex justify-center w-full;
}
</style>