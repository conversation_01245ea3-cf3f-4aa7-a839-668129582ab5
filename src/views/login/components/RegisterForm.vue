<template>
  <div>
    <!-- 注册表单 -->
    <a-form
      :model="formState"
      name="register"
      autocomplete="off"
      layout="vertical"
      size="large"
    >
      <a-form-item
        name="departIds"
        label="部门"
        :rules="formRules.departIds"
      >
        <a-tree-select
          v-model:value="formState.departIds"
          :tree-data="departmentTree"
          placeholder="请选择部门"
          :field-names="{ label: 'departName', value: 'id', children: 'children' }"
          tree-default-expand-all
          :show-line="{ showLeafIcon: false }"
        />
      </a-form-item>

      <a-form-item
        name="realname"
        label="姓名"
        :rules="formRules.realname"
      >
        <a-input
          v-model:value="formState.realname"
          placeholder="请输入姓名"
        >
          <template #prefix>
            <UserOutlined />
          </template>
        </a-input>
      </a-form-item>

      <a-form-item
        name="username"
        label="用户名"
        :rules="formRules.username"
      >
        <a-input
          v-model:value="formState.username"
          placeholder="请输入用户名"
        >
          <template #prefix>
            <UserOutlined />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item
        label="性别"
        name="sex"
      >
        <a-select
          v-model:value="formState.sex"
          placeholder="请选择性别"
          :options="sexOptions"
        >
          <template #suffixIcon>
            <DownOutlined />
          </template>
        </a-select>
      </a-form-item>

      <a-form-item
        name="password"
        label="密码"
        :rules="formRules.password"
      >
        <a-input-password
          v-model:value="formState.password"
          placeholder="请输入密码"
        >
          <template #prefix>
            <LockOutlined />
          </template>
        </a-input-password>
      </a-form-item>
      <a-form-item
        name="confirmPwd"
        label="确认密码"
        :rules="formRules.confirmPwd"
      >
        <a-input-password
          v-model:value="formState.confirmPwd"
          placeholder="请确认密码"
        >
          <template #prefix>
            <LockOutlined />
          </template>
        </a-input-password>
      </a-form-item>
      <!-- 二维码验证组件 -->
      <QRCodeVerification
        v-if="showQRVerification"
        :qr-code-url="qrCodeUrl"
        @register="handleRegister"
      />
      <a-form-item v-if="!showQRVerification">
        <button
          class="w-full bg-gray-800 hover:bg-gray-700 dark:!border dark:!bg-[var(--login-btn-color)]  dark:hover:!bg-primary-hover dark:!border-border text-white rounded-md py-3 font-medium"
          @click="handleSubmit"
        >
          下一步
        </button>
      </a-form-item>

     
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { UserOutlined, LockOutlined, DownOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { getFreeDepartList, register, getTOTPQRCode } from '@/apis/system'
import type { RegisterParams } from '@/apis/system'
import QRCodeVerification from './QRCodeVerification.vue' // 导入二维码验证组件

const emit = defineEmits(['switch-mode', 'register-success'])

const departmentTree = ref([])
const showQRVerification = ref(false) // 控制是否显示二维码验证组件
const registerData = ref<RegisterParams | null>(null) // 存储注册数据
const qrCodeUrl = ref('') // 二维码URL
const isVerified = ref(false) // 是否已验证第一次
const totpSecret = ref('') // TOTP密钥

const formState = reactive({
  departIds: '',
  realname: '',
  username: '',
  sex: 1,
  password: '',
  confirmPwd: ''
})
// 性别选项
const sexOptions = [
  { label: '男', value: 1 },
  { label: '女', value: 2 }
]

// 先定义验证函数
const validateconfirmPwd = async (_rule: any, value: string) => {
  if (value !== formState.password) {
    throw new Error('两次输入的密码不一致')
  }
}

// 再定义表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名' },
    {
      pattern: /^(?=.*[a-zA-Z])[a-zA-Z0-9]{4,20}$/,
      message: '用户名必须包含字母，长度4-20位，只能包含字母和数字'
    }
  ],
  realname: [{ required: true, message: '请输入姓名' }],
  departIds: [{ required: true, message: '请选择部门' }],
  password: [
    { required: true, message: '请输入密码' },
    { min: 6, message: '密码长度不能小于6位' }
  ],
  confirmPwd: [
    { required: true, message: '请确认密码' },
    { validator: validateconfirmPwd }
  ]
}

// 表单提交处理
const handleSubmit = async () => {
  try {
    // 表单验证
    const isValid = await validateForm()
    if (!isValid) return

    // 构造注册参数
    registerData.value = {
      username: formState.username,
      realname: formState.realname,
      password: formState.password,
      confirmPwd: formState.confirmPwd,
      departIds: formState.departIds, // 单个部门ID
      sex: formState.sex
    }

    // 获取TOTP二维码
    await fetchTOTPQRCode()

    // 显示二维码验证组件
    showQRVerification.value = true
  } catch (error: any) {
    message.error(error.message || '表单验证失败')
  }
}
// 注册
const handleRegister = async (code: string) => {
  try {
    if (!registerData.value) {
      message.error('注册数据丢失，请重新填写表单')
      showQRVerification.value = false
      return
    }

    // 添加TOTP验证码和密钥到注册数据
    registerData.value.totpCode = code
    registerData.value.totpSecret = totpSecret.value

    // 调用注册接口
    const response = await register(registerData.value)

    if (response.code === 200) {
      // message.success('注册成功')
      emit('register-success')
      emit('switch-mode', 'login')
    }
  } catch (error: any) {
    console.error('注册失败:', error)
    // message.error(error.message || '注册失败，请稍后重试')
  }
}

// 获取TOTP二维码
const fetchTOTPQRCode = async () => {
  try {
    const response = await getTOTPQRCode(formState)
    if (response.code === 200 && response.data) {
      qrCodeUrl.value = response.data.qrCode
      totpSecret.value = response.data.totpSecret
    }
  } catch (error: any) {
    throw error
  }
}

// 表单验证函数
const validateForm = async () => {
  // 检查必填字段
  if (!formState.departIds) {
    message.error('请选择部门')
    return false
  }
  if (!formState.realname) {
    message.error('请输入姓名')
    return false
  }
  if (!formState.username) {
    message.error('请输入用户名')
    return false
  }
  if (!formState.password) {
    message.error('请输入密码')
    return false
  }
  if (!formState.confirmPwd) {
    message.error('请确认密码')
    return false
  }

  // 验证用户名格式
  const usernamePattern = /^(?=.*[a-zA-Z])[a-zA-Z0-9]{4,20}$/
  if (!usernamePattern.test(formState.username)) {
    message.error('用户名必须包含字母，长度4-20位，只能包含字母和数字')
    return false
  }

  // 验证密码长度
  if (formState.password.length < 6) {
    message.error('密码长度不能小于6位')
    return false
  }

  // 验证两次密码是否一致
  if (formState.password !== formState.confirmPwd) {
    message.error('两次输入的密码不一致')
    return false
  }

  return true
}
// 处理第一次TOTP验证
const handleVerify = async (code: string) => {
  // try {
  //   const response = await verifyTOTP({
  //     username: formState.username,
  //     code,
  //     secret: totpSecret.value
  //   })
    
  //   if (response.code === 200) {
  //     message.success('验证成功，请再次输入验证码确认')
  //     isVerified.value = true
  //   } else {
  //     message.error(response.message || '验证失败，请重试')
  //   }
  // } catch (error: any) {
  //   console.error('验证TOTP失败:', error)
  //   message.error(error.message || '验证失败，请重试')
  // }
}

// 处理第二次TOTP验证并注册
const handleLogin = async (code: string) => {
  try {
    // 再次验证TOTP
    const verifyResponse = await verifyTOTP({
      username: formState.username,
      code,
      secret: totpSecret.value
    })
    
    if (verifyResponse.code !== 200) {
      message.error(verifyResponse.message || '验证失败，请重试')
      return
    }
    
    if (!registerData.value) {
      message.error('注册数据丢失，请重新填写表单')
      showQRVerification.value = false
      return
    }

    // 添加TOTP密钥到注册数据
    registerData.value.totpSecret = totpSecret.value

    // 调用注册接口
    const response = await register(registerData.value)

    if (response.code === 200) {
      message.success('注册成功')
      emit('register-success')
      emit('switch-mode', 'login')
    } else {
      message.error(response.message || '注册失败')
      isVerified.value = false // 重置验证状态
    }
  } catch (error: any) {
    console.error('注册失败:', error)
    message.error(error.message || '注册失败，请稍后重试')
    isVerified.value = false // 重置验证状态
  }
}

// 二维码验证取消处理
const handleVerificationCancel = () => {
  showQRVerification.value = false // 返回表单页面
  isVerified.value = false // 重置验证状态
}

// 获取部门树数据
const fetchDepartmentTree = async () => {
  try {
    const res = await getFreeDepartList()
    if (res.code === 200) {
      departmentTree.value = res.data
    } else {
      message.error(res.statusText || '获取部门数据失败')
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
    message.error('获取部门数据失败，请稍后重试')
  }
}

onMounted(() => {
  fetchDepartmentTree()
})
</script>

<style scoped>
.text-primary {
  @apply text-blue-600;
}
.text-primary-hover {
  @apply text-blue-700;
}

.verification-wrapper {
  @apply flex flex-col items-center;
}

.verification-actions {
  @apply flex justify-center;
}
</style>