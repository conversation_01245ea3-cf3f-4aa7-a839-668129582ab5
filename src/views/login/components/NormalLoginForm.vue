<template>
  <div class="space-y-6">
    <!-- 标题 -->
    <div class="text-center text-[14px] font-medium text-custom"> 请登录 </div>

    <!-- 用户名输入框 -->
    <div class="space-y-2">
      <div class="text-sm text-custom"> 用户名 </div>
      <div class="border border-border bg-input-fill border-input-border rounded-md px-4 py-3">
        <input
          ref="usernameInput"
          v-model="form.username"
          name="username"
          type="text"
          placeholder="请输入用户名"
          class="bg-transparent w-full outline-none text-sm text-custom"
        />
      </div>
    </div>

    <!-- 密码输入框 -->
    <div class="space-y-2">
      <div class="flex justify-between items-center">
        <span class="text-sm text-custom">密码</span>
      </div>
      <div class="border border-border bg-input-fill border-input-border rounded-md px-4 py-3">
        <input
          v-model="form.password"
          type="password"
          placeholder="请输入密码"
          class="bg-transparent w-full outline-none text-sm text-custom"
        />
      </div>
    </div>

    <!-- 多因素验证方式 - 始终显示 -->
    <div class="space-y-6">
      <div class="space-y-2">
        <div class="text-sm text-custom"> 多因素验证方式 </div>
        <div class="relative" :class="{'opacity-60 pointer-events-none': mfaType !== 'multi'}">
          <LoginModeIcons v-model="verifyType" :disabled="mfaType !== 'multi'" />
        </div>
      </div>

      <!-- 只有当mfaType为multi时才显示验证码输入框 -->
      <div v-if="mfaType === 'multi'">
        <!-- 手机验证码输入 -->
        <div v-if="verifyType === 'phone'" class="space-y-2">
          <div class="text-sm text-custom"> 手机号 </div>
          <div class="border border-border bg-white rounded-md px-4 py-3 flex items-center">
            <input
              v-model="form.phone"
              type="text"
              placeholder="请输入手机号"
              class="bg-transparent flex-1 outline-none text-sm text-text-primary"
            />
            <button class="text-sm text-primary hover:text-primary-hover dark:!text-[var(--grey-auxiliary-color)]"> 获取验证码 </button>
          </div>
          <div class="space-y-2">
            <div class="text-sm text-custom"> 验证码 </div>
            <VerificationInput
              v-model="dynamicCodeValue"
              :auto-focus="true"
              @complete="handleComplete"
            />
          </div>
        </div>

        <!-- 邮箱验证码输入 -->
        <div v-if="verifyType === 'email'" class="space-y-2">
          <div class="text-sm text-custom"> 邮箱 </div>
          <div class="border border-border bg-white rounded-md px-4 py-3 flex items-center">
            <input
              v-model="form.email"
              type="email"
              placeholder="请输入邮箱"
              class="bg-transparent flex-1 outline-none text-sm text-text-primary"
            />
            <button class="text-sm text-primary hover:text-primary-hover dark:!text-[var(--grey-auxiliary-color)]"> 获取验证码 </button>
          </div>
          <div class="space-y-2">
            <div class="text-sm text-custom"> 验证码 </div>
            <VerificationInput
              v-model="dynamicCodeValue"
              :auto-focus="true"
              @complete="handleComplete"
            />
          </div>
        </div>

        <!-- TOTP动态验证码输入框 -->
        <div v-if="verifyType === 'time'" class="space-y-2">
          <div class="text-sm text-custom"> TOTP验证码 </div>
          <VerificationInput v-model="dynamicCodeValue" :auto-focus="true" @complete="handleComplete" />
        </div>

        <!-- 点击验证 -->
        <div v-if="verifyType === 'click'" class="space-y-2">
          <div class="text-sm text-custom"> 点击验证 </div>
          <div class="border border-border bg-white rounded-md px-4 py-3 text-center">
            <span class="text-sm text-custom">请点击进行人机验证</span>
          </div>
        </div>

        <!-- 指纹验证 -->
        <div v-if="verifyType === 'fingerprint'" class="space-y-2">
          <div class="text-sm text-text-secondary"> 指纹验证 </div>
          <div class="border border-border bg-white rounded-md px-4 py-3 text-center">
            <span class="text-sm text-text-secondary">请进行指纹验证</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录按钮 -->
    <button 
      :disabled="shouldDisableLogin"
      class="w-full bg-[var(--login-btn-color)]  dark:hover:!bg-primary-hover dark:!border dark:!border-border text-white rounded-md py-3 font-medium"
      @click="handleManualLogin"
    >
      登录
    </button>

    <!-- 注册链接 -->
    <!-- <div class="flex items-center justify-between">
      <div class="text-center">
        <span class="text-text-secondary">还没有账号？</span>
        <span class="text-text-secondary">点此 </span>
        <span class="text-primary hover:text-primary-hover dark:!text-[var(--grey-auxiliary-color)] cursor-pointer">注册</span>
      </div>
      <a
        href="#"
        class="text-sm text-primary hover:text-primary-hover dark:!text-[var(--grey-auxiliary-color)] mr-2"
      >忘记密码?</a>
    </div> -->

    <!-- 添加插槽 -->
    <slot name="footer"></slot>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import LoginModeIcons from './LoginModeIcons.vue'
import VerificationInput from './VerificationInput.vue'

const props = defineProps({
  mfaType: {
    type: String,
    default: 'multi'
  },
  form: {
    type: Object,
    required: true
  },
  dynamicCode: {
    type: Array,
    default: () => new Array(6).fill('')  // 默认6位空字符串数组
  }
})

const emit = defineEmits(['update:form', 'update:dynamicCode', 'login'])

const verifyType = ref('time')  // 验证类型：'phone', 'email', 'time', 'click', 'fingerprint'
const form = ref({
  ...props.form,
  phone: '',
  email: ''
})
const dynamicCodeValue = ref([...props.dynamicCode])
const usernameInput = ref(null)

// 计算登录按钮禁用状态
const shouldDisableLogin = computed(() => {
  if (props.mfaType !== 'multi') return false
  return !Array.isArray(dynamicCodeValue.value) || 
         dynamicCodeValue.value.some(item => item === '')
})

onMounted(() => {
  // 使用ref直接聚焦
  if (usernameInput.value) {
    usernameInput.value.focus()
  }
})

// 监听表单变化
watch(form, (newVal) => {
  emit('update:form', newVal)
}, { deep: true })

// 监听动态验证码变化
watch(dynamicCodeValue, (newVal) => {
  emit('update:dynamicCode', newVal)
}, { deep: true })

// 处理验证码完成事件
const handleComplete = (code) => {
  emit('login', code)
}

// 处理手动点击登录按钮
const handleManualLogin = () => {
  emit('login', dynamicCodeValue.value)
}
</script>

<style scoped>
:deep(.ant-switch) {
  background: rgba(0, 0, 0, 0.25);
}
:deep(.ant-switch-checked) {
  background: #3182ce;
}
</style>
