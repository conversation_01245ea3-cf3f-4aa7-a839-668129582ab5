<template>
  <div class="flex flex-col relative min-h-screen bg-global-background">
    <!-- 背景图 - 改为 280px 高度 -->
    <div
      class="w-full h-[350px]"
      :style="{
        background: `url(${getImageUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }"
    >
      <!-- 内容区域 -->
      <div class="relative z-10">
        <!-- 顶部Logo和标题 -->
        <div class="flex items-center pt-8 px-8">
          <img :src="logoUrl" alt="Logo" class="h-8" />
          <div class="flex items-center text-white ml-4">
            <span class="text-2xl">|</span>
            <div class="ml-4 text-xl font-bold flex items-center">
              <AppstoreOutlined class="mr-2" />
              {{ WEBTITLE }}
            </div>
          </div>
        </div>

        <!-- 欢迎文本 -->
        <div class="text-center mt-6 flex-1" style="height: 200px;">
          <div class="text-white text-3xl font-bold mb-3">{{ welcomeText }}</div>
          <div class="text-white/80 max-w-2xl mx-auto space-y-1">
            <p v-for="(line, index) in loginDesc" :key="index">{{ line }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录框 -->
    <div
      class="flex flex-1 justify-center"
      :style="{
        // position: 'absolute',
        marginTop: currentMode === 'register' ? '-180px' : currentMode === 'login' ? '-140px' : '-140px',
        // left: '38%'
      }"
    >
      <div
        v-if="!showQRCode"
        style="height: 420px;"
        class="w-[457px] transition-transform scale-[0.7] bg-area-fill rounded-lg shadow-lg p-6 min-h-fit"
      >
        <div class="text-xl font-bold mb-6 text-center text-custom">
          {{ 
            currentMode === 'login' ? '用户登录' : 
            currentMode === 'register' ? '用户注册' : 
            '忘记密码'
          }}
        </div>
        <NormalLoginForm
          v-if="currentMode === 'login'"
            :mfa-type="mfaType"
          v-model:form="loginForm"
          @login="handleLogin"
          ref="loginFormRef"
        >
          <template #footer>
            <!-- 注册链接 -->
            <div class="flex items-center justify-between">
              <div class="text-center" v-if="showRegister">
                <span class="text-text-secondary">还没有账号？</span>
                <span class="text-text-secondary">点此 </span>
                <span
                  class="text-primary hover:text-primary-hover dark:!text-[var(--grey-auxiliary-color)] cursor-pointer"
                  @click="router.push('/auth/secure-signup/yss-verification/new-user')"
                  >注册</span
                >
              </div>
              <a
                href="#"
                class="text-sm text-primary hover:text-primary-hover dark:!text-[var(--grey-auxiliary-color)] mr-2"
                @click="currentMode = 'forgot-password'"
                >忘记密码?</a
              >
            </div>
          </template>
        </NormalLoginForm>

        <RegisterForm
          v-if="currentMode === 'register'"
          @switch-mode="currentMode = $event"
          @register-success="handleRegisterSuccess"
        />

        <ForgotPasswordForm
          v-if="currentMode === 'forgot-password'"
          @switch-mode="currentMode = $event"
        />
      </div>
      <!-- 二维码验证组件 -->
      <QRCodeVerification
        v-if="showQRCode"
        v-model:is-submitted="isSubmitted"
        :qr-code-url="qrCodeUrl"
        :code-length="6"
        :showQRCode="showQRCode"
        :autoFocus="true"
        @verify="handleVerify"
        @login="handleLogin"
      />
    </div>
    <Footer></Footer>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { AppstoreOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import NormalLoginForm from './components/NormalLoginForm.vue'
import RegisterForm from './components/RegisterForm.vue'
import ForgotPasswordForm from './components/ForgotPasswordForm.vue'
import { Footer } from '@/components'
import { WEBTITLE } from '@/constants'
import logoUrl from '@/assets/logo.png'
import getImageUrl from '@/assets/login_bg.png'
import { login, bindTotp, checkMFAType  } from '@/apis/system'
import QRCodeVerification from './components/QRCodeVerification.vue'
import { useAppStore } from '@/store/app'

const router = useRouter()

// 二维码相关状态
const showQRCode = ref(false)
const qrCodeUrl = ref('')
const isSubmitted = ref(false)
// const showQRCodeField = ref(false)

// 登录表单数据
const loginForm = ref({
  username: '',
  password: '',
  totpCode: ''
})
const dynamicCode = ref('')

const currentMode = ref('login') // 'login' | 'register' | 'forgot-password'

const appStore = useAppStore()

const loginFormRef = ref(null)
// 新增mfaType状态
const mfaType = ref('')

// 使用计算属性从枚举中获取欢迎文本
const welcomeText = computed(() => {
  const welcomeEnum = appStore.getEnum('login_welcome')
  return welcomeEnum.length > 0 ? welcomeEnum[0] : 'Welcome ：)'
})

// 使用计算属性从枚举中获取登录描述
const loginDesc = computed(() => {
  const descEnum = appStore.getEnum('login_desc')
  const desc = descEnum.length > 0 ? descEnum[0] : '在此进行YSS AI 子系统及模块应用管理，具体内容根据用户角色联动展示  本系统配备严格的身份验证体系，保障用户登录安全'

  // 将文本中的 \n 转换为数组，以便在模板中使用 v-for 渲染多行
  return desc.split('\\n')
})

// 是否显示注册选项
const showRegister = computed(() => {
  const registerEnum = appStore.getEnum('is_register')
  return registerEnum.length > 0 && registerEnum[0] === '1'
})

onMounted(async () => {
    // 获取多因素验证类型
    const res = await checkMFAType()
    mfaType.value = res.data.paramValue === 'Y' ?  'multi' : ''
 
})


// 添加 onMounted 钩子，在页面加载时获取枚举数据
onMounted(async () => {
  try {
    await appStore.fetchEnumList()
    console.log('枚举数据加载成功')
  } catch (error) {
    console.error('枚举数据加载失败:', error)
  }
})

const handleLogin = async (code) => {
  if (!loginForm.value.username) {
    message.error('请输入用户名')
    return
  }
  if (!loginForm.value.password) {
    message.error('请输入密码')
    return
  }
    // 根据验证类型调整验证逻辑
  if (mfaType.value === 'multi') {
    if (!code || code.length !== 6 || code.some(c => c === '')) {
      message.error('请输入完整的验证码')
      return
    }
  }

  try {
    const params = {
      username: loginForm.value.username,
      password: loginForm.value.password,
      // 根据验证类型动态传递参数
      ...(mfaType.value === 'multi' && { totpCode: code.join('') })
    }
    const response = await login(params)
    if (response.code === 200) {
      if (response.data.type === 1) {
        // 直接登录成功
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('userInfo', JSON.stringify(response.data.user))
        localStorage.setItem('menuTree', JSON.stringify(response.data.menuTree))
        message.success('登录成功')
        
        // 从localStorage获取重定向URL
        const redirectUrl = localStorage.getItem('redirectUrl')
        console.log('从localStorage获取的重定向URL：', redirectUrl)
        
        if (redirectUrl) {
          // 清除redirectUrl避免下次重用
          localStorage.removeItem('redirectUrl')
          // 跳转到保存的URL
          router.push(redirectUrl)
        } else {
          // 没有重定向URL，跳转到首页
          router.push('/')
        }
      } else if (response.data.type === 2) {
        // 显示二维码和验证码输入
        showQRCode.value = true
        qrCodeUrl.value = response.data.qrCode
        isSubmitted.value = false
      } else if (response.data.type === 3) {
        // 仅显示验证码输入
        showQRCode.value = true
        isSubmitted.value = true
      }
    }
  } catch (error) {
    message.error('登录失败,请稍后重试')
    console.error('登录错误：', error)
  }
}

const handleRegisterSuccess = () => {
  message.success('注册成功，请登录')
  currentMode.value = 'login'
}

// 处理二维码验证
const handleVerify = async (code) => {
  dynamicCode.value = code
  try {
    const response = await bindTotp({
      username: loginForm.value.username,
      password: loginForm.value.password,
      totpCode: code
    })

    if (response.code === 200) {
      // 验证成功后重新调用登录
      message.success('验证成功')((showQRCode.value = true)), (isSubmitted.value = true)
    } else {
      (showQRCode.value = true), (isSubmitted.value = false)
    }
  } catch (error) {
    message.error('验证失败，请重试')
  }
}

// 重置状态
const resetState = () => {
  showQRCode.value = false
  qrCodeUrl.value = ''
  isSubmitted.value = false
  loginForm.value = {
    username: '',
    password: '',
    totpCode: ''
  }
}

// 添加 onMounted 钩子，确保在组件挂载后聚焦用户名输入框
onMounted(() => {
  // 给浏览器一点时间来渲染DOM
  setTimeout(() => {
    if (currentMode.value === 'login' && !showQRCode.value) {
      // 找到用户名输入框并聚焦
      const usernameInput = document.querySelector('input[name="username"]') ||
                           document.querySelector('input[placeholder*="用户名"]') ||
                           document.querySelector('.login-form input:first-child');
      if (usernameInput) {
        usernameInput.focus();
      }
    }
  }, 100);
})

// 监听登录模式变化
watch(currentMode, (newMode) => {
  if (newMode !== 'login') {
    resetState()
  } else {
    // 当切换回登录模式时，也聚焦用户名输入框
    setTimeout(() => {
      const usernameInput = document.querySelector('input[name="username"]') ||
                           document.querySelector('input[placeholder*="用户名"]') ||
                           document.querySelector('.login-form input:first-child');
      if (usernameInput) {
        usernameInput.focus();
      }
    }, 100);
  }
})
</script>

<style scoped>
:deep(.ant-switch) {
  background: rgba(0, 0, 0, 0.25);
}
:deep(.ant-switch-checked) {
  background: #3182ce;
}
:deep(.ant-select-selection-placeholder) {
  color: rgba(0, 0, 0, 0.65);
}
</style>
