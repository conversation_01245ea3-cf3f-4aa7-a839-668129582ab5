<template>
  <div class="flex flex-col relative min-h-screen bg-global-background">
    <!-- 背景图 -->
    <div
      class="w-full h-[350px]"
      :style="{
        background: `url(${getImageUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }"
    >
      <!-- 内容区域 -->
      <div class="relative z-10">
        <!-- 顶部Logo和标题 -->
        <div class="flex items-center pt-8 px-8">
          <img :src="logoUrl" alt="Logo" class="h-8" />
          <div class="flex items-center text-white ml-4">
            <span class="text-2xl">|</span>
            <div class="ml-4 text-xl font-bold flex items-center">
              <AppstoreOutlined class="mr-2" />
              {{ WEBTITLE }}
            </div>
          </div>
        </div>

        <!-- 欢迎文本 -->
        <div class="text-center mt-6 flex-1" style="height: 200px;">
          <div class="text-white text-3xl font-bold mb-3">{{ welcomeText }}</div>
          <div class="text-white/80 max-w-2xl mx-auto space-y-1">
            <p v-for="(line, index) in loginDesc" :key="index">{{ line }}</p>
          </div>
        </div>
      </div> 
    </div>

    <!-- 注册框 -->
    <div
      class="flex flex-1 justify-center"
      :style="{
        marginTop: '-180px',
      }"
    >
      <div
        style="height: 420px;"
        class="w-[457px] transition-transform scale-[0.7] bg-area-fill rounded-lg shadow-lg p-6 min-h-fit"
      >
        <div class="text-xl font-bold mb-6 text-center text-custom">用户注册</div>
        <RegisterForm
          @switch-mode="goToLoginPage"
          @register-success="handleRegisterSuccess"
        />
      </div>
    </div>
    <Footer></Footer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { AppstoreOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import RegisterForm from '../login/components/RegisterForm.vue'
import { Footer } from '@/components'
import { WEBTITLE } from '@/constants'
import logoUrl from '@/assets/logo.png'
import getImageUrl from '@/assets/login_bg.png'
import { useAppStore } from '@/store/app'

const router = useRouter()
const appStore = useAppStore()

// 使用计算属性从枚举中获取欢迎文本
const welcomeText = computed(() => {
  const welcomeEnum = appStore.getEnum('login_welcome')
  return welcomeEnum.length > 0 ? welcomeEnum[0] : 'Welcome ：)'
})

// 使用计算属性从枚举中获取登录描述
const loginDesc = computed(() => {
  const descEnum = appStore.getEnum('login_desc')
  const desc = descEnum.length > 0 ? descEnum[0] : '在此进行YSS AI 子系统及模块应用管理，具体内容根据用户角色联动展示  本系统配备严格的身份验证体系，保障用户登录安全'

  // 将文本中的 \n 转换为数组，以便在模板中使用 v-for 渲染多行
  return desc.split('\\n')
})

// 添加 onMounted 钩子，在页面加载时获取枚举数据
onMounted(async () => {
  try {
    await appStore.fetchEnumList()
    console.log('枚举数据加载成功')
  } catch (error) {
    console.error('枚举数据加载失败:', error)
  }
})

const handleRegisterSuccess = () => {
  message.success('注册成功，请登录')
  // router.push('/login')
}

const goToLoginPage = () => {
  // router.push('/login')
}
</script>

<style scoped>
:deep(.ant-switch) {
  background: rgba(0, 0, 0, 0.25);
}
:deep(.ant-switch-checked) {
  background: #3182ce;
}
:deep(.ant-select-selection-placeholder) {
  color: rgba(0, 0, 0, 0.65);
}
</style> 