<template>
  <div class="h-full bg-global-background">
    <BaseTable :columns="columns" :dataSource="dataSource" :loading="loading" :search-schema="searchSchema"
      :total="total" @search="handleSearch" @reset="handleReset" @pageChange="handlePageChange">
      <!-- 操作按钮 -->
      <template #actions>
        <a-button type="primary" @click="handleAdd"> 创建 </a-button>
      </template>

      <template #action="{ record }">
        <a-space>
          <a-tooltip title="删除">
            <a-button type="link" @click="handleDelete(record)">
              <template #icon>
                <DeleteOutlined />
              </template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>

    <!-- 创建表单 -->
    <KeyPairForm v-model:visible="formVisible" @success="handleSuccess" />
  </div>
</template>
  
  <script lang="ts" setup>
  import { ref } from 'vue'
  import { message, Modal } from 'ant-design-vue'
  import { PlusOutlined, DownloadOutlined, DeleteOutlined } from '@ant-design/icons-vue'
  import { BaseTable } from '@/components'
  import { columns, searchSchema } from './constant'
  import type { KeyPairItem } from './constant'
  import KeyPairForm from './components/KeyPairForm.vue'
  import dayjs from 'dayjs'
  import { getKeyPairs, deleteKeyPair } from '@/apis/operations'
  
  // 表格数据
  const dataSource = ref<KeyPairItem[]>([])
  const loading = ref(false)
  const total = ref(0)
  const formVisible = ref(false)
  const pageNo = ref(1)
  const pageSize = ref(10)

  // 获取数据
  const getList = async (pageNo: number, pageSize: number, keyword: string | null) => {
    loading.value = true
    try {
      const res = await getKeyPairs({
        pageNo: pageNo,
        pageSize: pageSize,
        keyword: keyword,
      })
      dataSource.value = res?.data?.records || []
      total.value = res?.data?.total || 0
    } catch (error) {
      console.error('获取许可证列表失败:', error)
      message.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }
  
  // 搜索处理
  const handleSearch = (params) => {
    getList(pageNo.value, pageSize.value, params.keyword)
  }
  
  // 重置处理
  const handleReset = () => {
    pageNo.value = 1
    pageSize.value = 10
    getList(pageNo.value, pageSize.value, null)
  }
  
  // 分页处理
  const handlePageChange = ({ current, size, searchForm }) => {
    pageNo.value = current
    pageSize.value = size
    getList(pageNo.value, pageSize.value, searchForm.keyword || null)
  }

  // 创建
  const handleAdd = () => {
    formVisible.value = true
  }

  // 创建成功
  const handleSuccess = () => {
    getList(pageNo.value, pageSize.value, null)
  }

  // 删除
  const handleDelete = (record: KeyPairItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除密钥对 "${record.name}" 吗？`,
      okText: '确认',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          await deleteKeyPair(record.id)
          message.success('删除成功')
          getList(pageNo.value, pageSize.value, null)
        } catch (error) {
          console.error('删除失败:', error)
          message.error('删除失败')
        }
      }
    })
  }

  onMounted(() => {
    getList(pageNo.value, pageSize.value, null)
  })
  </script>
  