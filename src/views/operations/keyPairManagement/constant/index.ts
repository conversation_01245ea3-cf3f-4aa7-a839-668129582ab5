import dayjs from 'dayjs'

export interface KeyPairItem {
  id: string
  keyPairNo: string
  code: string
  name: string
  project: string
  description: string
  remarks: string
  createTime: string
}

export interface TableColumn {
  title: string
  dataIndex: string
  key: string
  width?: number
  fixed?: 'left' | 'right'
  slots?: { customRender: string }
  customRender?: (params: { text?: any; index?: number; record?: any }) => any
}

export interface SearchField {
  key: string
  label: string
  type: 'input' | 'select' | 'date' | 'dateRange' | 'custom'
  placeholder?: string
  options?: { label: string; value: any }[]
}

// 表格列定义
export const columns: TableColumn[] = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    customRender: ({ index }) => (index !== undefined ? index + 1 : '-'),
  },
  {
    title: '密钥对编号',
    dataIndex: 'code',
    key: 'code',
    width: 100,
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 150,
  },
  {
    title: '说明',
    dataIndex: 'remarks',
    key: 'remarks',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
    customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 100,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
]

// 搜索配置
export const searchSchema: SearchField[] = [
  {
    key: 'keyword',
    label: '关键字',
    type: 'input',
    placeholder: '请输入关键字',
  },
]
