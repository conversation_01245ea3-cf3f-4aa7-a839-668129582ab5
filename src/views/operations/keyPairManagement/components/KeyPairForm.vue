<template>
  <a-modal
    :visible="visible"
    title="新增密钥对"
    @cancel="handleCancel"
    @ok="handleSubmit"
    @update:visible="(val) => emit('update:visible', val)"
    width="600px"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入名称" />
      </a-form-item>
      <a-form-item label="说明" name="remarks">
        <a-textarea v-model:value="form.remarks" placeholder="请输入说明" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { createKeyPair } from '@/apis/operations'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref<FormInstance>()
const form = ref({
  name: '',
  remarks: ''
})

// 验证规则
const rules = {
  name: [{ required: true, message: '请输入项目名称' }]
}

// 重置表单
const resetForm = () => {
  form.value = {
    name: '',
    remarks: ''
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    const data = {
      name: form.value.name,
      remarks: form.value.remarks
    }
    const res = await createKeyPair(data)
    if (res.code === 200) {
      message.success('创建成功')
      emit('success')
      emit('update:visible', false)
      resetForm()
    } else {
      console.log('创建失败')
    }
  } catch (error) {
    console.error('创建失败:', error)
  }
}
</script> 