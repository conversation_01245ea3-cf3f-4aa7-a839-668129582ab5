<template>
  <div class="bg-gray-50 min-h-screen p-4 dark:!bg-area-fill">
    <div class="max-w-6xl mx-auto">
      <!-- 页面标题 -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">SSE 接口测试工具</h1>
        <p class="text-gray-500 mt-1">用于测试服务器发送事件(Server-Sent Events)接口</p>
      </div>
      
      <!-- 使用flex布局，强制左右结构 -->
      <div class="flex flex-row space-x-4 h-[600px]">
        <!-- 左侧配置面板 - 固定宽度 -->
        <div class="w-96 flex-shrink-0 bg-area-fill rounded-lg shadow-md overflow-hidden">
          <div class="bg-blue-50 dark:!bg-area-fill p-3 border-b border-blue-100">
            <h2 class="text-lg font-medium text-blue-800">SSE 请求配置</h2>
          </div>
          
          <div class="p-3 space-y-3 dark:!bg-area-fill">
            <!-- URL输入区域 -->
            <div class="space-y-1">
              <label class="block text-sm font-medium text-gray-700">请求地址</label>
              <a-input 
                v-model:value="sseUrl" 
                placeholder="请输入SSE请求地址" 
                class="w-full" 
                :class="{'border-red-300 focus:ring-red-500 focus:border-red-500': urlError}"
              />
              <p v-if="urlError" class="mt-1 text-sm text-red-600">{{ urlError }}</p>
            </div>
            
            <!-- 请求头设置 -->
            <div class="space-y-1">
              <div class="flex justify-between items-center">
                <label class="block text-sm font-medium text-gray-700">请求头</label>
                <a-button type="dashed" size="small" @click="addHeader" class="text-xs">
                  <template #icon><PlusOutlined /></template>
                  添加
                </a-button>
              </div>
              
              <div v-if="headers.length === 0" class="text-center py-2 bg-gray-50 dark:!bg-area-fill rounded-md border border-dashed border-gray-300">
                <p class="text-gray-500 text-sm">暂无请求头</p>
              </div>
              
              <div class="max-h-36 overflow-auto">
                <div v-for="(header, index) in headers" :key="index" class="flex items-center mb-1 space-x-2 p-1 bg-gray-50 dark:!bg-area-fill rounded-md animate__animated animate__fadeIn">
                  <a-input v-model:value="header.key" placeholder="键" class="w-2/5" />
                  <a-input v-model:value="header.value" placeholder="值" class="w-2/5" />
                  <a-button 
                    type="text" 
                    danger 
                    @click="removeHeader(index)" 
                    size="small"
                    class="flex items-center justify-center"
                  >
                    <template #icon><DeleteOutlined /></template>
                  </a-button>
                </div>
              </div>
            </div>
            
            <!-- 其他设置 -->
            <div class="space-y-2 pt-1 border-t border-gray-100">
              <div class="flex justify-between items-center">
                <label class="block text-sm font-medium text-gray-700">其他设置</label>
              </div>
              <div class="flex items-center space-x-2">
                <a-switch v-model:checked="autoReconnect" />
                <span class="text-sm text-gray-600">断线自动重连</span>
              </div>
            </div>
            
            <!-- 连接状态 -->
            <div class="p-2 rounded-lg mt-1 border border-gray-200 bg-gray-50 dark:!bg-area-fill">
              <!-- 已连接状态 -->
              <template v-if="eventSource">
                <div class="flex items-center space-x-2 mb-2">
                  <div class="w-3 h-3 rounded-full bg-green-500 dark:!bg-area-fill animate-pulse"></div>
                  <span class="text-sm font-medium text-green-700">已连接</span>
                </div>
                
                <div class="grid grid-cols-2 gap-1 text-sm text-gray-600 mb-2">
                  <div>收到消息数：</div>
                  <div class="font-medium text-right">{{ messages.length }}</div>
                  <div>连接时间：</div>
                  <div class="font-medium text-right">{{ connectionTime }}</div>
                </div>
                
                <a-button 
                  danger
                  @click="disconnectSSE"
                  block
                >
                  <template #icon><StopOutlined /></template>
                  断开连接
                </a-button>
              </template>
              
              <!-- 连接中状态 -->
              <template v-else-if="connecting">
                <div class="flex justify-center items-center py-2">
                  <a-spin />
                  <span class="ml-2 text-gray-600">连接中...</span>
                </div>
                
                <a-button 
                  @click="cancelConnect"
                  block
                >
                  <template #icon><CloseCircleOutlined /></template>
                  取消连接
                </a-button>
              </template>
              
              <!-- 未连接状态 -->
              <template v-else>
                <div class="flex items-center space-x-2 mb-2">
                  <div class="w-3 h-3 rounded-full bg-red-500"></div>
                  <span class="text-sm font-medium text-red-700">未连接</span>
                </div>
                
                <a-button 
                  type="primary"
                  @click="connectSSE"
                  block
                >
                  <template #icon><PlayCircleOutlined /></template>
                  开始连接
                </a-button>
              </template>
            </div>
            
            <!-- 清空消息按钮 -->
            <div class="mt-1">
              <a-button 
                :disabled="messages.length === 0"
                @click="clearMessages"
                block
              >
                <template #icon><ClearOutlined /></template>
                清空消息
              </a-button>
            </div>
          </div>
        </div>
        
        <!-- 右侧消息面板 - 占满剩余空间 -->
        <div class="flex-grow bg-area-fill rounded-lg shadow-md overflow-hidden flex flex-col">
          <div class="bg-indigo-50 dark:!bg-area-fill p-3 border-b border-indigo-100 flex justify-between items-center">
            <h2 class="text-lg font-medium text-indigo-800">接收到的消息</h2>
            <a-radio-group v-model:value="messageFilter" size="small" button-style="solid">
              <a-radio-button value="all">全部</a-radio-button>
              <a-radio-button value="message">消息</a-radio-button>
              <a-radio-button value="system">系统</a-radio-button>
              <a-radio-button value="error">错误</a-radio-button>
            </a-radio-group>
          </div>
          
          <div 
            ref="messagesContainer" 
            class="overflow-auto p-3 flex-1 flex flex-col justify-start items-center" 
            style="height: 100%;"
          >
            <div v-if="filteredMessages.length === 0" class="text-center py-10 text-gray-400 flex flex-col items-center w-full">
              <InboxOutlined style="font-size: 48px" class="mb-4 opacity-30" />
              <p>暂无消息</p>
              <p class="text-xs mt-2">连接SSE接口后，接收到的消息将显示在这里</p>
            </div>
            
            <div v-for="(msg, index) in filteredMessages" :key="index" class="mb-3 animate__animated animate__fadeIn w-full max-w-4xl">
              <div class="p-3 rounded-lg" :class="getMessageContainerClass(msg.event)">
                <div class="flex justify-between text-xs mb-1">
                  <div class="flex items-center">
                    <span 
                      class="inline-block w-2 h-2 rounded-full mr-2"
                      :class="getEventTypeColor(msg.event)"
                    ></span>
                    <span :class="getEventTypeTextColor(msg.event)">{{ getEventTypeLabel(msg.event) }}</span>
                  </div>
                  <span class="text-gray-500">{{ msg.time }}</span>
                </div>
                
                <pre 
                  class="whitespace-pre-wrap text-sm break-all p-2 rounded-md mt-1 max-h-80 overflow-auto"
                  :class="getMessageContentClass(msg.event)"
                >{{ formatMessageContent(msg.data) }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, watch, nextTick, onMounted } from 'vue'
import { 
  PlayCircleOutlined, 
  StopOutlined, 
  ClearOutlined, 
  DeleteOutlined, 
  PlusOutlined,
  InboxOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'

// 状态变量
const sseUrl = ref('')
const urlError = ref('')
const headers = ref([])
const messages = ref([])
const eventSource = ref(null)
const connecting = ref(false)
const messagesContainer = ref(null)
const autoReconnect = ref(false)
const connectionStartTime = ref(null)
const connectionTime = ref('--')
const timeInterval = ref(null)
const messageFilter = ref('all')

// 计算过滤后的消息
const filteredMessages = computed(() => {
  if (messageFilter.value === 'all') return messages.value
  return messages.value.filter(msg => {
    if (messageFilter.value === 'system') return msg.event === 'system'
    if (messageFilter.value === 'error') return msg.event === 'error'
    if (messageFilter.value === 'message') return msg.event !== 'system' && msg.event !== 'error'
    return true
  })
})

// 在组件挂载时开始
onMounted(() => {
  // 预设一个示例请求头
  addHeader()
})

// 监听消息变化，自动滚动到底部
watch(filteredMessages, () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
})

// 根据事件类型获取消息容器类
const getMessageContainerClass = (eventType) => {
  switch (eventType) {
    case 'error':
      return 'bg-red-50 border border-red-100'
    case 'system':
      return 'bg-blue-50 border border-blue-100'
    default:
      return 'bg-gray-50 border border-gray-200'
  }
}

// 根据事件类型获取消息内容类
const getMessageContentClass = (eventType) => {
  switch (eventType) {
    case 'error':
      return 'bg-white text-red-800'
    case 'system':
      return 'bg-white text-blue-800'
    default:
      return 'bg-white text-gray-800'
  }
}

// 获取事件类型标签
const getEventTypeLabel = (eventType) => {
  switch (eventType) {
    case 'error':
      return '错误'
    case 'system':
      return '系统'
    case 'message':
      return '消息'
    case 'update':
      return '更新'
    case 'ping':
      return '心跳'
    default:
      return eventType || '消息'
  }
}

// 获取事件类型颜色
const getEventTypeColor = (eventType) => {
  switch (eventType) {
    case 'error':
      return 'bg-red-500'
    case 'system':
      return 'bg-blue-500'
    case 'update':
      return 'bg-yellow-500'
    case 'ping':
      return 'bg-green-500'
    default:
      return 'bg-indigo-500'
  }
}

// 获取事件类型文本颜色
const getEventTypeTextColor = (eventType) => {
  switch (eventType) {
    case 'error':
      return 'text-red-600'
    case 'system':
      return 'text-blue-600'
    case 'update':
      return 'text-yellow-600'
    case 'ping':
      return 'text-green-600'
    default:
      return 'text-indigo-600'
  }
}

// 格式化消息内容
const formatMessageContent = (content) => {
  if (!content) return ''
  
  // 直接返回原始内容，不进行JSON解析
  return content
}

// 添加请求头
const addHeader = () => {
  headers.value.push({ key: '', value: '' })
}

// 删除请求头
const removeHeader = (index) => {
  headers.value.splice(index, 1)
}

// 验证URL
const validateUrl = () => {
  if (!sseUrl.value) {
    urlError.value = '请输入SSE请求地址'
    return false
  }
  
  try {
    new URL(sseUrl.value)
    urlError.value = ''
    return true
  } catch (e) {
    urlError.value = '请输入有效的URL地址'
    return false
  }
}

// 更新连接时间
const updateConnectionTime = () => {
  if (!connectionStartTime.value) {
    connectionTime.value = '--'
    return
  }
  
  const now = new Date()
  const diff = now - connectionStartTime.value
  const seconds = Math.floor(diff / 1000)
  
  if (seconds < 60) {
    connectionTime.value = `${seconds}秒`
  } else if (seconds < 3600) {
    connectionTime.value = `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  } else {
    connectionTime.value = `${Math.floor(seconds / 3600)}时${Math.floor((seconds % 3600) / 60)}分${seconds % 60}秒`
  }
}

// 连接SSE
const connectSSE = () => {
  if (!validateUrl()) return
  
  // 先断开旧连接
  disconnectSSE()
  
  connecting.value = true
  
  try {
    // 创建查询参数对象
    const params = {}
    
    // 添加请求头 (注：由于SSE API的限制，浏览器通常无法发送自定义请求头，
    // 但我们可以通过URL参数或在服务端配置CORS来解决)
    // 这里我们将请求头作为URL参数传递
    headers.value.forEach(h => {
      if (h.key && h.value) {
        params[h.key] = h.value
      }
    })
    
    // 将参数添加到URL
    let url = new URL(sseUrl.value)
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key])
    })
    
    // 创建EventSource对象
    eventSource.value = new EventSource(url.toString())
    
    // 设置连接开始时间
    connectionStartTime.value = new Date()
    
    // 开始计时
    timeInterval.value = setInterval(updateConnectionTime, 1000)
    updateConnectionTime()
    
    // 监听连接打开
    eventSource.value.onopen = () => {
      connecting.value = false // 连接成功，停止加载状态
    }
    
    // 监听消息
    eventSource.value.onmessage = (event) => {
      addMessage(event.data, 'message')
    }
    
    // 监听错误
    eventSource.value.onerror = (error) => {
      connecting.value = false
      
      if (autoReconnect.value) {
        setTimeout(() => {
          if (!eventSource.value) {
            connectSSE()
          }
        }, 3000)
      } else {
        disconnectSSE()
      }
    }
    
    // 支持自定义事件
    ['open', 'error', 'update', 'ping'].forEach(eventType => {
      eventSource.value.addEventListener(eventType, (event) => {
        addMessage(event.data, eventType)
      })
    })
    
  } catch (error) {
    connecting.value = false
  }
}

// 断开SSE连接
const disconnectSSE = () => {
  if (eventSource.value) {
    eventSource.value.close()
    eventSource.value = null
    connecting.value = false
    
    // 清除计时器
    clearInterval(timeInterval.value)
    connectionStartTime.value = null
    updateConnectionTime()
  }
}

// 添加消息到列表
const addMessage = (data, event = 'message') => {
  const now = new Date()
  const timeString = `${now.toLocaleDateString()} ${now.toLocaleTimeString()}`
  
  // 将消息添加到数组末尾，使其按时间顺序从上到下显示
  messages.value.push({
    time: timeString,
    data,
    event
  })
}

// 清空消息
const clearMessages = () => {
  messages.value = []
}

// 取消连接
const cancelConnect = () => {
  connecting.value = false
  if (eventSource.value) {
    disconnectSSE()
  }
}

// 组件销毁前断开连接
onBeforeUnmount(() => {
  disconnectSSE()
  clearInterval(timeInterval.value)
})
</script>

<style scoped>
/* 添加过渡动画 */
.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 