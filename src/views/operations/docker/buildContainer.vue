<template>
  <div>
    <a-drawer
      :visible="buildVisible"
      destroy-on-close
      width="80vw"
      title="重建容器"
      :mask-closable="false"
      :footer-style="{ textAlign: 'right' }"
      @close="
        () => {
          $emit('cancelBtnClick')
        }
      "
    >
      <a-alert
        v-if="containerData && Object.keys(containerData).length"
        style="margin-bottom: 10px"
        message="重建容器将删除原容器并使用相同配置重新创建一个新的容器"
        type="warning"
        show-icon
      >
        <template #description>
          重建容器将删除原容器并使用相同配置重新创建一个新的容器
          <div>
            <b style="color: red">注意：容器数据将丢失</b>,请提前备份数据
          </div>
          <div>
            <b>如果容器挂载了数据卷则数据不会丢失</b>
          </div>
        </template>
      </a-alert>

      <a-form
        ref="editForm"
        :rules="rules"
        :model="temp"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item
          label="镜像信息"
          name="name"
        >
          <a-row>
            <a-col :span="10">
              <a-input
                v-model:value="temp.image"
                disabled
                placeholder=""
              />
            </a-col>
            <a-col
              :span="4"
              style="text-align: right"
            >
              容器名称：
            </a-col>
            <a-col :span="10">
              <a-form-item-rest>
                <a-input
                  v-model:value="temp.name"
                  placeholder="请输入容器名称"
                />
              </a-form-item-rest>
            </a-col>
          </a-row>
        </a-form-item>

        <a-form-item label="端口">
          <a-form-item-rest>
            <a-space
              direction="vertical"
              style="width: 100%"
            >
              <a-row
                v-for="(item, index) in temp.exposedPorts"
                :key="index"
              >
                <a-col :span="21">
                  <a-space>
                    <a-input-group>
                      <a-row>
                        <a-col :span="8">
                          <a-input
                            v-model:value="item.ip"
                            addon-before="IP"
                            placeholder="0.0.0.0"
                          >
                          </a-input>
                        </a-col>
                        <a-col
                          :span="6"
                          :offset="1"
                        >
                          <a-input
                            v-model:value="item.publicPort"
                            addon-before="端口"
                            placeholder="端口"
                          >
                          </a-input>
                        </a-col>
                        <a-col
                          :span="8"
                          :offset="1"
                        >
                          <a-input
                            v-model:value="item.port"
                            addon-before="容器"
                            :disabled="item.disabled"
                            placeholder="容器端口"
                          >
                            <template #addonAfter>
                              <a-select
                                v-model:value="item.scheme"
                                :disabled="item.disabled"
                                placeholder="端口协议"
                              >
                                <a-select-option value="tcp">
                                  tcp
                                </a-select-option>
                                <a-select-option value="udp">
                                  udp
                                </a-select-option>
                                <a-select-option value="sctp">
                                  sctp
                                </a-select-option>
                              </a-select>
                            </template>
                          </a-input>
                        </a-col>
                      </a-row>
                    </a-input-group>
                  </a-space>
                </a-col>
                <a-col
                  :span="2"
                  :offset="1"
                >
                  <a-space>
                    <MinusCircleOutlined
                      v-if="temp.exposedPorts && temp.exposedPorts.length > 1"
                      @click="
                        () => {
                          temp.exposedPorts.splice(index, 1)
                        }
                      "
                    />

                    <PlusSquareOutlined
                      @click="
                        () => {
                          temp.exposedPorts.push({
                            scheme: 'tcp',
                            ip: '0.0.0.0'
                          })
                        }
                      "
                    />
                  </a-space>
                </a-col>
              </a-row>
            </a-space>
          </a-form-item-rest>
        </a-form-item>

        <a-form-item label="数据卷">
          <a-form-item-rest>
            <a-space
              direction="vertical"
              style="width: 100%"
            >
              <a-row
                v-for="(item, index) in temp.volumes"
                :key="index"
              >
                <a-col :span="10">
                  <a-input
                    v-model:value="item.host"
                    addon-before="主机"
                    placeholder="主机目录"
                  />
                </a-col>
                <a-col
                  :span="10"
                  :offset="1"
                >
                  <a-input
                    v-model:value="item.container"
                    addon-before="容器"
                    :disabled="item.disabled"
                    placeholder="容器目录"
                  />
                </a-col>
                <a-col
                  :span="2"
                  :offset="1"
                >
                  <a-space>
                    <MinusCircleOutlined
                      v-if="temp.volumes && temp.volumes.length > 1"
                      @click="
                        () => {
                          temp.volumes.splice(index, 1)
                        }
                      "
                    />

                    <PlusSquareOutlined
                      @click="
                        () => {
                          temp.volumes.push({})
                        }
                      "
                    />
                  </a-space>
                </a-col>
              </a-row>
            </a-space>
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="环境变量">
          <a-form-item-rest>
            <a-space
              direction="vertical"
              style="width: 100%"
            >
              <a-row
                v-for="(item, index) in temp.env"
                :key="index"
              >
                <a-col :span="10">
                  <a-input
                    v-model:value="item.key"
                    placeholder="变量名称"
                  />
                </a-col>
                <a-col
                  :span="10"
                  :offset="1"
                >
                  <a-input
                    v-model:value="item.value"
                    placeholder="变量值"
                  />
                </a-col>
                <a-col
                  :span="2"
                  :offset="1"
                >
                  <a-space>
                    <MinusCircleOutlined
                      v-if="temp.env && temp.env.length > 1"
                      @click="
                        () => {
                          temp.env.splice(index, 1)
                        }
                      "
                    />

                    <PlusSquareOutlined
                      @click="
                        () => {
                          temp.env.push({})
                        }
                      "
                    />
                  </a-space>
                </a-col>
              </a-row>
            </a-space>
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="命令">
          <a-form-item-rest>
            <a-space
              direction="vertical"
              style="width: 100%"
            >
              <a-row
                v-for="(item, index) in temp.commands"
                :key="index"
              >
                <a-col :span="20">
                  <a-input
                    v-model:value="item.value"
                    addon-before="命令"
                    placeholder="命令"
                  />
                </a-col>

                <a-col
                  :span="2"
                  :offset="1"
                >
                  <a-space>
                    <MinusCircleOutlined
                      v-if="temp.commands && temp.commands.length > 1"
                      @click="
                        () => {
                          temp.commands.splice(index, 1)
                        }
                      "
                    />
                    <PlusSquareOutlined
                      @click="
                        () => {
                          temp.commands.push({})
                        }
                      "
                    />
                  </a-space>
                </a-col>
              </a-row>
            </a-space>
          </a-form-item-rest>
        </a-form-item>
        <a-form-item
          label="hostname"
          name="hostname"
        >
          <a-input
            v-model:value="temp.hostname"
            placeholder="主机名 hostname"
          />
        </a-form-item>
        <a-form-item label="网络模式">
          <a-auto-complete
            v-model:value="temp.networkMode"
            placeholder="网络模式"
            :options="[
              {
                title: '桥接模式',
                value: 'bridge'
              },
              {
                title: '无网络',
                value: 'none'
              },
              {
                title: '重用另一个容器网络堆栈',
                value: 'container:<name|id>'
              },
              {
                title: '使用容器内的主机网络堆栈。 注意：主机模式赋予容器对本地系统服务（如 D-bus）的完全访问权限，因此被认为是不安全的。',
                value: 'host'
              }
            ]"
          >
            <template #option="item">
              {{ item.title }}
            </template>
          </a-auto-complete>
        </a-form-item>
        <a-form-item label="重启策略">
          <a-auto-complete
            v-model:value="temp.restartPolicy"
            placeholder="重启策略"
            :options="[
              {
                title: '不自动重启',
                value: 'no'
              },
              {
                title: '始终重启',
                value: 'always'
              },
              {
                title: '失败时重启',
                value: 'on-failure:1'
              },
              {
                title: '除非手动停止，否则始终重启',
                value: 'unless-stopped'
              }
            ]"
          >
            <template #option="item">
              {{ item.title }}
            </template>
          </a-auto-complete>
        </a-form-item>
        <a-form-item label="额外主机">
          <a-form-item-rest>
            <a-space
              direction="vertical"
              style="width: 100%"
            >
              <a-row
                v-for="(item, index) in temp.extraHosts"
                :key="index"
              >
                <a-col :span="20">
                  <a-input
                    v-model:value="temp.extraHosts[index]"
                    placeholder="格式：域名:IP"
                  />
                </a-col>

                <a-col
                  :span="2"
                  :offset="1"
                >
                  <a-space>
                    <MinusCircleOutlined
                      v-if="temp.extraHosts && temp.extraHosts.length > 1"
                      @click="
                        () => {
                          temp.extraHosts.splice(index, 1)
                        }
                      "
                    />
                    <PlusSquareOutlined
                      @click="
                        () => {
                          temp.extraHosts.push('')
                        }
                      "
                    />
                  </a-space>
                </a-col>
              </a-row>
            </a-space>
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="存储选项">
          <a-form-item-rest>
            <a-space
              direction="vertical"
              style="width: 100%"
            >
              <a-row
                v-for="(item, index) in temp.storageOpt"
                :key="index"
              >
                <a-col :span="10">
                  <a-input
                    v-model:value="item.key"
                    placeholder="选项名称"
                  />
                </a-col>
                <a-col
                  :span="10"
                  :offset="1"
                >
                  <a-input
                    v-model:value="item.value"
                    placeholder="选项值"
                  />
                </a-col>
                <a-col
                  :span="2"
                  :offset="1"
                >
                  <a-space>
                    <MinusCircleOutlined
                      v-if="temp.storageOpt && temp.storageOpt.length > 1"
                      @click="
                        () => {
                          temp.storageOpt.splice(index, 1)
                        }
                      "
                    />
                    <PlusSquareOutlined
                      @click="
                        () => {
                          temp.storageOpt.push({})
                        }
                      "
                    />
                  </a-space>
                </a-col>
              </a-row>
            </a-space>
          </a-form-item-rest>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-space>
          <a-button
            @click="
              () => {
                $emit('cancelBtnClick')
              }
            "
          >
            取 消
          </a-button>
          <a-button
            type="primary"
            :loading="loading"
            @click="handleBuildOk"
          >
            确 认
          </a-button>
        </a-space>
      </template>
    </a-drawer>
  </div>
</template>
<script>
import {
  dockerImageCreateContainer,
  dockerImageInspect,
  dockerInspectContainer,
  dockerContainerRebuildContainer
} from '@/apis/docker'
import { message } from 'ant-design-vue'
export default {
  props: {
    id: {
      type: String,
      default: ''
    },
    imageId: {
      type: String,
      default: ''
    },
    urlPrefix: {
      type: String,
      default: ''
    },
    machineDockerId: {
      type: String,
      default: ''
    },
    containerId: {
      type: String,
      default: ''
    },
    containerData: {
      type: Object,
      default: () => ({})
    },
    buildVisible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['cancelBtnClick', 'confirmBtnClick'],
  data() {
    return {
      temp: {},
      rules: {
        name: [
          { required: true, message: '容器名称必填', trigger: 'blur' },
          {
            pattern: /[a-zA-Z0-9][a-zA-Z0-9_.-]$/,
            message: '容器名称数字字母,且长度大于1',
            trigger: 'blur'
          }
        ]
      },
      loading: false
    }
  },
  computed: {
    reqDataId() {
      return this.id || this.machineDockerId
    },
    getLabels() {
      if (!this.containerData.labels) {
        return ''
      }
      let labels = ''
      Object.keys(this.containerData.labels).map((key) => {
        labels += `${key}=${this.containerData.labels[key]}&`
      })
      return labels.slice(0, -1)
    }
  },
  mounted() {
    this.createContainer()
  },
  methods: {
    // 构建镜像
    createContainer() {
      // 判断 containerId
      if (this.containerId) {
        // form container
        this.inspectContainer()
      } else {
        // form image
        this.inspectImage()
      }
    },
    getPortsFromPorts(ports) {
      const _ports = ports?.map((item) => {
        item.disabled = item.privatePort !== null
        item.port = item.privatePort
        return item
      })
      return _ports?.length > 0 ? _ports : null
    },
    getPortsFromExposedPorts(exposedPorts) {
      const _ports = exposedPorts?.map((item) => {
        item.disabled = item.port !== null
        item.ip = '0.0.0.0'
        item.scheme = item.scheme || 'tcp'
        return item
      })
      return _ports?.length > 0 ? _ports : null
    },
    getVolumesFromMounts(mounts) {
      const _mounts = mounts.map((item) => {
        item.disabled = item.destination !== null
        item.host = item.source
        item.container = item.destination?.path
        return item
      })
      return _mounts.length > 0 ? _mounts : null
    },
    getRestartPolicy(restartPolicy) {
      if (!restartPolicy) {
        return ''
      }
      const name = restartPolicy.name
      if (restartPolicy.maximumRetryCount) {
        return name + ':' + restartPolicy.maximumRetryCount
      }
      return name
    },
    // inspect container
    inspectContainer() {
      // 单独获取 image 信息
      this.temp = {}
      dockerImageInspect(
        this.reqDataId,
        this.imageId
      ).then((res) => {
        this.temp = { ...this.temp, image: (res.data.repoTags || []).join(',') }
      })

      dockerInspectContainer( this.reqDataId,
        this.containerId
      ).then((res) => {
        // this.buildVisible = true
        const storageOpt = res.data.hostConfig?.storageOpt || { '': '' }
        const extraHosts = res.data?.hostConfig?.extraHosts || ['']
        if (!extraHosts.length) {
          extraHosts.push('')
        }

        this.temp = {
          name: res.data?.name,
          labels: this.getLabels,
          volumes: this.getVolumesFromMounts(res.data?.mounts) || [{}],
          exposedPorts: this.getPortsFromPorts(this.containerData.ports) ||
            this.getPortsFromExposedPorts(res.data.config.exposedPorts) || [{}],
          autorun: true,
          imageId: this.imageId,
          env: (res.data?.config?.env || ['']).map((item) => {
            const i = item.indexOf('=')
            if (i == -1) {
              return {}
            }
            return {
              key: item.substring(0, i),
              value: item.substring(i + 1, item.length)
            }
          }) || [{}],
          storageOpt: Object.keys(storageOpt).map((item) => {
            return {
              key: item,
              value: storageOpt[item]
            }
          }),
          commands: (res.data?.config?.cmd || ['']).map((item) => {
            return {
              value: item || ''
            }
          }) || [{}],
          hostname: res.data?.config?.hostName,
          restartPolicy: this.getRestartPolicy(res.data?.hostConfig?.restartPolicy),
          networkMode: res.data?.hostConfig?.networkMode,
          runtime: res.data?.hostConfig?.runtime,
          privileged: res.data.hostConfig?.privileged || false,
          extraHosts: extraHosts,
          ...this.temp
        }
        this.$refs['editForm']?.resetFields()
      })
    },
    // inspect image
    inspectImage() {
      dockerImageInspect(
        this.reqDataId,
        this.imageId
      ).then((res) => {
        // this.buildVisible = true
        this.temp = {
          name: (res.data?.repoTags[0] || '').split(':')[0] || '',
          volumes: [{}],
          exposedPorts: (res.data?.config?.exposedPorts || [{}]).map((item) => {
            item.disabled = item.port !== null
            item.ip = '0.0.0.0'
            item.scheme = item.scheme || 'tcp'
            return item
          }),
          image: (res.data?.repoTags || []).join(','),
          autorun: true,
          imageId: this.imageId,
          env: [{}],
          storageOpt: [{}],
          commands: [{}],
          extraHosts: ['']
        }
        this.$refs['editForm']?.resetFields()
      })
    },
    // 创建容器
    handleBuildOk() {
      this.$refs['editForm'].validate().then(() => {
        const temp = {
          id: this.reqDataId,
          autorun: this.temp.autorun,
          imageId: this.temp.imageId,
          name: this.temp.name,
          env: {},
          commands: [],
          networkMode: this.temp.networkMode,
          privileged: this.temp.privileged,
          restartPolicy: this.temp.restartPolicy,
          labels: this.temp.labels,
          runtime: this.temp.runtime,
          hostname: this.temp.hostname,
          storageOpt: {},
          extraHosts: this.temp.extraHosts
        }
        temp.volumes = (this.temp.volumes || [])
          .filter((item) => {
            return item.host
          })
          .map((item) => {
            return item.host + ':' + item.container
          })
          .join(',')
        // 处理端口
        temp.exposedPorts = (this.temp?.exposedPorts || [])
          .filter((item) => {
            return item.publicPort && item.ip
          })
          .map((item) => {
            return item.ip + ':' + item.publicPort + ':' + item.port
          })
          .join(',')
        // 环境变量
        this.temp.env.forEach((item) => {
          if (item.key && item.key) {
            temp.env[item.key] = item.value
          }
        })
        this.temp.storageOpt.forEach((item) => {
          if (item.key && item.key) {
            temp.storageOpt[item.key] = item.value
          }
        })
        //
        temp.commands = (this.temp.commands || []).map((item) => {
          return item.value || ''
        })
        // 判断 containerId
        if (this.containerId) {
          temp.containerId = this.containerId
          this.loading = true
          dockerContainerRebuildContainer(temp)
            .then((res) => {
              if (res.code === 200) {
                message.success('重建成功')

                // 通知父组件关闭弹窗
                this.$emit('confirmBtnClick')
              }
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          this.loading = true
          dockerImageCreateContainer(temp)
            .then((res) => {
              if (res.code === 200) {
                message.success(res.statusText)

                // 通知父组件关闭弹窗
                this.$emit('confirmBtnClick')
              }
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script>
