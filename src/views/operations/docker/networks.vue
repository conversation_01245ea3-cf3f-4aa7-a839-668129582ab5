<template>
  <YssTable
    size="middle"
    :data="list"
    :columns="columns"
    :show-pagination="false"
    bordered
    :scroll="{
      x: 'max-content'
    }"
  >
    <template #title>
      <a-space>
        <a-input
          v-model:value="listQuery['name']"
          placeholder="名称"
          class="search-input-item"
          @press-enter="loadData"
        />
        <a-input
          v-model:value="listQuery['networkId']"
          placeholder="id"
          class="search-input-item"
          @press-enter="loadData"
        />

        <a-button
          type="primary"
          :loading="loading"
          @click="loadData"
        >
          搜索
        </a-button>
      </a-space>
    </template>
    <template #bodyCell="{ column, text, record }">
      <template v-if="column.dataIndex === 'Created'">
        <a-tooltip
          placement="topLeft"
          :title="record.rawValues && record.rawValues['Created']"
        >
          <span>{{ parseTime(record.rawValues && record.rawValues['Created']) }}</span>
        </a-tooltip>
      </template>
      <template v-else-if="column.dataIndex === 'ipam'">
        <a-tooltip
          placement="topLeft"
          :title="`${text && text.driver}  ${
            text &&
            text.config &&
            text.config
              .map((item) => {
                return ('网关：' + (item.gateway || '') + '#' + '子网掩码：' + (item.subnet || ''));
              })
              .join(',')
          }`"
        >
          <span>{{
            text &&
              text.config &&
              text.config
                .map((item) => {
                  return (item.gateway || '') + '#' + (item.subnet || '');
                })
                .join(',')
          }}</span>
        </a-tooltip>
      </template>

      <template v-else-if="column.tooltip">
        <a-tooltip
          placement="topLeft"
          :title="text"
        >
          <span>{{ text }}</span>
        </a-tooltip>
      </template>

      <template v-else-if="column.id">
        <a-tooltip :title="text">
          <span>{{ text.split(':')[1].slice(0, 12) }}</span>
        </a-tooltip>
      </template>
      <template v-else-if="column.dataIndex === 'operation'">
        <a-space>
          <a-tooltip title="删除">
            <a-button
              size="small"
              type="link"
              @click="doAction(record, 'remove')"
            >
              <DeleteOutlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </template>
  </YssTable>
</template>
<script>
import { renderSize, parseTime } from '@/utils/const'
import { dockerNetworksList, dockerVolumesRemove } from '@/apis/docker'
import { message, Modal } from 'ant-design-vue'
import { YssTable } from '@yss-design/ui'
export default {
  components: {
    YssTable
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    urlPrefix: {
      type: String,
      default: ''
    },
    machineDockerId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      listQuery: {
        dangling: false
      },
      renderSize,
      columns: [
        {
          title: '序号',
          width: 80,
          ellipsis: true,
          align: 'center',
          customRender: ({ index }) => `${index + 1}`
        },
        {
          title: '名称',
          dataIndex: 'name',
          ellipsis: true,
          tooltip: true
        },
        {
          title: 'id',
          dataIndex: 'id',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '范围',
          dataIndex: 'scope',
          ellipsis: true
        },
        {
          title: 'IPAM',
          dataIndex: 'ipam',
          ellipsis: true
        },
        {
          title: '类型',
          dataIndex: 'driver',
          ellipsis: true,
          width: 80
        },
        {
          title: '创建时间',
          dataIndex: 'Created',
          ellipsis: true,
          width: 180
          // sorter: (a, b) => new Date(a.rawValues.Created).getTime() - new Date(b.rawValues.Created).getTime(),
          // sortDirections: ['descend', 'ascend'],
          // defaultSortOrder: 'descend'
        }
        // { title: "操作", dataIndex: "operation", width: 80 },
      ],
      action: {
        remove: {
          msg: '您确定要删除当前卷吗？',
          api: dockerVolumesRemove
        }
      }
    }
  },
  computed: {
    reqDataId() {
      return this.id || this.machineDockerId
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    parseTime,
    // 加载数据
    loadData() {
      this.loading = true
      //this.listQuery.page = pointerEvent?.altKey || pointerEvent?.ctrlKey ? 1 : this.listQuery.page;
      this.listQuery.id = this.reqDataId
      dockerNetworksList(this.listQuery.id).then((res) => {
        if (res.code === 200) {
          this.list = res.data
        }
        this.loading = false
      })
    },
    doAction(record, actionKey) {
      const action = this.action[actionKey]
      if (!action) {
        return
      }
      Modal.confirm({
        title: '系统提示',
        content: action.msg,
        onOk: () => {
          return action
            .api( this.reqDataId, record.name)
            .then((res) => {
              if (res.code === 200) {
                message.success('操作成功')
                this.loadData()
              }
            })
        }
      })
    }
  }
}
</script>
