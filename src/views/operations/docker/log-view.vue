<template>
  <!-- console -->
  <div>
    <log-view2
      ref="logView"
      title-name="容器日志"
      :visible="visible"
      @close="
        () => {
          $emit('close')
        }
      "
    >
      <template #before>
        <a-space>
          <div>
            <a-input-number
              v-model:value="tail"
              placeholder="日志行数"
              style="width: 150px"
            >
              <template #addonBefore>
                <a-tooltip title="日志行数">
                  行数
                </a-tooltip>
              </template>
            </a-input-number>
          </div>
          <div>
            时间戳
            <a-switch
              v-model:checked="timestamps"
              checked-children="开"
              un-checked-children="关"
            />
          </div>
          <a-button
            type="primary"
            size="small"
            @click="initWebSocket"
          >
            <ReloadOutlined /> 刷新
          </a-button>
          |
          <a-button
            type="primary"
            :disabled="!logId"
            size="small"
            @click="download"
          >
            <DownloadOutlined /> 下载
          </a-button>
          |
        </a-space>
      </template>
    </log-view2>
  </div>
</template>
<script>
import { dockerContainerDownloaLog,getWebSocketUrl } from '@/apis/docker'
import LogView2 from '@/components/logView/index.vue'
import { message } from 'ant-design-vue'
export default {
  components: {
    LogView2
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    containerId: { type: String, default: '' },
    machineDockerId: {
      type: String,
      default: ''
    },
    urlPrefix: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close'],
  data() {
    return {
      socket: null,
      tail: 500,
      timestamps: false,
      logId: ''
    }
  },
  computed: {
    socketUrl() {
      return getWebSocketUrl(
        '/socket/docker_log',
        `userId=${JSON.parse(localStorage.getItem('userInfo')).id}&id=${this.id}&machineDockerId=${
          this.machineDockerId
        }&type=dockerLog&nodeId=system&workspaceId=1`
      )
    }
  },
  mounted() {
    this.initWebSocket()
    // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = () => {
      this.close()
    }
  },
  beforeUnmount() {
    this.close()
  },
  methods: {
    close() {
      clearInterval(this.heart)
      this.socket?.close()
    },
    // 初始化
    initWebSocket() {
      this.close()
      this.$refs.logView.clearLogCache()
      //
      let tail = parseInt(this.tail)
      this.tail = isNaN(tail) ? 500 : tail
      //
      this.socket = new WebSocket(this.socketUrl)

      // 连接成功后
      this.socket.onopen = () => {
        this.sendMsg('showlog')
      }
      this.socket.onerror = (err) => {
        console.error(err)
        message.error('web socket错误,请检查是否开启 ws 代理')
        clearInterval(this.heart)
      }
      this.socket.onclose = (err) => {
        //当客户端收到服务端发送的关闭连接请求时，触发onclose事件
        console.error(err)
        clearInterval(this.heart)
        $message.warning('会话已经关闭[docker-log]')
      }
      this.socket.onmessage = (msg) => {
        if (msg.data.indexOf('code') > -1 && msg.data.indexOf('msg') > -1) {
          try {
            const res = JSON.parse(msg.data)
            if (res.code === 200 && res.msg === 'JPOM_MSG_UUID') {
              this.logId = res.data
              return
            }
          } catch (e) {
            console.error(e)
          }
          //   return;
        }
        const msgLine = msg.data || ''
        // this.$refs.logView.appendLine(msgLine.substring(0, msgLine.lastIndexOf('\n')))
        this.$refs.logView.appendLine(msgLine)
        clearInterval(this.heart)
        // 创建心跳，防止掉线
        this.heart = setInterval(() => {
          this.sendMsg('heart')
        }, 5000)
      }
    },
    // 发送消息
    sendMsg(op) {
      const data = {
        op: op,
        containerId: this.containerId,
        tail: this.tail,
        timestamps: this.timestamps
      }
      this.socket.send(JSON.stringify(data))
    },
    // 下载
    download() {
      window.open(dockerContainerDownloaLog(this.urlPrefix, this.logId), '_blank')
    }
  }
}
</script>
