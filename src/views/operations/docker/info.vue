<template>
  <div>
    <a-timeline>
      <a-timeline-item>
        <span>
          基础信息：{{ temp.name }} - {{ temp.osType }} - {{ temp.operatingSystem }} -
          <a-tag>{{ temp.architecture }} </a-tag>
          <a-tag>{{ temp.id }}</a-tag>
        </span>
      </a-timeline-item>
      <a-timeline-item>
        <span>
          版本：<a-tag>{{ temp.serverVersion }}</a-tag>
          <a-tag>{{ temp.kernelVersion }}</a-tag>
        </span>
      </a-timeline-item>
      <a-timeline-item>
        <span>资源：<a-tag>cpu:{{ temp.nCPU || temp.NCPU }}</a-tag>
          <a-tag>内存:{{ renderSize(temp.memTotal) }}</a-tag>

          <a-tag>容器数：{{ temp.containers }}</a-tag>
          <a-tag>镜像数：{{ temp.images }}</a-tag>
        </span>
      </a-timeline-item>
      <a-timeline-item>
        <span>系统时间：{{ temp.systemTime }} </span>
      </a-timeline-item>
      <a-timeline-item>
        <span>运行目录：{{ temp.dockerRootDir }} </span>
      </a-timeline-item>
      <template v-if="temp.swarm">
        <a-timeline-item>
          <div>
            集群信息：
            <div style="padding-left: 10px">
              <a-space
                direction="vertical"
                style="width: 100%"
              >
                <div>
                  本地状态：<a-tag v-if="temp.swarm.nodeAddr">
                    {{ temp.swarm.nodeAddr }}
                  </a-tag>
                  <a-tag>{{ temp.swarm.localNodeState }}</a-tag>
                </div>
                <div v-if="temp.swarm.remoteManagers">
                  管理列表：
                  <a-tag
                    v-for="(item, index) in temp.swarm.remoteManagers"
                    :key="index"
                  >
                    {{ item.addr }}
                  </a-tag>
                </div>
                <div>
                  管理节点：
                  {{ temp.swarm.controlAvailable ? '是' : '否' }}
                </div>
              </a-space>
            </div>
          </div>
        </a-timeline-item>
      </template>
      <a-timeline-item v-if="temp.plugins">
        <div>
          插件：
          <a-list
            item-layout="horizontal"
            :data-source="Object.keys(temp.plugins)"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item class="dark:!text-white">
                {{ item }}
                <a-tag
                  v-for="(item1, index) in temp.plugins[item]"
                  :key="index"
                >
                  {{ item1 }}
                </a-tag>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </a-timeline-item>
      <a-timeline-item v-if="temp.registryConfig">
        <div>
          仓库：
          <a-list
            item-layout="horizontal"
            :data-source="Object.keys(temp.registryConfig.indexConfigs)"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item class="dark:!text-white">
                {{ item }}
                <a-tag
                  v-if="temp.registryConfig.indexConfigs[item].official"
                  color="green"
                >
                  官方
                </a-tag><a-tag
                  v-if="temp.registryConfig.indexConfigs[item].secure"
                  color="green"
                >
                  安全
                </a-tag>
                <a-tag
                  v-for="(item1, index) in temp.registryConfig.indexConfigs[item].mirrors"
                  :key="index"
                >
                  {{
                    item1
                  }}
                </a-tag>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </a-timeline-item>
    </a-timeline>
  </div>
</template>
<script>
import { dockerInfo } from '@/apis/docker'
import { renderSize } from '@/utils/const'
export default {
  props: {
    id: {
      type: String,
      default: ''
    },
    urlPrefix: {
      type: String,
      default: ''
    },
    machineDockerId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      temp: {},

      rules: {}
    }
  },
  computed: {
    reqDataId() {
      return this.id || this.machineDockerId
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    renderSize,
    // load data
    loadData() {
      dockerInfo(this.reqDataId
      ).then((res) => {
        if (res.code === 200) {
          this.temp = res.data
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
