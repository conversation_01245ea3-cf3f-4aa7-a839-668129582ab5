<template>
  <terminal :url="socketUrl" />
</template>
<script>
import { getWebSocketUrl } from '@/apis/docker'
import terminal from '@/components/terminal/index.vue'

export default {
  components: {
    terminal
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    containerId: {
      type: String,
      default: ''
    },
    machineDockerId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {
    socketUrl() {
      return getWebSocketUrl(
        '/aip/socket/docker_cli',
        `userId=${JSON.parse(localStorage.getItem('userInfo')).id}&userName=${JSON.parse(localStorage.getItem('userInfo')).realname}&id=${this.id}&machineDockerId=${
          this.machineDockerId
        }&nodeId=system&type=docker&containerId=${this.containerId}&workspaceId=1`
      )
    }
  },
  mounted() {},
  beforeUnmount() {},
  methods: {}
}
</script>
