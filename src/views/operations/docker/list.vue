<template>
  <div class="h-full bg-global-background rounded-md">
    <BaseTable
      ref="baseTable"
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :search-schema="searchSchema"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :pagination="tableConfig.pagination"
      @search="handleSearch"
      @reset="handleReset"
    >
      <template #actions>
        <div class="action-buttons">
          <a-button type="primary" @click="handleAdd"> 新增 </a-button>
          <a-tooltip class="ml-2" title="自动检测服务端所在服务器中是否存在 docker，如果存在将自动新增到列表中">
            <a-button type="primary" @click="handleAutoDetect">
              <QuestionCircleOutlined class="align-middle" />
              <span class="ml-1">自动探测</span>
            </a-button>
          </a-tooltip>
        </div>
      </template>

      <!-- 操作按钮区域 -->
      <!-- <template #headerRight>
        <div class="flex items-center space-x-2">
          <a-button
            type="primary"
            @click="handleSearch"
          >
            搜索
          </a-button>
          <a-button
            type="primary"
            @click="handleAdd"
          >
            新增
          </a-button>
          <a-button 
            type="primary" 
            :disabled="!selectedRowKeys.length"
            @click="handleBatchDistribute"
          >
            批量分配
          </a-button>
          <a-tooltip title="自动探测">
            <a-button
              type="primary"
              @click="handleAutoDetect"
            >
              <QuestionCircleOutlined class="align-middle" />
              <span class="ml-1">自动探测</span>
            </a-button>
          </a-tooltip>
        </div>
      </template> -->

      <!-- 状态列自定义渲染 -->
      <template #statusColumn="{ record }">
        <a-tag :color="DOCKER_STATUS_COLOR[record.status]" class="min-w-[64px] text-center">
          {{ DOCKER_STATUS_TEXT[record.status] }}
        </a-tag>
      </template>

      <!-- 操作列自定义渲染 -->
      <template #operationColumn="{ record }">
        <a-space>
          <a-tooltip title="控制台">
            <a-button type="link" @click="handleConsole(record)">
              <template #icon><CodeOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="编辑">
            <a-button type="link" @click="handleEdit(record)">
              <template #icon><EditOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="删除">
            <a-button type="link" danger @click="handleDelete(record)">
              <template #icon><DeleteOutlined /></template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>
    <docker-form></docker-form>

    <!-- 新增/编辑弹窗 -->
    <DockerForm v-model:visible="formVisible" :form-data="currentRecord" @success="handleSuccess" />
    <console
      v-if="consoleVisible"
      :id="temp?.id"
      :visible="consoleVisible"
      :machine-docker-id="temp?.id"
      url-prefix="/docker"
      @close="onClose"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { QuestionCircleOutlined, CodeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import BaseTable from '@/components/BaseTable/index.vue'
import DockerForm from './DockerForm.vue'
import { dockerList, deleteDcoker,tryLocalDocker } from '@/apis/docker'
import {
  columns,
  searchSchema,
  tableConfig,
  DOCKER_STATUS_COLOR,
  DOCKER_STATUS_TEXT
} from './constants'
import Console from './console.vue'  // 确保正确导入Console组件

// 数据源
const dataSource = ref([])
const loading = ref(false)
const formVisible = ref(false)
const currentRecord = ref<any>(null)
const selectedRowKeys = ref<string[]>([])

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  host: '',
  groupId: '',
  group: undefined
})

// 控制台相关状态
const consoleVisible = ref(false)
const temp = ref<any>(null)
const router = useRouter()
const route = useRoute()

// 加载数据
const fetchData = async (params = {}) => {
  loading.value = true
  try {
    const res = await dockerList({
      ...queryParams,
      ...params
    })
    if (res.code === 200) {
      dataSource.value = res.data.records
      tableConfig.pagination.total = res.data.total
    } else {
      message.error(res.message || '获取数据失败')
    }
  } catch (error) {
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 选择行变化
const onSelectChange = (keys: string[]) => {
  selectedRowKeys.value = keys
}

// 搜索处理
const handleSearch = () => {
  queryParams.pageNo = 1
  fetchData()
}

// 重置处理
const handleReset = () => {
  Object.assign(queryParams, {
    pageNo: 1,
    pageSize: 10,
    name: '',
    host: '',
    groupId: '',
    group: undefined
  })
  fetchData()
}

// 新增处理
const handleAdd = () => {
  currentRecord.value = null
  formVisible.value = true
}

// 编辑处理
const handleEdit = (record: any) => {
  currentRecord.value = { ...record }
  formVisible.value = true
}

// 删除处理
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除 "${record.name}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        const res = await deleteDcoker({ id: record.id })

        if (res.code === 200) {
          message.success('删除成功')
          if (dataSource.value.length === 1 && queryParams.pageNo > 1) {
            queryParams.pageNo--
          }
          await fetchData()
        } else {
          message.error(res.message || '删除失败')
        }
      } catch (error) {
        // message.error('删除失败，请稍后重试')
      }
    }
  })
}

// 批量分配处理
// const handleBatchDistribute = () => {
//   if (!selectedRowKeys.value.length) {
//     message.warning('请选择要分配的记录')
//     return
//   }
//   // TODO: 实现批量分配逻辑
// }

// 自动探测处理
const handleAutoDetect = async () => {
  try {
    const res = await tryLocalDocker()
    if (res.code === 200) {
      message.success('监测成功')
      await fetchData() // 使用全局定义的fetchData方法替代this.loadData
    }
  } catch (error) {
    // message.error('监测失败，请稍后重试')
  }
}

// 检查URL参数并自动打开控制台
onMounted(() => {
  const { dockerId, type } = route.query
  if (dockerId && type === 'docker') {
    // 根据dockerId获取对应的记录
    const record = dataSource.value.find((item: any) => item.id === dockerId)
    if (record) {
      temp.value = { ...record }
      consoleVisible.value = true
    }
  }
})

// 控制台处理
const handleConsole = (record: any) => {
  temp.value = { ...record }
  consoleVisible.value = true

  // 更新路由参数
  router.push({
    query: {
      ...route.query,
      dockerId: record.id,
      type: 'docker'
    }
  })
}

// 关闭控制台
const onClose = () => {
  consoleVisible.value = false
  temp.value = null

  // 清除路由参数
  const query = { ...route.query }
  delete query.dockerId
  delete query.type
  router.replace({ query })
}

// 表单提交成功处理
const handleSuccess = () => {
  formVisible.value = false
  fetchData()
  currentRecord.value = null
  selectedRowKeys.value = []
}

// 初始化
fetchData()
</script>

<style>
/* 覆盖一些 ant-design-vue 的默认样式 */
.ant-table-wrapper {
  @apply bg-transparent;
}

.ant-table {
  @apply bg-transparent;
}

.ant-table-container {
  @apply bg-transparent;
}

/* 自定义滚动条样式 */
.docker-list ::-webkit-scrollbar {
  @apply w-2 h-2;
}

.docker-list ::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.docker-list ::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full hover:bg-gray-400;
}

/* 确保输入框在暗色主题下也清晰可见 */
.ant-input,
.ant-select-selector {
  @apply border-gray-300 hover:border-blue-400 focus:border-blue-500;
}

/* 按钮hover效果优化 */
.ant-btn {
  @apply transition-all duration-300 ease-in-out;
}

/* 表格行hover效果 */
.ant-table-tbody > tr:hover > td {
  @apply bg-blue-50 !important;
}
</style>
