<template>
  <a-modal
    :visible="visible"
    :title="formData ? '编辑 Docker' : '新增 Docker'"
    width="600px"
    :confirmLoading="loading"
    @cancel="handleCancel"
    @ok="handleSubmit"
  >
    <!-- 安全提示信息 -->
    <a-alert
      class="mb-4"
      type="warning"
      show-icon
    >
      <template #message>
        <div class="space-y-2">
          <div>系统使用 docker http 接口实现和 docker 通讯和管理，但是默认<span class="text-red-500">没有开启任何认证</span></div>
          <div>这样使得 <span class="text-red-500">docker 极不安全</span></div>
          <div>如果端口暴露到公网<span class="text-red-500">很容易出现被矿</span>情况</div>
          <div>所以这里 我们<span class="text-red-500">强烈建议您使用 TLS 证书</span> (证书生成方式可以参考文档) 来连接 docker 提升安全性</div>
          <div>如果端口<span class="text-red-500">保证在内网中使用可以忽略 TLS 证书</span></div>
          <div>注意: <span class="text-red-500">证书的允许的 IP 需要和 docker host 一致</span></div>
        </div>
      </template>
    </a-alert>

    <!-- 表单内容 -->
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item
        label="容器名称"
        name="name"
        required
      >
        <a-input
          v-model:value="form.name"
          placeholder="请输入容器名称"
        />
      </a-form-item>

      <a-form-item
        label="Docker Host"
        name="host"
        required
      >
        <a-input
          v-model:value="form.host"
          placeholder="例如: tcp://*************:2375"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'
import { editDocker } from '@/apis/docker'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref()
const form = reactive({
  id: '',
  name: '',
  host: '',
  tlsCertPath: '',
  description: ''
})

const rules = {
  name: [{ required: true, message: '请输入容器名称' }],
  host: [{ required: true, message: '请输入Docker Host地址' }]
}

const loading = ref(false)

// 监听表单数据变化
watch(
  () => props.formData,
  (val) => {
    if (val) {
      // 编辑模式：复制现有数据
      Object.assign(form, {
        id: val.id,
        name: val.name,
        host: val.host,
        tlsCertPath: val.tlsCertPath || '',
        description: val.description || ''
      })
    } else {
      // 新增模式：重置表单
      Object.assign(form, {
        id: '',
        name: '',
        host: '',
        tlsCertPath: '',
        description: ''
      })
    }
  },
  { immediate: true }
)

const handleCancel = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = {
      ...form,
      ...(props.formData ? { id: form.id } : {})
    }
    
    const res = await editDocker(submitData)
    
    if (res.code === 200) {
      message.success(props.formData ? '修改成功' : '新增成功')
      emit('success')
      handleCancel()
    }
  } catch (error) {
    if (error.message) {
      message.error(error.message)
    }
  } finally {
    loading.value = false
  }
}
</script> 