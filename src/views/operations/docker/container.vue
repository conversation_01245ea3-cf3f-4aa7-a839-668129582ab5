<template>
  <div>
    <template v-if="type === 'container'">
      <YssTable
        :data="list"
        size="middle"
        :columns="columns"
        :show-pagination="false"
        bordered
        row-key="id"
        :scroll="{
          x: 'max-content'
        }"
      >
        <template #title>
          <a-space>
            <a-input
              v-model:value="listQuery['name']"
              placeholder="名称"
              class="search-input-item"
              @press-enter="loadData"
            />
            <a-input
              v-model:value="listQuery['containerId']"
              placeholder="容器id"
              class="search-input-item"
              @press-enter="loadData"
            />
            <a-input
              v-model:value="listQuery['imageId']"
              placeholder="镜像id"
              class="search-input-item"
              @keyup.enter="loadData"
            />
            <div class="dark:text-white">
              查看
              <a-switch
                v-model:checked="listQuery['showAll']"
                checked-children="所有"
                un-checked-children="运行中"
              />
            </div>
            <a-button
              type="primary"
              :loading="loading"
              @click="loadData"
            >
              搜索
            </a-button>
            <a-statistic-countdown
              format="s"
              title="刷新倒计时"
              :value="countdownTime"
              @finish="autoUpdate"
            >
              <template #suffix>
                <div style="font-size: 12px">
                  s 秒
                </div>
              </template>
            </a-statistic-countdown>
          </a-space>
        </template>
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'names'">
            <a-popover :title="`容器名称：${(text || []).join(',')}`">
              <template #content>
                <p>容器Id: {{ record.id }}</p>
                <p>镜像：{{ record.image }}</p>
                <p>镜像Id: {{ record.imageId }}</p>
              </template>

              <span>{{ (text || []).join(',') }}</span>
            </a-popover>
          </template>

          <template v-else-if="column.dataIndex === 'labels'">
            <a-popover title="容器名标签">
              <template #content>
                <template v-if="record.labels">
                  <p
                    v-for="(value, key) in record.labels"
                    :key="key"
                  >
                    {{ key }}
                    <ArrowRightOutlined />

                    {{ value }}
                  </p>
                </template>
              </template>
              <template v-if="record.labels && Object.keys(record.labels).length">
                <span>{{ (record.labels && Object.keys(record.labels).length) || 0 }} <TagsOutlined /></span>
              </template>
              <template v-else>
                -
              </template>
            </a-popover>
          </template>
          <template v-else-if="column.dataIndex === 'mounts'">
            <a-popover title="挂载">
              <template #content>
                <template v-if="record.mounts">
                  <div
                    v-for="(item, index) in record.mounts"
                    :key="index"
                  >
                    <p>
                      名称：{{ item.name }}
                      <a-tag>{{ item.rw ? '读写' : '读' }}</a-tag>
                    </p>
                    <p>路径：{source}(宿主机) => {destination}(容器)</p>
                    <a-divider></a-divider>
                  </div>
                </template>
              </template>
              <template v-if="record.mounts && Object.keys(record.mounts).length">
                <span>{{ (record.mounts && Object.keys(record.mounts).length) || 0 }} <ApiOutlined /></span>
              </template>
              <template v-else>
                -
              </template>
            </a-popover>
          </template>

          <template v-else-if="column.tooltip">
            <a-tooltip
              placement="topLeft"
              :title="text"
            >
              <span>{{ text }}</span>
            </a-tooltip>
          </template>

          <template v-else-if="column.showid">
            <a-tooltip
              placement="topLeft"
              :title="text"
            >
              <span style="display: none"> {{ (array = text.split(':')) }}</span>
              <span>{{ array[array.length - 1].slice(0, 12) }}</span>
            </a-tooltip>
          </template>

          <template v-else-if="column.dataIndex === 'ports'">
            <a-popover placement="topLeft">
              <template #title>
                网络端口
                <ul>
                  <li
                    v-for="(item, index) in text || []"
                    :key="index"
                  >
                    {{ item.type + ' ' + (item.ip || '') + ':' + (item.publicPort || '') + ':' + item.privatePort }}
                  </li>
                </ul>
              </template>
              <template #content>
                <template v-if="record.networkSettings">
                  <template v-if="record.networkSettings.networks">
                    <template v-if="record.networkSettings.networks.bridge">
                      桥接模式：
                      <p v-if="record.networkSettings.networks.bridge.ipAddress">
                        IP:
                        <a-tag>{{ record.networkSettings.networks.bridge.ipAddress }}</a-tag>
                      </p>
                      <p v-if="record.networkSettings.networks.bridge.macAddress">
                        MAC:
                        <a-tag>{{ record.networkSettings.networks.bridge.macAddress }}</a-tag>
                      </p>
                      <p v-if="record.networkSettings.networks.bridge.gateway">
                        网关:
                        <a-tag>{{ record.networkSettings.networks.bridge.gateway }}</a-tag>
                      </p>
                      <p v-if="record.networkSettings.networks.bridge.networkID">
                        networkID:
                        <a-tag>{{ record.networkSettings.networks.bridge.networkID }}</a-tag>
                      </p>
                      <p v-if="record.networkSettings.networks.bridge.endpointId">
                        endpointId:
                        <a-tag>{{ record.networkSettings.networks.bridge.endpointId }}</a-tag>
                      </p>
                    </template>
                    <template v-if="record.networkSettings.networks.ingress">
                      <p v-if="record.networkSettings.networks.ingress.ipAddress">
                        IP:
                        <a-tag>{{ record.networkSettings.networks.ingress.ipAddress }}</a-tag>
                      </p>
                      <p v-if="record.networkSettings.networks.ingress.macAddress">
                        MAC:
                        <a-tag>{{ record.networkSettings.networks.ingress.macAddress }}</a-tag>
                      </p>
                      <p v-if="record.networkSettings.networks.ingress.gateway">
                        网关:
                        <a-tag>{{ record.networkSettings.networks.ingress.gateway }}</a-tag>
                      </p>
                      <p v-if="record.networkSettings.networks.ingress.networkID">
                        networkID:
                        <a-tag>{{ record.networkSettings.networks.ingress.networkID }}</a-tag>
                      </p>
                      <p v-if="record.networkSettings.networks.ingress.endpointId">
                        endpointId:
                        <a-tag>{{ record.networkSettings.networks.ingress.endpointId }}</a-tag>
                      </p>
                    </template>
                  </template>
                </template>
              </template>
              <span>{{
                (text || [])
                  .slice(0, 2)
                  .map((item) => {
                    return item.type + ' ' + (item.publicPort || '') + ':' + item.privatePort
                  })
                  .join('/')
              }}</span>
            </a-popover>
          </template>

          <template v-else-if="column.dataIndex === 'state'">
            <a-tooltip
              :title="(record.status || '') + '点击查看日志'"
              @click="viewLog(record)"
            >
              <a-switch
                :checked="text === 'running'"
                :disabled="true"
              >
                <template #checkedChildren>
                  <CheckCircleOutlined />
                </template>
                <template #unCheckedChildren>
                  <WarningOutlined />
                </template>
              </a-switch>
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <a-space>
              <template v-if="record.state === 'running'">
                <!-- <a-tooltip title="容器是运行中可以进入终端">
                  <a-button
                    size="small"
                    type="link"
                    :disabled="record.state !== 'running'"
                    @click="handleTerminal(record)"
                  >
                    <CodeOutlined />
                  </a-button>
                </a-tooltip> -->
                <a-tooltip title="停止">
                  <a-button
                    size="small"
                    type="link"
                    @click="doAction(record, 'stop')"
                  >
                    <StopOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip title="重启">
                  <a-button
                    size="small"
                    type="link"
                    @click="doAction(record, 'restart')"
                  >
                    <ReloadOutlined />
                  </a-button>
                </a-tooltip>
              </template>
              <template v-else>
                <a-tooltip title="启动">
                  <a-button
                    size="small"
                    type="link"
                    @click="doAction(record, 'start')"
                  >
                    <PlayCircleOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip title="停止">
                  <a-button
                    size="small"
                    type="link"
                    :disabled="true"
                  >
                    <StopOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip title="重启">
                  <a-button
                    size="small"
                    type="link"
                    :disabled="true"
                  >
                    <ReloadOutlined />
                  </a-button>
                </a-tooltip>
              </template>

              <a-dropdown>
                <a
                  style="margin-top: 2px;"
                  @click="(e) => e.preventDefault()"
                >
                  ...
                  <!-- <MoreOutlined /> -->
                </a>
                <template #overlay>
                  <a-menu>
                    <a-menu-item>
                      <a-tooltip title="修改容器配置，重新运行">
                        <a-button
                          size="small"
                          type="link"
                          @click="rebuild(record)"
                        >
                          <!-- <RedoOutlined /> -->
                          重建
                        </a-button>
                      </a-tooltip>
                    </a-menu-item>
                    <a-menu-item>
                      <a-tooltip title="编辑容器的一些基础参数">
                        <a-button
                          size="small"
                          type="link"
                          :disabled="record.state !== 'running'"
                          @click="editContainer(record)"
                        >
                          <!-- <EditOutlined /> -->
                          编辑
                        </a-button>
                      </a-tooltip>
                    </a-menu-item>
                    <a-menu-item>
                      <a-tooltip title="点击查看日志">
                        <a-button
                          size="small"
                          type="link"
                          :loading="viewLogLoading"
                          @click="viewLog(record)"
                        >
                          <!-- <MessageOutlined /> -->
                          日志
                        </a-button>
                      </a-tooltip>
                    </a-menu-item>
                    <a-menu-item>
                      <a-tooltip title="强制删除">
                        <a-button
                          size="small"
                          type="link"
                          @click="doAction(record, 'remove')"
                        >
                          <!-- <DeleteOutlined /> -->
                          删除
                        </a-button>
                      </a-tooltip>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </YssTable>
    </template>
    <template v-else-if="type === 'compose'">
      <a-card>
        <template #title>
          <a-space>
            <a-input
              v-model:value="listQuery['name']"
              placeholder="名称"
              class="search-input-item"
              @press-enter="loadData"
            />
            <a-input
              v-model:value="listQuery['containerId']"
              placeholder="容器id"
              class="search-input-item"
              @press-enter="loadData"
            />
            <a-input
              v-model:value="listQuery['imageId']"
              placeholder="镜像id"
              class="search-input-item"
              @keyup.enter="loadData"
            />
            <div class="dark:text-white">
              查看
              <a-switch
                v-model:checked="listQuery['showAll']"
                checked-children="所有"
                un-checked-children="运行中"
              />
            </div>
            <a-button
              type="primary"
              :loading="loading"
              @click="loadData"
            >
              搜索
            </a-button>
            <a-statistic-countdown
              format="s"
              title="刷新倒计时"
              :value="countdownTime"
              @finish="autoUpdate"
            >
              <template #suffix>
                <div style="font-size: 12px">
                  s 秒
                </div>
              </template>
            </a-statistic-countdown>
          </a-space>
        </template>
        <a-collapse v-if="list && list.length">
          <a-collapse-panel
            v-for="(item2, index) in list"
            :key="index"
          >
            <template #header>
              <a-space>
                <span>{{ item2.name }}</span>
                <span>
                  <span style="display: none">
                    {{
                      (array = (item2.child || []).map((item) => {
                        return item.state
                      }))
                    }}
                    {{
                      (runningCount = array
                        .map((item) => {
                          return item === 'running' ? 1 : 0
                        })
                        .reduce((prev, curr) => {
                          return prev + curr
                        }, 0))
                    }}</span>
                  <span v-if="runningCount">Running({{ runningCount }}/{{ array.length }})</span>
                  <span v-else>Exited</span>
                </span>
              </a-space>
            </template>
            <YssTable
              :data="item2.child"
              size="middle"
              :columns="columns"
              :show-pagination="false"
              bordered
              row-key="id"
              :scroll="{
                x: 'max-content'
              }"
            >
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'names'">
                  <a-popover :title="`容器名称：${(text || []).join(',')}`">
                    <template #content>
                      <p>容器Id: {{ record.id }}</p>
                      <p>镜像：{{ record.image }}</p>
                      <p>镜像Id: {{ record.imageId }}</p>
                    </template>

                    <span>{{ (text || []).join(',') }}</span>
                  </a-popover>
                </template>

                <template v-else-if="column.dataIndex === 'labels'">
                  <a-popover title="容器名标签">
                    <template #content>
                      <template v-if="record.labels">
                        <p
                          v-for="(value, key) in record.labels"
                          :key="key"
                        >
                          {{ key }}

                          <ArrowRightOutlined />
                          {{ value }}
                        </p>
                      </template>
                    </template>
                    <template v-if="record.labels && Object.keys(record.labels).length">
                      <span>{{ (record.labels && Object.keys(record.labels).length) || 0 }} <TagsOutlined /></span>
                    </template>
                    <template v-else>
                      -
                    </template>
                  </a-popover>
                </template>
                <template v-else-if="column.dataIndex === 'mounts'">
                  <a-popover title="挂载">
                    <template #content>
                      <template v-if="record.mounts">
                        <div
                          v-for="(item, idx) in record.mounts"
                          :key="idx"
                        >
                          <p>
                            名称：{{ item.name }}
                            <a-tag>{{ item.rw ? '读写': '读' }}</a-tag>
                          </p>
                          <p>
                            {{ ('路径：{source}(宿主机) => {destination}(容器)', { source: item.source, destination: item.destination }) }}
                          </p>
                          <a-divider></a-divider>
                        </div>
                      </template>
                    </template>
                    <template v-if="record.mounts && Object.keys(record.mounts).length">
                      <span>{{ (record.mounts && Object.keys(record.mounts).length) || 0 }} <ApiOutlined /></span>
                    </template>
                    <template v-else>
                      -
                    </template>
                  </a-popover>
                </template>

                <template v-else-if="column.tooltip">
                  <a-tooltip
                    placement="topLeft"
                    :title="text"
                  >
                    <span>{{ text }}</span>
                  </a-tooltip>
                </template>

                <template v-else-if="column.showid">
                  <a-tooltip
                    placement="topLeft"
                    :title="text"
                  >
                    <span style="display: none"> {{ (array = text.split(':')) }}</span>
                    <span>{{ array[array.length - 1].slice(0, 12) }}</span>
                  </a-tooltip>
                </template>

                <template v-else-if="column.dataIndex === 'ports'">
                  <a-popover placement="topLeft">
                    <template #title>
                      网络端口
                      <ul>
                        <li
                          v-for="(item, idx) in text || []"
                          :key="idx"
                        >
                          {{
                            item.type + ' ' + (item.ip || '') + ':' + (item.publicPort || '') + ':' + item.privatePort
                          }}
                        </li>
                      </ul>
                    </template>
                    <template #content>
                      <template v-if="record.networkSettings">
                        <template v-if="record.networkSettings.networks">
                          <template v-if="record.networkSettings.networks.bridge">
                            桥接模式：
                            <p v-if="record.networkSettings.networks.bridge.ipAddress">
                              IP:
                              <a-tag>{{ record.networkSettings.networks.bridge.ipAddress }}</a-tag>
                            </p>
                            <p v-if="record.networkSettings.networks.bridge.macAddress">
                              MAC:
                              <a-tag>{{ record.networkSettings.networks.bridge.macAddress }}</a-tag>
                            </p>
                            <p v-if="record.networkSettings.networks.bridge.gateway">
                              网关:
                              <a-tag>{{ record.networkSettings.networks.bridge.gateway }}</a-tag>
                            </p>
                            <p v-if="record.networkSettings.networks.bridge.networkID">
                              networkID:
                              <a-tag>{{ record.networkSettings.networks.bridge.networkID }}</a-tag>
                            </p>
                            <p v-if="record.networkSettings.networks.bridge.endpointId">
                              endpointId:
                              <a-tag>{{ record.networkSettings.networks.bridge.endpointId }}</a-tag>
                            </p>
                          </template>
                          <template v-if="record.networkSettings.networks.ingress">
                            <p v-if="record.networkSettings.networks.ingress.ipAddress">
                              IP:
                              <a-tag>{{ record.networkSettings.networks.ingress.ipAddress }}</a-tag>
                            </p>
                            <p v-if="record.networkSettings.networks.ingress.macAddress">
                              MAC:
                              <a-tag>{{ record.networkSettings.networks.ingress.macAddress }}</a-tag>
                            </p>
                            <p v-if="record.networkSettings.networks.ingress.gateway">
                              网关:
                              <a-tag>{{ record.networkSettings.networks.ingress.gateway }}</a-tag>
                            </p>
                            <p v-if="record.networkSettings.networks.ingress.networkID">
                              networkID:
                              <a-tag>{{ record.networkSettings.networks.ingress.networkID }}</a-tag>
                            </p>
                            <p v-if="record.networkSettings.networks.ingress.endpointId">
                              endpointId:
                              <a-tag>{{ record.networkSettings.networks.ingress.endpointId }}</a-tag>
                            </p>
                          </template>
                        </template>
                      </template>
                    </template>
                    <span>{{
                      (text || [])
                        .map((item) => {
                          return item.type + ' ' + (item.publicPort || '') + ':' + item.privatePort
                        })
                        .join('/')
                    }}</span>
                  </a-popover>
                </template>

                <template v-else-if="column.dataIndex === 'state'">
                  <a-tooltip
                    :title="(record.status || '') + '点击查看日志'"
                    @click="viewLog(record)"
                  >
                    <a-switch
                      :checked="record.state === 'running'"
                      :disabled="true"
                    >
                      <template #checkedChildren>
                        <CheckCircleOutlined />
                      </template>
                      <template #unCheckedChildren>
                        <WarningOutlined />
                      </template>
                    </a-switch>
                  </a-tooltip>
                </template>
                <template v-else-if="column.dataIndex === 'operation'">
                  <a-space>
                    <template v-if="record.state === 'running'">
                      <!-- <a-tooltip title="容器是运行中可以进入终端">
                        <a-button
                          size="small"
                          type="link"
                          @click="handleTerminal(record)"
                        >
                          <CodeOutlined />
                        </a-button>
                      </a-tooltip> -->
                      <a-tooltip title="停止">
                        <a-button
                          size="small"
                          type="link"
                          @click="doAction(record, 'stop')"
                        >
                          <StopOutlined />
                        </a-button>
                      </a-tooltip>
                      <a-tooltip title="重启">
                        <a-button
                          size="small"
                          type="link"
                          @click="doAction(record, 'restart')"
                        >
                          <ReloadOutlined />
                        </a-button>
                      </a-tooltip>
                    </template>
                    <template v-else>
                      <a-tooltip title="启动">
                        <a-button
                          size="small"
                          type="link"
                          @click="doAction(record, 'start')"
                        >
                          <PlayCircleOutlined />
                        </a-button>
                      </a-tooltip>
                      <a-tooltip title="停止">
                        <a-button
                          size="small"
                          type="link"
                          :disabled="true"
                        >
                          <StopOutlined />
                        </a-button>
                      </a-tooltip>
                      <a-tooltip title="重启">
                        <a-button
                          size="small"
                          type="link"
                          :disabled="true"
                        >
                          <ReloadOutlined />
                        </a-button>
                      </a-tooltip>
                    </template>

                    <a-dropdown>
                      <a
                        style="margin-top: 2px;"
                        @click="(e) => e.preventDefault()"
                      >
                        <!-- <MoreOutlined /> -->
                        ...
                      </a>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item>
                            <a-tooltip title="修改容器配置，重新运行">
                              <a-button
                                size="small"
                                type="link"
                                @click="rebuild(record)"
                              >
                                <!-- <RedoOutlined /> -->
                                重建
                              </a-button>
                            </a-tooltip>
                          </a-menu-item>
                          <a-menu-item>
                            <a-tooltip title="编辑容器的一些基础参数">
                              <a-button
                                size="small"
                                type="link"
                                :disabled="record.state !== 'running'"
                                @click="editContainer(record)"
                              >
                                <!-- <EditOutlined /> -->
                                编辑
                              </a-button>
                            </a-tooltip>
                          </a-menu-item>
                          <a-menu-item>
                            <a-tooltip title="点击查看日志">
                              <a-button
                                size="small"
                                type="link"
                                :loading="viewLogLoading"
                                @click="viewLog(record)"
                              >
                                <!-- <MessageOutlined /> -->
                                日志
                              </a-button>
                            </a-tooltip>
                          </a-menu-item>
                          <a-menu-item>
                            <a-tooltip title="强制删除">
                              <a-button
                                size="small"
                                type="link"
                                @click="doAction(record, 'remove')"
                              >
                                <!-- <DeleteOutlined /> -->
                                删除
                              </a-button>
                            </a-tooltip>
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </a-space>
                </template>
              </template>
            </YssTable>
          </a-collapse-panel>
        </a-collapse>
        <a-empty
          v-else
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        >
          <template #description>
            暂无数据
          </template>
        </a-empty>
      </a-card>
    </template>
    <!-- 日志 -->

    <!-- <log-view2
      v-if="logVisible > 0"
      :id="id"
      :visible="logVisible != 0"
      :machine-docker-id="machineDockerId"
      :container-id="temp.id"
      @close="
        () => {
          logVisible = 0
        }
      "
    /> -->
    <!-- 终端 -->
    <!-- <a-modal
      v-if="terminalVisible"
      :visible="terminalVisible"
      width="80vw"
      :body-style="{
        padding: '0 10px',
        paddingTop: '10px',
        marginRight: '10px',
        height: `70vh`
      }"
      :title="`docker cli ${(temp.names || []).join(',')}`"
      :footer="null"
      :mask-closable="false"
      @cancel="
        terminalVisible = false
      "
    >
      <terminal2
        v-if="terminalVisible"
        :id="id"
        :machine-docker-id="machineDockerId"
        :container-id="temp.id"
      />
    </a-modal> -->
    <a-modal
      v-if="editVisible"
      :visible="editVisible"
      destroy-on-close
      :confirm-loading="confirmLoading"
      width="60vw"
      title="配置容器"
      :mask-closable="false"
      @cancel="handleEditCancel"
      @ok="
        () => {
          $refs.editContainer
            .handleEditOk()
            .then(() => {
              editVisible = false
              loadData()
            })
            .finally(() => {
              confirmLoading = false
            })
        }
      "
    >
      <editContainer
        :id="id"
        ref="editContainer"
        :machine-docker-id="machineDockerId"
        :container-id="temp.id"
      ></editContainer>
    </a-modal>

    <BuildContainer
      v-if="buildVisible"
      :id="id"
      :image-id="temp.imageId"
      :machine-docker-id="machineDockerId"
      :container-id="temp.id"
      :container-data="temp"
      :buildVisible="buildVisible"
      @cancel-btn-click="
        () => {
          buildVisible = false
        }
      "
      @confirm-btn-click="
        () => {
          buildVisible = false
          loadData()
        }
      "
    />
  </div>
</template>

<script>
import { 
  MessageOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  EditOutlined,
  CodeOutlined,
  TagsOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  MoreOutlined,
  ArrowRightOutlined,
  ApiOutlined,
  RedoOutlined
} from '@ant-design/icons-vue'

import { parseTime } from '@/utils/const'
import {
  dockerContainerList,
  dockerContainerRemove,
  dockerContainerRestart,
  dockerContainerStart,
  dockerContainerStop,
  dockerContainerListCompose
} from '@/apis/docker'
// import LogView2 from './log-view.vue'
// import Terminal2 from './terminal.vue'
import editContainer from './editContainer.vue'
import BuildContainer from './buildContainer.vue'
import { Empty , message ,Modal} from 'ant-design-vue'
import { YssTable } from '@yss-design/ui'
export default {
  name: 'Container',
  components: {
    // LogView2,
    // Terminal2,
    editContainer,
    BuildContainer,
    MessageOutlined,
    DeleteOutlined,
    PlayCircleOutlined,
    StopOutlined,
    ReloadOutlined,
    EditOutlined,
    CodeOutlined,
    TagsOutlined,
    CheckCircleOutlined,
    WarningOutlined,
    MoreOutlined,
    ArrowRightOutlined,
    ApiOutlined,
    RedoOutlined,
    YssTable
  },
  props: {
    id: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    // urlPrefix: {
    //   type: String,
    //   default: ''
    // },
    machineDockerId: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      // container  or compose
      default: 'container'
    }
  },
  data() {
    return {
      Empty,
      list: [],
      loading: false,
      listQuery: {
        showAll: true
      },
      // terminalVisible: false,
      // logVisible: 0,
      temp: {},
      confirmLoading: false,
      columns: [
        {
          title: '序号',
          width: '60px',
          // ellipsis: true,
          align: 'center',
          customRender: ({ index }) => `${index + 1}`
        },
        {
          title: '名称',
          dataIndex: 'names',
          ellipsis: true
          // width: 150
        },
        {
          title: '容器ID',
          dataIndex: 'id',
          ellipsis: true,
          width: '10px',
          showid: true
        },
        {
          title: '镜像ID',
          dataIndex: 'imageId',
          ellipsis: true,
          width: '130px',
          showid: true
        },
        {
          title: '状态',
          dataIndex: 'state',
          // ellipsis: true,
          align: 'center',
          width: '80px'
        },

        {
          title: '端口',
          dataIndex: 'ports',
          ellipsis: true,
          width: '100px'
        },

        {
          title: '标签',
          dataIndex: 'labels',
          ellipsis: true,
          width: '50px'
        },
        {
          title: '挂载',
          dataIndex: 'mounts',
          ellipsis: true,
          width: '50px'
        },
        {
          title: '命令',
          dataIndex: 'command',
          ellipsis: true,
          width: 150,
          tooltip: true
        },
        {
          title: '创建时间',
          dataIndex: 'created',
          ellipsis: true,
          // sorter: (a, b) => Number(a.created) - new Number(b.created),
          // sortDirections: ['descend', 'ascend'],
          // defaultSortOrder: 'descend',
          customRender: ({ text }) => parseTime(text),
          width: '170px'
        },
        {
          title: '操作',
          dataIndex: 'operation',
          fixed: 'right',
          width: '160px'
        }
      ],
      action: {
        remove: {
          msg: '您确定要删除当前容器吗？',
          api: dockerContainerRemove
        },
        stop: {
          msg: '您确定要停止当前容器吗？',
          api: dockerContainerStop
        },
        restart: {
          msg: '您确定要重启当前容器吗？',
          api: dockerContainerRestart
        },
        start: {
          msg: '您确定要启动当前容器吗？',
          api: dockerContainerStart
        }
      },
      editVisible: false,

      countdownTime: Date.now(),

      buildVisible: false,
      viewLogLoading: false
    }
  },
  computed: {
    reqDataId() {
      return this.id || this.machineDockerId
    }
  },
  beforeUnmount() {},
  mounted() {
    this.autoUpdate()
  },
  methods: {
    autoUpdate() {
      this.loadData()
    },
    // 加载数据
    loadData() {
      if (!this.visible) {
        return
      }
      this.loading = true
      //this.listQuery.page = pointerEvent?.altKey || pointerEvent?.ctrlKey ? 1 : this.listQuery.page;
      this.listQuery.id = this.reqDataId
      ;(this.type === 'container'
        ? dockerContainerList(this.listQuery.id)
        : dockerContainerListCompose(this.listQuery.id)
      ).then((res) => {
        if (res.code === 200) {
          this.list = this.sortPort(res.data || []).map((item) => {
            let child = item.child
            if (child) {
              child = this.sortPort(child)
            }
            return { ...item, child: child }
          })
        }
        this.loading = false
        this.countdownTime = Date.now() + 5 * 1000
      })
    },
    sortPort(list) {
      return list.map((item) => {
        let ports = item.ports
        if (ports) {
          try {
            ports = ports.sort(
              (a, b) =>
                a.privatePort - b.privatePort ||
                (a.type || '').toLowerCase().localeCompare((b.type || '').toLowerCase())
            )
          } catch (e) {
            console.error(e)
          }
        }

        return { ...item, ports: ports }
      })
    },
    doAction(record, actionKey) {
      const action = this.action[actionKey]
      if (!action) {
        return
      }
      Modal.confirm({
        title: '系统提示',
        content: action.msg,
        onOk: () => {
          return action
            .api({
              id: this.reqDataId,
              containerId: record.id
            })
            .then((res) => {
              if (res.code === 200) {
                message.success('操作成功')
                this.loadData()
              }
            })
        }
      })
    },
    viewLog(record) {
      this.viewLogLoading = true
      window.open(`/aip/assets/docker/container/download-log?id=${this.reqDataId}&containerId=${record.id}`, '_self')
      setTimeout(() => {
        this.viewLogLoading = false
      }, 5000)
      // console.log('-')
      // this.logVisible = new Date() * Math.random()
      // this.temp = record
    },
    // 进入终端
    // handleTerminal(record) {
    //   this.temp = Object.assign({}, record)
    //   this.terminalVisible = true
    // },
    editContainer(record) {
      this.temp = Object.assign({}, record)
      this.editVisible = true
    },
    handleEditCancel() {
      this.editVisible = false
    },
    // click rebuild button
    rebuild(record) {
      this.temp = Object.assign({}, record)
      this.buildVisible = true
    }
  }
}
</script>
<style scoped>
:deep(.ant-statistic div) {
  display: inline-block;
  font-weight: normal;
}
:deep(.ant-statistic-content-value, .ant-statistic-content) {
  font-size: 16px;
}
</style>
