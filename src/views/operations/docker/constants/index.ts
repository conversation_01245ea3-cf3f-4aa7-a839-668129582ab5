// 表格列配置
export const columns = [
  {
    type: 'selection',
    width: 60,
    align: 'center'
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: 'host',
    dataIndex: 'host',
    width: 300,
    ellipsis: true
  },
  {
    title: 'docker版本',
    dataIndex: 'dockerVersion',
    width: 150
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    slots: { customRender: 'statusColumn' }
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 200,
    // fixed: 'right',
    slots: { customRender: 'operationColumn' }
  }
]

// 搜索表单配置
export const searchSchema = [
  {
    key: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入名称'
  },
  {
    key: 'host', 
    label: 'host',
    type: 'input',
    placeholder: '请输入host'
  }
]

// Docker状态常量
export const DOCKER_STATUS = {
  NORMAL: 1,
  ABNORMAL: 0
} as const

// Docker状态文本映射
export const DOCKER_STATUS_TEXT = {
  [DOCKER_STATUS.NORMAL]: '正常连接',
  [DOCKER_STATUS.ABNORMAL]: '连接异常'
} as const

// Docker状态颜色映射
export const DOCKER_STATUS_COLOR = {
  [DOCKER_STATUS.NORMAL]: 'green',
  [DOCKER_STATUS.ABNORMAL]: 'red'
} as const

// 表格配置
export const tableConfig = {
  rowKey: 'id',
  pagination: {
    pageSize: 10,
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`
  }
}