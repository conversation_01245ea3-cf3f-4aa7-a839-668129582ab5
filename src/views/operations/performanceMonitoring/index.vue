<template>
  <a-layout>
    <a-layout-content class="bg-global-background">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-custom">服务器性能监控</h1>
        <div class="flex gap-4">
          <a-select v-model:value="timeRange" style="width: 120px" @change="timeRangeChange">
            <a-select-option value="1h">最近1小时</a-select-option>
            <a-select-option value="1d">最近24小时</a-select-option>
            <a-select-option value="7d">最近7天</a-select-option>
          </a-select>
          <a-button type="primary" @click="refreshData"> 刷新 </a-button>
        </div>
      </div>

      <a-row :gutter="16">
        <!-- CPU 使用率 -->
        <a-col :span="6">
          <YssCard class="w-full" :monitorContent="cpuCardData" cardType="monitor">
            <template #chart>
            <div ref="cpuChart" class="w-full h-48"></div>
          </template>
          </YssCard>
        </a-col>

        <!-- 内存使用情况 -->
        <a-col :span="6">
          <YssCard class="w-full" :monitorContent="memoryCardData" cardType="monitor">
            <template #chart>
              <div ref="memoryChart" class="w-full h-48"></div>
            </template>
          </YssCard>
        </a-col>

        <!-- 磁盘 I/O -->
        <a-col :span="6">
          <YssCard class="w-full" :monitorContent="diskIoCardData" cardType="monitor">
            <template #chart>
              <div ref="diskChart" class="w-full h-48"></div>
            </template>
          </YssCard>
        </a-col>

        <!-- 网络速率 -->
        <a-col :span="6">
          <YssCard class="w-full" :monitorContent="networkCardData" cardType="monitor">
            <template #chart>
              <div ref="networkChart" class="w-full h-48"></div>
            </template>
          </YssCard>
        </a-col>
      </a-row>

      <!-- 新增行显示更多指标 -->
      <a-row :gutter="16" class="mt-6">
        <!-- 系统负载 -->
        <a-col :span="6">
          <YssCard class="w-full" :monitorContent="systemLoadCardData" cardType="monitor">
            <template #chart>
              <div ref="loadChart" class="w-full h-48"></div>
            </template>
          </YssCard>
        </a-col>

        <!-- 进程数量 -->
        <a-col :span="6">
          <YssCard class="w-full" :monitorContent="processCountCardData" cardType="monitor">
            <template #chart>
              <div ref="processChart" class="w-full h-48"></div>
            </template>
          </YssCard>
        </a-col>

        <!-- 磁盘使用情况 -->
        <a-col :span="6">
          <YssCard class="w-full" :monitorContent="diskUsageCardData" cardType="monitor">
            <template #chart>
              <div ref="diskUsageChart" class="w-full h-48"></div>
            </template>
          </YssCard>
        </a-col>

        <!-- 网络延迟 -->
        <a-col :span="6">
          <YssCard class="w-full" :monitorContent="networkLatencyCardData" cardType="monitor">
            <template #chart>
              <div ref="networkLatencyChart" class="w-full h-48"></div>
            </template>
          </YssCard>
        </a-col>
      </a-row>
    </a-layout-content>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import { getMetricsHour, getMetricsDay, getMetricsWeek } from '@/apis/operations'
import { YssCard } from '@yss-design/ui'

// 定义图表容器
const cpuChart = ref(null)
const memoryChart = ref(null)
const diskChart = ref(null)
const networkChart = ref(null)
const loadChart = ref(null)
const processChart = ref(null)
const diskUsageChart = ref(null)
const networkLatencyChart = ref(null)

// 存储图表实例
const chartInstances = {
  cpuChart: null,
  memoryChart: null,
  diskChart: null,
  networkChart: null,
  loadChart: null,
  processChart: null,
  diskUsageChart: null,
  networkLatencyChart: null
}

// 时间范围选择
const timeRange = ref('1h')
// 性能监控数据
const cpuChartData = ref<Record<string, any>>({})
const systemLoadChartData = ref<Record<string, any>>({})
const processCountChartData = ref<Record<string, any>>({})
const cupCurrentData = ref<Record<string, any>>({})
const memoryChartData = ref<Record<string, any>>({})
const memoryCurrentData = ref<Record<string, any>>({})
const diskIoChartData = ref<Record<string, any>>({})
const diskUsedChartData = ref<Record<string, any>>({})
const diskIoCurrentData = ref<Record<string, any>>({})
const networkChartData = ref<Record<string, any>>({})
const networkLatencyChartData = ref<Record<string, any>>({})
const networkCurrentData = ref<Record<string, any>>({})

const isweek = computed(() => {
  return timeRange.value === '7d'
})

// 卡片数据
const cpuCardData = computed(() => {
  return {
    title: 'CPU 使用率',
    isNormal: cupCurrentData.value.isNormal,
    currentValue: [{
      name: '当前使用率',
      value: cupCurrentData.value.cpuUsage,
      unit: '%',
      status: 'success',
    },{
      name: '负载',
      value: cupCurrentData.value.systemLoad,
      unit: '',
    }],
  }
})

const memoryCardData = computed(() => {
  return {
    title: '内存使用情况',
    isNormal: memoryCurrentData.value.isNormal,
    currentValue: [{
      name: '总内存',
      value: memoryCurrentData.value.memoryTotal,
      unit: 'GB',
    },{
      name: '已用内存',
      value: memoryCurrentData.value.memoryUsed,
      unit: 'GB',
      status: 'warning',
    }],
  } 
})

const networkCardData = computed(() => {
  return {
    title: '网络速率',
    isNormal: networkCurrentData.value.isNormal,
    currentValue: [{
      name: '接收速率',
      value: networkCurrentData.value.networkReceived,
      unit: 'MB/s',
    },{
      name: '发送速率',
      value: networkCurrentData.value.networkSent,
      unit: 'MB/s',
    }],
  }
})

const diskIoCardData = computed(() => {
  return {
    title: '磁盘 I/O',
    isNormal: diskIoCurrentData.value.isNormal,
    currentValue: [{
      name: '读取速度',
      value: diskIoCurrentData.value.diskIoRead,
      unit: 'MB/s',
    },{
      name: '写入速度',
      value: diskIoCurrentData.value.diskIoWrite, 
      unit: 'MB/s',
    }],
  }
})

const systemLoadCardData = computed(() => {
  return {
    title: '系统负载',
    isNormal: cupCurrentData.value.isNormal,
    currentValue: [{
      name: '当前负载',
      value: cupCurrentData.value.systemLoad,
      unit: '',
    }],
  }
})

const processCountCardData = computed(() => {
  return {
    title: '进程数量',
    isNormal: cupCurrentData.value.isNormal,
    currentValue: [{
      name: '当前进程数',
      value: cupCurrentData.value.processCount,
      unit: '',
    }],
  }
})

const diskUsageCardData = computed(() => {
  return {
    title: '磁盘使用情况',
    currentValue: [{
      name: '已使用',
      value: diskIoCurrentData.value.diskUsedPercent,
      unit: '%',
    }],
  }
})

const networkLatencyCardData = computed(() => {
  return {
    title: '网络延迟',
    currentValue: [{
      name: '平均延迟',
      value: networkCurrentData.value.networkLatency,
      unit: 'ms',
    }],
  }
})

// 刷新数据
const refreshData = async () => {
  await fetchData()
  // 销毁已有的图表实例
  for (const key in chartInstances) {
    if (chartInstances[key]) {
      chartInstances[key].dispose()
      chartInstances[key] = null
    }
  }
  // 初始化各个图表
  initChart(cpuChart, 'cpuChart', 'CPU 使用率', cpuChartData.value)
  initChart(memoryChart, 'memoryChart', '内存使用情况', memoryChartData.value)
  initChart(diskChart, 'diskChart', '磁盘 I/O', diskIoChartData.value)
  initChart(networkChart, 'networkChart', '网络速率', networkChartData.value)
  initChart(loadChart, 'loadChart', '系统负载', systemLoadChartData.value)
  initChart(processChart, 'processChart', '进程数量', processCountChartData.value)
  initChart(diskUsageChart, 'diskUsageChart', '磁盘使用情况', diskUsedChartData.value)
  initChart(networkLatencyChart, 'networkLatencyChart', '网络延迟', networkLatencyChartData.value)
}

// 初始化图表
const initChart = (chartRef: globalThis.Ref<null, null>, chartKey: string, title: string, data: { labels?: any; values?: any }) => {
  // 销毁已有的实例
  if (chartInstances[chartKey]) {
    chartInstances[chartKey].dispose()
  }

  // 初始化新实例
  const chart = echarts.init(chartRef.value)
  const option = {
    title: {
      text: title,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.labels
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: title,
        type: 'line',
        data: data.values,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(135, 206, 250, 0.8)' },
            { offset: 1, color: 'rgba(135, 206, 250, 0.2)' }
          ])
        }
      }
    ]
  }
  chart.setOption(option)

  // 存储新实例
  chartInstances[chartKey] = chart
}

//  时间范围选择
const timeRangeChange = () => {
  refreshData()
}

// 模拟数据获取
const fetchData = async () => {
  let resData: IResType<any>
  if(timeRange.value === '1h') {
    resData = await getMetricsHour()
  } else if(timeRange.value === '1d') {
    resData = await getMetricsDay()
  } else {
    resData = await getMetricsWeek()
  }
  if(resData.code === 200) {
    getCpuData(resData.data.cpu)
    getMemoryData(resData.data.memory)
    getDiskIoData(resData.data.diskIo)
    getNetworkData(resData.data.network)
  }
  // chartData.value = {
  //   labels: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00'],
  //   values: [20, 40, 60, 80, 100, 80]
  // }
}

// cpu数据
const getCpuData = (cupData: any[]) => {
  cpuChartData.value = {
    labels: [],
    values: []
  }
  systemLoadChartData.value = {
    labels: [],
    values: []
  }
  processCountChartData.value = {
    labels: [],
    values: []
  }
  const currentCpuUsage = cupData[cupData.length - 1].cpuUsage
  cupCurrentData.value = {
    cpuUsage: currentCpuUsage,
    systemLoad: cupData[cupData.length - 1].systemLoad,
    processCount: cupData[cupData.length - 1].processCount,
    // 警告阈值 >= 75
    isNormal: currentCpuUsage < 75 ? true : false
  }
  cupData.forEach((item) => {
    let collectTimeFormat
    if(isweek.value) {
      collectTimeFormat = item.collectTime.split(' ')[0]
    } else {
      collectTimeFormat = item.collectTime.split(' ')[1]
      collectTimeFormat = collectTimeFormat ? collectTimeFormat.split(':')[0] + ':' + collectTimeFormat.split(':')[1] : ''
    }
    cpuChartData.value.labels.push(collectTimeFormat)
    cpuChartData.value.values.push(item.cpuUsage)
    systemLoadChartData.value.labels.push(collectTimeFormat)
    systemLoadChartData.value.values.push(item.systemLoad)
    processCountChartData.value.labels.push(collectTimeFormat)
    processCountChartData.value.values.push(item.processCount)
  })
}

// 内存数据
const getMemoryData = (memoryData: any[]) => {
  memoryChartData.value = {
    labels: [],
    values: []
  }
  const memoryTotal = memoryData[memoryData.length - 1].memoryTotal
  const memoryTotalFormat = memoryTotal ? (memoryTotal / 1024 / 1024 / 1024).toFixed(2) : ''
  const memoryUsed = memoryData[memoryData.length - 1].memoryUsed
  const memoryUsedFormat = memoryUsed ? (memoryUsed / 1024 / 1024 / 1024).toFixed(2) : ''
  memoryCurrentData.value = {
    memoryTotal: memoryTotalFormat,
    memoryUsed: memoryUsedFormat,
    // 警告阈值 >= 0.75
    isNormal: memoryUsed/memoryTotal < 0.75 ? true : false
  }
  memoryData.forEach((item) => {
    let collectTimeFormat
    if(isweek.value) {
      collectTimeFormat = item.collectTime.split(' ')[0]
    } else {
      collectTimeFormat = item.collectTime.split(' ')[1]
      collectTimeFormat = collectTimeFormat ? collectTimeFormat.split(':')[0] + ':' + collectTimeFormat.split(':')[1] : ''
    }
    memoryChartData.value.labels.push(collectTimeFormat)
    const memoryUsedItemFormat = item.memoryUsed ? (item.memoryUsed / 1024 / 1024 / 1024).toFixed(2) : ''
    memoryChartData.value.values.push(memoryUsedItemFormat)
  })
}

// 磁盘数据
const getDiskIoData = (diskIoData: any[])=> {
  diskIoChartData.value = {
    labels: [],
    values: []
  }
  diskUsedChartData.value = {
    labels: [],
    values: []
  }
  const diskIoRead = diskIoData[diskIoData.length - 1].diskIoRead
  const diskIoReadFormat = diskIoRead ? (diskIoRead / 1024 / 1024).toFixed(2) : 0
  const diskIoWrite = diskIoData[diskIoData.length - 1].diskIoWrite
  const diskIoWriteFormat = diskIoWrite ? (diskIoWrite / 1024 / 1024).toFixed(2) : 0
  const diskUsed = diskIoData[diskIoData.length - 1].diskUsed
  const diskTotal = diskIoData[diskIoData.length - 1].diskTotal
  diskIoCurrentData.value = {
    diskIoRead: diskIoReadFormat,
    diskIoWrite: diskIoWriteFormat,
    diskUsedPercent: ((diskUsed / diskTotal) * 100).toFixed(2),
    // 警告阈值  非0
    isNormal: !!diskIoRead || !!diskIoWrite
  }
  diskIoData.forEach((item) => {
    let collectTimeFormat
    if(isweek.value) {
      collectTimeFormat = item.collectTime.split(' ')[0]
    } else {
      collectTimeFormat = item.collectTime.split(' ')[1]
      collectTimeFormat = collectTimeFormat ? collectTimeFormat.split(':')[0] + ':' + collectTimeFormat.split(':')[1] : ''
    }
    diskIoChartData.value.labels.push(collectTimeFormat)
    const diskIoWriteItemFormat = item.diskIoWrite ? (item.diskIoWrite / 1024 / 1024).toFixed(2) : 0
    diskIoChartData.value.values.push(diskIoWriteItemFormat)
    diskUsedChartData.value.labels.push(collectTimeFormat)
    const diskUsedItemFormat = item.diskUsed ? (item.diskUsed / 1024 / 1024 / 1024).toFixed(2) : 0
    diskUsedChartData.value.values.push(diskUsedItemFormat)
  })
}

// 网络速率数据
const getNetworkData = (networkData: any[]) => {
  networkChartData.value = {
    labels: [],
    values: []
  }
  networkLatencyChartData.value = {
    labels: [],
    values: []
  }
  const networkReceived = networkData[networkData.length - 1].networkReceived
  const networkReceivedFormat = networkReceived ? (networkReceived / 1024 / 1024).toFixed(2) : 0
  const networkSent = networkData[networkData.length - 1].networkSent
  const networkSentFormat = networkSent ? (networkSent / 1024 / 1024).toFixed(2) : 0
  const networkLatency = networkData[networkData.length - 1].networkLatency
  let networkLatencyFormat
  let networkLatencyToal = 0
  networkData.forEach((item) => {
    let collectTimeFormat
    if(isweek.value) {
      collectTimeFormat = item.collectTime.split(' ')[0]
    } else {
      collectTimeFormat = item.collectTime.split(' ')[1]
      collectTimeFormat = collectTimeFormat ? collectTimeFormat.split(':')[0] + ':' + collectTimeFormat.split(':')[1] : ''
    }
    networkLatencyToal += item.networkLatency
    networkChartData.value.labels.push(collectTimeFormat)
    const networkReceivedItemFormat = item.networkReceived ? (item.networkReceived / 1024 / 1024).toFixed(2) : ''
    networkChartData.value.values.push(networkReceivedItemFormat)
    networkLatencyChartData.value.labels.push(collectTimeFormat)
    networkLatencyChartData.value.values.push(item.networkLatency)
  })
  networkLatencyFormat = networkData.length ? (networkLatencyToal/networkData.length).toFixed(4) : ''
  networkCurrentData.value = {
    networkReceived: networkReceivedFormat,
    networkSent: networkSentFormat,
    networkLatency: networkLatencyFormat,
    // 警告阈值 非0
    isNormal: !!networkReceived || !!networkSent
  }
}

onMounted(() => {
  refreshData()
})

onUnmounted(() => {
  // 销毁所有图表实例
  for (const key in chartInstances) {
    if (chartInstances[key]) {
      chartInstances[key].dispose()
    }
  }
})
</script>