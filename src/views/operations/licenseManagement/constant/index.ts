import dayjs from 'dayjs'
import { Tag } from 'ant-design-vue'
import { h } from 'vue'

// 许可证状态枚举 - 根据后端定义
export const LICENSE_STATUS = {
  VALID: 1, // 生效
  INVALID: 2, // 失效
  PENDING: 3, // 未生效
}

// 状态显示配置
const statusConfig = {
  [LICENSE_STATUS.VALID]: { color: 'success', text: '有效' },
  [LICENSE_STATUS.INVALID]: { color: 'error', text: '已失效' },
  [LICENSE_STATUS.PENDING]: { color: 'warning', text: '未生效' },
}

// 表格列定义
export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    customRender: ({ index }) => index + 1,
  },
  {
    title: '许可证编号',
    dataIndex: 'licenseNumber',
    width: 200,
  },
  {
    title: '项目',
    dataIndex: 'projectName',
    width: 150,
  },
  {
    title: '产品',
    dataIndex: 'productName',
    width: 150,
  },
  {
    title: '申请人',
    dataIndex: 'applicantName',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
  },
  {
    title: '许可证状态',
    dataIndex: 'status',
    width: 120,
    customRender: ({ text, record }) => {
      // 根据时间判断状态
      const now = dayjs()
      const issuedTime = record.issuedTime ? dayjs(record.issuedTime) : null
      const expiryTime = record.expiryTime ? dayjs(record.expiryTime) : null

      let status = LICENSE_STATUS.INVALID // 默认失效

      if (issuedTime && expiryTime) {
        if (now.isBefore(issuedTime)) {
          status = LICENSE_STATUS.PENDING // 未生效
        } else if (now.isAfter(expiryTime)) {
          status = LICENSE_STATUS.INVALID // 已失效
        } else {
          status = LICENSE_STATUS.VALID // 有效
        }
      }

      const config = statusConfig[status] || statusConfig[LICENSE_STATUS.INVALID]
      return h(Tag, { color: config.color }, () => config.text)
    },
  },
  {
    title: '生效时间',
    dataIndex: 'issuedTime',
    width: 180,
    customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
  },
  {
    title: '失效时间',
    dataIndex: 'expiryTime',
    width: 180,
    customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    width: 200,
  },
  {
    title: '许可证内容',
    dataIndex: 'content',
    width: 300,
    customRender: ({ text }) => {
      return text ? JSON.stringify(text) : '-'
    },
  },
  {
    title: '操作',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
]

// 搜索配置
export const searchSchema = [
  {
    key: 'keyword',
    label: '关键字',
    type: 'input',
    placeholder: '请输入关键字',
  },
]
