<template>
  <div class="h-full bg-global-background">
    <BaseTable
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :search-schema="searchSchema"
      :total="total"
      @search="handleSearch"
      @reset="handleReset"
      @pageChange="handlePageChange"
    >
      <!-- 操作按钮 -->
      <template #actions>
        <a-button type="primary" @click="handleAdd"> 新增许可证 </a-button>
      </template>

      <template #action="{ record }">
        <a-space>
          <a-tooltip title="编辑" v-if="record.ifLicenseGenerated != 'Y'">
            <a-button type="link" @click="handleEdit(record)">
              <template #icon><EditOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="下载许可证" v-if="record.ifLicenseGenerated == 'Y'">
            <a-button type="link" @click="handleDownload(record)">
              <template #icon><FileProtectOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="下载公钥">
            <a-button type="link" @click="handleDownloadPublicKey(record)">
              <template #icon><SecurityScanOutlined /></template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>

    <!-- 表单弹窗 -->
    <LicenseForm
      v-model:visible="formVisible"
      :form-data="currentLicense"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { FileProtectOutlined, SecurityScanOutlined, EditOutlined } from '@ant-design/icons-vue'
import BaseTable from '@/components/BaseTable/index.vue'
import LicenseForm from './components/LicenseForm.vue'
import { columns, searchSchema, LICENSE_STATUS } from './constant'
import type { License } from './types'
import { getSysLicenseList, downloadSysLicense, downloadSysLicensePublicKey } from '@/apis/operations'
import dayjs from 'dayjs'

// 表格数据
const dataSource = ref<License[]>([])
const loading = ref(false)
const total = ref(0)
const formVisible = ref(false)
const currentLicense = ref<License | null>(null)

// 获取数据
const getList = async (params = {}) => {
  loading.value = true
  try {
    const response = await getSysLicenseList({
      pageNo: params.current || 1,
      pageSize: params.size || 10,
      keyword: params.keyword || null
    })

    // 处理后端返回的数据，映射字段名称
    dataSource.value = response.data.records.map(item => {
      // 直接使用后端返回的状态
      const status = Number(item.licenseStatus)

      // 正确计算剩余天数
      let validityPeriod = 0
      if (item.expiryTime) {
        const expiryDate = dayjs(item.expiryTime)
        const today = dayjs().startOf('day')
        validityPeriod = expiryDate.diff(today, 'day')
      }

      return {
        ...item,
        licenseNo: item.licenseNumber,
        project: item.projectName,
        product: item.productName,
        applicant: item.applicantName,
        status: status,
        remark: item.remarks,
        validityPeriod: validityPeriod,
      }
    })

    total.value = response.data.total
  } catch (error) {
    console.error('获取许可证列表失败:', error)
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = (record) => {
  currentLicense.value = record
  formVisible.value = true
}

// 新增许可证
const handleAdd = () => {
  currentLicense.value = null
  formVisible.value = true
}

// 表单提交成功
const handleSuccess = () => {
  getList()
}

// 搜索处理
const handleSearch = (params) => {
  getList({
    ...params,
    current: 1
  })
}

// 重置处理
const handleReset = () => {
  getList({ current: 1 })
}

// 分页处理
const handlePageChange = ({ current, size, searchForm }) => {
  getList({
    current,
    size,
    ...searchForm
  })
}

// 下载许可证
const handleDownload = async (record) => {
  try {
    await downloadSysLicense(record.id)
    message.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    message.error('下载失败')
  }
}

// 下载公钥
const handleDownloadPublicKey = async (record) => {
  try {
    await downloadSysLicensePublicKey(record.id)
    message.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    message.error('下载失败')
  }
}

// 初始化
getList()
</script>
