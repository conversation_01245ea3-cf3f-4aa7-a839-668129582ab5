<template>
  <a-modal
    :visible="visible"
    :title="formData ? '编辑许可证' : '新增许可证'"
    @cancel="handleCancel"
    @ok="handleSubmit"
    @update:visible="(val) => emit('update:visible', val)"
    width="800px"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="许可证编号" v-if="formData">
        <span>{{ formData.licenseNumber }}</span>
      </a-form-item>
      <a-form-item label="项目" name="projectName">
        <a-input v-model:value="form.projectName" placeholder="请输入项目名称" />
      </a-form-item>
      <a-form-item label="产品" name="productName">
        <a-input v-model:value="form.productName" placeholder="请输入产品名称" />
      </a-form-item>
      <a-form-item label="申请人" name="applicantName">
        <a-input 
          v-model:value="form.applicantName" 
          placeholder="请输入申请人" 
          :disabled="isApplicantDisabled"
        />
      </a-form-item>

      <a-form-item label="有效期" name="validTime">
        <a-range-picker
          v-model:value="form.validTime"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :placeholder="['开始时间', '结束时间']"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="备注" name="remarks">
        <a-textarea v-model:value="form.remarks" placeholder="请输入备注" />
      </a-form-item>

      <a-form-item label="密钥对" name="keyPairId">
        <a-select
          v-model:value="form.keyPairId"
          placeholder="请选择密钥对"
          :loading="keyPairLoading"
          @dropdownVisibleChange="handleDropdownVisibleChange"
        >
          <a-select-option v-for="item in keyPairList" :key="item.id" :value="item.id">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="配置" name="content">
        <JsonEditorVue
          v-model="form.content"
          :main-menu-bar="false"
          :navigation-bar="false"
          :status-bar="false"
          mode="text"
          class="json-editor"
        />
      </a-form-item>
      
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import JsonEditorVue from 'json-editor-vue'
import type { License } from '../types'
import dayjs from 'dayjs'
import { createLicense, updateLicense, getKeyPairs, createSysLicense, updateSysLicense } from '@/apis/operations'

const props = defineProps<{
  visible: boolean
  formData: License | null
}>()

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref<FormInstance>()
const form = ref({
  projectName: '',
  productName: '',
  applicantName: '',
  validTime: [] as any[], // 时间范围
  remarks: '',
  content: '{}',
  keyPairId: '',
  issuedTime: '',
  expiryTime: ''
})

// 控制申请人字段是否禁用
const isApplicantDisabled = ref(false)

const initApplicant = () => {
  if (props.formData) {
    isApplicantDisabled.value = true
    return
  }

  try {
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr) {
      const userInfo = JSON.parse(userInfoStr)
      const applicant = userInfo.realname || userInfo.username
      if (applicant) {
        form.value.applicantName = applicant
        isApplicantDisabled.value = true
      }
    }
  } catch (e) {
    console.warn('获取用户信息失败:', e)
  }
}

// 在表单初始化时设置申请人
onMounted(() => {
  initApplicant()
})

// 密钥对列表
const keyPairList = ref<{ id: string; label: string }[]>([])
const keyPairLoading = ref(false)

// 获取所有密钥对数据并转换为select格式
const getAllKeyPairs = async () => {
  if (keyPairLoading.value || keyPairList.value.length > 0) return
  
  keyPairLoading.value = true
  try {
    // 第一次请求获取total
    const firstRes = await getKeyPairs({
      pageNo: 1,
      pageSize: 1,
      keyword: null,
    })
    const totalCount = firstRes?.data?.total || 0

    if (totalCount > 0) {
      // 第二次请求获取所有数据
      const secondRes = await getKeyPairs({
        pageNo: 1,
        pageSize: totalCount,
        keyword: null,
      })
      
      // 转换数据格式
      keyPairList.value = (secondRes?.data?.records || []).map(item => ({
        id: item.id,
        label: item.name
      }))
    }
  } catch (error) {
    console.error('获取所有密钥对数据失败:', error)
    message.error('获取密钥对列表失败')
  } finally {
    keyPairLoading.value = false
  }
}

// 根据是否为编辑模式动态设置验证规则
const rules = {
  projectName: [{ required: true, message: '请输入项目名称' }],
  productName: [{ required: true, message: '请输入产品名称' }],
  applicantName: [{ required: true, message: '请输入申请人' }],
  validTime: [{ required: true, type: 'array', message: '请选择有效期', trigger: 'change' }],
  content: [{ required: true, message: '请输入配置' }],
  keyPairId: [{ required: true, message: '请选择密钥对' }]
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {
    projectName: '',
    productName: '',
    applicantName: '',
    validTime: [],
    remarks: '',
    content: '{}',
    keyPairId: '',
    issuedTime: '',
    expiryTime: ''
  }
  initApplicant()
}

// 初始化表单数据
const initFormData = () => {
  if (props.formData) {
    form.value = {
      projectName: props.formData.projectName || '',
      productName: props.formData.productName || '',
      applicantName: props.formData.applicantName || '',
      validTime: props.formData.issuedTime && props.formData.expiryTime
        ? [dayjs(props.formData.issuedTime), dayjs(props.formData.expiryTime)]
        : [],
      remarks: props.formData.remarks || '',
      content: typeof props.formData.content === 'string'
        ? props.formData.content
        : JSON.stringify(props.formData.content || {}, null, 2),
      keyPairId: props.formData.keyPairId || ''
    }
    isApplicantDisabled.value = true
  } else {
    resetForm()
  }
}

watch(() => props.formData, () => {
  initFormData()
  if (props.formData) {
    getAllKeyPairs()
  }
}, { immediate: true })

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}

// 提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    if (props.formData?.id) {
      // 更新 - 包含所有必要字段，即使用户不能编辑某些字段
      const [effectiveTime, expiryTime] = form.value.validTime || []
      await updateSysLicense({
        id: props.formData.id,
        projectName: form.value.projectName,
        productName: form.value.productName,
        applicantName: form.value.applicantName,
        remarks: form.value.remarks,
        keyPairId: parseInt(form.value.keyPairId),
        // 保留原始的时间和内容字段
        issuedTime: effectiveTime ? (dayjs(effectiveTime).format('YYYY-MM-DD HH:mm:ss')) : '',
        expiryTime: expiryTime ? (dayjs(expiryTime).format('YYYY-MM-DD HH:mm:ss')) : '',
        content: typeof form.value.content === 'string'
          ? form.value.content
          : JSON.stringify(form.value.content),
      })
    } else {
      // 创建 - 包含所有字段
      const [effectiveTime, expiryTime] = form.value.validTime || []

      await createSysLicense({
        projectName: form.value.projectName,
        productName: form.value.productName,
        applicantName: form.value.applicantName,
        issuedTime: effectiveTime ? (dayjs(effectiveTime).format('YYYY-MM-DD HH:mm:ss')) : '',
        expiryTime: expiryTime ? (dayjs(expiryTime).format('YYYY-MM-DD HH:mm:ss')) : '',
        content: typeof form.value.content === 'string'
          ? form.value.content
          : JSON.stringify(form.value.content),
        remarks: form.value.remarks,
        keyPairId: parseInt(form.value.keyPairId)
      })
    }

    message.success('保存成功')
    emit('success')
    emit('update:visible', false)
    resetForm()
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败: ' + (error as Error).message)
  }
}

const handleDropdownVisibleChange = (visible: boolean) => {
  if (visible) {
    getAllKeyPairs()
  }
}
</script>

<style scoped>
.json-editor {
  height: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}

.config-display {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  background-color: #f5f5f5;
  font-family: monospace;
  white-space: pre-wrap;
}
</style>
