<template>
  <div class="h-full bg-global-background">
    <BaseTable
      :tableProps="tableProps"
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      :search-schema="searchSchema"
      :total="total"
      :current="page.current"
      :pageSize="page.pageSize"
      @search="handleSearch"
      @reset="handleReset"
      @pageChange="handlePageChange"
    >
      <template #actions>
        <a-button type="primary" @click="exportExcel">导出</a-button>
        <a-button type="primary" @click="handleDeleteBatch">批量删除</a-button>
      </template>

      <template #action="{ record }">
        <a-space>
          <a-tooltip title="查看详情">
            <a-button type="link" @click="handleViewDetail(record)">
              <template #icon><EyeOutlined /></template>
            </a-button>
          </a-tooltip>
          <a-tooltip title="删除">
            <a-button type="link" danger @click="handleDelete(record)">
              <template #icon><DeleteOutlined /></template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
    </BaseTable>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { DeleteOutlined, EyeOutlined } from '@ant-design/icons-vue'
import BaseTable from '@/components/BaseTable/index.vue'
import { getOperationLogs, operationLogsDeleteBatch, operationLogsExport } from '@/apis/operations'
import { columns, searchSchema } from './constant/index'
import { useRouter } from 'vue-router'
const router = useRouter()

// 表格数据
const dataSource = ref<any[]>([])
const loading = ref(false)
const total = ref(0)
const page = reactive({
  current: 1,
  pageSize: 10
})


type Key = string | number;
const state = reactive<{
  selectedRowKeys: Key[];
}>({
  selectedRowKeys: []
});
const onSelectChange = (selectedRowKeys: Key[]) => {
  // console.log('selectedRowKeys changed: ', selectedRowKeys)
  state.selectedRowKeys = selectedRowKeys
}
const tableProps = {
  rowKey: 'id',
  rowSelection: {
    type: 'checkbox',
    onChange: onSelectChange
  }
}
const hasSelected = computed(() => state.selectedRowKeys.length > 0)

// 获取数据
const getList = async (params = {}) => {
  loading.value = true
  try {
    const queryParams = {
      current: params.current || 1,
      size: params.pageSize || 10,
      operationType: params.operationType
    }

    const response = await getOperationLogs(queryParams)
    if(response.code === 200) {
      dataSource.value = response.data.records || []
      total.value = response.data.records.length || []
    }
  } catch (error) {
    console.error('获取枚举数据失败:', error)
    dataSource.value = []
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (params) => {
  getList({
    ...params,
    current: 1
  })
}

// 重置处理
const handleReset = () => {
  getList({ current: 1 })
}

// 分页处理
const handlePageChange = ({ current, pageSize, searchForm }) => {
  page.current = current!
  page.pageSize = pageSize!
  getList({
    current,
    pageSize,
    ...searchForm
  })
}

const handleViewDetail = (record) => {
  router.push(`/operations/logDetail/${record.id}`)
}

// 批量删除日志
const handleDeleteBatch = async () => {
  if (!hasSelected.value) {
    message.warning('请勾选数据')
    return
  }
  const params = {
    ids: state.selectedRowKeys || []
  }
  deleteconfirmFn(params)
}

// 删除处理
const handleDelete = (record) => {
  if (!record?.id) {
    message.error('无效的记录ID')
    return
  }
  const params = {
    ids: [record.id]
  }
  deleteconfirmFn(params)
}

const deleteconfirmFn = (params) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该枚举吗？',
    async onOk() {
      try {
        const extend = {
          headers: {
            'Content-Type': 'application/json'
          },
          data: JSON.stringify(params)
        }
        console.log(extend, 'extend')
        const response = await operationLogsDeleteBatch(extend)
        if (response.code === 200) {
          message.success('删除成功')
          getList()
        } else {
          message.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        message.error('删除失败')
      }
    }
  })
}

// 导出
const exportExcel = async () => {
  try {
    const response = await operationLogsExport()
    if(!response) {
      message.error('导出失败')
      return
    }
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const downloadUrl = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = downloadUrl
    a.download = `操作日志_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(downloadUrl)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

// 初始化
getList()
</script>
