import dayjs from 'dayjs'

// 表格列定义
export const columns = [
  {
    title: '操作类型',
    dataIndex: 'operationType',
    width: 100
  },
  {
    title: '操作时间',
    dataIndex: 'operationTime',
    width: 180,
    customRender: ({ text }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-'
    }
  },
  {
    title: '操作描述',
    dataIndex: 'description',
    width: 400
  },
  {
    title: '操作人用户名',
    dataIndex: 'username',
    width: 400
  },
  {
    title: '操作人真实姓名',
    dataIndex: 'realName',
    width: 200
  },
  {
    title: '部门名称',
    dataIndex: 'deptName',
    width: 400
  },
  {
    title: '操作人IP地址',
    dataIndex: 'ip',
    width: 400
  },
  {
    title: '操作',
    width: 120,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 搜索配置
export const searchSchema = [
  {
    key: 'operationType',
    label: '操作类型',
    type: 'input',
    placeholder: '请选择操作类型'
  }
]
