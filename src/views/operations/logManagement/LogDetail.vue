<template>
  <div class="log-detail-container">
    <a-card :bordered="false">
      <a-row>
        <a-col :span="24">
          <h2>操作日志详情</h2>
        </a-col>
      </a-row>
      <a-divider />

      <!-- 基础信息 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="基本信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="日志ID">
                {{ logDetail.id }}
              </a-descriptions-item>
              <a-descriptions-item label="操作类型">
                {{ logDetail.operationType }}
              </a-descriptions-item>
              <a-descriptions-item label="操作时间">
                {{ logDetail.operationTime }}
              </a-descriptions-item>
              <a-descriptions-item label="执行时长">
                {{ logDetail.executionTime }}毫秒
              </a-descriptions-item>
              <a-descriptions-item label="操作描述">
                {{ logDetail.description }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- 用户信息 -->
        <a-col :span="24">
          <a-card title="用户信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="部门名称">
                {{ logDetail.deptName }}
              </a-descriptions-item>
              <a-descriptions-item label="操作人IP">
                {{ logDetail.ip }}
              </a-descriptions-item>
              <a-descriptions-item label="用户名">
                {{ logDetail.username }}
              </a-descriptions-item>
              <a-descriptions-item label="真实姓名">
                {{ logDetail.realName }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>

      <!-- 请求信息 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="请求信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="请求方式">
                {{ logDetail.requestMethod }}
              </a-descriptions-item>
              <a-descriptions-item label="请求URL">
                {{ logDetail.requestUrl }}
              </a-descriptions-item>
              <a-descriptions-item label="请求参数">
                <template #default>
                  <pre>{{ logDetail.requestParams }}</pre>
                </template>
              </a-descriptions-item>
              <a-descriptions-item label="请求体">
                <template #default>
                  <pre>{{ logDetail.requestBody }}</pre>
                </template>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>

      <!-- 方法信息 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="方法信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="类名">
                {{ logDetail.className }}
              </a-descriptions-item>
              <a-descriptions-item label="方法名">
                {{ logDetail.methodName }}
              </a-descriptions-item>
              <a-descriptions-item label="方法参数">
                {{ logDetail.methodParams }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>

      <!-- 响应信息 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="响应信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="响应状态码">
                {{ logDetail.responseStatus }}
              </a-descriptions-item>
              <a-descriptions-item label="响应头信息">
                {{ logDetail.responseHeaders }}
              </a-descriptions-item>
              <a-descriptions-item label="响应内容">
                <template #default>
                  <pre>{{ logDetail.responseBody }}</pre>
                </template>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>

      <!-- 客户端信息 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="客户端信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="用户代理">
                {{ logDetail.userAgent }}
              </a-descriptions-item>
              <a-descriptions-item label="浏览器名称">
                {{ logDetail.browserName }}
              </a-descriptions-item>
              <a-descriptions-item label="浏览器版本">
                {{ logDetail.browserVersion }}
              </a-descriptions-item>
              <a-descriptions-item label="操作系统">
                {{ logDetail.osName }}
              </a-descriptions-item>
              <a-descriptions-item label="设备类型">
                {{ logDetail.deviceType }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>

      <!-- 业务信息 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-card title="业务信息">
            <a-descriptions :column="1">
              <a-descriptions-item label="业务模块">
                {{ logDetail.businessModule }}
              </a-descriptions-item>
              <a-descriptions-item label="业务类型">
                {{ logDetail.businessType }}
              </a-descriptions-item>
              <a-descriptions-item label="业务ID">
                {{ logDetail.businessId }}
              </a-descriptions-item>
              <a-descriptions-item label="租户ID">
                {{ logDetail.tenantId }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { operationLogsQueryId } from '@/apis/operations'

interface LogDetail {
  id: string
  operationTime: string
  description: string
  operationType: string
  executionTime: number
  username: string
  realName: string
  deptName: string
  ip: string
  requestMethod: string
  requestUrl: string
  requestParams: string
  requestBody: string
  className: string
  methodName: string
  methodParams: string
  responseStatus: number
  responseHeaders: string
  responseBody: string
  userAgent: string
  browserName: string
  browserVersion: string
  osName: string
  deviceType: string
  businessModule: string
  businessType: string
  businessId: string
  tenantId: string
}

const route = useRoute()
const logDetail = ref<LogDetail>({})

const getLogDetail = async () => {
  const id = route.params.id
  if (!id) {
    message.error('无效的日志ID')
    return
  }

  try {
    const response = await operationLogsQueryId(id)
    if (response.code === 200) {
      logDetail.value = response.data
    } else {
      message.error(response.message || '获取日志详情失败')
    }
  } catch (error) {
    console.error('获取日志详情失败:', error)
    message.error('获取日志详情失败')
  }
}

onMounted(async () => {
  getLogDetail()
})
</script>

<style lang="scss" scoped>
.log-detail-container {
  padding: 16px;
  background: #fff;
  border-radius: 4px;

  .ant-card {
    margin-bottom: 16px;
  }

  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    background: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
  }

  .ant-row {
    margin-bottom: 16px;
  }
}
</style>
