<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { ConfigProvider } from 'ant-design-vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import { useRoutesGroup } from '@/hooks/useRoutes'
import { getAgentNavigation } from '@/apis/model'
import Theme from '@/components/Theme/index.vue'

// 初始化路由分组
const routesGroup = useRoutesGroup()
localStorage.setItem('routesGroup', JSON.stringify(routesGroup))

// 在组件挂载时保存当前路径（如果不是白名单页面）
onMounted(() => {
  // 白名单路由
  const whiteList = ['/login', '/auth/secure-signup/yss-verification/new-user', '/workbench/pretreatment', '/designer/canvas']
  
  if (!whiteList.some(path => window.location.pathname.includes(path))) {
    localStorage.setItem('redirectUrl', window.location.pathname + window.location.search)
  }

  // 在组件挂载时调用接口（如果不是白名单页面）
  if (!whiteList.some(path => window.location.pathname.includes(path))) {
    getAgentNavigation()
  }
})

// 创建一个函数来获取 CSS 变量值
const getCssVar = (name: string) =>
  getComputedStyle(document.documentElement)
    .getPropertyValue(name)
    .trim()

const configTheme = computed(() => ({
  token: {
    colorPrimary: getCssVar('--primary-color'),
    colorPrimaryHover: getCssVar('--primary-hover'),
    colorBgContainer: getCssVar('--bg-color'),
    colorText: getCssVar('--text-primary'),
    colorTextSecondary: getCssVar('--text-secondary'),
    colorBorder: getCssVar('--border-color')
  }
}))
</script>

<template>
  <ConfigProvider :theme="configTheme" :locale="zhCN">
    <Theme />
    <Suspense>
      <RouterView />
    </Suspense>
  </ConfigProvider>
</template>

<style>
.route-view-container{
  height: 100%;
}
</style>
