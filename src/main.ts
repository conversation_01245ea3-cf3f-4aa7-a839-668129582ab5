import { createApp } from 'vue'
import App from './App.vue'
// 引入组件库样式
import '@yss-design/ui/dist/es/styles/index.css'
import '@yss/ai-chat-components/style.css'
import './styles/variables.scss'
import './styles/common.scss'  // 引入公共样式
import Antd from 'ant-design-vue'
import router from './router'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import 'splitpanes/dist/splitpanes.css'
import './styles/antvX6.scss'
import './styles/global.scss'
import 'ant-design-vue/dist/reset.css'
import './styles/tailwind.css'
import '@/assets/font/iconfont.css'
import jaison from 'jaison'


const app = createApp(App)
app.use(Antd)
app.use(jaison)

// 创建pinia实例
const pinia = createPinia()
// 使用持久化插件
pinia.use(piniaPluginPersistedstate)

app.use(router).use(pinia).mount('#app')
