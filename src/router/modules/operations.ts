export default {
  path: 'operations',
  meta: {
    title: '运维管理',
    icon: 'yss-setting'
  },
  children: [
    {
      path: 'docker',
      component: () => import('@/views/operations/docker/list.vue'),
      meta: {
        icon: 'yss-xueyuan-mulu',
        title: '资源节点管理'
      }
    },
    {
      path: 'performanceMonitoring',
      component: () => import('@/views/operations/performanceMonitoring/index.vue'),
      meta: {
        icon: 'yss-xueyuan-mulu',
        title: '性能监控'
      }
    },
    {
      path: 'logManagement',
      component: () => import('@/views/operations/logManagement/index.vue'),
      meta: {
        icon: 'yss-xueyuan-mulu',
        title: '日志中心'
      }
    },
    {
      path: 'logDetail/:id',
      name: 'LogDetail',
      component: () => import('@/views/operations/logManagement/LogDetail.vue'),
      meta: {
        title: '日志详情',
        icon: 'yss-xueyuan-mulu',
        hide: true
      }
    },    {
      path: 'licenseManagement',
      component: () => import('@/views/operations/licenseManagement/index.vue'),
      meta: {
        icon: 'yss-xueyuan-mulu',
        title: '许可证管理'
      }
    },
    {
      path: 'keyPairManagement',
      component: () => import('@/views/operations/keyPairManagement/index.vue'),
      meta: {
        icon: 'yss-xueyuan-mulu',
        title: '密钥对管理'
      }
    },
    {
      path: 'testSSE',
      component: () => import('@/views/operations/testSSE/index.vue'),
      meta: {
        icon: 'yss-xueyuan-mulu',
        title: '接口测试'
      }
    },
    {
      path: 'enum',
      component: () => import('@/views/designer/emun/index.vue'),
      meta: {
        icon: 'yss-xueyuan-mulu',
        title: '系统字典',
      },
    }

  ]
}
