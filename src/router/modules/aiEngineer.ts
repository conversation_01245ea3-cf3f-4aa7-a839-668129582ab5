import { isElectron } from '@/utils/utils'

export default {
  path: 'designer',
  meta: {
    title: 'AI工程师',
    icon: 'yss-zhishitupu',
  },
  children: [
    {
      path: 'modelSquare',
      component: () => import('@/views/designer/modelSquare/index.vue'),
      meta: {
        icon: 'yss-zhishitupu',
        title: '模型工作室',
      },
    },
    {
      path: '/designer/modelChat',
      component: () => import('@/views/designer/modelChat/index.vue'),
      meta: {
        icon: 'yss-zhishitupu',
        title: 'Prompt工作室',
        hide: false,
      },
    },
    {
      path: '/designer/modelChat/:id',
      name: 'prompt-detail',
      meta: { 
        title: '提示词详情',
        hide: true 
      },
      component: () => import('@/views/designer/modelChat/components/Prompt.vue')
    },
    {
      path: '/designer/canvas',
      component: () => import('@/views/designer/canvas/index.vue'),
      meta: {
        icon: 'yss-xiugai07',
        title: 'Agent设计器',
      },
    },
    {
      path: 'nodes',
      component: () => import('@/views/designer/nodes/index.vue'),
      meta: {
        icon: 'yss-lifangtilitiduomiantifangkuai2',
        title: 'Agent能力库',
      },
    },
    {
      path: 'files',
      component: () => import('@/views/designer/agentManagement/index.vue'),
      meta: {
        icon: 'yss-agent',
        title: 'Agent元团队',
      },
    },
    {
      path: 'agentDatabase',
      component: () => import('@/views/designer/agentDatabase/index.vue'),
      meta: {
        icon: 'yss-zhishitupu',
        title: 'Conn数据-DataGrid',
      },
    },
    {
      path: 'apiBank',
      component: () => import('@/views/unauthorized/index.vue'),
      meta: {
        icon: 'yss-zhishitupu',
        title: 'Conn能力-APIBank',
      },
    },
    {
      path: 'semantic',
      component: () => import('@/views/unauthorized/index.vue'),
      meta: {
        icon: 'yss-zhishitupu',
        title: 'Conn知识-Semantic',
      },
    },
    {
      path: 'timerTrigger',
      component: () => import('@/views/designer/timerTrigger/index.vue'),
      meta: {
        icon: 'yss-zhishitupu',
        title: '定时任务管理',
      },
    },
    {
      path: 'crawlerManagement',
      component: () => import('@/views/designer/crawlerManagement/index.vue'),
      meta: {
        icon: 'yss-zhishitupu',
        title: '爬虫管理',
      },
    },
    {
      path: 'modelSetting',
      component: () => import('@/views/designer/modelSquare/setting.vue'),
      meta: {
        icon: 'yss-zhishitupu',
        title: '模型设置',
        hide: true,
      },
    },
    {
      path: 'files',
      component: () => import('@/views/designer/files/index.vue'),
      meta: {
        icon: 'yss-agent',
        title: 'Agent端点',
        hide: true,
      },
    },
    {
      path: 'agentSetting',
      component: () => import('@/views/designer/agentSquare/setting.vue'),
      meta: {
        icon: 'yss-zhishitupu',
        title: 'Agent设置',
        hide: true,
      },
    },

    ...(isElectron() ? [
      {
        path: 'feCodeGeneration',
        component: () => import('@/views/designer/codeGeneration/index.vue'),
        meta: {
          icon: 'yss-zhishitupu',
          title: '前端生成'
        }
      }
    ] : [])
    // {
    //   path: 'clearingCodeGeneration',
    //   component: () => import('@/views/unauthorized/index.vue'),
    //   meta: {
    //     icon: 'yss-zhishitupu',
    //     title: '清算生成',
    //   },
    // },
    // {
    //   path: 'indicatorCodeGeneration',
    //   component: () => import('@/views/unauthorized/index.vue'),
    //   meta: {
    //     icon: 'yss-zhishitupu',
    //     title: '指标生成',
    //   },
    // },
  ]
}

