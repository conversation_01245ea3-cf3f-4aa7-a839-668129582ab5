// 定义菜单项数组，确保顺序
const menuItems = [
  {
    path: '',
    component: () => import('@/views/aiProducts/index.vue'),
    meta: {
      icon: 'yss-workbench',
      title: '工作台',
      sortNo: 1,
    },
  },
  {
    path: 'prototypes',
    component: () => import('@/views/aiProducts/pages/PrototypeList.vue'),
    meta: {
      icon: 'yss-prototype',
      title: '原型设计',
      sortNo: 2,
    },
  },
  {
    path: 'documents',
    component: () => import('@/views/aiProducts/pages/DocumentList.vue'),
    meta: {
      icon: 'yss-document',
      title: '文档中心',
      sortNo: 3,
    },
  },
]

export default {
  path: 'aiProducts',
  meta: {
    title: '产品工作台',
    icon: 'yss-product',
  },
  children: [
    ...menuItems,
    // 隐藏的子路由
    {
      path: 'prototypes/create',
      name: 'PrototypeCreate',
      meta: {
        title: '创建原型',
        hide: true
      },
      component: () => import('@/views/aiProducts/pages/PrototypeCreate.vue')
    },
    {
      path: 'prototypes/:id',
      name: 'PrototypeDetail',
      meta: {
        title: '原型详情',
        hide: true
      },
      component: () => import('@/views/aiProducts/pages/PrototypeDetail.vue')
    },
    {
      path: 'documents/create',
      name: 'DocumentCreate',
      meta: { 
        title: '创建文档',
        hide: true 
      },
      component: () => import('@/views/aiProducts/pages/DocumentCreate.vue')
    },
    {
      path: 'documents/:id',
      name: 'DocumentDetail',
      meta: { 
        title: '文档详情',
        hide: true 
      },
      component: () => import('@/views/aiProducts/pages/DocumentDetail.vue')
    }
  ]
}
