export default {
  path: 'system',
  meta: {
    title: '系统管理',
    icon: 'yss-setting',
  },
  children: [
    {
      path: 'user',
      component: () => import('@/views/system/user/index.vue'),
      meta: {
        icon: 'yss-user',
        title: '用户管理',
        keepAlive: true,
      },
    },
    {
      path: 'role',
      component: () => import('@/views/system/role/index.vue'),
      meta: {
        icon: 'yss-role',
        title: '角色管理',
        keepAlive: true,
      },
    },
    {
      path: 'department',
      component: () => import('@/views/system/department/index.vue'),
      meta: {
        icon: 'yss-department',
        title: '部门管理',
        keepAlive: true,
      },
    },
    {
      path: 'menu',
      component: () => import('@/views/system/menu/index.vue'),
      meta: {
        icon: 'yss-menu',
        title: '菜单管理',
        keepAlive: true,
      },
    },
  ],
}
