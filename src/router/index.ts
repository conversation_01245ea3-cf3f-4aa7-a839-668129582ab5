import { createRouter, createWebHashHistory } from 'vue-router'
import systemRoutes from './modules/system'
import operationsRoutes from './modules/operations'
import aiEngineerRoutes from './modules/aiEngineer'
import aiProductsRoutes from './modules/aiProducts'
import { useFirstLevelPath } from '@/hooks/useRoutes'
import { useAppStore } from '@/store/app'

// 添加模块路由
const baseRoutes = [
  {
    path: '/login',
    meta: { title: '登录页' },
    component: () => import('../views/login/index.vue'),
  },
  {
    path: '/auth/secure-signup/yss-verification/new-user',
    meta: { title: '用户注册' },
    component: () => import('../views/register/index.vue'),
  },
  {
    path: '/platform',
    meta: { title: 'AI产品预览' },
    component: () => import('@/views/iframe/index.vue'),
  },
  {
    path: '/',
    component: () => import('@/layouts/BaseLayout.vue'),
    children: [
      {
        path: '',
        meta: { title: '首页' },
        component: () => import('../views/home/<USER>'),
      },
      // 添加AI工程师路由模块
      aiEngineerRoutes,
      // 添加产品工作台路由模块
      aiProductsRoutes,
      // 添加系统路由模块
      systemRoutes,
      // 运维管理
      operationsRoutes,
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/',
  },
]

const router = createRouter({
  history: createWebHashHistory('/ai-agent/'),
  routes: baseRoutes,
})

// 添加全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置路由前缀标识
  const firstLevelPath = useFirstLevelPath(to)
  localStorage.setItem('currentRouteGroup', firstLevelPath)

  const appStore = useAppStore()
  const mode = to.query.mode as string
  if (mode === 'readonly' || mode === 'edit') {
    appStore.setMode(mode)
  } else {
    appStore.setMode('edit') // 默认为编辑模式
  }

  // 获取token
  const token = localStorage.getItem('token')
  
  // 白名单路由
  const whiteList = ['/login', '/auth/secure-signup/yss-verification/new-user', '/workbench/pretreatment', '/designer/canvas']
  
  
  // 判断条件:
  // 1. 如果是白名单路由，直接放行
  // 2. 如果是 readonly 模式，直接放行
  // 3. 如果不是白名单且不是 readonly 模式，则检查 token
  if (whiteList.some(path => to.path.includes(path)) || to.query.mode === 'readonly') {
    next()
  } else if (!token) {
    // 将原始访问地址存储到localStorage
    localStorage.setItem('redirectUrl', to.fullPath)
    // 直接跳转到登录页
    next('/login')
  } else {
    next() // 正常放行
  }
})

export default router
