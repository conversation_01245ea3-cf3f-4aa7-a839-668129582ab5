/// <reference types="vite/client" />

interface SiteConfig {
  DESIGNER_BASE_URL: string;
  RUNTIME_URL: string;
  BASE_URL?: string;
  STT_URL?: string;
  httpBaseUrl?: string;
  sseBaseUrl?: string;
  oneApiUrl?: string;
  timeout?: number;
}

declare global {
  interface Window {
    SITE_CONFIG: SiteConfig;
  }
  interface ImportMeta {
    env: {
      VITE_DIRECT_MODE?: string;
      [key: string]: string | undefined;
    };
  }
}

// 初始化站点配置
const SITE_CONFIG: SiteConfig = {
  DESIGNER_BASE_URL: `${window.location.origin}:9120/designerApi`,
  RUNTIME_URL: `${window.location.origin}`,
  timeout: 600000
};

// 测试环境配置
SITE_CONFIG.DESIGNER_BASE_URL = '';
// SITE_CONFIG.BASE_URL = 'https://ai.heartie.cn:9087'; // 演示环境
// SITE_CONFIG.BASE_URL = 'http://192.168.167.33:9083'; // 集成环境
// SITE_CONFIG.BASE_URL = 'http://192.168.96.40:9083'; // 宋鑫
// SITE_CONFIG.BASE_URL = 'http://192.168.96.41:9083'; // 贾京都
// SITE_CONFIG.BASE_URL = 'http://192.168.96.43:9083'; // 高明源

window.SITE_CONFIG = SITE_CONFIG;

// 检查是否为直连模式（通过环境变量控制）
export function isDirectMode(): boolean {
  return import.meta.env?.VITE_DIRECT_MODE === 'true';
}

// 直连模式配置
const DIRECT_CONFIG: Partial<SiteConfig> = {
  // 请求超时时间（毫秒）
  timeout: 600000
};

// 获取当前配置（优先使用直连配置，否则使用默认配置）
export function getApiConfig(): Partial<SiteConfig> {
  if (isDirectMode()) {
    return DIRECT_CONFIG;
  }

  // 默认使用 window.SITE_CONFIG 配置
  return {
    timeout: 600000
  };
} 