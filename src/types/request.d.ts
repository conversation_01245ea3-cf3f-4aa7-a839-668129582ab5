declare interface IRes {
  code: number
  msg?: string
}
declare interface IResType<T> extends IRes {
  data?: T
  error?: string
  statusText: string
}
declare interface IRequest {
  get<T>(url: string, params?: unknown, isFlow?: boolean): Promise<IResType<T>>
  delete<T>(url: string, params?: unknown, extend?: unknown): Promise<IResType<T>>
  post<T>(
    url: string,
    params?: unknown,
    uploadProgress?: ((progressEvent: AxiosProgressEvent) => void) | undefined,
    isFlow?: boolean,
  ): Promise<IResType<T>>
  postBinary<T>(url: string, params?: unknown): Promise<IResType<T>>
  put<T>(url: string, params?: unknown): Promise<IResType<T>>
  patch<T>(url: string, params?: unknown): Promise<IResType<T>>
}
