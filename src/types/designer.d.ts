import { Cell } from "@antv/x6"

interface EditorTabData {
  id: number
  name: string
  path: string
  content: Content
  breadCrumbPath:string
}

interface FileFlow {
  file: File
  fileType: string
  resolveType: string
  isCreate: number // 0导入 1 创建
  collectionId?: number // 导入现有传这个
  collectionType: string // 数据集类型
  datasetId?: string // 新建传这个
  parentCollectionId?: number // 新建传这个
  textSeparator: string // 分隔符
  startRow: number // 起始行
  encoding: 'utf-8' | 'gbk' // 编码
  textIdentifier: string // 文本标识符 || 字段包围符
  qIndex: string
  aIndex: string
}
interface DesignerTabContent {
  cells: Array
  mode: String
}
interface TabData extends EditorTabData{
  id:string
  directoryId:string
  breadCrumbPath?: string
  content: DesignerTabContent
}


interface IModDesignerState {
  dndInstance:Dnd
  modGraph: Graph
  fileFlow: FileFlow
  connectedNodePortId: Array<string>
  allPortsId: Array<string>
  allNodesId: Array<string>
  rightPanelVisiable: boolean
  currentNodeData: Group
  currentNode: Cell
  currentFile: TabData
  tabData:  Map<string,TabData>
  currentSelectNode: []
  mode:String
  isSafari: boolean
  nodePorts: Map<string,number>
}

interface NodeParams {
  id?:string
  field?:string
  srcField?: string
  destField?: string
  type: string
  chineseName: string
  required: boolean
  isArray: boolean
  defaultValue: string
  fieldValue: string
  desc: string
  precision: string
  minLength: string
  maxLength: string
  isEdit: boolean
  transformer?: string
}

interface Options{
  name: string
  desc?: string
  width?: number
  height?: number
  fileType?: string
  inputParams?: NodeParams[]
  outputParams?: NodeParams[]
  inputMapping?: NodeParams[]
  outputMapping?: NodeParams[]
  nodeList?:string
}
interface FlowiseNodeData {
  label: string
  description: string
}
interface Group {
  id: string
  type: string
  title: string
  icon?: string
  desc: string
  compType?: string
  compId?: string
  options: Options
  disabled?: boolean
  resizing?: boolean
  hasSubNode?: boolean
  appendNode?: boolean
  expand?: boolean
  inputs?: Array
  outputs?: Array
  properties?: Object
  only?: boolean
  group?: Array<Group>
}
interface PaletteData {
  id: string
  type: string
  title: string
  group: Array<Group>
}

interface DirectoryData {
  children: DirectoryData[]
  id: string
  label: string
  level: number
  dir: boolean
  path: string
}

interface FlowiseInputParam {
  description:string
  id:string
  label:string
  name:string
  placeholder:string
  type:string
  additionalParams: boolean
  default?: string
  acceptVariable?: boolean
}

interface UrlParams {
  myDialogueId: string
}

interface Output {
  paramsName:string
  variableType: string
  value: string
}

interface NodeParamsData {
  paramsName: string,
  type?: string
  variableType?: string,
  value: string,
  quoteVariable?: string,
  quoteNodeId?: string
}

interface QuoteParams {
  label: string,
  value: string,
  id: string
}