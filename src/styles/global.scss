body {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Arial', sans-serif;
  margin: 0;
  background-color: #f4f6f8;
  p {
    margin: 0;
  }
  div {
    box-sizing: border-box;
  }
  .yss_scroll-bar {
    * {
      &:hover::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        transition: all 0.3s;
      }

      &:not(:hover)::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        transition: all 0.3s;
      }
    }

    &-mini {
      * {
        &:hover::-webkit-scrollbar {
          width: 6px;
          height: 6px;
          transition: all 0.3s;
        }

        &:not(:hover)::-webkit-scrollbar {
          width: 6px;
          height: 6px;
          transition: all 0.3s;
        }
      }
    }
  }

  /** 滚动条轨道 */
  ::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 8px;
    &:hover {
      background-color: #eceff7;
      transition: all 0.3s;
    }
  }

  /** 滚动条上的滚动滑块 */
  ::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border-style: dashed;
    border-color: transparent;
    border-width: 1px;
    background-clip: content-box;
    background-color: rgba(98, 103, 114, 0.4);
    transition: all 0.3s;
    /** 移入到滑块 滑块颜色 */
    &:hover {
      width: 14px;
      height: 14px;
      border-radius: 8px;
      border-style: dashed;
      border-color: transparent;
      border-width: 1px;
      background-clip: padding-box;
      transition: all 0.3s;
      background-color: #626772;
    }
  }
  * {
    &:hover::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      transition: all 0.3s;
    }

    &:not(:hover)::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      transition: all 0.3s;
    }
  }

  /**iframe**/
  iframe {
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    border: none;
  }

  /**媒体查询适配**/
  @media screen and (max-width: 750px) {
    .ada-m-hide {
      @apply hidden;
    }
  }
  @media screen and (min-width: 750px) {
    .ada-pc-hide {
      @apply hidden;
    }
  }

  /****/
  /**dark 模式**/
  .van-icon {
    @apply dark:text-white;
  }

  /**兼容 iphone**/
  & {
    -webkit-overflow-scrolling: touch;
  }
  
  .fixfixed {
    .chat-header-container {
      position: absolute !important;
    }
    .send-message-container {
      position: absolute !important;
    }
  }

  .yss_insert_style {
    position: absolute;
    left: 0;
    top: 0;
    height: 0;
    z-index: 999999;
  }
}

.dark-dialog {
  border-radius: 10px !important;
  background: #000 !important;
  box-shadow:
    0 0 2px #414141,
    0 0 2px #414141 inset !important;
  color: #fff;
  .el-dialog__title,
  .el-dialog__close,
  .el-form-item__label {
    color: #fff;
  }
  .el-input__wrapper,
  .el-textarea__inner,
  .el-select__wrapper {
    background: #000;
    width: 100%;
    font-size: 14px;
    box-shadow:
      0 0 2px #414141,
      0 0 2px #414141 inset;
  }
}

html {
  font-size: 16px;
}

/* 响应式布局CSS变量 */
:root {
  /* 普通模式列宽 */
  --param-name-width-normal: 220px;
  --param-type-width-normal: 120px;
  --param-value-width-normal: 260px;
  --param-default-width-normal: 140px;
  --param-desc-width-normal: 50px;
  --param-length-width-normal: 100px;

  /* 全屏模式列宽 */
  --param-name-width-fullscreen: 300px;
  --param-type-width-fullscreen: 150px;
  --param-value-width-fullscreen: 400px;
  --param-default-width-fullscreen: 300px;
  --param-desc-width-fullscreen: 120px;
  --param-length-width-fullscreen: 120px;

  /* 间距 */
  --param-gap-normal: 8px;
  --param-gap-fullscreen: 16px;

  /* 行高 */
  --param-row-height-normal: auto;
  --param-row-height-fullscreen: 48px;
}

@import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css';
@import '../views/designer/canvas/components/configRender/responsive-layout.scss';
