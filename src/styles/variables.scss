// 定义 CSS 变量
:root {
  --primary-color: #3182ce;
  --primary-hover: #2b6cb0;
  --primary-light: rgba(49, 130, 206, 0.8);
  --bg-color: #f8fafc;
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --text-white: #ffffff;
  --border-color: #e2e8f0;
  --text-label: rgba(55, 67, 106, 0.5);
  --warn-text: rgb(255, 129, 26);
  --warn-bg: rgba(255, 188, 133, 0.2);
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --danger-color: #e53e3e;
  --grey-auxiliary-color: #a0aec0; /* 辅助色 */
  --black-auxiliary-color: #2d3748; /* 辅助色 */
  --global-background-color: #f7fafc; /* 全局背景 */
  --left-nav-bar-border-color: #e4e4e4; /*左侧导航栏 边框*/
  --left-nav-bar-fill-color: #ffffff; /*左侧导航栏 填充*/
  --top-menu-border-color: #e4e4e4; /*顶部菜单栏 边框*/
  --top-menu-fill-color: #ffffff; /*顶部菜单栏 填充*/
  --area-fill-color: #ffffff; /*区域 填充*/
  --input-border-color: #e2e8f0; /*输入控件 边框*/
  --input-fill-color: #ffffff; /*输入控件 填充*/
  --text-color: #2d3748; /*一般文本*/
  --guide-text-color: #a0aec0; /* 指引文本 */
  --bg-header: var(--primary-color); /*区域 填充*/
  --table-hover-color: #f5faff; /*表格 悬浮色*/
  --bg-footer: #f4f6f8; /*底部 填充*/
  --canvas-fill-color: #f2f2f2; /*画布 填充*/
  --area-hover-color: #f7fafc; /*区域 悬浮色*/
  --canvas-tab-color: #f5f5f5; /* 画布标签栏 填充*/
  --splitpanes-splitter-color: #eee; /* 分割线 填充*/
  --splitpanes-splitter-btn: #e5e7eb; /* 分割线 按钮*/
  --login-btn-color: #2d3748; /*登录按钮 填充 仅适用于登录组件*/
  --canvas-border-select: #1890ff;
}

:root.dark {
  --primary-color: #ffffff;
  --primary-hover: #313131;
  --primary-light: #313131;
  --bg-color: #f8fafc;
  --text-primary: #ffffff;
  --text-secondary: #ffffffd9;
  --text-white: #ffffff;
  --border-color: #303030;
  --text-label: rgba(55, 67, 106, 0.5);
  --warn-text: rgb(255, 129, 26);
  --warn-bg: rgba(255, 188, 133, 0.2);
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --danger-color: #e53e3e;
  --grey-auxiliary-color: #ffffffd9;/* 辅助色 */
  --black-auxiliary-color: #141414; /* 辅助色 */
  --global-background-color: black; /* 全局背景 */
  --left-nav-bar-fill-color: var(--global-background-color); /*左侧导航栏 填充*/
  --top-menu-border-color: #303030; /*顶部菜单栏 填充*/
  --top-menu-fill-color: var(--global-background-color); /*顶部菜单栏 填充*/
  --area-fill-color: var(--global-background-color); /*区域 填充*/
  --input-border-color: #303030; /*输入控件 边框*/
  --input-fill-color: var(--global-background-color); /*输入控件 填充*/
  --text-color: #ffffff; /*一般文本*/
  --guide-text-color: #ffffffd9; /* 指引文本 */
  --bg-header: var(--global-background-color); /*区域 填充*/
  --table-hover-color: #313131; /*表格 悬浮色*/
  --bg-footer: var(--global-background-color); /*底部 填充*/
  --canvas-fill-color: var(--global-background-color); /*画布 填充*/
  --area-hover-color: #1b254b; /*区域 悬浮色*/
  --canvas-tab-color: var(--global-background-color); /* 画布标签栏 填充*/
  --splitpanes-splitter-color: #313131; /* 分割线 填充*/
  --splitpanes-splitter-btn: #ffffff; /* 分割线 按钮*/
  --mx-menu-backgroud: var(--global-background-color); /* 树下拉框 背景 */
  --mx-menu-text: #ffffff; /* 树下拉框 文本 */
  --mx-menu-hover-backgroud:var(--primary-hover);
  --mx-menu-hover-text: var(--primary-color);
  --btn-color: var(--global-background-color);
  --login-btn-color: var(--global-background-color); /*登录按钮 填充 仅适用于登录组件*/
  --select-color:var(--primary-hover);
  --canvas-border-select: var(--border-color);
  --jse-background-color: black;
  --jse-panel-background: black;
}

// SCSS 变量引用 CSS 变量
$primary-color: var(--primary-color);
$primary-hover: var(--primary-hover);
$primary-light: var(--primary-light);
$bg-color: var(--bg-color);
$text-primary: var(--text-primary);
$text-secondary: var(--text-secondary);
$text-white: var(--text-white);
$border-color: var(--border-color);
$text-label: var(--text-label);
$warn-text: var(--warn-text);
$warn-bg: var(--warn-bg);
