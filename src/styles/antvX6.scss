.x6-node-selected {
  .comp-node-container {
    border: 1px solid #4e40e5 !important;
  }
}

.x6-edge:hover path:nth-child(2) {
  stroke: #1890ff;
  stroke-width: 1px;
}

.x6-edge-selected path:nth-child(2) {
  stroke: #1890ff;
  stroke-width: 1.5px !important;
}

.comp-node-container {
  width: 200px;
  border: 1px solid #ccc;
  background-color: #fff;
  position: fixed;
  .comp-node-title {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    padding: 0 6px;
    border-bottom: 1px solid #ccc;
    background-color: #f8f8f8;
    .node-title-icon {
      cursor: pointer;
    }
    .tool-bar-group {
      width: 100%;
      height: 32px;
      position: absolute;
      left: 0;
      top: -32px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .tool-btn {
        padding: 5px;
        margin-right: 2px;
        cursor: pointer;
        font-size: 18px;
      }
    }
    .node-title-body {
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .node-title-text {
      display: inline-block;
      font-weight: bold;
      font-size: 16px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .node-content-wrap {
    .node-content-title {
      height: 30px;
      padding: 0 10px;
      border-bottom: 1px solid #ccc;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-weight: bold;
    }
    .input-data-wrap {
      width: 100%;
      height: 30px;
      background-color: #eee;
      margin-top: 20px;
      line-height: 30px;
      text-indent: 10px;
    }
    .form {
      padding: 10px;
    }
    .output-data-wrap {
      width: 100%;
      height: 30px;
      background-color: #eee;
      margin-bottom: 20px;
      line-height: 30px;
      text-align: right;
      padding-right: 10px;
    }
    .node-content-list {
      width: 100%;
      margin: 0;
      padding: 0;
      li {
        margin: 0;
        padding: 0;
        list-style: none;
      }
      .node-content-item {
        min-height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px dashed #ccc;
        padding: 10px;
        &.node-input-item {
          border-left: 4px solid #08979c;
        }
        &.node-output-item {
          border-right: 4px solid #0958d9;
        }
        &.node-form-item {
          height: 72px;
        }
        .node-content-datatype {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
    .node-content-btn-group {
      padding: 5px 10px;
      display: flex;
      justify-content: flex-end;
      height: 30px;
      align-items: center;
      .btn-icon {
        cursor: pointer;
      }
    }
  }
}
