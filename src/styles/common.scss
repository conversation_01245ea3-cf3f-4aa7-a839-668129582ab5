/* 分页器主题样式 */
.ant-pagination-item,
.ant-pagination-prev button,
.ant-pagination-next button {
  @apply dark:border-input-border dark:!text-white dark:!bg-area-fill;
}

.ant-pagination-item-active {
  @apply dark:!text-white dark:!border-border dark:!bg-[var(--select-color)];
}

.ant-select-selection-item {
  @apply dark:!text-white;
}

.ant-pagination-item:hover,
.ant-pagination-prev button:hover,
.ant-pagination-next button:hover {
  @apply dark:!bg-[var(--black-auxiliary-color)];
}

.ant-pagination-options-quick-jumper {
  @apply dark:!text-white;
}

.ant-pagination-options-quick-jumper input {
  @apply dark:!border-input-border dark:!text-white;
}

.ant-select-arrow {
  @apply dark:!text-white;
}

.ant-pagination-total-text {
  @apply dark:!text-white;
}

.ant-pagination-item a {
  @apply dark:!text-white;
}

.ant-pagination-item-active a {
  @apply dark:!text-primary;
}

.ant-input::placeholder {
  @apply dark:!text-[var(--grey-auxiliary-color)];
}

/* 全局表单 label 颜色 */
.ant-form-item {
  .ant-col {
    @apply dark:!text-white;
  }
}

.ant-form-item-label > label {
  @apply dark:!text-white;
}

.ant-select-selection-placeholder {
  @apply dark:!text-[var(--grey-auxiliary-color)];
}

/* 全局 tabs 样式 */
.ant-tabs-tab {
  @apply dark:!bg-input-fill dark:!border dark:!border-border dark:!text-white;
  &-active {
    @apply dark:!bg-input-fill dark:!border dark:!border-border;
  }
}

.ant-tabs-tab:hover {
  @apply dark:!text-primary;
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
  @apply dark:!text-primary;
}

.ant-tabs-nav {
  @apply dark:!border-input-border dark:!bg-global-background;
}

.ant-tabs-ink-bar{
  @apply dark:!bg-primary;
}

/* 全局 input 样式 */
.ant-input-group-wrapper {
  .ant-input-group-addon {
    @apply dark:!border-input-border dark:!text-white;
  }
  @apply dark:!border-input-border dark:!text-white;
}

.ant-input-affix-wrapper {
  @apply dark:!bg-input-fill dark:!border-input-border;
  &:hover {
    @apply dark:!border-border;
  }
}

.ant-input-suffix {
  @apply dark:!text-white;
}

.ant-input,
.ant-input-password {
  @apply dark:!text-white dark:!bg-input-fill dark:!border-input-border;
}

.ant-input:hover,
.ant-input-password:hover {
  @apply dark:!border-border;
}

.ant-input:focus {
  @apply dark:!border-border dark:!shadow-none;
}

.ant-input-affix-wrapper-focused {
  @apply dark:!border-border dark:!shadow-none;
}

.ant-input::placeholder {
  @apply dark:!text-[var(--guide-text-color)];
}

.ant-input-password-icon {
  @apply dark:!text-white;
}

.ant-input-prefix {
  @apply dark:!text-white;
}

.ant-input-search-button {
  @apply dark:!bg-input-fill dark:!text-white dark:!border dark:!border-border;
}

.ant-input-clear-icon {
  @apply dark:!text-white;
}

/* 未选中的 radio 按钮样式 */
.ant-radio-button-wrapper {
  @apply dark:!bg-primary-light dark:!text-[#ffffff80] dark:!border dark:!border-border;
}

.ant-radio-button-wrapper::before {
  @apply dark:!bg-transparent;
}

.ant-radio-button-wrapper-checked {
  @apply dark:!bg-primary dark:!text-white;
}

/* dark模式下 ant-dropdown-trigger 下拉菜单样式 */
.ant-dropdown .ant-dropdown-menu {
  @apply dark:!bg-area-fill dark:!text-white dark:!border dark:!border-border;
}

.ant-dropdown .ant-dropdown-menu-item {
  @apply dark:!bg-area-fill dark:!text-white;
}

.ant-dropdown .ant-dropdown-menu-item:hover {
  @apply dark:!bg-primary-hover dark:!text-primary;
}

/* dark模式下 ant-select 下拉框样式 */
.ant-select-dropdown {
  @apply dark:!bg-area-fill dark:!text-white dark:!border dark:!border-border;
}

.ant-select-item-option-selected {
  @apply dark:!bg-[var(--select-color)] dark:!text-white;
}

.ant-select-item-option-active:not(.ant-select-item-option-selected) {
  @apply dark:!bg-area-fill dark:!text-white;
}

.ant-select-item-option {
  @apply dark:!text-white;
}

.ant-select-item-option:hover {
  @apply dark:!bg-primary-light dark:!text-primary;
}

.ant-select-selector {
  @apply dark:!bg-input-fill dark:!text-white dark:!border-input-border dark:!shadow-none;
}

.ant-select-selector:hover {
  @apply dark:!border-border dark:!shadow-none;
}

.ant-select-clear {
  @apply dark:!text-white dark:!bg-input-fill;
}

.ant-empty-description {
  @apply dark:!text-[var(--guide-text-color)];
}

/* 表格主题色适配 */
.ant-table,
.ant-table-container,
.ant-table-content,
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  @apply dark:!bg-area-fill dark:!text-white;
}

.ant-table-thead > tr > th.ant-table-cell:not([colspan])::before {
  width: 0 !important;
  display: none !important;
  background: none !important;
}

.ant-table-tbody > tr:not(.ant-table-placeholder):hover > td {
  background: var(--table-hover-color) !important;
  transition: background 0.2s;
}

.ant-table-placeholder:hover > td {
  @apply dark:!bg-input-fill;
}

.ant-modal-content {
  @apply dark:!bg-area-fill dark:!text-white;
  .ant-modal-title {
    @apply dark:!bg-area-fill dark:!text-white;
  }
  .ant-modal-close {
    @apply dark:!text-white;
  }
}

.ant-table-row-expand-icon {
  @apply dark:!text-white dark:!bg-area-fill dark:!border-input-border dark:hover:!text-primary;
}

.ant-radio-wrapper-in-form-item {
  @apply dark:!text-white;
}

.ant-input-number {
  @apply dark:bg-input-fill dark:!text-white;
}

.ant-input-number-input {
  @apply dark:!text-white;
}

.ant-tree-list {
  @apply dark:!bg-area-fill dark:!text-white;
}

.ant-tree {
  @apply bg-area-fill
}

.ant-tree-node-content-wrapper:hover {
  @apply dark:!bg-primary-hover
}

.ant-btn {
  @apply dark:!text-white dark:bg-[var(--btn-color)] dark:!border dark:!border-solid dark:!border-border;
  &:hover{
    @apply dark:!bg-primary-hover dark:!border-none;
  }
}

/* 全局抽屉样式 */
.ant-drawer-title {
  @apply dark:!text-white;
}

.ant-drawer-content {
  @apply dark:!bg-input-fill;
  .ant-drawer-close {
    @apply dark:!text-white;
  }
}

/* 全局菜单样式 */
.ant-menu {
  @apply dark:!bg-area-fill dark:!text-white;
  .ant-menu-item:hover {
    @apply dark:!text-white;
  }
  .ant-menu-submenu{
    .ant-menu-submenu-title{
      @apply dark:!text-white;
    }
  }
  .ant-menu-submenu-selected{
    .ant-menu-submenu-title{
      @apply dark:!text-primary;
    }
    @apply dark:!bg-input-fill;
  }
}

.ant-menu-item-selected{
  @apply dark:!bg-[var(--select-color)] dark:!text-white;
  &:hover{
    @apply dark:!bg-primary-hover;
  }
  &::after{
    @apply dark:!border-white;
  }
}

.ant-menu-item:hover{
  @apply dark:!bg-primary-hover;
  &::after{
    @apply dark:!border-white;
  }
}

.ant-radio-button-checked{
  @apply dark:!bg-[var(--top-menu-fill-color)];
}

/* 全局统计样式 */
.ant-statistic {
  .ant-statistic-title {
    @apply dark:!text-[var(--guide-text-color)];
  }
  .ant-statistic-content {
    @apply dark:!text-white;
  }
}

/* 全局卡片样式 */
.ant-card {
  @apply dark:!bg-area-fill dark:!text-white;
  .ant-card-head{
    @apply dark:!text-white;
  }
}

/* 全局折叠面板样式 */
.ant-collapse {
  .ant-collapse-header {
    @apply dark:!bg-area-fill dark:!text-white;
  }
  .ant-collapse-content {
    @apply dark:!bg-area-fill;
  }
}

/* 全局时间轴样式 */
.ant-timeline {
  @apply dark:!text-white;
  .ant-timeline-item-tail {
    @apply dark:!border-[var(--grey-auxiliary-color)];
  }  
}

.ant-timeline-item-head-blue{
  @apply dark:!border-primary;
}

/* 全局模态框样式 */
.ant-modal {
  @apply dark:!border-solid dark:!border-border;
  .ant-modal-confirm-title {
    @apply dark:!text-white;
  }
  .ant-modal-confirm-content {
    @apply dark:!text-white;
  }
}

/* 全局折叠面板样式 */
.ant-collapse-item .ant-collapse-header {
  @apply dark:!rounded-[8px];
}

/* 全局气泡框样式 */
.ant-popover-inner,
.ant-popover-inner * {
  @apply dark:!text-white dark:!bg-area-fill;
}

.ant-popover-arrow::before,
.ant-popover-arrow::after {
  @apply dark:!bg-area-fill;
}

/* ant-picker 深色模式适配 */
.ant-picker {
  @apply dark:!bg-input-fill dark:!text-white dark:!border-input-border;
}
.ant-picker-input > input {
  @apply dark:!bg-input-fill dark:!text-white dark:!placeholder-[var(--guide-text-color)];
}
.ant-picker-suffix,
.ant-picker-clear {
  @apply dark:!text-white;
}
.ant-picker-dropdown,
.ant-picker-panel,
.ant-picker-panel-container {
  @apply dark:!bg-area-fill dark:!text-white;
}
.ant-picker-cell {
  @apply dark:!text-white;
}
.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner,
.ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
.ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
  @apply dark:!bg-primary dark:!text-white;
}
.ant-picker-header,
.ant-picker-footer {
  @apply dark:!bg-area-fill dark:!text-white;
}
.ant-picker-today-btn {
  @apply dark:!text-primary;
}
.ant-picker-dropdown .ant-picker-panel-container {
  @apply dark:!border-input-border;
}

/* 区间中间背景色（覆盖伪元素） */
td.ant-picker-cell.ant-picker-cell-in-range::before,
td.ant-picker-cell.ant-picker-cell-range-start::before,
td.ant-picker-cell.ant-picker-cell-range-end::before {
  @apply dark:!bg-primary-light;
}

.ant-picker-clear {
  @apply dark:!bg-input-fill;
}

.ant-picker-suffix{
  @apply dark:!bg-input-fill;
}

.ant-picker-separator {
  @apply dark:!text-white;
}

.ant-breadcrumb-separator{
  @apply dark:!text-[var(--grey-auxiliary-color)];
}

.ant-switch-inner{
  @apply dark:!bg-[var(--btn-color)];
}

.ant-checkbox{
  .ant-checkbox-inner{
    @apply dark:!bg-[var(--grey-auxiliary-color)] dark:!border dark:!border-border;   
  }
}
.ant-checkbox-indeterminate{
  .ant-checkbox-inner{
    &::after{
      @apply dark:!bg-white;
    }  
  }
}

.ant-checkbox-checked::after{
  @apply dark:!border dark:!border-border
}

.ant-select-tree-dropdown {
  @apply dark:!bg-global-background dark:!text-white; 
}

.ant-select-tree-dropdown,
.ant-select-dropdown,
.ant-select-tree {
  @apply dark:!bg-global-background dark:!text-white;
  .ant-select-tree-node-content-wrapper :hover{
    @apply dark:!bg-primary-hover
  }
}
.ant-select-tree-node-selected{
  @apply dark:!bg-primary-hover
}

.cm-content{
  @apply dark:!text-white;
}
