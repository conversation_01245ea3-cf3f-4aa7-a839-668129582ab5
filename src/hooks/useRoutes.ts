import { useRouter } from 'vue-router'

// 处理单个路由项
const processRoute = (route: any) => {
  // 生成key属性
  const key = route?.path
    ?.replace(/^\//, '') // 移除开头的/
    ?.replace(/\/(.+)/, '-$1') // 将/替换为-
    ?.replace(/\/+/g, '-') // 将多个/替换为单个-
    ?.toLowerCase()

  // 解构meta属性到当前对象
  const meta = route.meta || {}
  const routeItem = {
    ...route,
    isLeaf: !!route?.children?.length,
    key,
    ...meta,
    icon: meta.icon ? `iconfont ${meta.icon} text-xl` : '',
  }

  // 如果有children，递归处理
  if (route.children) {
    routeItem.children = route.children.map(processRoute)
  }

  return routeItem
}

// 分组路由的hooks
export const useRoutesGroup = () => {
  const router = useRouter()
  const routes = router.getRoutes()

  // 创建分组对象
  const groupRoutes = {}

  // 遍历顶级路由
  routes.forEach((route, index) => {
    // 处理当前路由
    const processedRoute = processRoute(route)
    if (processedRoute?.hide) {
      return
    }
    processedRoute.key = processedRoute.key + index

    // 提取第一级路径
    const firstLevelPath = useFirstLevelPath(processedRoute)

    // 如果分组不存在，则创建新数组
    if (!groupRoutes[firstLevelPath]) {
      groupRoutes[firstLevelPath] = []
    }

    // 将完整的路由对象添加到分组中
    groupRoutes[firstLevelPath].push(processedRoute)
  })

  return groupRoutes
}

export const useFirstLevelPath = (route: any) => {
  const pathSegments = route?.path?.split('/')
  let firstLevelPath = 'home'
  if (pathSegments) {
    firstLevelPath = pathSegments[1] || 'home'
  }
  return firstLevelPath
}
