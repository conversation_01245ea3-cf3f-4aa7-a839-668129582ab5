/**
 * 版本管理工具类
 * 处理画布版本、Agent版本的访问逻辑
 */

export interface VersionInfo {
  id: string
  version: string
  description: string
  isActive: boolean
  isLatest: boolean
  publishTime: string
  config?: any
}

export interface AgentVersionInfo {
  id: string
  agentId: string
  version: string
  description: string
  isActive: boolean
  isLatest: boolean
  isSystem: boolean
  createTime: string
  config?: any
}

export class VersionManager {
  private static instance: VersionManager
  private canvasVersions: Map<string, VersionInfo[]> = new Map()
  private agentVersions: Map<string, AgentVersionInfo[]> = new Map()
  private activeCanvasVersions: Map<string, string> = new Map()
  private activeAgentVersions: Map<string, string> = new Map()

  private constructor() {
    this.initializeVersions()
  }

  public static getInstance(): VersionManager {
    if (!VersionManager.instance) {
      VersionManager.instance = new VersionManager()
    }
    return VersionManager.instance
  }

  /**
   * 初始化版本数据
   */
  private initializeVersions() {
    // 从localStorage或API加载版本数据
    this.loadVersionsFromStorage()
  }

  /**
   * 从存储加载版本数据
   */
  private loadVersionsFromStorage() {
    try {
      const canvasData = localStorage.getItem('canvas-versions')
      const agentData = localStorage.getItem('agent-versions')
      const activeCanvasData = localStorage.getItem('active-canvas-versions')
      const activeAgentData = localStorage.getItem('active-agent-versions')

      if (canvasData) {
        const parsed = JSON.parse(canvasData)
        this.canvasVersions = new Map(parsed)
      }

      if (agentData) {
        const parsed = JSON.parse(agentData)
        this.agentVersions = new Map(parsed)
      }

      if (activeCanvasData) {
        const parsed = JSON.parse(activeCanvasData)
        this.activeCanvasVersions = new Map(parsed)
      }

      if (activeAgentData) {
        const parsed = JSON.parse(activeAgentData)
        this.activeAgentVersions = new Map(parsed)
      }
    } catch (error) {
      console.error('加载版本数据失败:', error)
    }
  }

  /**
   * 保存版本数据到存储
   */
  private saveVersionsToStorage() {
    try {
      localStorage.setItem('canvas-versions', JSON.stringify(Array.from(this.canvasVersions.entries())))
      localStorage.setItem('agent-versions', JSON.stringify(Array.from(this.agentVersions.entries())))
      localStorage.setItem('active-canvas-versions', JSON.stringify(Array.from(this.activeCanvasVersions.entries())))
      localStorage.setItem('active-agent-versions', JSON.stringify(Array.from(this.activeAgentVersions.entries())))
    } catch (error) {
      console.error('保存版本数据失败:', error)
    }
  }

  /**
   * 获取画布的当前活跃版本
   */
  public getCurrentCanvasVersion(canvasId: string): VersionInfo | null {
    const activeVersionId = this.activeCanvasVersions.get(canvasId)
    if (!activeVersionId) return null

    const versions = this.canvasVersions.get(canvasId) || []
    return versions.find(v => v.id === activeVersionId) || null
  }

  /**
   * 获取画布的最新版本
   */
  public getLatestCanvasVersion(canvasId: string): VersionInfo | null {
    const versions = this.canvasVersions.get(canvasId) || []
    return versions.find(v => v.isLatest) || null
  }

  /**
   * 获取Agent的当前活跃版本
   */
  public getCurrentAgentVersion(agentId: string): AgentVersionInfo | null {
    const activeVersionId = this.activeAgentVersions.get(agentId)
    if (!activeVersionId) return null

    const versions = this.agentVersions.get(agentId) || []
    return versions.find(v => v.id === activeVersionId) || null
  }

  /**
   * 获取Agent的最新版本
   */
  public getLatestAgentVersion(agentId: string): AgentVersionInfo | null {
    const versions = this.agentVersions.get(agentId) || []
    return versions.find(v => v.isLatest) || null
  }

  /**
   * 获取系统级Agent的版本（始终返回最新版本）
   */
  public getSystemAgentVersion(agentId: string): AgentVersionInfo | null {
    const versions = this.agentVersions.get(agentId) || []
    const systemVersions = versions.filter(v => v.isSystem)
    return systemVersions.find(v => v.isLatest) || null
  }

  /**
   * 设置画布活跃版本
   */
  public setActiveCanvasVersion(canvasId: string, versionId: string): boolean {
    const versions = this.canvasVersions.get(canvasId) || []
    const version = versions.find(v => v.id === versionId)
    
    if (!version) return false

    // 更新活跃状态
    versions.forEach(v => v.isActive = v.id === versionId)
    this.activeCanvasVersions.set(canvasId, versionId)
    this.saveVersionsToStorage()
    
    return true
  }

  /**
   * 设置Agent活跃版本
   */
  public setActiveAgentVersion(agentId: string, versionId: string): boolean {
    const versions = this.agentVersions.get(agentId) || []
    const version = versions.find(v => v.id === versionId)
    
    if (!version) return false

    // 系统级Agent不允许手动设置版本
    if (version.isSystem) return false

    // 更新活跃状态
    versions.forEach(v => v.isActive = v.id === versionId)
    this.activeAgentVersions.set(agentId, versionId)
    this.saveVersionsToStorage()
    
    return true
  }

  /**
   * 创建画布版本
   */
  public createCanvasVersion(canvasId: string, versionInfo: Omit<VersionInfo, 'id'>): string {
    const versions = this.canvasVersions.get(canvasId) || []
    const newVersion: VersionInfo = {
      ...versionInfo,
      id: `canvas-${canvasId}-${Date.now()}`
    }

    // 如果是最新版本，取消其他版本的最新状态
    if (newVersion.isLatest) {
      versions.forEach(v => v.isLatest = false)
    }

    // 如果是活跃版本，取消其他版本的活跃状态并设置为当前活跃版本
    if (newVersion.isActive) {
      versions.forEach(v => v.isActive = false)
      this.activeCanvasVersions.set(canvasId, newVersion.id)
    }

    versions.push(newVersion)
    this.canvasVersions.set(canvasId, versions)
    this.saveVersionsToStorage()

    return newVersion.id
  }

  /**
   * 创建Agent版本
   */
  public createAgentVersion(agentId: string, versionInfo: Omit<AgentVersionInfo, 'id'>): string {
    const versions = this.agentVersions.get(agentId) || []
    const newVersion: AgentVersionInfo = {
      ...versionInfo,
      id: `agent-${agentId}-${Date.now()}`
    }

    // 如果是最新版本，取消其他版本的最新状态
    if (newVersion.isLatest) {
      versions.forEach(v => v.isLatest = false)
    }

    // 如果是活跃版本，取消其他版本的活跃状态并设置为当前活跃版本
    if (newVersion.isActive) {
      versions.forEach(v => v.isActive = false)
      this.activeAgentVersions.set(agentId, newVersion.id)
    }

    versions.push(newVersion)
    this.agentVersions.set(agentId, versions)
    this.saveVersionsToStorage()

    return newVersion.id
  }

  /**
   * 获取访问时应该使用的版本
   * 画布：始终使用最新版本
   * Agent：系统级使用最新版本，自定义使用指定版本
   */
  public getAccessVersion(type: 'canvas' | 'agent', id: string, isSystem = false): VersionInfo | AgentVersionInfo | null {
    if (type === 'canvas') {
      // 画布访问时始终使用最新版本
      return this.getLatestCanvasVersion(id)
    } else {
      // Agent访问逻辑
      if (isSystem) {
        // 系统级Agent始终使用最新版本
        return this.getSystemAgentVersion(id)
      } else {
        // 自定义Agent使用当前活跃版本，如果没有则使用最新版本
        return this.getCurrentAgentVersion(id) || this.getLatestAgentVersion(id)
      }
    }
  }

  /**
   * 版本回退
   */
  public rollbackToVersion(type: 'canvas' | 'agent', id: string, versionId: string): boolean {
    if (type === 'canvas') {
      return this.setActiveCanvasVersion(id, versionId)
    } else {
      return this.setActiveAgentVersion(id, versionId)
    }
  }

  /**
   * 获取所有版本列表
   */
  public getVersionList(type: 'canvas' | 'agent', id: string): (VersionInfo | AgentVersionInfo)[] {
    if (type === 'canvas') {
      return this.canvasVersions.get(id) || []
    } else {
      return this.agentVersions.get(id) || []
    }
  }

  /**
   * 删除版本
   */
  public deleteVersion(type: 'canvas' | 'agent', id: string, versionId: string): boolean {
    const versions = type === 'canvas' 
      ? this.canvasVersions.get(id) || []
      : this.agentVersions.get(id) || []

    const versionIndex = versions.findIndex(v => v.id === versionId)
    if (versionIndex === -1) return false

    const version = versions[versionIndex]
    
    // 不能删除活跃版本或最新版本
    if (version.isActive || version.isLatest) return false

    versions.splice(versionIndex, 1)
    
    if (type === 'canvas') {
      this.canvasVersions.set(id, versions)
    } else {
      this.agentVersions.set(id, versions)
    }
    
    this.saveVersionsToStorage()
    return true
  }
}

// 导出单例实例
export const versionManager = VersionManager.getInstance()

// 版本比较工具函数
export function compareVersion(v1: string, v2: string): number {
  const parts1 = v1.replace(/^v/, '').split('.').map(Number)
  const parts2 = v2.replace(/^v/, '').split('.').map(Number)
  
  for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
    const part1 = parts1[i] || 0
    const part2 = parts2[i] || 0
    
    if (part1 > part2) return 1
    if (part1 < part2) return -1
  }
  
  return 0
}

// 生成下一个版本号
export function generateNextVersion(currentVersion: string, type: 'major' | 'minor' | 'patch'): string {
  const parts = currentVersion.replace(/^v/, '').split('.').map(Number)
  
  switch (type) {
    case 'major':
      return `v${parts[0] + 1}.0.0`
    case 'minor':
      return `v${parts[0]}.${(parts[1] || 0) + 1}.0`
    case 'patch':
      return `v${parts[0]}.${parts[1] || 0}.${(parts[2] || 0) + 1}`
    default:
      return currentVersion
  }
}
