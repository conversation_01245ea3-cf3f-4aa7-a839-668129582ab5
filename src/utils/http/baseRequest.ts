// @ts-nocheck
import request from './request'

const baseRequest: IRequest = {
  /**
   * @func get请求
   * @desc get方式请求
   * @param { String } url
   * @param { Object } params
   * @param { Object } arraybuffer
   * @return { Promise } Promise对象
   */
  get: (url, params, isFlow?) =>
    new Promise((resolve, reject) => {
      request({
        method: 'get',
        url,
        params,
        responseType: isFlow ? 'arraybuffer' : undefined,
      })
        .then((response) => {
          resolve(response.data)
        })
        .catch((error) => {
          reject(error)
        })
    }),
  /**
   * @func delete请求
   * @desc delete方式请求
   * @param { String } url
   * @param { Object } params
   * @return { Promise } Promise对象
   */
  delete: (url, params, extend = {}) =>
    new Promise((resolve, reject) => {
      request({
        method: 'delete',
        url,
        params,
        ...extend,
      })
        .then((response) => {
          resolve(response.data)
        })
        .catch((error) => {
          reject(error)
        })
    }),
  /**
   * @func post请求
   * @desc post方式请求
   * @param { String } url
   * @param { Object } data
   * @param { Function } onUploadProgress
   * @return { Promise } Promise对象
   */
  post: (url: string, data?: any, headers?: Record<string, string>, config?: any) =>
    new Promise((resolve, reject) => {
      request({
        method: 'post',
        url,
        data,
        headers: {
          ...headers,
        },
        ...config,
      })
        .then((response) => {
          resolve(response.data)
        })
        .catch((error) => {
          reject(error)
        })
    }),
  /**
   * @func post请求
   * @desc post发送二进制
   * @param { String } url
   * @param { Object } formData
   * @return { Promise } Promise对象
   */
  postBinary: <T>(url: string, postData: any) =>
    new Promise<IResType<T>>((resolve, reject) => {
      const formData = new FormData()
      formData.append('temperature', '0.0')
      formData.append('temperature_inc', '0.2')
      formData.append('response_format', 'json')
      // 添加二进制文件和其他数据到 FormData 中
      Object.keys(postData).forEach((key) => {
        // 如果是二进制数据，则单独处理
        if (key === 'binaryData') {
          formData.append('file', postData[key])
        } else {
          formData.append(key, postData[key])
        }
      })

      request({
        method: 'post',
        url,
        data: formData, // 将 FormData 作为请求体
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
        .then((response: any) => {
          // 使用 any 类型接收 Axios 响应
          // 在此处进行你需要的处理
          resolve(response)
        })
        .catch((error: any) => {
          reject(error)
        })
    }),
  /**
   * @func put请求
   * @desc put方式请求
   * @param { String } url
   * @param { Object } data
   * @return { Promise } Promise对象
   */
  put: (url, data) =>
    new Promise((resolve, reject) => {
      request({
        method: 'put',
        url,
        data,
      })
        .then((response) => {
          resolve(response.data)
        })
        .catch((error) => {
          reject(error)
        })
    }),
  /**
   * @func patch请求
   * @desc patch方式请求
   * @param { String } url
   * @param { Object } data
   * @return { Promise } Promise对象
   */
  patch: (url, data) =>
    new Promise((resolve, reject) => {
      request({
        method: 'patch',
        url,
        data,
      })
        .then((response) => {
          resolve(response.data)
        })
        .catch((error) => {
          reject(error)
        })
    }),
  /**
   * @func formDataRequest
   * @desc 通用的FormData请求方法
   * @param { String } url 请求地址
   * @param { Object } data 请求数据
   * @param { String } method 请求方法，默认为 'post'
   * @return { Promise } Promise对象
   */
  formDataRequest: <T>(
    url: string,
    data: Record<string, any>,
    method: 'post' | 'put' | 'patch' = 'post',
  ) =>
    new Promise<T>((resolve, reject) => {
      const formData = new FormData()

      // 将所有数据添加到 FormData 中
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, value)
      })

      request({
        method,
        url,
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
        .then((response) => {
          resolve(response.data)
        })
        .catch((error) => {
          reject(error)
        })
    }),
  /**
   * @func downloadFile
   * @desc 通用文件下载方法
   * @param { String } url 请求地址
   * @param { Object } params 请求参数
   * @param { String } fileName 下载文件名
   * @return { Promise } Promise对象
   */
  downloadFile: (url: string, params: any = {}, fileName?: string) =>
    new Promise((resolve, reject) => {
      request({
        method: 'get',
        url,
        params,
        responseType: 'arraybuffer',
      })
        .then((response) => {
          // 获取文件名
          let downloadFileName = fileName

          // 如果没有提供文件名，尝试从响应头获取
          if (!downloadFileName) {
            const contentDisposition = response.headers['content-disposition']
            if (contentDisposition) {
              const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
              const matches = filenameRegex.exec(contentDisposition)
              if (matches != null && matches[1]) {
                downloadFileName = matches[1].replace(/['"]/g, '')
              }
            }
            // 如果仍然没有文件名，使用默认名称
            if (!downloadFileName) {
              downloadFileName = 'download.file'
            }
          }

          // 创建Blob对象
          const blob = new Blob([response.data], {
            type: response.headers['content-type'] || 'application/octet-stream',
          })

          // 创建下载链接
          const downloadUrl = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = downloadUrl
          link.download = downloadFileName

          // 触发下载
          document.body.appendChild(link)
          link.click()

          // 清理
          window.URL.revokeObjectURL(downloadUrl)
          document.body.removeChild(link)

          resolve(true)
        })
        .catch((error) => {
          reject(error)
        })
    }),
}

export default baseRequest
