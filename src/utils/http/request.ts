// @ts-nocheck
import axios from 'axios'
import { message } from 'ant-design-vue'
import { tokenError } from './data/ErrorMap'
import router from '@/router'

const http = axios.create({
  baseURL: window?.SITE_CONFIG?.DESIGNER_BASE_URL || '',
  timeout: 600000,
})

// 请求头拦截器
http.interceptors.request.use(
  (config) => {
    // oneApi接口
    if (config.url?.startsWith('/v1/chat/completions')) {
      config.baseURL = window?.SITE_CONFIG?.ONE_API_URL
    }
    config.headers['Authorization'] = localStorage.getItem('token')
    return config
  },
  (error) => {
    // TODO 对请求错误做些什么
    return Promise.reject(error)
  },
)

// 响应体拦截器
http.interceptors.response.use(
  (response) => {
    if (response.status === 200) {
      if (response.data.code !== 200 && response.data.statusText) {
        message.error(response.data.statusText)
      }
      return response
    }
    return Promise.reject(response)
  },
  (error) => {
    if (axios.isCancel(error)) {
      return Promise.reject(error)
    }
    if (error.response?.status === 401) {
      // 401 未授权
      router.push('/login')
    } else {
      message.error('服务开小差了, 请稍后重试')
    }
    return Promise.reject(error)
  },
)

export default http
