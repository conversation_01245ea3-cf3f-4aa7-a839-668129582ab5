///
/// Copyright (c) 2019 Of Him Code Technology Studio
/// Jpom is licensed under Mulan PSL v2.
/// You can use this software according to the terms and conditions of the Mulan PSL v2.
/// You may obtain a copy of Mulan PSL v2 at:
/// 			http://license.coscl.org.cn/MulanPSL2
/// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
/// See the Mulan PSL v2 for more details.
///
import { ref } from 'vue'

export function getHashQuery() {
  const querys: Record<string, string> = {}
  location.hash.replace(/[?&]+([^=&]+)=([^&]*)/gi, (_match: string, key: string, value: string) => {
    querys[key] = value
    return ''
  })
  return querys
}

// root 元素
const root = document.documentElement
// 获取计算后的样式
// https://developer.mozilla.org/en-US/docs/Web/API/Window/getComputedStyle
const style = getComputedStyle(root)
const zIndexStart = Number(style.getPropertyValue('--increase-z-index'))
let incCount = 0

export function increaseZIndex() {
  const useIndex = zIndexStart + incCount++
  // 设置全局变量,避免全局提示，下拉框，下拉菜单显示错位
  document.documentElement.style.setProperty('--increase-z-index', String(zIndexStart + incCount))
  return useIndex
}

// 检查是否在Electron环境中
export const isElectron = () => {
  return window?.navigator?.userAgent?.toLowerCase()?.includes('electron')
}

// 检查文件（iconsUrl）里是否存在某个图标（id为type）
export const checkIconExists = async (type, iconsUrl) => {
  try {
    // 1. 首先尝试从已加载的DOM中查找
    const existingSymbol = document.querySelector(`#${type}`);
    if (existingSymbol) return true;
    
    // 2. 如果DOM中没有，尝试从SVG文件中查找
    const response = await fetch(iconsUrl);
    const svgText = await response.text();
    return svgText.includes(`id="${type}"`);
  } catch (error) {
    console.error('检查图标失败:', error);
    return false;
  }
};

// 创建一个单例的图标缓存
const iconTypes = ref(new Map())

// 检查图标是否存在，不存在展示默认图标
export const validateIconType = (type: string, iconsUrl: string) => {
  // 如果已经检查过，直接返回结果
  if (iconTypes.value.has(type)) {
    return iconTypes.value.get(type)
  }
  
  // 首次检查时，先返回默认图标
  iconTypes.value.set(type, 'defaultIcon')
  
  // 异步检查并更新
  checkIconExists(type, iconsUrl).then(hasIcon => {
    const newMap = new Map(iconTypes.value)
    newMap.set(type, hasIcon ? type : 'defaultIcon')
    iconTypes.value = newMap
  })
  
  return iconTypes.value.get(type)
}