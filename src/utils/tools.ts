export const getRandomStr = function (len = 32) {
  const $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
  const maxPos = $chars.length
  let str = ''
  for (let i = 0; i < len; i++) {
    str += $chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return str
}


// 判断2个数组对象是否不同，包括索引位置不同
export const areObjectArraysDifferent = (arr1, arr2) => {
  // 如果数组长度不同，则它们必定不相同
  if (arr1.length !== arr2.length) {
    return true
  }

  // 遍历数组元素进行比较
  for (let i = 0; i < arr1.length; i++) {
    const obj1 = arr1[i]
    const obj2 = arr2[i]

    // 获取对象的属性名数组
    const keys1 = Object.keys(obj1)
    const keys2 = Object.keys(obj2)

    // 遍历对象属性进行比较
    for (const key of keys1) {
      // 如果对象2中不存在当前属性，则跳过当前循环
      if (!keys2.includes(key)) {
        continue
      }

      const value1 = obj1[key]
      const value2 = obj2[key]

      // 如果当前属性值不相同，则对象数组不相同
      if (value1 !== value2) {
        return true
      }
    }
  }

  // 遍历完所有元素后没有发现不同的项，说明对象数组相同，返回 false
  return false
}

export const initializeDefaultNodeData = (nodeParams) => {
  const initialValues = {}

  for (let i = 0; i < nodeParams.length; i += 1) {
    const input = nodeParams[i]
    initialValues[input.name] = input.default || ''
  }

  return initialValues
}
interface Inputs {
  id?:string
}

interface Outputs {
  name: string
  label: string
  type: string
  description:string
  options?:Options[]
  default?: string
}
interface Options {
  id: string
  name: string
  label: string
  description: string
  type:string
}
export const initNode = (nodeData, newNodeId) => {
  const inputAnchors = <Inputs[]>[]
  const inputParams = <Inputs[]>[]
  const incoming = nodeData.inputs ? nodeData.inputs.length : 0
  const outgoing = 1

  const whitelistTypes = [
    'asyncOptions',
    'options',
    'multiOptions',
    'datagrid',
    'string',
    'number',
    'boolean',
    'password',
    'json',
    'code',
    'date',
    'file',
    'folder'
  ]

  // Inputs
  for (let i = 0; i < incoming; i += 1) {
    const newInput = {
      ...nodeData.inputs[i],
      id: `${newNodeId}-input-${nodeData.inputs[i].name}-${nodeData.inputs[i].type}`
    }
    if (whitelistTypes.includes(nodeData.inputs[i].type)) {
      inputParams.push(newInput)
    } else {
      inputAnchors.push(newInput)
    }
  }

  // Credential
  if (nodeData.credential) {
    const newInput = {
      ...nodeData.credential,
      id: `${newNodeId}-input-${nodeData.credential.name}-${nodeData.credential.type}`
    }
    inputParams.unshift(newInput)
  }

  // Outputs
  const outputAnchors = <Outputs[]>[]

  for (let i = 0; i < outgoing; i += 1) {
    if (nodeData.outputs && nodeData.outputs.length) {
      const options = <Options[]>[]
      for (let j = 0; j < nodeData.outputs.length; j += 1) {
        let baseClasses = ''
        let type = ''

        const outputBaseClasses = nodeData.outputs[j].baseClasses ?? []
        if (outputBaseClasses.length > 1) {
          baseClasses = outputBaseClasses.join('|')
          type = outputBaseClasses.join(' | ')
        } else if (outputBaseClasses.length === 1) {
          baseClasses = outputBaseClasses[0]
          type = outputBaseClasses[0]
        }

        const newOutputOption = {
          id: `${newNodeId}-output-${nodeData.outputs[j].name}-${baseClasses}`,
          name: nodeData.outputs[j].name,
          label: nodeData.outputs[j].label,
          description: nodeData.outputs[j].description ?? '',
          type
        }
        options.push(newOutputOption)
      }
      const newOutput = {
        name: 'output',
        label: 'Output',
        type: 'options',
        description: nodeData.outputs[0].description ?? '',
        options,
        default: nodeData.outputs[0].name
      }
      outputAnchors.push(newOutput)
    } else {
      const newOutput = {
        id: `${newNodeId}-output-${nodeData.name}-${nodeData.baseClasses.join('|')}`,
        name: nodeData.name,
        label: nodeData.type,
        description: nodeData.description ?? '',
        type: nodeData.baseClasses.join(' | ')
      }
      outputAnchors.push(newOutput)
    }
  }

  // Inputs
  if (nodeData.inputs) {
    nodeData.inputAnchors = inputAnchors
    nodeData.inputParams = inputParams
    nodeData.inputs = initializeDefaultNodeData(nodeData.inputs)
  } else {
    nodeData.inputAnchors = []
    nodeData.inputParams = []
    nodeData.inputs = {}
  }

  // Outputs
  if (nodeData.outputs) {
    nodeData.outputs = initializeDefaultNodeData(outputAnchors)
  } else {
    nodeData.outputs = {}
  }
  nodeData.outputAnchors = outputAnchors

  // Credential
  if (nodeData.credential) nodeData.credential = ''

  nodeData.id = newNodeId

  return nodeData
}
/**
* 获取url参数
* @param url
* @returns {Object} 返回对象
*/
export const getUrlParams = (url?)=> {
  // 如果url未提供，默认使用当前页面的URL
  if (!url) url = window.location.href

  const pattern = /(\w+)=(\w+)/ig
  const parames = {}
  url.replace(pattern, ($, $1, $2) => {
    parames[$1] = $2
  })
  return parames
}

/**
 * 向iframe父级发送消息
 * @param target 目标
 * @param msgType 消息类型
 * @param data 消息数据
 */
export const sendMessageToParent = (target: string, msgType: string, data: unknown) => {
  window.parent?.postMessage({
    data: {
      target,
      msgType,
      params: data
    },
    source: 'subIframe'
  })
}

export const generateUUID = () => {
  return 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[x]/g, function(c) {
    const r = Math.random() * 16 | 0,
      v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 防抖函数：在一定时间内，多次触发同一事件，只执行最后一次
 * @param fn 需要防抖的函数
 * @param delay 延迟时间（毫秒），默认300ms
 * @returns 返回一个防抖后的函数
 * @example
 * const debouncedFn = debounce(() => {
 *   console.log('执行操作');
 * }, 500);
 */
export const debounce = <T extends (...args: unknown[]) => unknown>(fn: T, delay = 300) => {
  let timer: ReturnType<typeof setTimeout> | null = null
  
  return function (this: unknown, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    
    timer = setTimeout(() => {
      fn.apply(this, args)
      timer = null
    }, delay)
  }
}

