/**
 * 文件选择函数
 * @param options 选项配置
 * @param options.multiple 是否允许多选文件
 * @param options.accept 接受的文件类型
 * @returns Promise<File[]> 返回选中的文件数组
 */
export const selectFile = (
  options: {
    multiple?: boolean
    accept?: string
  } = {},
): Promise<File[]> => {
  return new Promise((resolve, reject) => {
    // 创建input元素
    const input = document.createElement('input')
    input.type = 'file'

    // 设置是否多选
    if (options.multiple) {
      input.multiple = true
    }

    // 设置接受的文件类型
    if (options.accept) {
      input.accept = options.accept
    }

    // 监听change事件
    input.onchange = (event) => {
      const files = (event.target as HTMLInputElement).files
      if (files && files.length > 0) {
        resolve(Array.from(files))
      } else {
        reject(new Error('未选择任何文件'))
      }
    }

    // 触发点击事件
    input.click()
  })
}
