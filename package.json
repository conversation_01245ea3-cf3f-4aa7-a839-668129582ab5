{"name": "ai-biggroup-front", "displayName": "Agent平台", "description": "Agent平台", "private": true, "version": "0.0.1", "scripts": {"dev": "vite", "build": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-dnd": "^2.0.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-plugin-selection": "^2.2.1", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-vue-shape": "^2.1.2", "@imengyu/vue3-context-menu": "^1.3.9", "@langchain/openai": "^0.0.28", "@monaco-editor/loader": "^1.5.0", "@undecaf/zbar-wasm": "^0.11.0", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/compiler-dom": "^3.5.13", "@vueuse/core": "^12.7.0", "@yss-design/ui": "^0.2.13", "@yss-design/utils": "^0.2.3", "@yss/ai-chat-components": "^1.7.4", "ant-design-vue": "^4.2.6", "axios": "^1.6.5", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "echarts": "^5.5.1", "jaison": "^1.1.0", "json-editor-vue": "^0.17.3", "json-editor-vue3": "^1.1.1", "jsoneditor": "^10.1.3", "langchain": "^0.1.34", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "pako": "^2.1.0", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "4.1.0", "prismjs": "^1.29.0", "qrcode": "^1.5.4", "splitpanes": "^3.1.5", "vite-plugin-vue-inspector": "^5.3.1", "vue": "^3.2.47", "vue-icons-plus": "^0.1.8", "vue-router": "^4.1.6", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-sfc-loader": "^0.9.5", "vue3-smooth-dnd": "^0.0.6", "vuedraggable": "^4.1.0", "x6-html-shape": "0.4.9", "xterm": "^5.3.0", "xterm-addon-attach": "^0.9.0", "xterm-addon-fit": "^0.8.0"}, "devDependencies": {"@babel/cli": "^7.24.1", "@babel/core": "^7.24.4", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/preset-env": "^7.24.4", "@types/node": "^20.14.2", "@types/splitpanes": "^2.2.6", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-vue": "^5.0.4", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.20", "babel-core": "^6.26.3", "babel-loader": "^9.1.3", "babel-preset-env": "^1.7.0", "babel-preset-stage-3": "^6.24.1", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "^9.22.0", "lodash-es": "^4.17.21", "postcss": "^8.5.1", "prettier-eslint": "^16.3.0", "sass": "^1.83.4", "tailwindcss": "^3.4.17", "typescript": "^5.4.5", "unplugin-auto-import": "^0.17.2", "uuid": "^11.0.5", "vite": "^5.2.0", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^2.0.6"}}