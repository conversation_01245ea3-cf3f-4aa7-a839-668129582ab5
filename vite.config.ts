import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import AutoImport from 'unplugin-auto-import/vite'
import viteCompression from 'vite-plugin-compression'
import vuejsx from '@vitejs/plugin-vue-jsx'
import inspector from 'vite-plugin-vue-inspector'
// jenkinsimy
// https://vitejs.dev/config/
export default defineConfig({
  css: {
    preprocessorOptions: {
      scss: {
        api: "modern-compiler",
        silenceDeprecations: ["legacy-js-api"],
      },
    },
  },
  plugins: [
    vue(),
    vuejsx(),
    inspector(),
    viteCompression({
      verbose: true,
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz',
      deleteOriginFile: false,
    }),
    AutoImport({
      imports: ['vue', 'vue-router'],
      dts: 'src/auto-import.d.ts',
      eslintrc: {
        // 已存在文件设置默认 false，需要更新时再打开，防止每次更新都重新生成
        enabled: true,
        // 生成文件地址和名称
        filepath: './.eslintrc-auto-import.json',
        globalsPropValue: true,
      }
    })
  ],
  base: '/ai-agent/',
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'xterm/css': fileURLToPath(new URL('./node_modules/xterm/css', import.meta.url)),
      vue: 'vue/dist/vue.esm-bundler.js',
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5174,
    proxy: {
      '/aip': {
        // 后端服务地址
        target: 'http://**********:8200/',
        changeOrigin: true,
      },
      '/crawler': {
        // 爬虫服务地址
        target: 'http://**********:7001',
        changeOrigin: true,
      }
    },
  },
})
