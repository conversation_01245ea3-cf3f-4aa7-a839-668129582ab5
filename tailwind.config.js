/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: 'var(--primary-color)',
        'primary-hover': 'var(--primary-hover)',
        'primary-light': 'var(--primary-light)',
        'text-primary': 'var(--text-primary)',
        'text-secondary': 'var(--text-secondary)',
        'text-white': 'var(--text-white)',
        'bg-light': 'var(--bg-light)',
        bg: 'var(--bg-color)',
        border: 'var(--border-color)',
        'text-label': 'var(--text-label)',
        'warn-text': 'var(--warn-text)',
        'warn-bg': 'var(--warn-bg)',
        global: {
          background: 'var(--global-background-color)',
        },
        'area-fill': 'var(--area-fill-color)',
        'input-fill': 'var(--input-fill-color)',
        'input-border': 'var(--input-border-color)',
      },
      textColor: {
        custom: 'var(--text-color)',
      },
    }
  },
  plugins: [],
  darkMode: 'class',
}
