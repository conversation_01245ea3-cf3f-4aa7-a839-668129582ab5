<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>预览模板</title>
  <style>
    /* 示例内容样式 */
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .header {
      background-color: #f8f9fa;
      padding: 20px;
      margin-bottom: 20px;
      border-radius: 5px;
    }
    .content {
      display: flex;
      gap: 20px;
    }
    .sidebar {
      width: 250px;
      background-color: #f0f0f0;
      padding: 15px;
      border-radius: 5px;
    }
    .main {
      flex: 1;
      background-color: #fff;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 5px;
    }
    .footer {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      text-align: center;
      border-radius: 5px;
    }
    .btn {
      display: inline-block;
      padding: 8px 16px;
      background-color: #007bff;
      color: white;
      border-radius: 4px;
      text-decoration: none;
      margin-right: 10px;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>网页设计预览</h1>
      <p>这是一个用于演示DOM元素选择和高亮的示例页面</p>
      <div>
        <a href="#" class="btn">按钮1</a>
        <a href="#" class="btn">按钮2</a>
      </div>
    </div>
    
    <div class="content">
      <div class="sidebar">
        <h3>侧边栏</h3>
        <ul>
          <li><a href="#">菜单项 1</a></li>
          <li><a href="#">菜单项 2</a></li>
          <li><a href="#">菜单项 3</a></li>
          <li><a href="#">菜单项 4</a></li>
        </ul>
      </div>
      
      <div class="main">
        <h2>主要内容区域</h2>
        <p>这里是主要内容。您可以在此区域添加文本、图片和其他元素。</p>
        
        <div class="card">
          <h3>卡片标题 1</h3>
          <p>这是一个卡片组件的示例，包含一些文本内容。</p>
          <button class="btn">了解更多</button>
        </div>
        
        <div class="card">
          <h3>卡片标题 2</h3>
          <p>这是另一个卡片组件的示例，您可以添加不同的内容。</p>
          <button class="btn">查看详情</button>
        </div>
      </div>
    </div>
    
    <div class="footer">
      <p>页脚区域 &copy; 2025</p>
    </div>
  </div>
</body>
</html>
